package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ExpireGrabOrderExeCmd;
import com.ctrip.dcs.application.command.api.ExpireGrabOrderCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class ExpireGrabOrderListener {

    @Autowired
    private ExpireGrabOrderExeCmd expireGrabOrderExeCmd;

    @Autowired
    private GrabCentreRepository grabCentreRepository;
    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    @QmqLogTag
    @QmqConsumer(prefix = EventConstants.EXPIRE_GRAB_ORDER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        ExpireGrabOrderCommand command = MessageConverter.INSTANCE.toExpireGrabOrderCommand(message);
        if(CollectionUtils.isEmpty(command.getDriverIds())){
            expireGrabOrderExeCmd.execute(command);
            return;
        }
        command.getDriverIds().forEach(x -> {
            command.setDriverId(x);
            expireGrabOrderExeCmd.execute(command);
        });
    }

    @QmqLogTagPair(key = "supplyOrderId", alias = "supplyOrderId")
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onQunarDispatcherConfirmMessage(Message message) {
        String dspOrderId = message.getStringProperty("supplyOrderId");
        if (StringUtils.isBlank(dspOrderId)) {
            return;
        }
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);
    }
}
