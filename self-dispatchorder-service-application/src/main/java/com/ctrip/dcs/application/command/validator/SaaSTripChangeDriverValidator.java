package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DriverSOPEventTypeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.SopRecord;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.carconfig.UpdateDriverAfterTimeCarConf;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.UseDays;
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil;
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class SaaSTripChangeDriverValidator extends AbstractSaaSOperateValidator {

    public static final Logger logger = LoggerFactory.getLogger(SaaSTripChangeDriverValidator.class);


    /**
     * 查询携程订单用
     */
    @Autowired
    protected DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;
    @Autowired
    protected UpdateDriverAfterTimeCarConf updateDriverAfterTimeCarConf;
    @Autowired
    protected DateZoneConvertUtil dateZoneConvertUtil;


    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();

        //接送机订单状态限制
        Integer orderStatus = dspOrderVO.getOrderStatus();
        //调度确认<接送机订单状态<开始服务
        if (CategoryCodeEnum.isAirportOrStationOrPoint(dspOrderVO.getCategoryCode().getType())) {
            if (dspOrderVO.getOrderStatus() <= OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) {
                logger.warn("SaaSTripChangeDriverValidator_isAirportOrStation_order_status_un_support_assign_error", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }

            if (orderStatus >= OrderStatusEnum.DRIVER_SERVICE_START.getCode()) {
                logger.warn("SaaSTripChangeDriverValidator_isAirportOrStation_order_status_un_support_assign_error", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }

            //未命中策略：当前时间(当地)<最晚派遣司机车辆时间(当地)
            //命中策略：当前时间(当地)<预估用车时间(当地) + 配置的小时
            if (isUpdateDriverAfterTime(dspOrderVO)) {
                throw ErrorCode.CURRENT_TIME_AFTER_LAST_CONFIRM_CAR_TIME_ERROR.getBizException();
            }
        }

        //包车
        if (CategoryCodeEnum.isCharterOrder(dspOrderVO.getCategoryCode().getType())) {
            UseDays useDays = dspOrderVO.getUseDays();
            //包车使用一天
            if(useDays.days(dspOrderVO.getCategoryCode()) == 1){
                //调度确认<包车使用一天，订单状态<开始服务
                if (dspOrderVO.getOrderStatus() <= OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) {
                    logger.warn("SaaSTripChangeDriverValidator_isCharterOrder_order_status_un_support_assign_error", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
                    throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
                }
                if (orderStatus >= OrderStatusEnum.DRIVER_SERVICE_START.getCode()) {
                    logger.warn("SaaSTripChangeDriverValidator_isCharterOrder_order_status_un_support_assign_error", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
                    throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
                }
            }else{
                List<SopRecord> sopRecords = dspOrderVO.getSopRecords();
                //调度确认<包车使用多天，订单状态<=开始服务。当日SOP完成，操作之后的更换
                boolean res = checkCurrentSOP(dspOrderVO, sopRecords);
                if(!res){
                    throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
                }
            }
        }
        //特惠订单不允许指派
        if (dspOrderVO.isSpecialSaleOrder()) {
            throw ErrorCode.SPECIAL_ORDER_NOT_ASSIGN.getBizException();
        }
        //包车需要车
        if (CategoryCodeEnum.isCharterOrder(dspOrderVO.getCategoryCode().getType()) && StringUtils.isEmpty(cmd.getCarLicense())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }

        validateSuppliers(cmd, saaSBusinessVO);

        //司机ID不能相同,禁止重复指派司机
        if(cmd.getDriverId().equals(dspOrderVO.getDriverId().toString())){
            throw ErrorCode.DUPLICATE_ASSIGN_DRIVER_ERR.getBizException();
        }
        // 供应商更改司机，校验是否允许更改
        if (!isOpenChangeButton(cmd, saaSBusinessVO.getDspOrderVO())) {
            throw ErrorCode.ORDER_CAN_NOT_ASSIGN_DRIVER.getBizException();
        }
    }



    private boolean checkCurrentSOP(DspOrderVO dspOrderVO, List<SopRecord> sopRecords) {
        Integer orderStatus = dspOrderVO.getOrderStatus();
        // 获取当前时间
        String currentDateStr = DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT);
        String localDateStr = dateZoneConvertUtil.getLocalTimeByCityId(currentDateStr, DateZoneConvertUtil.BEIJING_CITY_ID, dspOrderVO.getCityId());
        logger.info("SaaSTripChangeDriverValidator_checkCurrentSOP", LocalJsonUtils.toJson(localDateStr));
        String currentDate = DateUtil.formatDate(DateUtil.parse(localDateStr,  DateUtil.DATE_FORMAT), DateUtil.DATE_FORMAT);
        List<SopRecord> currentList = Optional.ofNullable(sopRecords).orElse(Collections.emptyList()).stream().
                filter(x -> x.getItineraryDateLocal().equals(currentDate)).collect(Collectors.toList());
        if(OrderStatusEnum.isDriverOrCarConfirmed(orderStatus)){
            //当前订单状态为司机已确认（230）、司机车辆已确认（240）且司机没有SOP操作（出发、就位、开始服务）,可更换
            if(LocalCollectionUtils.isEmpty(currentList)){
                return true;
            }
            long count = currentList.stream().filter(x -> DriverSOPEventTypeEnum.END.getCode().equals(x.getEventId())).count();
            //当前订单状态为司机已确认（230）、司机车辆已确认（240）且司机有SOP操作（出发、就位、开始服务、结束服务）,当前状态可更改司机
            if(count >= 1){
                return true;
            }
            logger.warn("SaaSTripChangeDriverValidator_checkCurrentSOP_isDriverOrCarConfirmed", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
        }
        //如果是300,400，会走false的逻辑
        if(Objects.equals(OrderStatusEnum.DRIVER_SERVICE_START.getCode(), orderStatus)){
            if(LocalCollectionUtils.isEmpty(currentList)){
                logger.warn("SaaSTripChangeDriverValidator_checkCurrentSOP_500_currentList_null", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
                return false;
            }
            long count = currentList.stream().filter(x -> DriverSOPEventTypeEnum.END.getCode().equals(x.getEventId())).count();
            //当前订单状态为开始服务（500）且司机sop是服务完成,当前状态可更改司机
            if(count >= 1){
                return true;
            }
            logger.warn("SaaSTripChangeDriverValidator_checkCurrentSOP_500", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
        }
        return false;
    }


    public boolean isUpdateDriverAfterTime(DspOrderVO dspOrderVO) {
        Integer time = updateDriverAfterTimeCarConf.get(dspOrderVO.getCategoryCode().getType(), dspOrderVO.getCityId());
        logger.info("SaaSTripChangeDriverValidator_isUpdateDriverAfterTime", LocalJsonUtils.toJson(time));
        if(Objects.isNull(time)){
            time = 0;
        }
        Date date = DateUtil.addHours(dspOrderVO.getEstimatedUseTimeBj(), time);
        return new Date().getTime() > date.getTime();
    }
}
