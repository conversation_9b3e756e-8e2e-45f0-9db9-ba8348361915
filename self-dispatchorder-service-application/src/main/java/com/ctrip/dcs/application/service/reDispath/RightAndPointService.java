package com.ctrip.dcs.application.service.reDispath;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.igt.framework.common.result.Result;

/**
 * 查询权益
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/5/25 15:17
 */
public interface RightAndPointService {

    /**
     * 查询权益
     * 策略基类
     */
    Result<RightAndPointVO> queryRightAndPointDTO(RightAndPointCommand command, BaseDetailVO detailVO);

    /**
     * 入口参数校验
     * @param command
     * @param detailVO
     */
    Result convertAndCheckPrm(ReDispatchSubmitCommand command, BaseDetailVO detailVO);

}