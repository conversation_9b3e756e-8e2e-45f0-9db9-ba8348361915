package com.ctrip.dcs.application.provider.executor.drivergrab;

import com.ctrip.dcs.application.command.CreateGrabOrderExeCmd;
import com.ctrip.dcs.application.command.api.CreateGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabOrderType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 迁移前抢单大厅订单
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class CreateDriverGrabOrdersExecutor extends AbstractRpcExecutor<CreateDriverGrabOrdersRequestType, CreateDriverGrabOrdersResponseType> implements Validator<CreateDriverGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(CreateDriverGrabOrdersExecutor.class);

    @Autowired
    private CreateGrabOrderExeCmd createGrabOrderExeCmd;

    @Override
    public CreateDriverGrabOrdersResponseType execute(CreateDriverGrabOrdersRequestType requestType) {
        try {
            Integer grabOrderTypeValue = requestType.getGrabOrderType();
            if(Objects.isNull(grabOrderTypeValue)){
                grabOrderTypeValue = 1;
            }
            if(grabOrderTypeValue != 1 && grabOrderTypeValue != 0){
                return ServiceResponseUtils.fail(new CreateDriverGrabOrdersResponseType(), ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            GrabOrderType grabOrderType = grabOrderTypeValue == 1 ? GrabOrderType.GRAB_CENTRE : GrabOrderType.BROADCAST;
            createGrabOrderExeCmd.execute(new CreateGrabOrderCommand(requestType.getDspOrderId(), requestType.getDuid(), Sets.newHashSet(requestType.getDriverIds()), grabOrderType));
            return ServiceResponseUtils.success(new CreateDriverGrabOrdersResponseType());
        } catch (Exception e) {
            logger.error("CreateDriverGrabOrdersExecutorError", e);
        }
        return ServiceResponseUtils.fail(new CreateDriverGrabOrdersResponseType());
    }

    @Override
    public void validate(AbstractValidator<CreateDriverGrabOrdersRequestType> validator) {
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("duid").notNull().notEmpty();
        validator.ruleFor("driverIds").notNull().notEmpty();
    }
}
