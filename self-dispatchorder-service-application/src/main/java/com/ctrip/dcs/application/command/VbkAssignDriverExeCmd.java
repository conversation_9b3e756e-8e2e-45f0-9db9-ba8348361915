package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class VbkAssignDriverExeCmd extends AbstractAssignDriverExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(VbkAssignDriverExeCmd.class);

    @Override
    protected void preCheck(DspOrderVO order, DriverVO driverVO, OperateAssignDriverCommand cmd) {
        //不能重复指派司机
        Long orderSupplierId = 0L;
        DspOrderDO dspOrderDO = dspOrderRepository.find(order.getDspOrderId());
        if (dspOrderDO.getConfirmRecordId() != null && dspOrderDO.getConfirmRecordId() > 0L) {
            DspOrderConfirmRecordVO recordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
            orderSupplierId = recordVO.getSupplierInfo().getSupplierId();
            //状态校验
            if (!ConfirmType.DISPATCH_CONFIRMED.getCode().equals(recordVO.getConfirmType())) {
                logger.info("vbk_assign_driver_info", "Order is not assignable.dspOrderId=" + cmd.getDspOrderId());
                throw ErrorCode.ORDER_STATUS_CANNOT_DSP.getBizException();
            }
        }

        //canAssistSupplerIds
        Set<String> canAssistSuppliers = Sets.newHashSet(driverVO.getSupplier().getSupplierId().toString());
        canAssistSuppliers.addAll(CollectionUtils.isEmpty(driverVO.getSupplier().getDispatchSupplierIdList()) ? Lists.newArrayList() : driverVO.getSupplier().getDispatchSupplierIdList().stream().map(Object::toString).collect(Collectors.toList()));
        if (!canAssistSuppliers.contains(orderSupplierId.toString())
                || !cmd.getSupplierId().equals(orderSupplierId)
                || !canAssistSuppliers.contains(cmd.getSupplierId().toString())) {
            logger.info("vbk_assign_driver_info", "Cross supplier assignment not allowed.dspOrderId=" + cmd.getDspOrderId());
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }
        super.preCheck(order, driverVO, cmd);
    }


    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, OperateAssignDriverCommand cmd) {
        //        return DuidVO.of(cmd.getDuid()).getSubSkuId();
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, groupVO.getTransportGroupMode().getCode(), SysConstants.AssignRole.SYS_ROLE_SUPPLIER);

    }
}
