package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CreateOrderWayPointTagCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 缓存途径点标识
 * 缓存在41597中使用
 * <AUTHOR>
 */
@Component
public class CreateOrderWayPointTagExeCmd {

    private static final String ORDER_WAY_POINT_TAG_KEY = "dcs_self_order_way_point_tag_%s";

    private static final int HALF_MONTH_SECONDS = 15 * 24 * 60 * 60;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Autowired
    private TRocksProviderAdapter rocksProviderAdapter;

    public void execute(CreateOrderWayPointTagCommand command) {
        UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(command.getUserOrderId());
        if (userOrderDetail == null || userOrderDetail.getBookInfo() == null || userOrderDetail.getBookInfo().getWayPointTag() == null) {
            throw ErrorCode.QUERY_USER_ORDER_EXCEPTION.getBizException();
        }
        String wayPointTag = userOrderDetail.getBookInfo().getWayPointTag().toString();
        rocksProviderAdapter.setex(String.format(ORDER_WAY_POINT_TAG_KEY, command.getUserOrderId()), wayPointTag, getSeconds(userOrderDetail));
        if (StringUtils.isNotBlank(command.getDspOrderId())) {
            rocksProviderAdapter.setex(String.format(ORDER_WAY_POINT_TAG_KEY, command.getDspOrderId()), wayPointTag, getSeconds(userOrderDetail));
        }
    }

    /**
     * 缓存时间，用车时间+15天
     * @param userOrderDetail
     * @return
     */
    public int getSeconds(UserOrderDetail userOrderDetail) {
        if (StringUtils.isBlank(userOrderDetail.getBookInfo().getUseTime())) {
            return HALF_MONTH_SECONDS;
        }
        Date bookTime = DateUtil.parseDateStr2Date(userOrderDetail.getBookInfo().getUseTime());
        if (bookTime == null || bookTime.before(new Date())) {
            return HALF_MONTH_SECONDS;
        }
        return (int) DateUtil.seconds(new Date(), DateUtil.parseDateStr2Date(userOrderDetail.getBookInfo().getUseTime())) + HALF_MONTH_SECONDS;
    }
}
