package com.ctrip.dcs.application.listener.driver;

import com.ctrip.dcs.application.event.IDriverCityModifyEventHandler;
import com.ctrip.dcs.application.event.IDriverInfoModifyEventHandler;
import com.ctrip.dcs.application.event.IDriverSupplierModifyEventHandler;
import com.ctrip.dcs.application.event.IDriverVehicleInfoModifyEventHandler;
import com.ctrip.dcs.application.event.dto.DriverCityModifyEvent;
import com.ctrip.dcs.application.event.dto.DriverInfoModifyEvent;
import com.ctrip.dcs.application.event.dto.DriverSupplierModifyEvent;
import com.ctrip.dcs.application.event.dto.DriverVehicleInfoModifyEvent;
import com.ctrip.dcs.application.listener.driver.handler.TourDriverQMQHandler;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/***
 * 司机信息变更消息 dcs.tms.transport.driver.info.changed
 * 司机更换供应商 tag_driver_supplier_change
 * 司机更换城市信息 tag_driver_city_change
 * 司机登录账号变更 tag_driver_account_change
 * 车辆车型变更不会触发改派 tag_driverinfo_modify
 */
@Component
public class DriverModifyListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverModifyListener.class);
    @Autowired
    private IDriverCityModifyEventHandler driverCityModifyEventHandler;
    @Autowired
    private IDriverInfoModifyEventHandler driverInfoModifyEventHandler;
    @Autowired
    private IDriverSupplierModifyEventHandler driverSupplierModifyEventHandler;
    @Autowired
    private IDriverVehicleInfoModifyEventHandler driverVehicleInfoModifyEventHandler;

    @QmqLogTag(tagKeys = {"driverId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_MODIFY_TOPIC,
            tagType = TagType.OR,
            tags = {"tag_driver_supplier_change","tag_driver_city_change","tag_driver_account_change","tag_driverinfo_modify"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handle(Message message){
        logger.info("DriverModifyListener_msg", LocalJsonUtils.toJson(message));
        //司机供应商变更
        if(message.getTags().contains("tag_driver_supplier_change")){
            String drvId = message.getStringProperty("drvId");
            long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
            String accountType = message.getStringProperty("accountType");
            if(LocalStringUtils.isEmpty(drvId) || "0".equals(drvId)){
                logger.error("driverId_null", LocalJsonUtils.toJson(message));
                return;
            }
            if(supplierId == 0){
                logger.error("supplierId_null", LocalJsonUtils.toJson(message));
                return;
            }
            DriverSupplierModifyEvent event = new DriverSupplierModifyEvent();
            event.setDriverId(Long.valueOf(drvId));
            event.setSupplierId(supplierId);
            event.setAccountType(accountType);
            driverSupplierModifyEventHandler.handle(event);
        }
        //司机城市变更
        if(message.getTags().contains("tag_driver_city_change")){
            String drvId = message.getStringProperty("drvId");
            String cityId = message.getStringProperty("cityId");
            long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
            String accountType = message.getStringProperty("accountType");
            if(LocalStringUtils.isEmpty(drvId) || "0".equals(drvId)){
                logger.error("driverId_null", LocalJsonUtils.toJson(message));
                return;
            }
            if(LocalStringUtils.isEmpty(cityId) || "0".equals(cityId)){
                logger.error("cityId_null", LocalJsonUtils.toJson(message));
                return;
            }
            DriverCityModifyEvent event = new DriverCityModifyEvent();
            event.setCityId(Long.valueOf(cityId));
            event.setDriverId(Long.valueOf(drvId));
            event.setAccountType(accountType);
            event.setSupplierId(supplierId);
            driverCityModifyEventHandler.handle(event);
        }
        //司机信息变更
        if(message.getTags().contains("tag_driverinfo_modify")){
            String drvId = message.getStringProperty("drvId");
            long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
            if(LocalStringUtils.isEmpty(drvId) || "0".equals(drvId)){
                logger.error("driverId_null", LocalJsonUtils.toJson(message));
                return;
            }
            DriverInfoModifyEvent event = new DriverInfoModifyEvent();
            event.setDriverId(Long.valueOf(drvId));
            event.setFromDrvGuide(false);
            event.setSupplierId(supplierId);
            driverInfoModifyEventHandler.handle(event);
        }
    }

    /**
     * 司导平台供应链信息变更通知处理
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     *
     * @param message 消息内容
     */
    @QmqLogTag
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_DRIVER_INFO_CHANGED,
            tagType = TagType.OR,
            tags = {"tag_driver_supplier_change", "tag_driver_geoid_change", "tag_driver_info_change", "tag_driver_vehicle_change"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handleTourDriverPlatformQmq(Message message) {
        // 公共参数获取
        long drvId = message.getLongProperty("driverId");// 司导ID
        long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
        String accountType = message.getStringProperty("operateFrom"); // 操作来源(1.供应商,2.BD)
        //String ctripUID = message.getStringProperty("ctripUID"); // 操作者UID
        String strDriverProductLineList = message.getStringProperty("driverProductLineList"); // 司机所属产线列表，多个产线以逗号分割

        // 参数校验
        if (drvId <= 0) {// 检查是否有效的司机ID
            logger.error(message.getSubject() + "_" + message.getMessageId(), "driverId is invalid: " + drvId);
            return;
        }
        if (supplierId <= 0) {// 检查是否有效的供应商ID
            logger.error(message.getSubject() + "_" + message.getMessageId(), "supplierId is invalid: " + supplierId);
            return;
        }

        // 检查司机产线是否含有包车，没有则不处理直接返回
        if (!TourDriverQMQHandler.isCharteredDriverCarChanged(strDriverProductLineList)) {
            logger.info(message.getSubject() + "_" + message.getMessageId(), "driverProductLineList don't contain charteredCar, return. driverProductLineList: " + strDriverProductLineList);
            return;
        }

        // 司机供应商变更
        if (message.getTags().contains("tag_driver_supplier_change")) {
            DriverSupplierModifyEvent supplierModEvent = new DriverSupplierModifyEvent();
            supplierModEvent.setDriverId(drvId);
            supplierModEvent.setSupplierId(supplierId);
            supplierModEvent.setAccountType(accountType);
            supplierModEvent.setFromDrvGuide(true);
            driverSupplierModifyEventHandler.handle(supplierModEvent);
        }

        // 司机城市变化
        if (message.getTags().contains("tag_driver_geoid_change")) {
            long newCityId = message.getLongProperty("newGeoId"); // 修改后的服务城市
            // 如果新城市ID有值，表示司机城市变化
            if (newCityId > 0) {
                DriverCityModifyEvent cityModEvent = new DriverCityModifyEvent();
                cityModEvent.setDriverId(drvId);
                cityModEvent.setCityId(newCityId);
                cityModEvent.setAccountType(accountType);
                cityModEvent.setFromDrvGuide(true);
                cityModEvent.setSupplierId(supplierId);
                driverCityModifyEventHandler.handle(cityModEvent);
            }
        }

        // 司机基础信息变更
        if (message.getTags().contains("tag_driver_info_change")) {
            // 城市变化和基础信息变化会在同一个消息中
            DriverInfoModifyEvent drvInfoModEvent = new DriverInfoModifyEvent();
            drvInfoModEvent.setDriverId(drvId);
            drvInfoModEvent.setFromDrvGuide(true);
            drvInfoModEvent.setSupplierId(supplierId);
            driverInfoModifyEventHandler.handle(drvInfoModEvent);
        }

        // 司机更换车辆
        if (message.getTags().contains("tag_driver_vehicle_change")) {
            long vehicleId = message.getLongProperty("vehicleId"); // 更换后的车辆ID
            if (vehicleId <= 0) {
                logger.info(message.getSubject() + "_" + message.getMessageId(), "vehicleId is 0 or null: " + vehicleId);
                return;
            }

            DriverVehicleInfoModifyEvent event = new DriverVehicleInfoModifyEvent();
            event.setVehicleId(vehicleId);
            event.setDriverId(drvId);
            event.setAccountType(accountType);
            event.setSupplierId(supplierId);
            event.setFromDrvGuide(true);
            driverVehicleInfoModifyEventHandler.handle(event);
        }
    }

}
