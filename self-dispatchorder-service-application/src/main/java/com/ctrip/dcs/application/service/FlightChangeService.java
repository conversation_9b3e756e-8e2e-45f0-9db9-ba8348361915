package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.gateway.remind.FlightProtectAppPushRemindGatewayImpl;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class FlightChangeService {
    private static final Logger logger = LoggerFactory.getLogger(FlightChangeService.class);

    @Autowired
    SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    FlightProtectAppPushRemindGatewayImpl flightProtectAppPushRemindGateway;

    @Autowired
    QueryTransportGroupService queryTransportGroupService;

    @Autowired
    BusinessTemplateInfoConfig businessTemplateInfoConfig;


    public void flightProtectNoticeVbk(String userOrderId){
        //开关
        if( !"ON".equals(businessTemplateInfoConfig.getValueByKey(ConfigKey.FLIGHT_PROTECT_PUSH_SWITCH))){
            logger.info("flightProtectNoticeVbk_" + userOrderId,"switch is off,return");
            return;
        }

        BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetailForFlightProtect(userOrderId);

        if(Objects.isNull(baseDetailVO) || OrderStatusEnum.isCancel(baseDetailVO.getOrderStatus())){
            logger.info("flightProtectNoticeVbk","dspOrderId is cancel or null ,return");
            return;
        }

        //如果没有供应商或者运力组，则不需要推送
        if(baseDetailVO.getSupplierId() == null || baseDetailVO.getSupplierId().equals(0)  || baseDetailVO.getTransportGroupId() == null || baseDetailVO.getTransportGroupId().equals(0L)){
            logger.info("flightProtectNoticeVbk_" + baseDetailVO.getUserOrderId(),"getSupplierId or transportGroupId is illegal,return");
            return;
        }
        //查询运力组信息
        TransportGroupVO transportGroupDetail = queryTransportGroupService.queryTransportGroup(baseDetailVO.getTransportGroupId());
        if (transportGroupDetail == null) {
            throw new BizException("query transport group is failed");
        }


        //推送push
        flightProtectAppPushRemindGateway.push(baseDetailVO,transportGroupDetail);

    }

}
