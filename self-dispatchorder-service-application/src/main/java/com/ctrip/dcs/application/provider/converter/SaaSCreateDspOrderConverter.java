package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.domain.common.enums.ConnectModeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.ProductTypeEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.XproductVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.domain.dsporder.entity.FromPoiDTO;
import com.ctrip.dcs.domain.dsporder.entity.ToPoiDTO;
import com.ctrip.dcs.domain.dsporder.value.UseDays;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSBaseInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtendInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtraFlightInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSFeeInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSPoiInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSUserCountInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSXproductInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaasCreateDspOrderInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;


public class SaaSCreateDspOrderConverter {

    public static CreateDspOrderCommand converter(SaasCreateDspOrderRequestType request) {
        Assert.notNull(request);
        SaasCreateDspOrderInfo createDspOrderInfo = request.getCreateDspOrderInfo();
        SaaSBaseInfo baseInfo = createDspOrderInfo.getBaseInfo();
        Assert.notNull(baseInfo.getVbkOrderId());
        Assert.notNull(baseInfo.getUserOrderId());
        SaaSExtendInfo extendInfo = createDspOrderInfo.getExtendInfo();
        SaaSFeeInfo feeInfo = createDspOrderInfo.getFeeInfo();
        SaaSXproductInfo xproductInfo = createDspOrderInfo.getXproductInfo();
        SaaSExtraFlightInfo extraFlightInfo = createDspOrderInfo.getExtraFlightInfo();
        SaaSPoiInfo fromPoi = createDspOrderInfo.getFromPoi();
        SaaSPoiInfo toPoi = createDspOrderInfo.getToPoi();
        SaaSUserCountInfo userCount = createDspOrderInfo.getUserCount();



        CreateDspOrderCommand cmd = new CreateDspOrderCommand();
        cmd.setDspOrderDO(buildOrder(baseInfo, fromPoi, toPoi, xproductInfo));

        cmd.setDspOrderDetailDO(buildOrderDetail(extraFlightInfo, xproductInfo, userCount, extendInfo, baseInfo));
        cmd.setDspOrderFeeDO(buildFee(feeInfo, baseInfo));
        return cmd;
    }


    private static DspOrderDO buildOrder(SaaSBaseInfo baseInfo, SaaSPoiInfo fromPoi, SaaSPoiInfo toPoi,SaaSXproductInfo xproductInfo) {
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDO.setSalesMode(0);
        dspOrderDO.setSupplyOrderId(StringUtils.EMPTY);
        dspOrderDO.setUserOrderId(baseInfo.getUserOrderId());
        dspOrderDO.setUid(StringUtils.EMPTY);
        dspOrderDO.setProductName(StringUtils.EMPTY);
        dspOrderDO.setConnectMode(ConnectModeEnum.VBK.mode);
        dspOrderDO.setProductType(ProductTypeEnum.GENERAL.getCode());
        dspOrderDO.setDriverOrderId(StringUtils.EMPTY);
        if(Objects.nonNull(xproductInfo)){
            dspOrderDO.setProductName(xproductInfo.getPackageName());
        }
        dspOrderDO.setDspOrderId(baseInfo.getDspOrderId());
        dspOrderDO.setVbkOrderId(baseInfo.getVbkOrderId());
        dspOrderDO.setOrderStatus(baseInfo.getOrderStatus());
        dspOrderDO.setCategoryCode(baseInfo.getCategoryCode());
        dspOrderDO.setCityId(baseInfo.getCityId());
        dspOrderDO.setCityName(baseInfo.getCityName());
        dspOrderDO.setFromCityId(baseInfo.getFromCityId());
        dspOrderDO.setToCityId(baseInfo.getToCityId());
        dspOrderDO.setVehicleGroupId(baseInfo.getVehicleGroupId());
        dspOrderDO.setVehicleGroupName(baseInfo.getVehicleGroupName());
        dspOrderDO.setEstimatedUseTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getEstimatedUseTime()).getTime()));
        if(Objects.isNull(baseInfo.getUseDays())){
            baseInfo.setUseDays(BigDecimal.ONE);
        }
        if(!OrderSourceCodeEnum.TRIP.getCode().equals(baseInfo.getOrderSourceCode())){
            if(!(BigDecimal.ONE.compareTo(baseInfo.getUseDays()) == 0)){
                throw ErrorCode.ERROR_PARAMS.getBizException();
            }
        }
        dspOrderDO.setUseDays(new UseDays(baseInfo.getUseDays()));
        dspOrderDO.setSupplierId(baseInfo.getSupplierId());
        dspOrderDO.setDriverOrderId(baseInfo.getDriverOrderId());
        dspOrderDO.setOrderSourceCode(baseInfo.getOrderSourceCode());

        FromPoiDTO fromPoiDTO = new FromPoiDTO();
        fromPoiDTO.setCityId(fromPoi.getCityId());
        fromPoiDTO.setFromAddress(fromPoi.getAddress());
        fromPoiDTO.setDeptCity(baseInfo.getDeptCity());
        dspOrderDO.setFromPoi(LocalJsonUtils.toJson(fromPoiDTO));

        ToPoiDTO toPoiDTO = new ToPoiDTO();
        toPoiDTO.setCityId(toPoi.getCityId());
        toPoiDTO.setToAddress(toPoi.getAddress());
        toPoiDTO.setArriveCityName(baseInfo.getArriveCityName());
        dspOrderDO.setToPoi(LocalJsonUtils.toJson(toPoiDTO));
        dspOrderDO.setEstimatedKm(baseInfo.getEstimatedKm());
        if(StringUtils.isNotEmpty(baseInfo.getLastConfirmCarTime())){
            dspOrderDO.setLastConfirmCarTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmCarTime()).getTime()));
        }
        if(StringUtils.isNotEmpty(baseInfo.getLastConfirmCarTimeBj())){
            dspOrderDO.setLastConfirmCarTimeBj(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmCarTimeBj()).getTime()));
        }

        return dspOrderDO;
    }


    private static DspOrderDetailDO buildOrderDetail(SaaSExtraFlightInfo extraFlightInfo,
                                                     SaaSXproductInfo xproductInfo, SaaSUserCountInfo userCount,
                                                     SaaSExtendInfo extendInfo,
                                                     SaaSBaseInfo baseInfo) {
        DspOrderDetailDO dspOrderDetailDO = new DspOrderDetailDO();

        dspOrderDetailDO.setSupplyOrderId(StringUtils.EMPTY);
        dspOrderDetailDO.setUserOrderId(baseInfo.getUserOrderId());
        dspOrderDetailDO.setDspOrderId(StringUtils.EMPTY);
        if(Objects.nonNull(extraFlightInfo)){
            dspOrderDetailDO.setFlightInfo(LocalJsonUtils.toJson(extraFlightInfo));
        }
        XproductVO xproductVO = new XproductVO();
        if(xproductInfo != null){
            xproductVO.setAdditionalServices(xproductInfo.getAdditionalServices());
            dspOrderDetailDO.setXproductInfo(LocalJsonUtils.toJson(Lists.newArrayList(xproductVO)));
        }
        if(userCount != null){
            dspOrderDetailDO.setUserCount(LocalJsonUtils.toJson(userCount));
        }
        dspOrderDetailDO.setLocale(StringUtils.EMPTY);
        if(extendInfo != null){
            dspOrderDetailDO.setExtendInfo(LocalJsonUtils.toJson(extendInfo));
        }
        return dspOrderDetailDO;
    }

    private static DspOrderFeeDO buildFee(SaaSFeeInfo feeInfo, SaaSBaseInfo baseInfo) {
        DspOrderFeeDO dspOrderFeeDO = new DspOrderFeeDO();
        dspOrderFeeDO.setSalesAmount(BigDecimal.ZERO);
        dspOrderFeeDO.setUserOrderId(baseInfo.getUserOrderId());
        if(Objects.nonNull(feeInfo)){
            dspOrderFeeDO.setCostAmount(Objects.isNull(feeInfo.getCostAmount()) ? BigDecimal.ZERO : feeInfo.getCostAmount());
            dspOrderFeeDO.setUserCurrency(Objects.isNull(feeInfo.getUserCurrency()) ? StringUtils.EMPTY : feeInfo.getUserCurrency());
            dspOrderFeeDO.setSupplierCurrency(Objects.isNull(feeInfo.getSupplierCurrency()) ? StringUtils.EMPTY : feeInfo.getSupplierCurrency());
        }
        return dspOrderFeeDO;
    }
}
