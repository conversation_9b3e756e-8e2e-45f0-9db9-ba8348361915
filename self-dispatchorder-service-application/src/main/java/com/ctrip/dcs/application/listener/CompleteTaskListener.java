package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.ExecuteScheduleEvent;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Objects;

/**
 * 派发任务执行完成消息
 * <AUTHOR>
 */
@Component
public class CompleteTaskListener {

    private static final Logger logger = LoggerFactory.getLogger(CompleteTaskListener.class);

    @Autowired
    private ExecuteScheduleExeCmd executeScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private MessageProviderService messageProviderService;

    @QmqLogTag(tagKeys = {"scheduleId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.COMPLETE_TASK_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        int confirm = message.getIntProperty("confirm");
        Long scheduleId = message.getLongProperty("scheduleId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (YesOrNo.NO.getCode() == confirm) {
            try {
                List<ScheduleTaskDO> scheduleTasks = taskRepository.query(scheduleId, dspOrderId);
                if (ScheduleDO.isExecuting(scheduleTasks)) {
                    logger.info("CompleteTaskListenerInfo", "schedule task is executing.dspOrderId:{}", dspOrderId);
                    return;
                }
                List<ScheduleTaskDO> waitExecuteTask = ScheduleDO.getWaitExecuteTask(scheduleTasks);
                if (CollectionUtils.isEmpty(waitExecuteTask)) {
                    logger.info("CompleteTaskListenerInfo", "wait schedule task is empty.dspOrderId:{}", dspOrderId);
                    return;
                }
                List<ScheduleTaskDO> nextTasks = ScheduleDO.getHighestPriorityTask(waitExecuteTask);
                boolean res = scheduleTasks.stream().map(ScheduleTaskDO::getSubSku).filter(Objects::nonNull).map(SubSkuVO::getDspType).filter(Objects::nonNull).allMatch(this::matchDispatchDspType);
                logger.info("CompleteTaskListener_matchDispatchAssignDspType_res", JsonUtil.toJson(res));
                if(res){

                    // 发送待执行任务消息
                    executeScheduleExeCmd.sendWaitExecuteDispatchTaskMessage(nextTasks);
                }else {
                    // 发送待执行任务消息
                    executeScheduleExeCmd.sendWaitExecuteTaskMessage(nextTasks);
                }
            } catch (Exception e) {
                logger.error("ExecuteScheduleListenerError", e);
                // 埋点
                MetricsUtil.recordValue(MetricsConstants.EXECUTE_SCHEDULE_ERROR_COUNT);
            }
        }
    }

    private boolean matchDispatchDspType(DspType dspType){
        return ExecuteScheduleExeCmd.DISPATCHER_ASSIGN_LIST.contains(dspType);
    }


}
