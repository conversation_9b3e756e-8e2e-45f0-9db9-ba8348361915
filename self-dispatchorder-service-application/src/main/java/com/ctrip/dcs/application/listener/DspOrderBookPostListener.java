package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CreateOrderWayPointTagExeCmd;
import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.ReDispatchConfirmExeCmd;
import com.ctrip.dcs.application.command.api.CreateOrderWayPointTagCommand;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.ReDispatchConfirmCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.application.processor.OriginalOrderModifyProcessor;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSceneEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.entity.PlatformPriceStrategyDO;
import com.ctrip.dcs.domain.dsporder.gateway.OrderLocaleGateway;
import com.ctrip.dcs.domain.dsporder.logic.PlatformPriceStrategyLogic;
import com.ctrip.dcs.domain.dsporder.logic.PremiumOrderLogic;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

/**
 * 派发单下单后续流程
 *
 * <AUTHOR>
 */
@Component
public class DspOrderBookPostListener {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderBookPostListener.class);

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @Autowired
    private PremiumOrderLogic premiumOrderLogic;

    @Autowired
    private OrderLocaleGateway orderLocaleGateway;

    @Autowired
    private PlatformPriceStrategyLogic platformPriceStrategyLogic;

    @Autowired
    private ReDispatchConfirmExeCmd reDispatchConfirmExeCmd;

    @Autowired
    private CreateOrderWayPointTagExeCmd createOrderWayPointTagExeCmd;

    @Autowired
    private OriginalOrderModifyProcessor originalOrderModifyProcessor;


    ////上游传的有 100041593_contract

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_strategy", idempotentChecker = "redisIdempotentChecker")
    public void onOrderBookMessage1(Message message) {
        logger.info("DspOrderBookPostListener_strategy.onMessage begin", JacksonUtil.serialize(message));
        String userOrderId = message.getStringProperty("userOrderId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (Strings.isBlank(userOrderId) || Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DspOrderBookPostListener_strategy qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {

            Result<PlatformPriceStrategyDO> saveStrategyRes = platformPriceStrategyLogic.savePriceStrategy(dspOrderId);

            if (saveStrategyRes == null || !saveStrategyRes.isSuccess()) {
                logger.error("SyncPlatformPriceStrategyCallable_" + dspOrderId, "Sync SyncPlatformPriceStrategyCallable Order, sync Q");
            }

        } catch (Exception e) {
            logger.error("DspOrderBookPostListener_strategy.error", e);
            throw e;
        }
    }

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_order_locale", idempotentChecker = "redisIdempotentChecker")
    public void onOrderBookMessage2(Message message) {
        logger.info("DspOrderBookPostListener_order_locale.onMessage begin", JacksonUtil.serialize(message));
        String userOrderId = message.getStringProperty("userOrderId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (Strings.isBlank(userOrderId) || Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DspOrderBookPostListener_order_locale qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            orderLocaleGateway.saveOrderLocale(dspOrderId);

        } catch (Exception e) {
            logger.error("DspOrderBookPostListener_order_locale.error", e);
            throw e;
        }
    }

    //@QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_premium_order_flag", idempotentChecker = "redisIdempotentChecker")
    //优质单逻辑废弃
    public void onOrderBookMessage3(Message message) {
        logger.info("DspOrderBookPostListener_premium_order_flag.onMessage begin", JacksonUtil.serialize(message));
        String userOrderId = message.getStringProperty("userOrderId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (Strings.isBlank(userOrderId) || Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DspOrderBookPostListener_premium_order_flag qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            premiumOrderLogic.premiumOrderProcess(dspOrderId);

        } catch (Exception e) {
            logger.error("DspOrderBookPostListener_premium_order_flag.error", e);
            throw e;
        }

    }


    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_create_schedule", idempotentChecker = EventConstants.IDEMPOTENT_CHECKER)
    public void onCreateSchedule(Message message) {
        Integer orderScene = message.getIntProperty("orderScene");
        if (!Objects.equals(orderScene, OrderSceneEnum.NORMAL.getCode())) {
            // 客服指派下单，不进行系统调度
            logger.info("DSP_ORDER_BOOK_CREATE_SCHEDULE", "orderScene is not normal, orderScene:{}", orderScene);
            return;
        }
        try {
            // 创建调度
            CreateScheduleCommand command = MessageConverter.INSTANCE.toCreateScheduleCommand(message);
            createScheduleExeCmd.execute(command);
            // 埋点
            MetricsUtil.recordValue(MetricsConstants.CREATE_SCHEDULE_COUNT);
        } catch (Exception e) {
            logger.error("DspOrderBookPostListener_create_schedule.error", e);
            MetricsUtil.recordValue(MetricsConstants.CREATE_SCHEDULE_ERROR_COUNT);
            throw new NeedRetryException("DSP_ORDER_BOOK_CREATE_SCHEDULE_ERROR");
        }
    }

    @QmqLogTag(tagKeys = {"userOrderId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_order_modify", idempotentChecker = EventConstants.IDEMPOTENT_CHECKER)
    public void onOrderModify(Message message) {
        Integer orderScene = message.getIntProperty("orderScene");
        String newDspOrderId = message.getStringProperty("dspOrderId");
        String oldDspOrderId = message.getStringProperty("oldDspOrderId");
        Integer needSupplierAgreeModify = message.getIntProperty("needSupplierAgreeModify");
        // 仅处理原单修改场景的订单
        if (OrderSceneEnum.ORDER_MODIFY.getCode().equals(orderScene)) {
            originalOrderModifyProcessor.apply(newDspOrderId, oldDspOrderId, YesOrNo.isYes(needSupplierAgreeModify));
        }
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_buffer_change", idempotentChecker = EventConstants.IDEMPOTENT_CHECKER)
    public void onBufferChange(Message message) {
        Integer orderScene = message.getIntProperty("orderScene");
        if (!Objects.equals(orderScene, OrderSceneEnum.BUFFER_CHANGE.getCode())) {
            logger.info("DSP_ORDER_BOOK_BUFFER_CHANGE", "orderScene is not buffer change, orderScene:{}", orderScene);
            return;
        }
        try {
            // 创建调度
            ReDispatchConfirmCommand command = MessageConverter.INSTANCE.toReDispatchConfirmCommand(message);
            reDispatchConfirmExeCmd.execute(command);
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.BUFFER_CHANGE_CONFIRM_ERROR);
            logger.warn("DspOrderBookPostListener_buffer_change.error", e);
            throw ErrorCode.BUFFER_CHANGE_ERROR.getBizException();
        }
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_BOOK_TOPIC, consumerGroup = "100041593_order_way_point_tag", idempotentChecker = EventConstants.IDEMPOTENT_CHECKER)
    public void onOrderWayPointTag(Message message) {
        try {
            if (message.times() > QMQ_RETRY_TIMES) {
                logger.info("DspOrderBookPostListener_order_way_point_tag qmq retries exceeds the maximum", JacksonUtil.serialize(message));
                return;
            }
            // 创建调度
            CreateOrderWayPointTagCommand command = MessageConverter.INSTANCE.toCreateOrderWayPointTagCommand(message);
            createOrderWayPointTagExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("DspOrderBookPostListener_order_way_point_tag.error", e);
            throw e;
        }
    }

}
