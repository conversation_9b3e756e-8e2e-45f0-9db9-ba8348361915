package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.provider.converter.QueryAvailableDriversConverter;
import com.ctrip.dcs.application.query.AvailableDriversExeQry;
import com.ctrip.dcs.application.query.api.AvailableDriversQuery;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.AvailableDriverDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId")
public class QueryAvailableDriversExecutor extends AbstractRpcExecutor<QueryAvailableDriversRequestType, QueryAvailableDriversResponseType> implements Validator<QueryAvailableDriversRequestType> {

    @Autowired
    private AvailableDriversExeQry availableDriversExeQry;

    @Override
    public QueryAvailableDriversResponseType execute(QueryAvailableDriversRequestType requestType) {
        AvailableDriversQuery query = QueryAvailableDriversConverter.toAvailableDriversQuery(requestType);
        List<SortModel> sort = availableDriversExeQry.query(query);
        List<AvailableDriverDTO> list = QueryAvailableDriversConverter.toAvailableDriverDTO(sort, requestType.getDuid());
        QueryAvailableDriversResponseType responseType = new QueryAvailableDriversResponseType();
        responseType.setAvailableDrivers(list);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryAvailableDriversRequestType> validator) {
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("duid").notNull().notEmpty();
    }
}
