package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CancelDspOrderExeCmd;
import com.ctrip.dcs.application.processor.PushSettlementProcessor;
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.CancelReasonIdEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderConfirmRecordDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderConfirmRecordPO;
import com.ctrip.dcs.infrastructure.common.constants.PushSettlementTypeEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

/**
 * 司机单取消消息
 *
 * <AUTHOR>
 */
@Component
public class DriverOrderCancelListener {

    private static final Logger logger = LoggerFactory.getLogger(DriverOrderCancelListener.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private CancelDspOrderExeCmd cancelDspOrderExeCmd;
    @Autowired
    private DspDrvOrderLimitTakenRecordGateway dspDrvOrderLimitTakenRecordGateway;

    @Autowired
    private PushSettlementProcessor pushSettlementProcessor;

    @Autowired
    private DspOrderConfirmRecordDao dspOrderConfirmRecordDao;

    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_drvorder_cancel", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        logger.info("DriverOrderCancelListener.onMessage begin", JacksonUtil.serialize(message));
        //"drvOrderId":"司机单号","dspOrderId":"派发单号",
        String dspOrderId = message.getStringProperty("dspOrderId");
        String drvOrderId = message.getStringProperty("driverOrderId");
        String cancelRecord = message.getStringProperty("cancelRecord");

        if (Strings.isBlank(dspOrderId) || Strings.isBlank(cancelRecord)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DriverOrderCancelListener_drvorder_cancel qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            if (dspOrderDO == null) {
                logger.info("DriverOrderFinishListener_drvorder_cancel dspOrder is null,dspOrderId=" + dspOrderId);
                return;
            }
            dspDrvOrderLimitTakenRecordGateway.orderCancel(dspOrderId);
            if (OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus())) {
                logger.info("DriverOrderFinishListener_drvorder_cancel dspOrder Illegal status,dspOrderStr=" + JacksonUtil.serialize(dspOrderDO));
                return;
            }
            Map<String,Object> cancelRecordMap = JacksonUtil.deserialize(cancelRecord, HashMap.class);
            Integer cancelCode = Integer.valueOf(cancelRecordMap.get("causeCode").toString());
            String cancelReason = cancelRecordMap.get("cancelReason").toString();
            if (!CancelReasonIdEnum.DRIVER_CANCEL.getCode().equals(cancelCode)) {
                return;
            }
            cancelDspOrderExeCmd.execute(CancelDspOrderConverter.converter(dspOrderDO,cancelCode,cancelReason));
            // 司机单取消推送费项消息
            DspOrderConfirmRecordPO dspOrderConfirmRecord = dspOrderConfirmRecordDao.findByDspOrderIdAndDrvOrderId(dspOrderId, drvOrderId);
            if(Objects.nonNull(dspOrderConfirmRecord)){
                pushSettlementProcessor.process(dspOrderId, dspOrderConfirmRecord.getRecordId(), drvOrderId,
                        PushSettlementTypeEnum.SUPPLY_ORDER_CANCEL);
            }
        } catch (Exception e) {
            logger.error("DriverOrderCancelListener.error", e);
            throw new BizException(e);
        }
    }


}
