package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverOfflineEvent;
import com.ctrip.dcs.application.event.impl.DriverOfflineEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverOfflineTestService")
public class DriverOfflineTestService implements ITestDspOrderService{
    @Autowired
    private DriverOfflineEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverOfflineEvent event = new DriverOfflineEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setAccountType(params.get("accountType"));
        event.setIsFromPenalty(params.get("isFromPenalty"));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
