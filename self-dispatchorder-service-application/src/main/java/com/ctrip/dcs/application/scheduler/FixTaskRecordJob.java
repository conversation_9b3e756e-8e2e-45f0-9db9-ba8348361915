package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKGrabTaskRecordDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;
import java.util.UUID;

@Component
public class FixTaskRecordJob {
    private static final Logger logger = LoggerFactory.getLogger(FixTaskRecordJob.class);


    @Autowired
    private VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;

    @QSchedule("fix.task.record.job")
    public void execute(Parameter param) {
        try{
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            logger.info("FixTaskRecordJob_start", JsonUtils.toJson(param));
            Long supplierId = getProperty(param);
            List<VBKGrabTaskRecordDO> vbkGrabTaskRecordDOS = vbkGrabTaskRecordRepository.queryBySupplierId(supplierId);
            logger.info("FixTaskRecordJob_vbkGrabTaskRecordDOS", JsonUtils.toJson(vbkGrabTaskRecordDOS));
            vbkGrabTaskRecordDOS.forEach(this::updateSupplierIdById);
        }catch (Exception ex){
            logger.error("FixTaskRecordJob_vbkGrabTaskRecordDOS_error", ex);
        }
    }

    public boolean updateSupplierIdById(VBKGrabTaskRecordDO x) {
        try {
            logger.info("FixTaskRecordJob_updateSupplierIdById_VBKGrabTaskRecordDO", JsonUtils.toJson(x));
            VBKDriverGrabTaskDO vbkDriverGrabTaskDO = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(x.getVbkGrabTaskId());
            int num = vbkGrabTaskRecordRepository.updateSupplierId(x.getId(), vbkDriverGrabTaskDO.getSupplierId());
            logger.info("FixTaskRecordJob_updateSupplierIdById", JsonUtils.toJson(num));
            return true;
        } catch (Exception ex) {
            logger.error("FixTaskRecordJob_updateSupplierIdById_error", ex);
            return false;
        }
    }


    private Long getProperty(Parameter parameter) {
        Long supplierId = parameter.getProperty("supplierId", Long.class);
        return supplierId == null ? 0L : supplierId;
    }
}
