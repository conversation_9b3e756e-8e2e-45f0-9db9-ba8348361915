package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.TriggerTypeEnum;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.service.SendEmailService;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.TripSendEmailVO;
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.gateway.remind.DispatcherIgtOrderIvrRemindImpl;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

@Component
public class UseTimeChangeListener {
    private static final Logger logger = LoggerFactory.getLogger(UseTimeChangeListener.class);

    @Autowired
    IvrCallService ivrCallService;

    @Autowired
    QueryDspOrderService queryDspOrderService;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    private SendEmailService sendEmailService;

    @Autowired
    SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private DspDrvOrderLimitTakenRecordGateway dspDrvOrderLimitTakenRecordGateway;

    @Autowired
    private DispatcherIgtOrderIvrRemindImpl dispatcherIgtOrderIvrRemind;

    /**
     * 支持发生邮件的订单状态
     */
    private static List<Integer> SUPPORT_ORDER_STATUS_LIST = Lists.newArrayList(
            OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode(),
            OrderStatusEnum.DRIVER_TO_MEET.getCode(),
            OrderStatusEnum.DRIVER_ARRIVE.getCode()
    );

    @QmqLogTag(tagKeys = { "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");

        String oldLastConfirmCarTime = message.getStringProperty("oldLastConfirmCarTime");
        String qmqNewLastConfirmCarTime = message.getStringProperty("lastConfirmTime");

        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info(" dcs.self.driver.order.estimate.time.change qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        if (Strings.isBlank(oldLastConfirmCarTime) || Strings.isBlank(qmqNewLastConfirmCarTime)) {
            logger.info("dcs.self.driver.order.estimate.time.change lastConformCarTime is null", JacksonUtil.serialize(message));
            return;
        }

        if(oldLastConfirmCarTime.equals(qmqNewLastConfirmCarTime)){
            //如果新老时间一致，说明最晚派遣时间没有改变，不需要操作
            logger.info("dcs.self.driver.order.estimate.time.change lastConformCarTime is not change", JacksonUtil.serialize(message));
            return;
        }
        try {
            // 取得订单信息
            DspOrderVO dspOrder = queryDspOrderService.queryBase(dspOrderId);
            if (dspOrder == null) {
                logger.warn("dispatcherRemind_qmq_consume", true, "dspOrder is null。 orderId：" + dspOrderId);
            } else{
                ivrCallService.dispatcherOverTimeIvrRemind(dspOrder, TriggerTypeEnum.CONFIRM_TIME_CHANGE);
            }
            // 更新公里数限制订单接单记
            dspDrvOrderLimitTakenRecordGateway.bookTimeChg(dspOrder);
            //境外超时未派遣提醒
            dispatcherIgtOrderIvrRemind.push(dspOrder,null);
        } catch (Exception e) {
            logger.error("purchaseSupplyOrderCancelByRedispatch.error", e);
            throw e;
        }
    }

    /**
     * C:用车时间变更通知调度
     *
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE, consumerGroup = "100041593-notice-dispatcher", idempotentChecker = "redisIdempotentChecker")
    public void onMessageNoticeDispatcher(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (StringUtils.isBlank(dspOrderId)) {
            logger.error("onMessageNoticeDispatcherDspOrderIdError", "dspOrderIdError");
            return;
        }
        try {
            BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetail(dspOrderId);
            Pair<Boolean, TransportGroupVO> switchAndFindEmailPair = this.judgeSwitchAndFindEmail(baseDetailVO);
            if (Boolean.FALSE.equals(switchAndFindEmailPair.getKey())) {
                return;
            }
            // 主题
            String subjectTemp = businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ESTIMATED_USE_TIME_CHANGE_SUBJECT);
            String subject = String.format(subjectTemp, baseDetailVO.getUserOrderId());
            // 内容
            String contentTemp = businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ESTIMATED_USE_TIME_CHANGE_CONTENT);
            String content = String.format(contentTemp, baseDetailVO.getUserOrderId(), baseDetailVO.getEstimatedUseTime(), baseDetailVO.getEstimatedUseTimeBj());
            // 发送邮件
            this.sendEmail(baseDetailVO.getUserOrderId(), switchAndFindEmailPair.getValue(), subject, content);
        } catch (Exception e) {
            logger.warn("purchaseSupplyOrderCancelByRedispatch.error", e);
            throw e;
        }
    }

    /**
     * 判断是否可发送邮件,返回运力组
     *
     * @param baseDetailVO
     * @return
     */
    public Pair<Boolean, TransportGroupVO> judgeSwitchAndFindEmail(BaseDetailVO baseDetailVO) {
        if (baseDetailVO == null || baseDetailVO.getTransportGroupId() == null) {
            logger.warn("judgeSwitchAndFindEmail_orderIsNull", "dspOrder is null");
            return Pair.of(Boolean.FALSE, null);
        }
        // 支持的供应商
        String supplierIds = businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ESTIMATED_USE_TIME_CHANGE_SUPPLIER_IDS);
        if (StringUtils.isBlank(supplierIds)) {
            logger.warn("judgeSwitchAndFindEmail_supplierIdsError", "config supplierId is null");
            return Pair.of(Boolean.FALSE, null);
        }
        List<String> supplierIdList = Splitter.on(",").splitToList(supplierIds);

        // 供应商ID
        Integer supplierId = baseDetailVO.getSupplierId();
        if (supplierId == null || !supplierIdList.contains(supplierId.toString())) {
            logger.info("judgeSwitchAndFindEmail_supplierId_notSupport", "supplierId=" + baseDetailVO.getSupplierId());
            return Pair.of(Boolean.FALSE, null);
        }

        // 状态不支持
        if (!SUPPORT_ORDER_STATUS_LIST.contains(baseDetailVO.getOrderStatus())) {
            logger.warn("judgeSwitchAndFindEmail_orderStatus_notSupport", "orderStatus=" + baseDetailVO.getOrderStatus());
            return Pair.of(Boolean.FALSE, null);
        }

        // 查询运力组
        Long transportGroupId = baseDetailVO.getTransportGroupId();
        TransportGroupVO transportGroupVO = queryTransportGroupService.queryTransportGroup(transportGroupId);
        if (transportGroupVO == null) {
            logger.warn("judgeSwitchAndFindEmail_null", "transportGroupId=" + transportGroupId);
            return Pair.of(Boolean.FALSE, null);
        }
        // 通知开关是否打开
        if (transportGroupVO.getInformSwitch() == null || transportGroupVO.getInformSwitch().intValue() != 1) {
            logger.warn("judgeSwitchAndFindEmail_notice_switch_false", "transportGroupId=" + transportGroupId);
            return Pair.of(Boolean.FALSE, null);
        }
        // 邮箱为空 不通知
        if (LocalStringUtils.isEmpty(transportGroupVO.getInformEmail())) {
            logger.info("judgeSwitchAndFindEmail_phone_email_null", "transportGroupId=" + transportGroupId);
            return Pair.of(Boolean.FALSE, null);
        }

        return Pair.of(Boolean.TRUE, transportGroupVO);
    }

    /**
     * 发送邮件
     *
     * @param userOrderId
     * @param transportGroupVO
     * @param subject
     * @param content
     */
    public void sendEmail(String userOrderId, TransportGroupVO transportGroupVO, String subject, String content) {
        TripSendEmailVO tripSendEmailVO = TripSendEmailVO.builder()
                .orderId(userOrderId)
                .producer(TripSendEmailVO.Producer.builder()
                        .email(SysConstants.Email.COMMON_EMAIL_SENDER)
                        .name(businessTemplateInfoConfig.getValueByKey(SysConstants.Email.DISPATCHER_CONFIRM_SENDER_NAME))
                        .build())
                .receiver(Lists.newArrayList(TripSendEmailVO.Receiver.builder().email(transportGroupVO.getInformEmail()).name(transportGroupVO.getTransportGroupName()).build()))
                .subject(subject)
                .content(content)
                .channel(SysConstants.Email.channel)
                .contentType(SysConstants.Email.contentType)
                .hasAttach(false)
                .build();
        sendEmailService.send(tripSendEmailVO);
    }


}
