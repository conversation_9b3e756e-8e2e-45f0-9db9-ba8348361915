package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.PushOrderRemindCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PushOrderRemindValidator {

    public void validate(PushOrderRemindCommand command) {
        if (Objects.isNull(command) || Objects.isNull(command.getDspOrder()) || Objects.isNull(command.getTransportGroup()) || CollectionUtils.isEmpty(command.getTypes())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }
        if (Objects.isNull(command.getDspOrder().getCityId()) || Objects.isNull(command.getDspOrder().getLastConfirmCarTime()) || Objects.isNull(command.getDspOrder().getEstimatedUseTime())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }
        if (Objects.isNull(command.getTransportGroup().getSupplierId())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }

    }
}
