package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.service.ConfigService;

import java.util.Map;

public class TestConfigService implements ConfigService {
    @Override
    public String getString(String key) {
        return null;
    }

    @Override
    public String getString(String key, String defaultValue) {
        return null;
    }

    @Override
    public Integer getInteger(String key) {
        return null;
    }

    @Override
    public Integer getInteger(String key, Integer defaultValue) {
        return null;
    }

    @Override
    public Long getLong(String key) {
        return null;
    }

    @Override
    public Long getLong(String key, Long defaultValue) {
        return null;
    }

    @Override
    public Double getDouble(String key) {
        return null;
    }

    @Override
    public Double getDouble(String key, Double defaultValue) {
        return null;
    }

    @Override
    public Map<String, String> getMap(String key) {
        return null;
    }
}
