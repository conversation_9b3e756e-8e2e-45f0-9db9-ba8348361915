package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.UpdateDriverHeadTailOrderCommand;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.schedule.repository.DriverHeadTailOrderRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.NeedRetryException;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class UpdateDriverHeadTailOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateDriverHeadTailOrderExeCmd.class);

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DriverHeadTailOrderRepository driverHeadTailOrderRepository;

    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;

    public void execute(UpdateDriverHeadTailOrderCommand command) {
        Integer flag = commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode());
        if (YesOrNo.isNo(flag)) {
            return;
        }
        DspOrderVO dspOrder = queryOrder(command);
        DriverVO driver = queryDriver(command, dspOrder);
        if (Objects.nonNull(driver) && Objects.nonNull(dspOrder) && Objects.nonNull(dspOrder.getEstimatedUseTime())) {
            execute(driver, dspOrder.getEstimatedUseTime());
        }
    }

    public void execute(DriverVO driver, Date date) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock("UPDATE_DRIVER_HEAD_TAIL_ORDER_" + driver.getDriverId() + "_" + DateUtil.formatDate(date, DateUtil.DATE_FORMAT));
        try {
            if (!lock.tryLock()) {
                // 500ms后重试
                throw new NeedRetryException(System. currentTimeMillis() + 500L, "another processing is ongoing, retry later.");
            }
            // 获取司机对应日期的工作时段
            DriverWorkTimeVO workTime = DriverWorkTimeUtil.getDriverWorkTime(driver, date);
            logger.info("UpdateDriverHeadTailOrderInfo", "driver workTime! driver id: {}, workTime: {}", driver.getDriverId(), JacksonUtil.serialize(workTime), ImmutableMap.of("driverId", driver.getDriverId()));
            if (Objects.nonNull(workTime) && DateUtil.isAfter(DateUtil.addDays(workTime.getStart(), 1), new Date())) {
                // 司机开始工作时间+1天 > 当前时间，说明司机工作时间未过期
                // 处理司机首单
                handleHeadOrder(driver, workTime);
                // 处理司机尾单
                handleTailOrder(driver, workTime);
            }
        } catch (NeedRetryException retryException) {
            throw retryException;
        } catch (Exception e) {
            logger.error("UpdateDriverHeadTailOrderError", "UpdateDriverHeadTailOrderError", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }

    public DspOrderVO queryOrder(UpdateDriverHeadTailOrderCommand command) {
        if (Objects.isNull(command) || StringUtils.isBlank(command.getDspOrderId())) {
            return null;
        }
        return queryDspOrderService.queryOrderDetailForSchedule(command.getDspOrderId(),false,false);
    }

    public DriverVO queryDriver(UpdateDriverHeadTailOrderCommand command, DspOrderVO dspOrder) {
        if (Objects.isNull(command.getDriverId()) || Objects.isNull(dspOrder) || Objects.isNull(dspOrder.getCategoryCode()) || Objects.isNull(dspOrder.getSupplierId())) {
            return null;
        }
        return queryDriverService.queryDriver(command.getDriverId(), CategoryUtils.selfGetParentType(dspOrder), dspOrder.getSupplierId().longValue());
    }

    public void handleHeadOrder(DriverVO driver, DriverWorkTimeVO workTime) {
        // 查询首单
        DriverHeadOrderVO headOrder = queryHeadOrder(driver, workTime);
        // 保存首单
        driverHeadTailOrderRepository.save(headOrder);
    }

    public void handleTailOrder(DriverVO driver, DriverWorkTimeVO workTime) {
        // 查询尾单
        DriverTailOrderVO tailOrder = queryTailOrder(driver, workTime);
        // 保存尾单
        driverHeadTailOrderRepository.save(tailOrder);
    }


    /**
     * 清除司机首尾单记录
     *
     * @param driver 司机信息
     * @param workDate 工作日期
     */
    public void delDriverHeadTailOrder(DriverVO driver, Date workDate) {
        driverHeadTailOrderRepository.del(driver.getDriverId(), workDate);
    }


    public DriverHeadOrderVO queryHeadOrder(DriverVO driver, DriverWorkTimeVO workTime) {
        // 查询司机开始工作时段前后2小时内订单
        List<DspOrderVO> orders = queryDriverOrderList(driver, DateUtil.addHours(workTime.getStart(), -2), DateUtil.addHours(workTime.getStart(), 2));
        // 首单=用车时间最早的订单
        DspOrderVO dspOrder = orders.stream().min(Comparator.comparing(DspOrderVO::getEstimatedUseTime)).orElse(null);
        return Objects.isNull(dspOrder) ? new DriverHeadOrderVO(driver.getDriverId(), workTime.getStart()) : new DriverHeadOrderVO(driver.getDriverId(), workTime.getStart(), dspOrder.getDspOrderId(), dspOrder.getEstimatedUseTime());
    }

    public DriverTailOrderVO queryTailOrder(DriverVO driver, DriverWorkTimeVO workTime) {
        // 查询司机结束工作时段前后2小时内订单
        List<DspOrderVO> orders = queryDriverOrderList(driver, DateUtil.addHours(workTime.getEnd(), -2), DateUtil.addHours(workTime.getEnd(), 2));
        // 尾单=用车时间最晚的订单
        DspOrderVO dspOrder = orders.stream().max(Comparator.comparing(DspOrderVO::getEstimatedUseTime)).orElse(null);
        return Objects.isNull(dspOrder) ? new DriverTailOrderVO(driver.getDriverId(), workTime.getStart()) : new DriverTailOrderVO(driver.getDriverId(), workTime.getStart(), dspOrder.getDspOrderId(), dspOrder.getEstimatedUseTime());
    }

    public List<DspOrderVO> queryDriverOrderList(DriverVO driver, Date begin, Date end) {
        List<OrderStatusEnum> orderStatusList = Lists.newArrayList(
                OrderStatusEnum.DRIVER_CONFIRMED,
                OrderStatusEnum.DRIVER_CAR_CONFIRMED,
                OrderStatusEnum.DRIVER_TO_MEET,
                OrderStatusEnum.DRIVER_ARRIVE,
                OrderStatusEnum.DRIVER_SERVICE_START,
                OrderStatusEnum.DRIVER_SERVICE_END,
                OrderStatusEnum.ORDER_FINISH
        );
        return queryDspOrderService. queryOrderListWithDriverAndTime(driver, begin, end, orderStatusList, false, false);
    }
}
