package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.api.Approve2SuccessDispatcherGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DispatcherGrabOrderPO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class Approve2SuccessDispatcherGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(Approve2SuccessDispatcherGrabOrderExeCmd.class);

    @Autowired
    private DispatcherGrabOrderDao dispatcherGrabOrderDao;

    public void execute(Approve2SuccessDispatcherGrabOrderCommand command) {
        logger.info("Approve2SuccessDispatcherGrabOrderExeCmd_" + command.getOriDspOrderId(), "command:" + JacksonSerializer.INSTANCE().serialize(command));
        try {
            DispatcherGrabOrderPO dispatcherGrabOrderPO = dispatcherGrabOrderDao.query(command.getUserOrderId(), command.getModifyVersion());
            if (Objects.isNull(dispatcherGrabOrderPO) || !Objects.equals(DispatcherGrabOrderStatusEnum.APPROVE.getCode(), dispatcherGrabOrderPO.getGrabStatus())) {
                return;
            }
            dispatcherGrabOrderDao.approve2Success(command.getOriDspOrderId(), command.getModifyVersion(), command.getSupplierId());
        } catch (Exception e) {
            logger.error("CancelDispatcherGrabOrderExeCmdError", e);
            Cat.logEvent("dcs.self.dsp.dispatcher.confirm.approve2SuccessDispatcherGrabOrder", "0");
            throw ErrorCode.APPROVE2SUCCESS_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        Cat.logEvent("dcs.self.dsp.dispatcher.confirm.approve2SuccessDispatcherGrabOrder", "1");
    }

}
