package com.ctrip.dcs.application.command;

import cn.hutool.core.math.Money;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.saas.AssignResLogVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public abstract class AbstractSaaSOperateExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(AbstractSaaSOperateExeCmd.class);

    @Autowired
    protected QueryDriverService queryDriverService;
    @Autowired
    protected QueryVehicleService queryVehicleService;
    @Autowired
    protected QueryDspOrderService orderQueryService;
    @Autowired
    protected CheckService checkService;
    @Autowired
    protected DriverOrderFactory driverOrderFactory;

    @Autowired
    protected ConfirmDspOrderService confirmDspOrderService;
    @Autowired
    protected ConfirmSaasDspOrderServiceImpl confirmSaasDspOrderService;
    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    protected IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;
    @Autowired
    protected QueryTransportGroupService queryTransportGroupService;

    @Autowired
    protected DcsVbkSupplierOrderGateway dcsVbkSupplierOrderGateway;

    public String execute(SaaSOperateDriverCarCommand cmd) {
        logger.info("AbstractSaaSOperateExeCmd_enter", LocalJsonUtils.toJson(cmd));
        SaaSBusinessVO saaSBusinessVO = buildSaaSBusinessVO(cmd);
        logger.info("AbstractSaaSOperateExeCmd_buildSaaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
        //构建日志参数-vbk融合项目
        cmd.setAssignResLogVO(buildResLogVO(saaSBusinessVO,cmd));
        //业务校验
        this.validBusiness(cmd, saaSBusinessVO);
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, cmd.getDspOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
            // 原单修改待确认时不允许任何操作
            OrderModifyHelper.assertNotToBeConfirmed(dspOrderVO.getDspOrderId());
            Long supplierId = Long.valueOf(saaSBusinessVO.getDspOrderVO().getSupplierId());
            ScheduleTaskDO scheduleTaskDO = buildDefaultScheduleTaskDO(cmd);
            //携程单相关独有逻辑
            if(OrderSourceCodeEnum.TRIP.equals(cmd.getOrderSourceCode())){
                //匹配策略
                HashMap<Object, String> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
                objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
                logger.info("AbstractSaaSOperateExeCmd_matchAssignStrategy_enter", LocalJsonUtils.toJson(objectObjectHashMap));
                scheduleTaskDO = matchAssignStrategy(cmd, saaSBusinessVO);
                logger.info("AbstractSaaSOperateExeCmd_matchAssignStrategy_exit", LocalJsonUtils.toJson(scheduleTaskDO));
                //vbk三期，临时司机接单检查不走
                if(cmd.isNewProcess()){
                    if(cmd.getTransportGroupId() == null){
                        cmd.setTransportGroupId(saaSBusinessVO.getDspOrderVO().getTransportGroupId());
                        logger.info("AbstractSaaSOperateExeCmd_transportgroupId_reset","the transportgroupId of param is null,reset transportgroupId of order，transgroupid is " + saaSBusinessVO.getDspOrderVO().getTransportGroupId());
                    }
                    saaSBusinessVO.setTransportGroupVO(getFitTransportGroupInfo(cmd.getTransportGroupId(),saaSBusinessVO.getDspOrderVO()));
                    if(!Integer.valueOf(YesOrNo.YES.getCode()).equals(saaSBusinessVO.getDriverVO().getDrvTemporaryMark())){
                        CheckModel checkModel = preCheck(saaSBusinessVO, scheduleTaskDO, dspOrderVO);
                        if(!checkModel.getCheckCode().isPass()){
                            CheckCode checkCode = checkModel.getCheckCode();
                            throw new BizException(String.valueOf(checkCode.getCode()), checkCode.getDesc());
                        }
                        saaSBusinessVO.setTransportGroupVO(checkModel.getModel().getTransportGroup());
                    }
                }else {
                    CheckModel checkModel = preCheck(saaSBusinessVO, scheduleTaskDO, dspOrderVO);
                    if(!checkModel.getCheckCode().isPass()){
                        CheckCode checkCode = checkModel.getCheckCode();
                        throw new BizException(String.valueOf(checkCode.getCode()), checkCode.getDesc());
                    }
                    saaSBusinessVO.setTransportGroupVO(checkModel.getModel().getTransportGroup());
                }
            }
            DriverOrderVO driverOrder;
            HashMap<Object, String> objectObjectHashMap0 = new HashMap<>();
            objectObjectHashMap0.put("scheduleTaskDO", LocalJsonUtils.toJson(scheduleTaskDO));
            objectObjectHashMap0.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap0.put("supplierId", LocalJsonUtils.toJson(supplierId));
            logger.info("AbstractSaaSOperateExeCmd_createDriverOrder_enter", LocalJsonUtils.toJson(objectObjectHashMap0));
            // 创建司机单
            driverOrder = createDriverOrder(scheduleTaskDO, saaSBusinessVO, supplierId, cmd.getIsSelfDriver(), cmd.isNewProcess(), cmd.getCheckCode());
            logger.info("AbstractSaaSOperateExeCmd_createDriverOrder_exit", LocalJsonUtils.toJson(driverOrder));

            //更新数据库并发消息
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("scheduleTaskDO", LocalJsonUtils.toJson(scheduleTaskDO));
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            objectObjectHashMap.put("driverOrder", LocalJsonUtils.toJson(driverOrder));
            logger.info("AbstractSaaSOperateExeCmd_assign_enter", LocalJsonUtils.toJson(objectObjectHashMap));
            String assign = assign(scheduleTaskDO, saaSBusinessVO, cmd, supplierId, driverOrder);
            logger.info("AbstractSaaSOperateExeCmd_assign_exit", LocalJsonUtils.toJson(assign));
            return assign;
        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } finally {
            lock.unlock();
        }
    }

    protected abstract SaaSBusinessVO buildSaaSBusinessVO(SaaSOperateDriverCarCommand cmd);


    protected CheckModel preCheck(SaaSBusinessVO saaSBusinessVO, ScheduleTaskDO scheduleTaskDO, DspOrderVO dspOrderVO) {
        CarVO carVO = null;
        if(saaSBusinessVO.getVehicleVO() != null){
            carVO = new CarVO();
            BeanUtils.copyProperties(saaSBusinessVO.getVehicleVO(), carVO);
        }
        TakenCheckCommand takenCheckCommand = new TakenCheckCommand(dspOrderVO, scheduleTaskDO.getSubSku(), saaSBusinessVO.getDriverVO(), carVO, DuidVO.of(scheduleTaskDO),saaSBusinessVO.getTransportGroupVO());
        logger.info("AbstractSaaSOperateExeCmd_preCheck_req", LocalJsonUtils.toJson(takenCheckCommand));
        CheckModel checkModel = checkService.check(takenCheckCommand);
        logger.info("AbstractSaaSOperateExeCmd_preCheck_res", LocalJsonUtils.toJson(checkModel));
        return checkModel;
    }

    protected abstract void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO);



    protected ScheduleTaskDO matchAssignStrategy(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("cmd", cmd);
        objectObjectHashMap.put("saaSBusinessVO", saaSBusinessVO);
        logger.info("AbstractSaaSOperateExeCmd_matchAssignStrategy", LocalJsonUtils.toJson(objectObjectHashMap));
        return null;
    }


    protected DriverOrderVO createDriverOrder(ScheduleTaskDO task, SaaSBusinessVO saaSBusinessVO, Long supplierId, Integer isSelfDriver,boolean newProcess,Integer checkCode) {
        DspOrderVO order = saaSBusinessVO.getDspOrderVO();
        DriverVO driver = saaSBusinessVO.getDriverVO();
        VehicleVO vehicleVO = saaSBusinessVO.getVehicleVO();
        TransportGroupVO transportGroup = saaSBusinessVO.getTransportGroupVO();
        // 创建司机单
        DriverOrderVO driverOrder;
        CarVO carVO = buildCarVO(vehicleVO);
        DspModelVO model = new DspModelVO(order, driver, transportGroup, carVO);
        Integer forceAssignCode = checkCode;
        if (Objects.equals(BizAreaTypeEnum.IGT.getCtripCode(), order.getBizAreaType()) || Objects.equals(BizAreaTypeEnum.IGT.getQunarCode(), order.getBizAreaType())) {
            logger.info("manual operation process", "igt order force assign! dsp order id:{}, driver id:{}", task.getDspOrderId(), model.getDriver().getDriverId());
            forceAssignCode = ForceAssignEnum.FORCED_ASSIGN.getCode();
        }
        driverOrder = driverOrderFactory.createForSaaS(model, new SupplierVO(supplierId), task, isSelfDriver,newProcess, forceAssignCode);
        if (Objects.isNull(driverOrder)) {
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("model", LocalJsonUtils.toJson(model));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            objectObjectHashMap.put("task", LocalJsonUtils.toJson(task));
            objectObjectHashMap.put("isSelfDriver", LocalJsonUtils.toJson(isSelfDriver));
            logger.warn("AbstractSaaSOperateExeCmd_driverOrderFactory.create", LocalJsonUtils.toJson(objectObjectHashMap));
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_ERR.getBizException();
        }
        return driverOrder;
    }

    private CarVO buildCarVO(VehicleVO vehicleVO) {
        if(vehicleVO == null){
            return null;
        }
        CarVO carVO = new CarVO();
        carVO.setCarLicense(vehicleVO.getCarLicense());
        carVO.setCarId(vehicleVO.getCarId());
        carVO.setCarBrandId(vehicleVO.getCarBrandId());
        carVO.setCarColorId(vehicleVO.getCarColorId());
        carVO.setCarColor(vehicleVO.getCarColor());
        carVO.setCarSeriesId(vehicleVO.getCarSeriesId());
        carVO.setCarTypeId(vehicleVO.getCarTypeId());
        carVO.setCarBrandName(vehicleVO.getCarBrandName());
        carVO.setCarTypeName(vehicleVO.getCarTypeName());
        carVO.setCarSeriesName(vehicleVO.getCarSeriesName());
        carVO.setIsEnergy(vehicleVO.getEnergyType());
        carVO.setVehicleStatus(vehicleVO.getVehicleStatus());
        return carVO;
    }

    protected ScheduleTaskDO buildDefaultScheduleTaskDO(SaaSOperateDriverCarCommand cmd){
        ScheduleTaskDO task = ScheduleTaskDO.builder()
                .subSku(new SubSkuVO())
                .dspOrderId(cmd.getDspOrderId())
                .status(ScheduleTaskStatus.INIT)
                .reward(new Money(BigDecimal.ZERO))
                .scheduleId(System.currentTimeMillis())
                .taskId(NumberUtils.LONG_ONE)
                .round(NumberUtils.INTEGER_ONE)
                .dspRewardStrategyId(NumberUtils.LONG_ZERO)
                .build();
        return task;
    }

    protected abstract String assign(ScheduleTaskDO task ,SaaSBusinessVO saaSBusinessVO, SaaSOperateDriverCarCommand cmd,
                                      Long supplierId, DriverOrderVO driverOrder);

    /**
     * 指派解析前段传的duid，更改司机自己match配置
     *
     * @param order
     * @param groupVO
     * @return
     */
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, SaaSOperateDriverCarCommand cmd){
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("cmd", cmd);
        objectObjectHashMap.put("order", order);
        objectObjectHashMap.put("groupVO", groupVO);
        logger.info("AbstractSaaSOperateExeCmd_getSubskuId", LocalJsonUtils.toJson(objectObjectHashMap));
        return null;
    }
    private TransportGroupVO getFitTransportGroupInfo( Long transportGroupId,DspOrderVO dspOrder) {
        if(transportGroupId == null){
            return null;
        }
        List<TransportGroupVO> transportGroups = queryTransportGroupService.queryTransportGroups(dspOrder.getSkuId(), dspOrder.getSupplierId(), transportGroupId.intValue());
        if (CollectionUtils.isEmpty(transportGroups)) {
            throw new BizException(String.valueOf( CheckCode.NULL_TRANSPORT_GROUP.getCode()),CheckCode.NULL_TRANSPORT_GROUP.getDesc());
        }
        return transportGroups.getFirst();

    }

    public AssignResLogVO buildResLogVO(SaaSBusinessVO saaSBusinessVO,SaaSOperateDriverCarCommand command){

        try {
            if(!command.isNewProcess()){
                return null;
            }
            if(saaSBusinessVO == null || saaSBusinessVO.getDspOrderVO() == null){
                return null;
            }
            AssignResLogVO resLogVO = new AssignResLogVO();
            if(OrderSourceCodeEnum.isTrip(saaSBusinessVO.getDspOrderVO().getOrderSourceCode()) ){
                resLogVO.setUserOrderId(saaSBusinessVO.getDspOrderVO().getUserOrderId());
                resLogVO.setIsInland(BizAreaTypeEnum.getQunarCodeByCtripCode(saaSBusinessVO.getDspOrderVO().getBizAreaType()));
            }else {
                resLogVO.setUserOrderId(saaSBusinessVO.getDspOrderVO().getVbkOrderId());
                resLogVO.setIsInland(BizAreaTypeEnum.IGT.getQunarCode());
            }

            resLogVO.setDspOrderId(saaSBusinessVO.getDspOrderVO().getDspOrderId());
            resLogVO.setOrderSourceCode(saaSBusinessVO.getDspOrderVO().getOrderSourceCode());
            if(saaSBusinessVO.getUserOrderDetail() != null){
                if(saaSBusinessVO.getUserOrderDetail().getBookInfo() != null){
                    resLogVO.setOrderTimeBj(saaSBusinessVO.getUserOrderDetail().getBookInfo().getOrderTimeBj());
                }
            }
            resLogVO.setUseTimeLocal(saaSBusinessVO.getDspOrderVO().getEstimatedUseTime());
            resLogVO.setUserTimeBj(saaSBusinessVO.getDspOrderVO().getEstimatedUseTimeBj());
            resLogVO.setCityId(saaSBusinessVO.getDspOrderVO().getCityId() == null ? null :saaSBusinessVO.getDspOrderVO().getCityId().longValue() );
            resLogVO.setSupplierId(command.getSupplierId());
            resLogVO.setAssignEvent(command.getEvent().getCode());
            resLogVO.setTkeSource(command.getTkeSource());
            resLogVO.setAssignWay(command.isBatchAssign() ? 1 : 2);
            Map<String,Object> content = Maps.newHashMap();
            if(saaSBusinessVO.getDriverVO() != null){
                content.put("drvId",saaSBusinessVO.getDriverVO().getDriverId() !=null ?saaSBusinessVO.getDriverVO().getDriverId().toString() : "");
                content.put("drvName",saaSBusinessVO.getDriverVO().getDriverName());
                content.put("drvPhone",saaSBusinessVO.getDriverVO().getDriverPhone());
            }
            if(saaSBusinessVO.getVehicleVO() != null){
                content.put("carId",saaSBusinessVO.getVehicleVO().getCarId() == null ? "" : saaSBusinessVO.getVehicleVO().getCarId().toString());
                content.put("carLicense",saaSBusinessVO.getVehicleVO().getCarLicense());
            }
            content.put("driverRemark",command.getDriverVisibleRemark());
            content.put("supplierRemark",command.getSupplierRemark());
            content.put("currency",command.getDriverSettleCurrency());
            content.put("settlePrice",command.getDriverSettlePrice());
            resLogVO.setContent(content);
            logger.info("buildResLogVO_" +resLogVO.getUserOrderId() , JacksonSerializer.INSTANCE().serialize(resLogVO));
            return resLogVO;
        }catch (Exception e){
            logger.warn("buildResLogVO",e);
        }
        return null;
    }
    
    
    protected boolean isNeedUpdateSupplierRemark(boolean notUpdateSupplierRemark, boolean batchAssign) {
        //如果不需要更新，则直接返回false
        if (notUpdateSupplierRemark) {
            return false;
        }
        //否则根据是否为“批量指派”来判断是否需要更新“供应商备注”
        return batchAssign;
    }

}
