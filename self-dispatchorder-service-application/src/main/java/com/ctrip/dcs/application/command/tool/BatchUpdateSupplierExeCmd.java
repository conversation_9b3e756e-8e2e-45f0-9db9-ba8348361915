package com.ctrip.dcs.application.command.tool;

import com.ctrip.dcs.application.command.converter.SupplierInfoConverter;
import com.ctrip.dcs.domain.common.enums.ConnectModeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.dsporder.entity.tool.BatchUpdateSupplierExecContext;
import com.ctrip.dcs.domain.dsporder.entity.tool.SupplierInfoDO;
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.dto.ServiceProviderContractInfoDTO;
import com.ctrip.dcs.infrastructure.adapter.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryContractByServiceProviderIdResponseType;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BizErrorDetail;
import com.ctrip.dcs.self.dispatchorder.interfaces.ErrorCodeEnum;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsForDspInfo;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsForDspRequestType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupDetailSOAType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class BatchUpdateSupplierExeCmd {
    private static final Logger logger = LoggerFactory.getLogger(BatchUpdateSupplierExeCmd.class);

    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private TmsTransportServiceProxy tmsTransportServiceProxy;
    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private ScmMerchantServiceGateway scmMerchantServiceGateway;

    private final static String TRIPLE_SKUID_TMSID_SUPPLIERID = "%s_%s_%s";
    private final static String SP_ID_CITY_ID_SUPPLIER_ID = "%s_%s_%s";

    public BatchUpdateSupplierResponseType execute(BatchUpdateSupplierRequestType command) {
        BatchUpdateSupplierExecContext context = new BatchUpdateSupplierExecContext();
        // check order exists and order status and connect mode
        checkOrderId(command.getSupplierInfoList(), context);
        // check service provider id and supplierId
        checkSpIdAndSupplierId(command.getSupplierInfoList(), queryContractMap(command), context);
        // check skuId and tmsId if exists and match
        checkTmsAndSkuId(command, context);
        BatchUpdateSupplierResponseType responseType = new BatchUpdateSupplierResponseType();
        // return if eny error occurs
        List<BizErrorDetail> bizErrorDetails = transformBizErrorDetails(context);
        if (!bizErrorDetails.isEmpty()) {
            responseType.setBizErrorDetailList(bizErrorDetails);
            return ServiceResponseUtils.fail(responseType);
        }
        List<SupplierInfoDO> supplierInfoDOList = SupplierInfoConverter.convert(command.getSupplierInfoList(), context);
        populateContractInfo(supplierInfoDOList);
        // batch operate db for updating supplier
        dspOrderRepository.batchUpdateSupplier(supplierInfoDOList,context.getDspOrderIds());
        return ServiceResponseUtils.success(responseType);
    }

    private Map<Long, List<Contract>> queryContractMap(BatchUpdateSupplierRequestType command) {
        Set<Long> spIdSet = command.getSupplierInfoList().stream().map(SupplierInfo::getSpId).collect(Collectors.toSet());
        spIdSet.add(-1L);
        return contractRepository.findManyByServiceProviderIds(Lists.newArrayList(spIdSet), null);
    }

    private void populateContractInfo(List<SupplierInfoDO> supplierInfoDOS) throws BizException {
        // 通过服务商 + 共供应商 + 城市 查询合同信息
        Map<String, ListenableFuture<QueryContractByServiceProviderIdResponseType>> futureMap = Maps.newHashMap();
        supplierInfoDOS.forEach(supplierInfoDO -> {
            var future = scmMerchantServiceGateway.queryContractByServiceProviderIdAsync(
                    Math.toIntExact(supplierInfoDO.getSpId()), supplierInfoDO.getCityId(), Math.toIntExact(supplierInfoDO.getSupplierId()));
            futureMap.put(String.format(SP_ID_CITY_ID_SUPPLIER_ID, supplierInfoDO.getSpId(), supplierInfoDO.getCityId(),
                    supplierInfoDO.getSupplierId()), future);
        });
        for (SupplierInfoDO supplierInfoDO : supplierInfoDOS) {
            String tuple = String.format(SP_ID_CITY_ID_SUPPLIER_ID, supplierInfoDO.getSpId(),
                    supplierInfoDO.getCityId(), supplierInfoDO.getSupplierId());
            var future = futureMap.get(tuple);
            try {
                QueryContractByServiceProviderIdResponseType response = future.get(1000, TimeUnit.MILLISECONDS);
                Optional.ofNullable(response).map(it -> {
                    ServiceProviderContractInfoDTO spContractInfo = JacksonUtil.deserialize(JacksonUtil.serialize(response), new TypeReference<>() {
                    });
                    return JacksonUtil.serialize(spContractInfo);
                }).ifPresentOrElse(supplierInfoDO::setSpContractInfo, () -> {
                    throw new BizException("queryContractByServiceProviderId_process_failed: " + tuple);
                });
            } catch (Exception e) {
                throw new BizException("queryContractByServiceProviderId_failed", e);
            }
        }
    }

    private List<BizErrorDetail> transformBizErrorDetails(BatchUpdateSupplierExecContext context) {
        Table<String, List<String>, String> bizErrorDetailMap = context.getBizErrorDetailMap();
        List<BizErrorDetail> bizErrorDetails = Lists.newArrayList();
        for (String code : bizErrorDetailMap.rowKeySet()) {
            BizErrorDetail bizErrorDetail = new BizErrorDetail();
            bizErrorDetails.add(bizErrorDetail);
            bizErrorDetail.setErrorCode(ErrorCodeEnum.valueOf(code));
            if (ErrorCodeEnum.UNDEFINED.name().equals(code)) {
                bizErrorDetail.setErrorTips(bizErrorDetailMap.get(code, Collections.emptyList()));
            } else {
                bizErrorDetail.setUserOrderIdList(bizErrorDetailMap.row(code).keySet().stream().flatMap(List::stream).toList());
            }
        }
        return bizErrorDetails;
    }

    private void checkOrderId(List<SupplierInfo> supplierInfoList, BatchUpdateSupplierExecContext context) {
        Set<String> orderIdSet = Sets.newHashSet();
        for (SupplierInfo supplierInfo : supplierInfoList) {
            if (!orderIdSet.add(supplierInfo.getUserOrderId())) {
                logger.warn("userOrderId duplicate", supplierInfo.getUserOrderId());
                context.putUndefinedErrorDetail(ErrorCodeEnum.UNDEFINED.name(), "userOrderId duplicate");
                return;
            }
        }
        List<SupplierInfoDO> supplierInfoDOS = dspOrderRepository.batchQueryDspOrdersByUserOrderIds(Lists.newArrayList(orderIdSet));
        ArrayListMultimap<String, String> errorCodes = ArrayListMultimap.create();
        if (CollectionUtils.isEmpty(supplierInfoDOS)) {
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_USER_ORDER_ID_NOT_EXISTS.name(), Lists.newArrayList(orderIdSet));
            return;
        }
        Set<String> persisted = Sets.newHashSet();
        Set<String> hasConfirmed = Sets.newHashSet();

        List<String> collect = supplierInfoDOS.stream().map(SupplierInfoDO::getDspOrderId).distinct().toList();
        context.setDspOrderIds(collect);

        Set<String> nonVBKOrderIdSet = Sets.newHashSet();
        for (SupplierInfoDO supplierInfoDO : supplierInfoDOS) {
            persisted.add(supplierInfoDO.getUserOrderId());
            if (Objects.equals(OrderStatusEnum.DISPATCH_CONFIRMED.getCode(), supplierInfoDO.getOrderStatus())) {
                hasConfirmed.add(supplierInfoDO.getUserOrderId());
            }
            if (!Objects.equals(ConnectModeEnum.VBK.mode, supplierInfoDO.getConnectMode())) {
                nonVBKOrderIdSet.add(supplierInfoDO.getUserOrderId());
                continue;
            }
            context.putPersistSupplierInfoDO(supplierInfoDO.getUserOrderId(), supplierInfoDO);
        }
        Sets.difference(orderIdSet, persisted).forEach(userOrderId -> {
            errorCodes.put(ErrorCodeEnum.SUPPLIER_USER_ORDER_ID_NOT_EXISTS.name(), userOrderId);
        });
        Sets.difference(orderIdSet, hasConfirmed).forEach(userOrderId -> {
            errorCodes.put(ErrorCodeEnum.SUPPLIER_ORDER_STATUS_NOT_CONFIRMED.name(), userOrderId);
        });
        nonVBKOrderIdSet.forEach(userOrderId -> {
            errorCodes.put(ErrorCodeEnum.SUPPLIER_ORDER_CONNECT_MODE_UNSUPPORTED.name(), userOrderId);
        });
        if (!errorCodes.isEmpty()) {
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_USER_ORDER_ID_NOT_EXISTS.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_USER_ORDER_ID_NOT_EXISTS.name()));
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_ORDER_STATUS_NOT_CONFIRMED.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_ORDER_STATUS_NOT_CONFIRMED.name()));
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_ORDER_CONNECT_MODE_UNSUPPORTED.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_ORDER_CONNECT_MODE_UNSUPPORTED.name()));
        }
    }

    private void checkTmsAndSkuId(BatchUpdateSupplierRequestType command, BatchUpdateSupplierExecContext context) {
        List<Integer> skuIdList = Lists.transform(command.getSupplierInfoList(), SupplierInfo::getSkuId);
        List<Long> skuIdListLong = Lists.transform(skuIdList, Integer::longValue);
        var requestType = new QueryTransportGroupsForDspRequestType();
        requestType.setSkuIds(skuIdListLong);
        var transportGroupList = tmsTransportServiceProxy.queryTransportGroupsForDsp(requestType);
        if (Objects.isNull(transportGroupList)) {
            context.putUndefinedErrorDetail(ErrorCodeEnum.UNDEFINED.name(), "query transport group list failed");
            return;
        }
        if (CollectionUtils.isEmpty(transportGroupList.getData())) {
            List<String> userOrderIds = Lists.transform(command.getSupplierInfoList(), SupplierInfo::getUserOrderId);
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_SKU_ID_TMS_ID_NOT_MATCH.name(), userOrderIds);
            return;
        }
        Set<String> skuIdAndTmsIdSet = Sets.newHashSet();
        Set<Long> skuIdSet = Sets.newHashSet();
        Set<Long> tmsIdSet = Sets.newHashSet();
        for (QueryTransportGroupsForDspInfo transportGroup : transportGroupList.getData()) {
            if (CollectionUtils.isNotEmpty(transportGroup.getTransportGroupList())) {
                for (TransportGroupDetailSOAType transportGroupDetailSOAType : transportGroup.getTransportGroupList()) {
                    context.putTransportGroup(transportGroupDetailSOAType.getTransportGroupId(), SupplierInfoConverter.convert(transportGroupDetailSOAType));
                    skuIdAndTmsIdSet.add(String.format(TRIPLE_SKUID_TMSID_SUPPLIERID, transportGroup.getSkuId(),
                            transportGroupDetailSOAType.getTransportGroupId(), transportGroupDetailSOAType.getSupplierId()));
                    tmsIdSet.add(transportGroupDetailSOAType.getTransportGroupId());
                    skuIdSet.add(transportGroup.getSkuId());
                }
            }
        }

        StringJoiner sjTrace = new StringJoiner(",");
        ArrayListMultimap<String, String> errorCodes = ArrayListMultimap.create();
        for (SupplierInfo supplierInfo : command.getSupplierInfoList()) {
            if (!skuIdSet.contains(supplierInfo.getSkuId().longValue())) {
                errorCodes.put(ErrorCodeEnum.SUPPLIER_SKU_ID_NOT_EXISTS.name(), supplierInfo.getUserOrderId());
                sjTrace.add(ErrorCode.BATCH_UPDATE_SUPPLIER_SKU_ID_NOT_EXISTS.getDesc()).add(supplierInfo.getUserOrderId() + " skuId:" + supplierInfo.getSkuId());
            }
            if (!tmsIdSet.contains(supplierInfo.getTransportGroupId())) {
                errorCodes.put(ErrorCodeEnum.SUPPLIER_TMS_ID_NOT_EXISTS.name(), supplierInfo.getUserOrderId());
                sjTrace.add(ErrorCode.BATCH_UPDATE_SUPPLIER_TMS_ID_NOT_EXISTS.getDesc()).add(supplierInfo.getUserOrderId() + " tmsId:" + supplierInfo.getTransportGroupId());
            }
            if (!skuIdAndTmsIdSet.contains(String.format(TRIPLE_SKUID_TMSID_SUPPLIERID, supplierInfo.getSkuId(), supplierInfo.getTransportGroupId(), supplierInfo.getSupplierId()))) {
                errorCodes.put(ErrorCodeEnum.SUPPLIER_SKU_ID_TMS_ID_NOT_MATCH.name(), supplierInfo.getUserOrderId());
                sjTrace.add(ErrorCode.BATCH_UPDATE_SUPPLIER_SKU_ID_TMS_ID_NOT_MATCH.getDesc()).add(supplierInfo.getUserOrderId() + " skuId:" + supplierInfo.getSkuId() + " tmsId:" + supplierInfo.getTransportGroupId());
            }
        }
        if (!errorCodes.isEmpty()) {
            logger.error("checkTmsAndSkuId", sjTrace.toString());
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_SKU_ID_NOT_EXISTS.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_SKU_ID_NOT_EXISTS.name()));
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_TMS_ID_NOT_EXISTS.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_TMS_ID_NOT_EXISTS.name()));
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_SKU_ID_TMS_ID_NOT_MATCH.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_SKU_ID_TMS_ID_NOT_MATCH.name()));
        }
    }

    private void checkSpIdAndSupplierId(List<SupplierInfo> supplierInfoList, Map<Long, List<Contract>> contractMap, BatchUpdateSupplierExecContext context) {
        if (contractMap.isEmpty()) {
            context.putUndefinedErrorDetail(ErrorCodeEnum.UNDEFINED.name(), "contract not found");
            return;
        }
        StringJoiner sjTrace = new StringJoiner(",");
        ArrayListMultimap<String, String> errorCodes = ArrayListMultimap.create();
        for (SupplierInfo supplierInfo : supplierInfoList) {
            List<Contract> contracts = contractMap.get(supplierInfo.getSpId());
            if (CollectionUtils.isEmpty(contracts)) {
                errorCodes.put(ErrorCodeEnum.SUPPLIER_SP_ID_NOT_EXISTS.name(), supplierInfo.getUserOrderId());
                sjTrace.add(ErrorCode.BATCH_UPDATE_SUPPLIER_SP_ID_NOT_EXISTS.getDesc()).add(supplierInfo.getUserOrderId() + " spId:" + supplierInfo.getSpId());
            } else {
                Collection<Contract> result = Collections2.filter(contracts, contract -> contract.getSupplierId().equals(supplierInfo.getSupplierId()));
                if (CollectionUtils.isEmpty(result)) {
                    errorCodes.put(ErrorCodeEnum.SUPPLIER_ID_NOT_EXISTS.name(), supplierInfo.getUserOrderId());
                    sjTrace.add(ErrorCode.BATCH_UPDATE_SUPPLIER_ID_NOT_EXISTS.getDesc()).add(supplierInfo.getUserOrderId() + " supplierId:" + supplierInfo.getSupplierId());
                }
            }
        }

        if (!errorCodes.isEmpty()) {
            logger.error("checkSpIdAndSupplierId", sjTrace.toString());
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_SP_ID_NOT_EXISTS.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_SP_ID_NOT_EXISTS.name()));
            context.putBizErrorDetail(ErrorCodeEnum.SUPPLIER_ID_NOT_EXISTS.name(), errorCodes.get(ErrorCodeEnum.SUPPLIER_ID_NOT_EXISTS.name()));
        }
    }
}
