package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class BindCarAndTakenListener {

    private static final Logger logger = LoggerFactory.getLogger(BindCarAndTakenListener.class);

    @Autowired
    protected PurchaseSupplyOrderGateway purchaseSupplyOrderGateway;

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderConfirmRecordRepository confirmRecordRepository;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.BIND_CAR_AND_TAKEN_TOPIC, consumerGroup = "syncPurchase", idempotentChecker = "redisIdempotentChecker")
    public void syncPurchase(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            DspOrderDO dspOrder = dspOrderRepository.find(dspOrderId);
            DspOrderConfirmRecordVO orderConfirmRecord = confirmRecordRepository.find(dspOrder.getConfirmRecordId());
            purchaseSupplyOrderGateway.confirm(orderConfirmRecord);
        } catch (Exception e) {
            logger.warn("BindCarAndTakenListenerError", e);
            throw new NeedRetryException("BindCarAndTakenListenerError");
        }
    }
}
