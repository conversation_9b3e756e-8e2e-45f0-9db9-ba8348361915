package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.IdGeneratorService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.CancelOrderInOtherTaskEvent;
import com.ctrip.dcs.domain.schedule.event.VBKGrabDspTaskEvent;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskOperateEvent;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class CreateVBKGrabTaskExeCmd extends AbstractVBKGrabTaskExeCmd{

    private static final Logger logger = LoggerFactory.getLogger(CreateVBKGrabTaskExeCmd.class);

    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    private QueryDspOrderService queryDspOrderService;

    public Pair<Integer, Integer> execute(VBKGrabTaskDTO requestType) throws Exception{
        logger.info("CreateVBKGrabTaskExeCmd_enter", LocalJsonUtils.toJson(requestType));
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_GRAB_TASK, requestType.getSupplierId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            //构建VBK抢单任务实体
            VBKDriverGrabTaskDO vbkDriverGrabTaskDO = super.buildVBKDriverGrabTaskDO(requestType);
            Integer taskNo = vbkDriverGrabTaskRepository.queryMaxTaskNoBySupplierId(requestType.getSupplierId());
            logger.info("CreateVBKGrabTaskExeCmd_enter", LocalJsonUtils.toJson(taskNo));
            vbkDriverGrabTaskDO.setTaskNo(taskNo + 1);

            //构建VBK待抢订单实体
            List<DspOrderVO> dspOrderVOS = queryDspOrderService.selectDspOrderVOs(requestType.getOrderIdList());
            if(dspOrderVOS.size() != requestType.getOrderIdList().size()){
                throw ErrorCode.PLEASE_TRY_AGAIN.getBizException();
            }
            Map<String, DspOrderVO> dspOrderVOMap = dspOrderVOS.stream().filter(x -> x.getOrderStatus().equals(OrderStatusEnum.DISPATCH_CONFIRMED.getCode())).collect(Collectors.toMap(DspOrderVO::getDspOrderId, o -> o, (o1, o2) -> o2));
            logger.info("QueryDspOrderServiceImpl_dspOrderVOMap", LocalJsonUtils.toJson(dspOrderVOMap));

            if(dspOrderVOMap.size() == 0){
                throw ErrorCode.ORDER_STATUS_CHANGED.getBizException();
            }
            //构建VBK待抢订单实体
            List<VBKDriverGrabOrderDO> vbkDriverGrabOrderDOList = requestType.getOrderIdList().stream().map(dspOrderId -> super.buildVbkDriverGrabOrderDO(requestType, dspOrderVOMap, dspOrderId)).filter(Objects::nonNull).collect(Collectors.toList());
            //构建VBK抢单司机实体
            List<VBKDriverGrabDriverDO> vbkDriverGrabDriverDOList = requestType.getDriverIdList().stream().map(driverId -> super.buildVbkDriverGrabDriverDO(requestType, driverId)).collect(Collectors.toList());
            //数据存储
            Pair<String, Integer> pair = vbkDriverGrabTaskRepository.createVBKGrabTask(vbkDriverGrabTaskDO, vbkDriverGrabOrderDOList, vbkDriverGrabDriverDOList, requestType.getOrderIdList(), super.getDbBatchSize());
            if(Objects.isNull(pair)){
                throw ErrorCode.PLEASE_TRY_AGAIN.getBizException();
            }
            int total = requestType.getOrderIdList().size();
            //发送派发消息
            sendVBKGrabDspTask(vbkDriverGrabOrderDOList);
            //把有抢单任务的订单从原任务中剔除，再将全部订单创建新抢单任务
            sendCancelOrderInOtherTask(pair.getKey(), requestType.getOrderIdList());
            //操作记录
            sendVBKGrabTaskOperateEvent(requestType, pair.getKey());
            return new Pair<>(pair.getValue(), total - pair.getValue());
        } catch (DistributedLockRejectedException e) {
            logger.error("CreateVBKGrabTaskExeCmd_error", e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        }  finally {
            lock.unlock();
        }
    }

    public void sendCancelOrderInOtherTask(String vbkGrabTaskId, List<String> orderIdList) {
        try{
            logger.info("CreateVBKGrabTaskExeCmd_sendCancelOrderInOtherTask", vbkGrabTaskId);
            if(CollectionUtils.isEmpty(orderIdList)){
                return;
            }
            String orderIdStr = StringUtils.join(orderIdList, ",");
            CancelOrderInOtherTaskEvent cancelOrderInOtherTaskEvent = new CancelOrderInOtherTaskEvent(vbkGrabTaskId, orderIdStr);
            logger.info("CreateVBKGrabTaskExeCmd_sendCancelOrderInOtherTask_event", LocalJsonUtils.toJson(cancelOrderInOtherTaskEvent));
            messageProducer.send(cancelOrderInOtherTaskEvent);
        }catch (Exception ex){
            logger.error("CreateVBKGrabTaskExeCmd_sendCancelOrderInOtherTask", ex);
        }
    }


    public void sendVBKGrabTaskOperateEvent(VBKGrabTaskDTO requestType, String vbkGrabTaskId) {
        try{
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = new VBKGrabTaskOperateDTO();
            vbkGrabTaskOperateDTO.setTaskId(vbkGrabTaskId);
            vbkGrabTaskOperateDTO.setCityId(requestType.getCityId());
            vbkGrabTaskOperateDTO.setSupplierId(requestType.getSupplierId());
            vbkGrabTaskOperateDTO.setOperatorName(requestType.getOperatorName());
            vbkGrabTaskOperateDTO.setOperatorType(VBKGrabOperationTypeEnum.OperationTypeEnum.CREATE.getCode());
            vbkGrabTaskOperateDTO.setOperatorDesc(VBKGrabOperationTypeEnum.OperationTypeEnum.CREATE.getDes());
            vbkGrabTaskOperateDTO.setCurrentDateStr(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));
            vbkGrabTaskOperateDTO.setInitialType(requestType.getInitial().getType());
            vbkGrabTaskOperateDTO.setInitialValue(requestType.getInitial().getValue());
            vbkGrabTaskOperateDTO.setInitialRate(requestType.getInitial().getRate());
            vbkGrabTaskOperateDTO.setInitialCurrency(requestType.getInitial().getCurrency());
            vbkGrabTaskOperateDTO.setDriverNum((long) requestType.getDriverIdList().size());
            vbkGrabTaskOperateDTO.setRewardsType(requestType.getRewards().getType());
            vbkGrabTaskOperateDTO.setRewardsValue(requestType.getRewards().getValue());
            vbkGrabTaskOperateDTO.setRewardsRate(requestType.getRewards().getRate());
            vbkGrabTaskOperateDTO.setRewardsCurrency(requestType.getRewards().getCurrency());
            vbkGrabTaskOperateDTO.setPayForDriver(requestType.getPayForDriver());
            vbkGrabTaskOperateDTO.setGrabEndTime(requestType.getGrabEndTime());
            vbkGrabTaskOperateDTO.setMakeUpEffectTime(requestType.getMakeUpEffectTime());
            String jsonStr = JsonUtils.toJson(vbkGrabTaskOperateDTO);
            logger.info("CreateVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", jsonStr);
            VBKGrabTaskOperateEvent shutdownScheduleEvent = new VBKGrabTaskOperateEvent(jsonStr);
            messageProducer.send(shutdownScheduleEvent);
        }catch (Exception ex){
            logger.error("CreateVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", ex);
        }
    }

}
