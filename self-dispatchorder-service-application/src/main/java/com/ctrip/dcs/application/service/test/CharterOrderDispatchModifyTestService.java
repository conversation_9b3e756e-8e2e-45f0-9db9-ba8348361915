package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.impl.CharterOrderDispatchModifyMsgService;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.OrderVO;
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component("CharterOrderDispatchModifyTestService")
public class CharterOrderDispatchModifyTestService implements ITestDspOrderService{
    @Autowired
    private CharterOrderDispatchModifyMsgService service;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Override
    public String test(Map<String, String> params) {
        String driverId = params.get("driverId");
        Date startTime = LocalDateUtils.toDate(params.get("startTime"),LocalDateUtils.YYYY_MM_DD_HH_MM_SS);
        Date endTime = LocalDateUtils.toDate(params.get("endTime"),LocalDateUtils.YYYY_MM_DD_HH_MM_SS);
        //查询需要改派得订单
        DriverVO driverVO = new DriverVO(Long.valueOf(driverId));
        List<OrderStatusEnum> orderStatusList = Arrays.asList(OrderStatusEnum.DRIVER_ARRIVE,OrderStatusEnum.DRIVER_CAR_CONFIRMED);
        List<OrderVO> orderVOList = queryDspOrderService.queryNewOrderList(driverVO,startTime,endTime,orderStatusList);
        if(LocalCollectionUtils.isEmpty(orderVOList)){
            return "order_null";
        }
        boolean result = service.sendMsg(orderVOList.get(0));
        return String.valueOf(result);
    }
}
