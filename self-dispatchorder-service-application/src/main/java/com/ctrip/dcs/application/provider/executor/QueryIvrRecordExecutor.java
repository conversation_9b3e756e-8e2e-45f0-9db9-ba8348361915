package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.QueryIvrRecordExeCmd;
import com.ctrip.dcs.application.provider.converter.CreateIvrRecordConverter;
import com.ctrip.dcs.application.provider.converter.QueryIvrRecordConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryIvrRecordExecutor extends AbstractRpcExecutor<QueryIvrRecordRequestType, QueryIvrRecordResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryIvrRecordExecutor.class);

    @Autowired
    private QueryIvrRecordExeCmd queryIvrRecordExeCmd;

    @Override
    public QueryIvrRecordResponseType execute(QueryIvrRecordRequestType requestType) {
        QueryIvrRecordResponseType responseType = new QueryIvrRecordResponseType();
        if (requestType == null || (StringUtils.isEmpty(requestType.getUserOrderId())
                && StringUtils.isEmpty(requestType.getSupplyOrderId())
                && StringUtils.isEmpty(requestType.getRecordGuid()))){
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            List<IvrRecordInfo> data = queryIvrRecordExeCmd.execute(QueryIvrRecordConverter.converter(requestType));
            responseType.setData(data);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("query_ivr_record_error", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }
}
