package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.VbkBindCarAndTakenExeCmd;
import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 * vbk指派
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class VbkBindCarAndTakenExecutor extends AbstractRpcExecutor<VbkBindCarAndTakenRequestType, VbkBindCarAndTakenResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(VbkBindCarAndTakenExecutor.class);

    @Autowired
    private VbkBindCarAndTakenExeCmd vbkBindCarAndTakenExeCmd;


    @Override
    public VbkBindCarAndTakenResponseType execute(VbkBindCarAndTakenRequestType requestType) {
        VbkBindCarAndTakenResponseType responseType = new VbkBindCarAndTakenResponseType();
        if (!validatePrm(requestType)) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

        }
        try {
            //todo 需要传运力组id或自查duid
            vbkBindCarAndTakenExeCmd.execute(CustomerAssignDriverConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            logger.error("vbkBindCarAndTakenException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }

    private boolean validatePrm(VbkBindCarAndTakenRequestType requestType) {
        return org.apache.commons.lang.StringUtils.isNotBlank(requestType.getDspOrderId())
                && Objects.nonNull(requestType.getDriverId())
                && requestType.getDriverId() > 0L
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserName())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getSysUserAccount())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserType())
                && StringUtils.isNotBlank(requestType.getCarLicense())
                && Objects.nonNull(requestType.getCarTypeId())
                && StringUtils.isNotBlank(requestType.getCarColor())
                && StringUtils.isNotBlank(requestType.getCarDesc())
                && Objects.nonNull(requestType.getSupplierId());
    }
}
