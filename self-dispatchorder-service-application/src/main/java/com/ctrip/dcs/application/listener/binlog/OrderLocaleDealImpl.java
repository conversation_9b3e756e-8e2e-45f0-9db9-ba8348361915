package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.entity.OrderLocaleDO;
import com.ctrip.dcs.domain.dsporder.repository.OrderLocaleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("order_locale")
public class OrderLocaleDealImpl implements BinlogDeal{

    @Autowired
    private OrderLocaleRepository orderLocaleRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        String locale = dataChange.getAfterColumnValue("locale");
        orderLocaleRepository.clearCache(dspOrderId, locale);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> {
            List<OrderLocaleDO> list = orderLocaleRepository.findByDspOrderId(dspOrderId);
            list.forEach(orderLocaleDO -> orderLocaleRepository.clearCache(orderLocaleDO.getDspOrderId(), orderLocaleDO.getLocale()));
        });
    }
}
