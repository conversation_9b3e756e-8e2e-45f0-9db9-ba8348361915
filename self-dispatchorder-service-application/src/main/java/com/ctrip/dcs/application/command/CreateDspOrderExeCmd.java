package com.ctrip.dcs.application.command;

import cn.hutool.core.util.NumberUtil;
import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.BaseLocationVO;
import com.ctrip.dcs.domain.common.value.LocationRouteVO;
import com.ctrip.dcs.domain.common.value.OrderWayPointVO;
import com.ctrip.dcs.domain.dsporder.entity.*;
import com.ctrip.dcs.domain.dsporder.event.OrderBookEvent;
import com.ctrip.dcs.domain.dsporder.gateway.CityGateway;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleStrategyRepository;
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO;
import com.ctrip.dcs.infrastructure.adapter.carconfig.DspRotateAbTestConfig;
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.di.data.abtest.client.ABTestClient;
import com.ctrip.di.data.abtestservice.Alternative;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.Cat;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;

import static com.ctrip.dcs.domain.common.constants.SysConstants.DSP_ORDER_CREATE_IDEMPOTENT;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class CreateDspOrderExeCmd {
    private static final Logger logger = LoggerFactory.getLogger(CreateDspOrderExeCmd.class);
    public static final Integer ACTIVITY_TYPE_RATE_PLAN = 1002;

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderOperateRepository dspOrderOperateRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private IdGeneratorService idGeneratorService;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Autowired
    private CityGateway cityGateway;

    @Autowired
    private HighLevelCheckService highLevelCheckService;

    @Autowired
    private CancelDspOrderExeCmd cancelDspOrderExeCmd;
    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    private QueryDriverLocationService queryDriverLocationService;
    @Autowired
    private DspRotateAbTestConfig dspRotateAbTestConfig;
    @Autowired
    private ScheduleStrategyRepository strategyRepository;
    @Autowired
    @Qualifier("commonConfConfig")
    private BaseConfigService commonConfig;
    @Autowired
    private ShortDistanceStrategyExeCmd shortDistanceStrategyExeCmd;

    @Autowired
    private LocationBasedService locationBasedService;

    public String execute(CreateDspOrderCommand cmd) {

        DspOrderDO dspOrderDO = cmd.getDspOrderDO();
        String idempotentKey = String.format(DSP_ORDER_CREATE_IDEMPOTENT, dspOrderDO.getUserOrderId(), dspOrderDO.getSpId(), dspOrderDO.getVehicleGroupId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.LOCK_FAILED.getBizException();
            }
            if (OrderSceneEnum.BUFFER_CHANGE.getCode().equals(cmd.getOrderScene()) || OrderSceneEnum.ASSIGN_DIFF_SSUPPLIER_CREATE.getCode().equals(cmd.getOrderScene())) {
                String cancelReason = businessTemplateInfoConfig.getCancelReasonMap().get(CancelReasonIdEnum.BUFFER_CHANGE_CANCEL.getCode().toString());
                cancelDspOrderExeCmd.execute(CancelDspOrderConverter.converter(cmd, cancelReason));
            }
            // 设置原单修改标识
            if (OrderSceneEnum.ORDER_MODIFY.getCode().equals(cmd.getOrderScene())) {
                dspOrderDO.setOriOrderModify(YesOrNo.YES.getCode());
            }
            //数据库幂等
            String dbDspOrderId = OrderSceneEnum.ORDER_MODIFY.getCode().equals(cmd.getOrderScene()) ? checkDbExistForOrderModify(dspOrderDO) : checkDbExist(dspOrderDO);
            if (!StringUtils.isEmpty(dbDspOrderId)) {
                return dbDspOrderId;
            }
            UserOrderDetail userOrderDetail = assembleOtherFields(cmd);
            setDspRotateAbTest(cmd,userOrderDetail);
            String dspOrderId = idGeneratorService.generateId().toString();
            dspOrderDO.setDspOrderId(dspOrderId);
            dspOrderDO.setOrderStatus(OrderStatusEnum.TO_BE_CONFIRMED.getCode());
            cmd.getDspOrderDetailDO().setDspOrderId(dspOrderId);
            cmd.getDspOrderFeeDO().setDspOrderId(dspOrderId);
            setRatePlanId(cmd);
            if (userOrderDetail.getBookInfo() != null && userOrderDetail.getBookInfo().getNonOccupancyAmount() != null && userOrderDetail.getBookInfo().getNonOccupancyAmount().compareTo(BigDecimal.ZERO) > 0) {
                cmd.getDspOrderFeeDO().setNonOccupancyAmount(userOrderDetail.getBookInfo().getNonOccupancyAmount());
            }
            //西安高速费处理且是接送机
            if (queryDriverLocationService.judgeXianSpecialCity(cmd.getDspOrderDO().getUserOrderId()) && CategoryCodeEnum.isAirportOrStationOrPoint(cmd.getDspOrderDO().getCategoryCode())) {
                if (cmd.getDspOrderFeeDO() != null && cmd.getDspOrderFeeDO().getOriTollFee() != null && cmd.getDspOrderFeeDO().getOriTollFee().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal tollFee = cmd.getDspOrderFeeDO().getOriTollFee();
                    logger.info("dsp_order_fee_xian" + cmd.getDspOrderDO().getUserOrderId(), "tollFee=" + tollFee);
                    DspOrderFeeDO dspOrderFeeDO = cmd.getDspOrderFeeDO();
                    logger.info("dsp_order_fee_xian" + cmd.getDspOrderDO().getUserOrderId(), "dspOrderFeeDO=" + JacksonUtil.serialize(dspOrderFeeDO));
                    dspOrderFeeDO.setTollFee(tollFee);
                    dspOrderFeeDO.setCostAmount(dspOrderFeeDO.getCostAmount().add(tollFee));
                    dspOrderFeeDO.setNoCommisionAmount(dspOrderFeeDO.getNoCommisionAmount().add(tollFee));
                    logger.info("dsp_order_fee_xian_after" + cmd.getDspOrderDO().getUserOrderId(), "modify after dspOrderFeeDO=" + JacksonUtil.serialize(dspOrderFeeDO));
                    cmd.setDspOrderFeeDO(dspOrderFeeDO);
                    cmd.getDspOrderDO().setSpecialCity(Boolean.TRUE);
                }
            }
            // 判断短公里订单
            checkShortDisOrder(cmd);
            // 修复订单预估时长和里程
            fixOrderEstimatedInfo(cmd);
            logger.info("CreateDspOrderExeCmd", "CreateDspOrderExeCmd param =" + JacksonUtil.serialize(cmd));
            dspOrderOperateRepository.save(cmd.getDspOrderDO(), cmd.getDspOrderDetailDO(), cmd.getDspOrderFeeDO(), cmd.getPlatformPriceStrategyDO(), cmd.getOrderWayPointList(), cmd.getOrderExtendAttributeInfo(), cmd.getPlatformRewardInfo());
            WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.createDspOrderWorkBenchLog(dspOrderDO.getUserOrderId(), dspOrderId, dspOrderDO.getSpName(), dspOrderDO.getSpId());
            workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
            messageProvider.send(new OrderBookEvent(dspOrderId, dspOrderDO.getUserOrderId(), cmd.getOrderScene(), dspOrderDO.getOldDspOrderId(), dspOrderDO.getNeedSupplierAgree(), 0L));
            MetricsUtil.recordValue("create.dsporder.count", getMetricsTags(cmd, "200"));
            return dspOrderId;
        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            MetricsUtil.recordValue("create.dsporder.count", getMetricsTags(cmd, ErrorCode.LOCK_FAILED.getCode()));
            throw ErrorCode.LOCK_FAILED.getBizException();
        } catch (Exception e) {
            logger.error(e);
            MetricsUtil.recordValue("create.dsporder.count", getMetricsTags(cmd, ErrorCode.SERVER_ERROR.getCode()));
            throw ErrorCode.SERVER_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }

    /**
     * 兜底流程，修复订单预估时长
     * 1、非包车产线
     * 2、订单预估时长为空或者是0，则根据出发地、目的地、途径点，查询lbs服务，获取预估时长和里程
     * @param cmd
     */
    public void fixOrderEstimatedInfo(CreateDspOrderCommand cmd) {
        try {
            String categoryCode = cmd.getDspOrderDO().getCategoryCode();
            if (CategoryCodeEnum.isCharterOrder(categoryCode)) {
                // 包车订单，预估时长或里程有可能为空，不做处理
                return;
            }
            BigDecimal estimatedMin = NumberUtil.null2Zero(cmd.getDspOrderDO().getEstimatedMin());
            logger.info("fixOrderEstimatedInfo", "userOrderId:{}, estimatedMin:{}", cmd.getDspOrderDO().getUserOrderId(), estimatedMin);
            if (NumberUtil.isGreater(estimatedMin, BigDecimal.ZERO)) {
                // >0，不做处理
                return;
            }
            // 埋点
            MetricsUtil.recordValue(MetricsConstants.CREATE_DSP_ORDER_ESTIMATED_MIN_NULL);
            // 查询lbs
            Integer fromCityId = cmd.getDspOrderDO().getFromCityId();
            FromPoiDTO fromPoiDTO = cmd.getDspOrderDO().getFromPoiDTO();
            Integer toCityId = cmd.getDspOrderDO().getToCityId();
            ToPoiDTO toPoiDTO = cmd.getDspOrderDO().getToPoiDTO();
            List<OrderWayPointVO> orderWayPointList = cmd.getOrderWayPointList();
            LocationRouteVO route = locationBasedService.query(BaseLocationVO.newBaseLocation(fromCityId, fromPoiDTO), BaseLocationVO.newBaseLocation(toCityId, toPoiDTO), BaseLocationVO.newBaseLocations(orderWayPointList));
            // 更新预估时长和预估完成时间
            cmd.getDspOrderDO().setEstimatedMin(BigDecimal.valueOf(route.getDuration()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP));
            cmd.getDspOrderDO().setEstimatedKm(BigDecimal.valueOf(route.getDistance()).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP));
            cmd.getDspOrderDO().setPredicServiceStopTime(new Timestamp(DateUtil.addSeconds(new Date(cmd.getDspOrderDO().getEstimatedUseTime().getTime()), route.getDuration()).getTime()));
            cmd.getDspOrderDO().setPredicServiceStopTimeBj(new Timestamp(DateUtil.addSeconds(new Date(cmd.getDspOrderDO().getEstimatedUseTimeBj().getTime()), route.getDuration()).getTime()));
        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void setRatePlanId(CreateDspOrderCommand cmd) {
        if (cmd.getDspOrderFeeDO().getSupplierBorneDTO() != null && !CollectionUtils.isEmpty(cmd.getDspOrderFeeDO().getSupplierBorneDTO().getActivity())) {
            List<ActivityInfoDTO> activities = cmd.getDspOrderFeeDO().getSupplierBorneDTO().getActivity();
            ActivityInfoDTO activityInfo = activities.stream()
                    .filter(a -> ACTIVITY_TYPE_RATE_PLAN.equals(a.getActivityType()))
                    .findFirst()
                    .orElse(null);

            if (activityInfo != null && activityInfo.getActivityId() != null) {
                cmd.getDspOrderDO().setRatePlanId(activityInfo.getActivityId().intValue());
            }
        }
    }

    private void checkShortDisOrder(CreateDspOrderCommand cmd) {
        cmd.getDspOrderDO().setShortDisOrder(0);
        try {
            // 范围：境内接送机站
            if (BizAreaTypeEnum.isCHF(cmd.getDspOrderDO().getBizAreaType()) && CategoryCodeEnum.isAirportOrStationOrPoint(cmd.getDspOrderDO().getCategoryCode())) {
                // 灰度城市
                String grayCityIds = commonConfig.getString("short_dis_gray_city_ids", Strings.EMPTY);
                boolean gray = "all".equals(grayCityIds) || Arrays.stream(grayCityIds.split(",")).filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(Integer::parseInt).toList().contains(cmd.getDspOrderDO().getCityId());
                if (gray) {
                    // 查询短公里订单策略配置
                    boolean checkShortDisOrder = shortDistanceStrategyExeCmd.checkShortDisOrder(cmd.getDspOrderDO().getCityId(), cmd.getDspOrderDO().getCategoryCode(), cmd.getDspOrderDO().getVehicleGroupId().longValue()
                            , cmd.getDspOrderDO().getDistributionChannelId().longValue(), cmd.getDspOrderDO().getEstimatedKm(), cmd.getDspOrderDO().getEstimatedUseTimeBj());
                    if (checkShortDisOrder) {
                        cmd.getDspOrderDO().setShortDisOrder(1);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("checkShortDisOrder_error", e);
        }
        Cat.logEvent("Dcs.Dsp.CreateDspOrder.CheckShortDisOrder", String.valueOf(cmd.getDspOrderDO().getShortDisOrder()));
    }

    private Map<String, String> getMetricsTags(CreateDspOrderCommand command, String resCode) {
        Map<String, String> tags = new HashMap<>();
        try {
            DspOrderDO dspOrderDO = command.getDspOrderDO();
            DspOrderDetailDO detail = command.getDspOrderDetailDO();
            tags.put("bizAreaType", String.valueOf(dspOrderDO.getBizAreaType()));
            if (MapUtils.isNotEmpty(detail.getExtendInfoMap()) && detail.getExtendInfoMap().get(SysConstants.Order.ORDER_SYS_TYPE) != null) {
                tags.put("orderSysType", String.valueOf(detail.getExtendInfoMap().get(SysConstants.Order.ORDER_SYS_TYPE)));
            }
            tags.put("categoryCode", dspOrderDO.getCategoryCode());
            tags.put("cityId", String.valueOf(dspOrderDO.getCityId()));
            tags.put("shortDisOrder", String.valueOf(dspOrderDO.getShortDisOrder()));
            tags.put("resCode", resCode);
        } catch (Exception e) {
            logger.warn("getMetricsTags_error", e);
        }
        return tags;
    }

    // 原单修改的数据库幂等检查
    private String checkDbExistForOrderModify(DspOrderDO dspOrderDO) {
        List<DspOrderDO> list = dspOrderRepository.queryValidDspOrders(dspOrderDO.getUserOrderId());
        if (!CollectionUtils.isEmpty(list)) {
            Optional<DspOrderDO> first = list.stream().filter(f -> f.getSupplyOrderId().equals(dspOrderDO.getSupplyOrderId()) && f.getModifyVersion().equals(dspOrderDO.getModifyVersion()) && f.getVehicleGroupId().equals(dspOrderDO.getVehicleGroupId())).findFirst();
            if (first.isPresent()) {
                logger.info("create_order_" + dspOrderDO.getUserOrderId(), "db exist valid dborder=" + JacksonUtil.serialize(first.get()));
                return first.get().getDspOrderId();
            }
        }
        return null;
    }
    private String checkDbExist(DspOrderDO dspOrderDO) {
        List<DspOrderDO> list = dspOrderRepository.queryValidDspOrders(dspOrderDO.getUserOrderId());
        if (!CollectionUtils.isEmpty(list)) {
            Optional<DspOrderDO> first = list.stream().filter(f -> f.getSpId().equals(dspOrderDO.getSpId()) && f.getVehicleGroupId().equals(dspOrderDO.getVehicleGroupId())).findFirst();
            if (first.isPresent()) {
                logger.info("create_order_" + dspOrderDO.getUserOrderId(), "db exist valid dborder=" + JacksonUtil.serialize(first.get()));
                return first.get().getDspOrderId();
            }
        }
        return null;
    }

    private void setDspRotateAbTest(CreateDspOrderCommand cmd,UserOrderDetail userOrderDetail) {
        DspOrderDO order = cmd.getDspOrderDO();
        try {
            UserBookInfo userBookInfo = userOrderDetail.getBookInfo();
            if (CategoryCodeEnum.isCharterOrder(order.getCategoryCode()) || Objects.equals(userBookInfo.getOrderSysType(),3)) {
                logger.info("setDspRotateAbTest", "CharterOrder not abTest. userOrderId={}", order.getUserOrderId());
                return;
            }
            Pair<Integer, Long> dspStrategyId = dspRotateAbTestConfig.getDspStrategyId(order.getCityId(), order.getVehicleGroupId(), DateUtil.formatDate(order.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT));
            ScheduleStrategyVO strategyVO = strategyRepository.find(dspStrategyId.getRight());
            if (dspStrategyId.getRight() != 0L && !CollectionUtils.isEmpty(strategyVO.getItems())) {
                String dspStrategyStr = cmd.getDspOrderDetailDO().getDspStrategyStr();
                HashMap map = JsonUtil.fromJson(dspStrategyStr, HashMap.class);
                String strategyId = MapUtils.getString(map, "dspStrategyId");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(strategyId) && Objects.equals(dspStrategyId.getKey(), Integer.valueOf(1))) {
                    map.put("dspStrategyId", dspStrategyId.getRight());
                    logger.info("setDspRotateAbTest", "abTest dsoOrderId={},dspStrategyId={}", order.getUserOrderId(), dspStrategyId.getRight());
                    cmd.getDspOrderDetailDO().setDspStrategyStr(JacksonSerializer.INSTANCE().serialize(map));
                }
                // 时间范围ABT
                if (org.apache.commons.lang3.StringUtils.isNotBlank(strategyId) && Objects.equals(dspStrategyId.getKey(), Integer.valueOf(3))) {
                    map.put("dspStrategyId", dspStrategyId.getRight());
                    logger.info("setDspRotateAbTest", "abTest dsoOrderId={},dspStrategyId={}", order.getUserOrderId(), dspStrategyId.getRight());
                    cmd.getDspOrderDetailDO().setDspStrategyStr(JacksonSerializer.INSTANCE().serialize(map));
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(strategyId) && Objects.equals(dspStrategyId.getKey(), Integer.valueOf(2))) {
                    //if(abt 分流方式)
                    Alternative abtVersion = ABTestClient.getAlternative(businessTemplateInfoConfig.getValueByKey(SysConstants.ABTEST_EXP_CODE), order.getUserOrderId(), null, null);
                    //if(abt 分流方式) 版本1 给实验组策略；版本2给对照组策略
                    if (abtVersion != null && Objects.equals(abtVersion.getVersion(), "B")) {
                        map.put("dspStrategyId", dspStrategyId.getRight());
                        logger.info("setDspRotateAbTest", "abt dsoOrderId={},dspStrategyId={}", order.getUserOrderId(), dspStrategyId.getRight());
                        cmd.getDspOrderDetailDO().setDspStrategyStr(JacksonSerializer.INSTANCE().serialize(map));
                    }

                }
            }
        } catch (Exception e) {
            logger.warn("setDspRotateAbTest_error", e);
        }

    }

    private UserOrderDetail assembleOtherFields(CreateDspOrderCommand command) {
        UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(command.getDspOrderDO().getUserOrderId());
        if (userOrderDetail == null || userOrderDetail.getBookInfo() == null) {
            throw ErrorCode.QUERY_USER_ORDER_EXCEPTION.getBizException();
        }
        UserBookInfo userBookInfo = userOrderDetail.getBookInfo();
        DspOrderDO order = command.getDspOrderDO();

        // 出发地
        FromPoiDTO fromPoi = new FromPoiDTO();
        // 经纬度判空
        if (userBookInfo.getActualFromLatitude() != null && userBookInfo.getActualFromLongitude() != null) {
            fromPoi.setActualFromCoordsys(userBookInfo.getActualFromCoordsys());
            fromPoi.setActualFromLatitude(BigDecimal.valueOf(userBookInfo.getActualFromLatitude()));
            fromPoi.setActualFromLongitude(BigDecimal.valueOf(userBookInfo.getActualFromLongitude()));
        }
        fromPoi.setDepPoiCode(userBookInfo.getDepPoiCode());
        fromPoi.setDepPoiSourceType(userBookInfo.getDepPoiSourceType());
        fromPoi.setFromName(userBookInfo.getFromName());
        fromPoi.setFromAddress(userBookInfo.getFromAddress());
        // poi项目新增。平台层传递
        fromPoi.setDeptCarPlaceId(order.getDeptCarPlaceId());
        order.setFromPoi(JacksonUtil.serialize(fromPoi));

        // 目的地
        ToPoiDTO toPoi = new ToPoiDTO();
        // 经纬度判空
        if (userBookInfo.getActualToLatitude() != null && userBookInfo.getActualToLongitude() != null) {
            toPoi.setActualToCoordsys(userBookInfo.getActualToCoordsys());
            toPoi.setActualToLatitude(BigDecimal.valueOf(userBookInfo.getActualToLatitude()));
            toPoi.setActualToLongitude(BigDecimal.valueOf(userBookInfo.getActualToLongitude()));
        }

        toPoi.setArrivePoiCode(userBookInfo.getArrivePoiCode());
        toPoi.setArrivePoiSourceType(userBookInfo.getArrivePoiSourceType());
        toPoi.setToName(userBookInfo.getToName());
        toPoi.setToAddress(userBookInfo.getToAddress());
        // poi项目新增。从平台层传递
        toPoi.setArriveCarPlaceId(order.getArriveCarPlaceId());
        order.setToPoi(JacksonUtil.serialize(toPoi));

        DspOrderDetailDO detail = command.getDspOrderDetailDO();
        UserCountDTO userCountDTO = new UserCountDTO();
        userCountDTO.setAdultCount(userBookInfo.getAuditQuantity() == null ? 1 : userBookInfo.getAuditQuantity());
        userCountDTO.setBagCount(userBookInfo.getBagQuantity() == null ? 0 : userBookInfo.getBagQuantity());
        userCountDTO.setChildCount(userBookInfo.getChildQuantity() == null ? 0 : userBookInfo.getChildQuantity());
        userCountDTO.setMaxBagCount(userBookInfo.getMaxBagQuantity());
        detail.setUserCount(JacksonUtil.serialize(userCountDTO));
        detail.setPackageKiloLength(userBookInfo.getyKiloLength());
        detail.setPackageTimeLength(userBookInfo.getxTimeLength());
        //  急单项目新增。取值定前快照com.ctrip.dcs.order.snapshot.contract.OrderBaseInfoDTO
        detail.setRushOrder(userBookInfo.getRushOrder() != null && userBookInfo.getRushOrder() ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        // 扩展字段Map
        Map<String, Object> extendInfoMap = buildExtendInfoMap(userBookInfo, detail);
        extendInfoMap.put(SysConstants.Order.USE_TIME_NO_DRIVER_CANCEL_BUFFER, command.getUseTimeNoDriverCancelBuffer());
        detail.setExtendInfo(JacksonSerializer.INSTANCE().serialize(extendInfoMap));
        detail.setLocale(userBookInfo.getLocaleCode());
        detail.setNewPayProcess(userBookInfo.getNewPaymentProcess() ? 1 : 0);
        detail.setExtendRule(userOrderDetail.getExtraFeeRule());
        detail.setCommissionRule(userOrderDetail.getCommissionRule());
        // 打高星用户标记
        try {
            if (userBookInfo.getDispatchGoldDriver() == null) {
                logger.info("create_dsp_order", "setHighGradeOrder is old flow,orderId=" + userBookInfo.getUserOrderId());
                command.getDspOrderDetailDO().setHighGradeOrder(highLevelCheckService.isHighLevelUserOrder(userOrderDetail.getBookInfo().getCityId(), userOrderDetail.getOrderMemberInfo().getVipGrade()) ? 1 : 0);
            } else {
                logger.info("create_dsp_order", "setHighGradeOrder is new flow,orderId=" + userBookInfo.getUserOrderId() + ",userOrdergetDispatchGoldDriver is" + userBookInfo.getDispatchGoldDriver());
                command.getDspOrderDetailDO().setHighGradeOrder(Boolean.TRUE.equals(userBookInfo.getDispatchGoldDriver()) ? 1 : 0);
            }
        } catch (Exception e) {
            logger.error("setHighGradeOrder_failed_" + userBookInfo.getUserOrderId(), e);
        }

        // reset bizAreaType
        order.setBizAreaType(resetOrderBizAreaType(userBookInfo.getBizAreaType(), order.getCityId() == null ? userBookInfo.getCityId() : order.getCityId(), userBookInfo.getUserOrderId()));
        order.setTimeZone(userBookInfo.getTimeZone());
        order.setProductName(userBookInfo.getProductName());
        order.setProductCode(userBookInfo.getProductCode());
        order.setTargetId(userBookInfo.getTargetId());
        order.setTerminalId(userBookInfo.getTerminalId());
        order.setDistributionChannelId(userBookInfo.getDistributionChannel());

        return userOrderDetail;
    }

    /**
     * 重置bizAreaType字段含义
     * 定义：中国大陆为境内，其他国家地区（包含港澳台）为境外
     *
     * @param bizAreaType 用户订单原始值
     * @param cityId      订单用车城市
     * @return 重置后的bizAreaType
     */
    private Integer resetOrderBizAreaType(Integer bizAreaType, Integer cityId, String userOrderId) {
        Integer newBizAreaType = bizAreaType;
        try {
            boolean isChineseMainland = cityGateway.isChineseMainland(cityId.longValue());
            if (isChineseMainland) {
                newBizAreaType = BizAreaTypeEnum.CHF.getCtripCode();
            } else {
                newBizAreaType = BizAreaTypeEnum.IGT.getCtripCode();
            }
        } catch (Exception e) {
            logger.warn("resetOrderBizAreaType_failed", "Failed to reset bizAreaType,demote to default value. userOrderId=" + userOrderId);
        }

        return newBizAreaType;

    }

    /**
     * 向extendinfoMap中新增扩展字段
     *
     * @param userBookInfo 用户订单信息
     * @param detail       前置构建的派发单详情
     * @return 扩展字段
     */
    private static Map<String, Object> buildExtendInfoMap(UserBookInfo userBookInfo, DspOrderDetailDO detail) {
        Map<String, Object> extendInfoMap = MapUtils.isNotEmpty(detail.getExtendInfoMap()) ? detail.getExtendInfoMap() : new HashMap<>();

        // 原始用户的用车时间
        extendInfoMap.put(SysConstants.Order.USE_TIME, userBookInfo.getUseTime());

        // 订单渠道:CQT
        if (userBookInfo.getOrderSysType() != null) {
            extendInfoMap.put(SysConstants.Order.ORDER_SYS_TYPE, userBookInfo.getOrderSysType());
        }

        // 跨境类型
        if (Objects.nonNull(detail.getCrossBorderType())) {
            extendInfoMap.put(SysConstants.Order.CROSS_BORDER_TYPE, detail.getCrossBorderType());
        }

        // 分流标识、分流供应商ID（两者都不为空，才落数据，单独落数据没有任何意义）
        if (detail.getShuntFlag() != null && detail.getShuntSupplierId() != null) {
            extendInfoMap.put(SysConstants.Order.SHUNT_FLAG, detail.getShuntFlag());
            extendInfoMap.put(SysConstants.Order.SHUNT_SUPPLIER_ID, detail.getShuntSupplierId());
        }

        // 是否延后派立即预确认标识
        if (detail.getIsPreConfirm() != null) {
            extendInfoMap.put(SysConstants.Order.PRE_CONFIRM_ORDER, detail.getIsPreConfirm());
        }

        // 新增用户订单备注
        extendInfoMap.put(SysConstants.Order.REMARK, userBookInfo.getUserRemark());
        // 用户自选buffer-分钟
        extendInfoMap.put(SysConstants.Order.SELF_BUFFER, detail.getSelfBuffer());

        logger.info("buildExtendInfoMap_" + userBookInfo.getUserOrderId(), "original extendInfoMap: [" + detail.getExtendInfo() + "], new extendInfoMap: [" + JacksonSerializer.INSTANCE().serialize(extendInfoMap) + "]");
        return extendInfoMap;
    }


}
