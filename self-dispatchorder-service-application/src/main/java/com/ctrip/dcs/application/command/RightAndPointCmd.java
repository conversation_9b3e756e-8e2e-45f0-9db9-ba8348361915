package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.service.reDispath.ReassignTaskManger;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.adapter.monitoring.SelfDspOrderMetric;
import com.ctrip.dcs.infrastructure.common.constants.DescribeConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 改派权益
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/5/18 16:54
 */
@Component
public class RightAndPointCmd {

    public static final Logger logger = LoggerFactory.getLogger(RightAndPointCmd.class);

    @Resource
    private SelfOrderQueryGateway selfOrderQueryGateway;

    public Result<RightAndPointVO> execute(RightAndPointCommand command) {
        // 定位角色
        ReassignTaskEnum.RoleEnum role = ReassignTaskEnum.RoleEnum.getInstance(command.getRoleId());

        // 无角色 - 阻断异常跳出
        if (role == null) {
            SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("missRole");
            logger.error("RightAndPointCmdError", "miss role:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), command.getRoleId());
            return Result.Builder.<RightAndPointVO>newResult().fail().withCode(ErrorCode.ROLE_MISS_ERROR.getCode()).withMsg(ErrorCode.ROLE_MISS_ERROR.getDesc()).build();
        }

        // 定位策略
        RightAndPointService service = ReassignTaskManger.getAgent(role.getDesc());

        // 无策略 - 阻断异常跳出
        if (service == null) {
            SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("missService");
            logger.error("RightAndPointCmdError", "miss Service:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), role.getDesc());
            return Result.Builder.<RightAndPointVO>newResult().fail().withCode(ErrorCode.UN_KNOWN.getCode()).withMsg(ErrorCode.UN_KNOWN.getDesc()).build();
        }

        // 定位订单
        BaseDetailVO detailVO = selfOrderQueryGateway.queryOrderBaseDetail(command.getUserOrderId(), null);

        // 订单数据缺失 - 阻断异常跳出
        if (detailVO == null) {
            SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("missOrder");
            logger.error("RightAndPointCmdError", "invalid userOrderId:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), command.getUserOrderId());
            return Result.Builder.<RightAndPointVO>newResult().fail().withCode(ErrorCode.NULL_ORDER_ERROR.getCode()).withMsg(ErrorCode.NULL_ORDER_ERROR.getDesc()).build();
        }

        try {

            // 查询权益
            Result<RightAndPointVO> res = service.queryRightAndPointDTO(command, detailVO);

            if (res == null || !res.isSuccess() || res.getData() == null) {
                SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("queryFail");
                logger.error("RightAndPointCmdError", "query Fail userOrderId:{} res:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), command.getUserOrderId(), JsonUtil.toJson(res));
                return res;
            }

            RightAndPointVO rightAndPointVO = res.getData();

            // 来自司机端
            if (role.isFromDriverApp()) {
                // 含权益 则展示使用权益按钮
                rightAndPointVO.setShowRightButton(res.getData().getDispatchRightInfo() != null ? Boolean.TRUE : Boolean.FALSE);
            } else {
                rightAndPointVO.setShowRightButton(Boolean.TRUE);
            }

            // 权益描述文案
            dealWithRightDetailDesc(rightAndPointVO, DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()));

            return res;

        } catch (BizException be) {
            SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("exception");
            logger.error("RightAndPointCmdError", "service:{} params:{} errorMsg:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), role.getDesc(), JsonUtil.toJson(command), be.getMessage());
            return Result.Builder.<RightAndPointVO>newResult().fail().
                    withCode(be.getCode()).withMsg(be.getMessage()).build();
        } catch (Exception e) {
            SelfDspOrderMetric.selfDspOrderRightAndPointFailRecordInc("exception");
            logger.error("RightAndPointCmdError", "service:{} params:{} errorMsg:{}", ImmutableMap.of("userOrderId", command.getUserOrderId()), role.getDesc(), JsonUtil.toJson(command), e.getMessage());
            return Result.Builder.<RightAndPointVO>newResult().fail().
                    withCode(ErrorCode.UN_KNOWN.getCode()).withMsg(ErrorCode.UN_KNOWN.getDesc()).build();
        }

    }

    /**
     * 权益描述文案调整
     * 这为毛不直接在权益信息里给我们吐出来呢？还得2次加工.....
     */
    private void dealWithRightDetailDesc(RightAndPointVO rightAndPointVO, Date sysExpectBookTimeBj) {
        // 没有权益，无需调整文案
        if (rightAndPointVO.getDispatchRightInfo() == null) {
            return;
        }
        // 改派原因：车辆故障 特殊文案
        if (rightAndPointVO.getReasonDetail() != null && ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode().equals(rightAndPointVO.getReasonDetail().getReasonId()) && !Strings.isNullOrEmpty(rightAndPointVO.getReasonDetail().getCarFaultTime())) {
            Date nowBj = new Date();
            String startTimeStr = DateUtil.formatDate(nowBj, DateUtil.CHINESE_DATE_FORMAT);
            String endTimeStr = DateUtil.formatDate(DateUtil.addHours(sysExpectBookTimeBj.before(nowBj) ? nowBj : sysExpectBookTimeBj, Integer.valueOf(rightAndPointVO.getReasonDetail().getCarFaultTime())), DateUtil.CHINESE_DATE_FORMAT);
            rightAndPointVO.getDispatchRightInfo().
                    setRightDetailDesc(String.format(DescribeConstants.CAR_FAULT_APPEAL, rightAndPointVO.getDispatchRightInfo().getRightDetailDesc(), startTimeStr, endTimeStr));
            return;
        }
        // 默认文案
        rightAndPointVO.getDispatchRightInfo().
                setRightDetailDesc(String.format(DescribeConstants.USE_RIGHTS_CANNOT_BE_REVOKED_OR_APPEALED_AFTER_USE, rightAndPointVO.getDispatchRightInfo().getRightDetailDesc()));
    }

}