package com.ctrip.dcs.application.command.dto;

/**
 * 境外订单取消DTO
 */
public class AbroadOrderCancelDTO {

    /**
     * 派发单ID
     */
    private String dspOrderId;

    /**
     * 司机单ID
     */
    private String driverOrderId;

    /**
     * 运力组
     */
    private Integer transportGroupId;

    /**
     * 司机ID
     */
    private Integer driverId;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    private String cancelReasonId;

    public String getDspOrderId() {
        return dspOrderId;
    }

    public void setDspOrderId(String dspOrderId) {
        this.dspOrderId = dspOrderId;
    }

    public String getDriverOrderId() {
        return driverOrderId;
    }

    public void setDriverOrderId(String driverOrderId) {
        this.driverOrderId = driverOrderId;
    }

    public Integer getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Integer transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public Integer getDriverId() {
        return driverId;
    }

    public void setDriverId(Integer driverId) {
        this.driverId = driverId;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getCancelReasonId() {
        return cancelReasonId;
    }

    public void setCancelReasonId(String cancelReasonId) {
        this.cancelReasonId = cancelReasonId;
    }
}
