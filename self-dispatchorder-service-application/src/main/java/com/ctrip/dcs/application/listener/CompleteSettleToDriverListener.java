package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.SendEmailService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.TripSendEmailVO;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderPushRuleRepository;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.handler.DispatcherGrabOrderPushRuleHandler;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Component
public class CompleteSettleToDriverListener {

    private static final Logger logger = LoggerFactory.getLogger(CompleteTaskListener.class);

    @Autowired
    private UpdateOrderSettlementBeforeTakenCmd cmd;

    @Autowired
    private GrabDspOrderPushRuleRepository grabDspOrderPushRuleRepository;

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @Autowired
    private SendEmailService sendEmailService;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    private DispatcherGrabOrderPushRuleHandler dispatcherGrabOrderPushRuleHandler;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.COMPLETE_SETTLE_TO_DRIVER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        int times = message.times();
        if (times <= 3) {
            OrderSettlePriceVO orderSettlePrice = null;
            UpdateOrderSettlementBeforeTakenCommand command = MessageConverter.INSTANCE.toUpdateOrderSettlementBeforeTakenCommand(message);
            try {
                orderSettlePrice = cmd.execute(command);
            } catch (Exception e) {
                logger.warn("CompleteSettleToDriverListenerError", e);
                throw new NeedRetryException(System.currentTimeMillis() + 500, "CompleteSettleToDriverListenerError");
            }
            try {
                // 创建VBK司机抢单调度
                createSchedule(command, orderSettlePrice);
            } catch (Exception e) {
                logger.warn("CompleteSettleToDriverListenerError", e);
            }
        }
    }

    public void createSchedule(UpdateOrderSettlementBeforeTakenCommand command, OrderSettlePriceVO orderSettlePrice) {
        if (command.getSupplierId() == null || command.getCityId() == null || command.getCategoryCode() == null || command.getCarTypeId() == null) {
            return;
        }
        // 匹配发单规则
        GrabDspOrderPushRuleDO rule = dispatcherGrabOrderPushRuleHandler.queryGrabOrderPushRule(command.getSupplierId(), command.getCityId().longValue(), command.getCategoryCode(), command.getCarTypeId().longValue());
        if (Objects.isNull(rule)) {
            logger.info("CompleteSettleToDriverListenerInfo", "rules is empty, supplierId: {}", command.getSupplierId());
            return;
        }
        if (YesOrNo.isNo(command.getSettleToDriver()) && (orderSettlePrice == null || StringUtils.isBlank(orderSettlePrice.getPreDriverGuideCurrency()) || Objects.isNull(orderSettlePrice.getPreDriverGuideAmount()))) {
            logger.info("CompleteSettleToDriverListenerInfo", "orderSettlePrice is empty, dspOrderId: {}", command.getDspOrderId());
            // 不跟司机结算的订单，无司机指导价，邮件告警
            MetricsUtil.recordValue(MetricsConstants.NO_PRE_DRIVER_GUIDE_AMOUNT_ERROR);
            sendEmail(command.getUserOrderId(), command.getSupplierId(), command.getCityId());
        }
        // 若发单规则不为空，则创建调度
        createScheduleExeCmd.execute(new CreateScheduleCommand(command.getDspOrderId(), ScheduleEventType.VBK_GRAB_SYSTEM));
    }

    public void sendEmail(String userOrderId, Long supplierId, Integer cityId) {
        try {
            String senderEmail = businessTemplateInfoConfig.getValueByKey(ConfigKey.COMMON_EMAIL_SENDER);
            String senderName = businessTemplateInfoConfig.getValueByKey(ConfigKey.COMMON_NAME_SENDER);
            String subject = businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_TITLE);
            String content = businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_CONTEND);
            String receiverEmail = businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_RECEIVER_EMAIL);
            List<TripSendEmailVO.Receiver> receivers = Splitter.on(",")
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(receiverEmail)
                    .stream()
                    .map(email -> TripSendEmailVO.Receiver.builder().email(email).build())
                    .toList();
            TripSendEmailVO sendEmail = TripSendEmailVO.builder()
                    .orderId(userOrderId)
                    .producer(TripSendEmailVO.Producer.builder()
                            .email(senderEmail)
                            .name(senderName)
                            .build())
                    .receiver(receivers)
                    .subject(subject)
                    .contentType(SysConstants.Email.contentType)
                    .channel(SysConstants.Email.channel)
                    .hasAttach(false)
                    .content(String.format(content, supplierId, cityId))
                    .build();
            sendEmailService.send(sendEmail);
        } catch (Exception e) {
            logger.warn("CompleteSettleToDriverListenerError", e);
        }
    }
}
