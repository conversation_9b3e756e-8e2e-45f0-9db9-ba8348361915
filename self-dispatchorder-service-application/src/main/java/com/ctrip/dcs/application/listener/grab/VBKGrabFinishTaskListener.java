package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskOperateEvent;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

import static com.ctrip.dcs.domain.common.constants.EventConstants.VBK_GRAB_FINISH_TASK_OPERATE_TOPIC;


@Component
public class VBKGrabFinishTaskListener extends MqListener{

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabFinishTaskListener.class);

    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    protected MessageProviderService messageProducer;



    @QmqConsumer(prefix = VBK_GRAB_FINISH_TASK_OPERATE_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message msg) {
        if(super.validConsumerTimes(msg, MetricsConstants.FINISH_GRAB_TASK_ERROR_COUNT) == 0){
            return;
        }
        try {
            String taskIdStr = msg.getStringProperty("taskIdStr");
            int grabStatus = msg.getIntProperty("grabStatus");
            logger.info("VBKGrabFinishTaskListener_onMessage", taskIdStr);
            if(StringUtils.isBlank(taskIdStr) || grabStatus == 0){
                return;
            }
            String[] taskIds = taskIdStr.split(",");
            vbkDriverGrabTaskRepository.finishTaskBatch(Arrays.asList(taskIds), grabStatus);

            for (int i = 0; i < taskIds.length; i++) {
                sendVBKGrabTaskOperateEvent(taskIds[i]);
            }
            // 埋点
//            MetricsUtil.recordValue(MetricsConstants.FINISH_GRAB_TASK_COUNT);
        } catch (Exception e) {
            logger.error("VBKGrabFinishTaskListener", e);
            // 埋点
//            MetricsUtil.recordValue(MetricsConstants.FINISH_GRAB_TASK_ERROR_COUNT);
            throw e;
        }
    }

    private void sendVBKGrabTaskOperateEvent(String vbkGrabTaskId) {
        try{
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = new VBKGrabTaskOperateDTO();
            vbkGrabTaskOperateDTO.setTaskId(vbkGrabTaskId);
            vbkGrabTaskOperateDTO.setOperatorName("system");
            vbkGrabTaskOperateDTO.setOperatorType(VBKGrabOperationTypeEnum.OperationTypeEnum.FINISH.getCode());
            vbkGrabTaskOperateDTO.setOperatorDesc(VBKGrabOperationTypeEnum.OperationTypeEnum.FINISH.getDes());
            vbkGrabTaskOperateDTO.setCurrentDateStr(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));
            String jsonStr = JsonUtils.toJson(vbkGrabTaskOperateDTO);
            logger.info("VBKGrabFinishTaskListener_sendVBKGrabTaskOperateEvent", jsonStr);
            VBKGrabTaskOperateEvent shutdownScheduleEvent = new VBKGrabTaskOperateEvent(jsonStr);
            messageProducer.send(shutdownScheduleEvent);
        }catch (Exception ex){
            logger.error("VBKGrabFinishTaskListener_sendVBKGrabTaskOperateEvent", ex);
        }
    }
}
