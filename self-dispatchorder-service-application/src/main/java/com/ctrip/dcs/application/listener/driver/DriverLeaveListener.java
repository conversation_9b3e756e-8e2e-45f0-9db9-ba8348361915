package com.ctrip.dcs.application.listener.driver;

import com.ctrip.dcs.application.event.IDriverLeaveEventHandler;
import com.ctrip.dcs.application.event.dto.DriverLeaveEvent;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;

/***
 * 司机请假消息 dcs.tms.transport.driver.leave.confirmed
 * 司机请假 tag_driverleave_add
 * 司机销假 tag_driverleave_close
 */
@Component
public class DriverLeaveListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverLeaveListener.class);
    @Autowired
    private IDriverLeaveEventHandler handler;

    @Resource
    private SysSwitchConfig sysSwitchConfig;

    @QmqLogTag(tagKeys = {"driverId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_LEAVE_TOPIC,
            tagType = TagType.OR,
            tags = {"tag_driverleave_add"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handle(Message message){
        logger.info("DriverLeaveListener_msg",LocalJsonUtils.toJson(message));
        // C 流程开关 默认关闭
        if (!sysSwitchConfig.getLeaveContinueSwitch()) {
            logger.info("LeaveContinueSwitch_false",LocalJsonUtils.toJson(message));
            return;
        }
        //司机销假不处理
        if(!message.getTags().contains("tag_driverleave_add")){
            logger.info("driver_leave_close",LocalJsonUtils.toJson(message));
            return;
        }
        //校验司机id参数
        Long driverId = getDriverId(message);
        if(driverId == null){
            logger.error("driverId_null", LocalJsonUtils.toJson(message));
            return;
        }
        //校验请假时间参数
        String startTime = message.getStringProperty("startTime");
        String endTime = message.getStringProperty("endTime");
        if(LocalStringUtils.isEmpty(startTime) || LocalStringUtils.isEmpty(endTime)){
            logger.error("startTime_endTime_null", LocalJsonUtils.toJson(message));
            return;
        }
        // 1.供应商,2.BD
        String accountType = message.getStringProperty("accountType");
        //处理事件
        handler.handle(new DriverLeaveEvent(driverId,startTime,endTime,accountType), null);
    }

    /**
     * 获取司机id
     * @param message
     * @return
     */
    private Long getDriverId(Message message){
        try{
            String driverId = message.getStringProperty("driverId");
            if(LocalStringUtils.isEmpty(driverId)){
                return null;
            }
            Long driverIdLong = Long.valueOf(driverId);
            if(driverIdLong < 1){
                return null;
            }
            return driverIdLong;
        }catch (Exception e){
            return null;
        }
    }
}
