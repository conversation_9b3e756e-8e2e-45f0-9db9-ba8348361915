package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.UpdateOrderSettleToDriverBeforeTakenCommand;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryOrderSettlePriceService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.event.CompleteSettleToDriverEvent;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 更新订单明细结算信息
 * <AUTHOR>
 */
@Component
public class UpdateOrderSettleToDriverBeforeTakenCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateOrderSettleToDriverBeforeTakenCmd.class);

    @Autowired
    private QueryOrderSettlePriceService queryOrderSettlePriceService;

    @Autowired
    private UpdateOrderSettlementBeforeTakenCmd cmd;

    @Autowired
    protected MessageProviderService messageProducer;

    public void execute(UpdateOrderSettleToDriverBeforeTakenCommand command) {
        try {
            if (!OrderSourceCodeEnum.isTrip(command.getOrderSourceCode())) {
                // 非携程订单，无结算信息
                return;
            }
            CategoryCodeEnum category = CategoryCodeEnum.getByType(command.getCategoryCode()).orElseThrow(() -> new IllegalArgumentException("categoryCode is invalid"));
            // 查询结算信息
            Boolean isPayForDriver = queryOrderSettlePriceService.isPayForDriver(command.getSupplierId(), Lists.newArrayList(category), command.getSalesMode(),command.getServiceProviderId());
            OrderSettlePriceVO orderSettlePrice = OrderSettlePriceVO.builder()
                    .dspOrderId(command.getDspOrderId())
                    .settleToDriver(isPayForDriver ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode())
                    .build();
            if (Objects.equals(OrderVersionEnum.CTRIP.getVersion(), command.getOrderVersion())) {
                // 迁移后订单
                cmd.updateOrderDetailExtendInfo(orderSettlePrice);
            }
            // 发送消息
            messageProducer.send(new CompleteSettleToDriverEvent(
                    command.getUserOrderId(),
                    command.getDspOrderId(),
                    command.getCarTypeId(),
                    command.getCategoryCode(),
                    command.getSupplierId(),
                    command.getServiceProviderId(),
                    command.getOrderSourceCode(),
                    command.getOrderVersion(),
                    isPayForDriver ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode(),
                    command.getCityId(),
                    1000L)
            );
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.UPDATE_ORDER_DETAIL_SETTLEMENT_ERROR);
            logger.error("UpdateOrderDetailSettlementInfoError", e);
            throw ErrorCode.UPDATE_ORDER_DETAIL_SETTLEMENT_ERROR.getBizException();
        }
    }

}
