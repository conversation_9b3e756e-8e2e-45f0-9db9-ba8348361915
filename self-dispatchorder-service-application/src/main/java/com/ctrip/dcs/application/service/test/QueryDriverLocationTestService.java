package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.service.QueryDriverLocationService;
import com.ctrip.dcs.domain.common.value.BaseLocationVO;
import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2024/9/4 15:09
 */
@Component("QueryDriverLocationTestService")
public class QueryDriverLocationTestService implements ITestDspOrderService {

    @Autowired
    private QueryDriverLocationService queryDriverLocationService;

    @Override
    public String test(Map<String, String> params) {

        List<DriverOrderLocationVO> driverOrderLocationVOS = Lists.newArrayList();

        String drvStrArr = params.get("drvIdArr");
        List<Long> driverIdList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(drvStrArr).stream().map(r -> Long.valueOf(r)).collect(Collectors.toList());

        for (Long drvId : driverIdList) {
            driverOrderLocationVOS.add(new DriverOrderLocationVO(drvId + "", drvId, new BaseLocationVO(BigDecimal.valueOf(124.323), BigDecimal.valueOf(74.323), "GCJ02")));
        }

        queryDriverLocationService.queryDriverToOrderDis(driverOrderLocationVOS);

        return LocalJsonUtils.toJson("success");
    }
}