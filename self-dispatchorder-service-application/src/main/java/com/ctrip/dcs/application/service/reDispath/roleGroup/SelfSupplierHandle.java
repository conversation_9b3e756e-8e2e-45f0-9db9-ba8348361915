package com.ctrip.dcs.application.service.reDispath.roleGroup;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.service.RedispatchRightAndPointService;
import com.ctrip.dcs.application.service.reDispath.LeaveRightsBaseHandle;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/5/30 10:55
 */
@Component(value = ReassignTaskEnum.SELF_SUPPLIER)
public class SelfSupplierHandle implements RightAndPointService {

    @Resource
    private RedispatchRightAndPointService redispatchRightAndPointService;

    @Override
    public Result<RightAndPointVO> queryRightAndPointDTO(RightAndPointCommand command, BaseDetailVO detailVO) {
        Result<RightAndPointVO> pointResult = redispatchRightAndPointService.checkPunishReason(command, detailVO);
        if (pointResult != null && pointResult.isSuccess() && pointResult.getData() != null) {
            pointResult.getData().setReasonDetail(null);
            if (Boolean.FALSE.equals(pointResult.getData().getResponsable())) {
                pointResult.getData().setReasonDetail(null);
                pointResult.getData().setPunishInfo(null);
                pointResult.getData().setPunishRuleInfo(null);
                pointResult.getData().setDriverPunishInfo(null);
            }
        }
        /**
         * 请假权益 抽离为公共方法处理
         */
        if (ReassignTaskEnum.ReassignmentTypeEnum.DRIVER_LEAVE.equals(command.getReassignmentType())) {
            return LeaveRightsBaseHandle.dealWithDriverLeaveRights(command, detailVO, pointResult);
        }
        return pointResult;
    }

    @Override
    public Result convertAndCheckPrm(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        return Result.Builder.newResult().success().build();

    }

}