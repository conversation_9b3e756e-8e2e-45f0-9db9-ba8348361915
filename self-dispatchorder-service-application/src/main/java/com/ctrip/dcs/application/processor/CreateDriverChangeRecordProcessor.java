package com.ctrip.dcs.application.processor;

import com.ctrip.dcs.domain.dsporder.repository.DspOrderChangeDriverRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderChangeDriverRecordVO;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> liu
 */
@Component
public class CreateDriverChangeRecordProcessor {
    private static final Logger logger = LoggerFactory.getLogger(CreateDriverChangeRecordProcessor.class);

    @Autowired
    DspOrderChangeDriverRecordRepository dspOrderChangeDriverRecordRepository;

    @Autowired
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    public void process(long originalConfirmRecordId, long newConfirmRecordId) {
        if( newConfirmRecordId == 0L ) {
            logger.error("newConfirmRecordId_null", "newConfirmRecordId from qmq is null, originalConfirmRecordId=" + originalConfirmRecordId);
            return;
        }
        DspOrderChangeDriverRecordVO dspOrderChangeDriverRecord = fillChangeDriverRecord(originalConfirmRecordId, newConfirmRecordId);
        // 如果查询到原应单记录 则落表
        if( Objects.nonNull(dspOrderChangeDriverRecord) ) {
            dspOrderChangeDriverRecordRepository.insertRecord(dspOrderChangeDriverRecord);
        }
        // 如果没有查询到原应单记录或者查询失败 则不落表 记录异常日志
        else {
            logger.error("create_driver_change_record_error", "dspOrderChangeDriverRecord is null");
        }
    }

    private DspOrderChangeDriverRecordVO fillChangeDriverRecord(long originalConfirmRecordId, long newConfirmRecordId) {
        DspOrderChangeDriverRecordVO dspOrderChangeDriverRecord = new DspOrderChangeDriverRecordVO();
        try{
            // 查询原应单记录
            DspOrderConfirmRecordVO oldDspOrderConfirmRecord = dspOrderConfirmRecordRepository.find(originalConfirmRecordId);
            if( Objects.isNull(oldDspOrderConfirmRecord) ) {
                logger.error("dspOrderConfirmRecord_not_found", "original confirm record not found, confirmRecordId={}", originalConfirmRecordId);
                return null;
            }
            // 查询新应单记录
            DspOrderConfirmRecordVO newDspOrderConfirmRecord = dspOrderConfirmRecordRepository.find(newConfirmRecordId);
            if( Objects.isNull(newDspOrderConfirmRecord) ) {
                logger.error("dspOrderConfirmRecord_not_found", "new confirm record not found, confirmRecordId={}", newConfirmRecordId);
                return null;
            }
            // 根据应单记录的司机信息填充原司机id
            dspOrderChangeDriverRecord.setDspOrderId(oldDspOrderConfirmRecord.getDspOrderId());
            dspOrderChangeDriverRecord.setUserOrderId(oldDspOrderConfirmRecord.getUserOrderId());
            dspOrderChangeDriverRecord.setSupplierId(oldDspOrderConfirmRecord.getSupplierInfo().getSupplierId().intValue());
            dspOrderChangeDriverRecord.setFromDriverId(oldDspOrderConfirmRecord.getDriverInfo().getDriverId().toString());
            dspOrderChangeDriverRecord.setToDriverId(newDspOrderConfirmRecord.getDriverInfo().getDriverId().toString());
            dspOrderChangeDriverRecord.setOperator(newDspOrderConfirmRecord.getOperateRecord().getOperateUserAccount());
            dspOrderChangeDriverRecord.setOperateTime(new LocalDate());
        } catch (Exception e) {
            logger.error("dspOrderConfirmRecord_query_error", "query confirm record error, " +
                    "originalConfirmRecordId={}, newConfirmRecordId={}", originalConfirmRecordId, newConfirmRecordId, e);
            return null;
        }
        return dspOrderChangeDriverRecord;
    }
}
