package com.ctrip.dcs.application.command;

import cn.hutool.core.map.MapUtil;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskSettlement;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.schedule.event.VBKGrabDspTaskEvent;
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;


public abstract class AbstractVBKGrabTaskExeCmd{

    private static final Logger logger = LoggerFactory.getLogger(AbstractVBKGrabTaskExeCmd.class);


    @Autowired
    protected DateZoneConvertUtil dateZoneConvertUtil;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;
    @Autowired
    protected MessageProviderService messageProducer;


    protected int getDbBatchSize(){
        try{
            Integer dbBatchSize = commonConfConfig.getInteger("dbBatchSize");
            logger.info("AbstractVBKGrabTaskExeCmd_getDbBatchSize", JsonUtils.toJson(dbBatchSize));
            return dbBatchSize == null ? 500 : dbBatchSize;
        }catch (Exception ex){
            logger.error("AbstractVBKGrabTaskExeCmd_getVBKGrabDspTaskDelay", ex);
        }
        return 500;
    }

    protected VBKDriverGrabDriverDO buildVbkDriverGrabDriverDO(VBKGrabTaskDTO requestType, String driverId) {
        VBKDriverGrabDriverDO vbkDriverGrabDriverDO = new VBKDriverGrabDriverDO();
        vbkDriverGrabDriverDO.setVbkGrabTaskId(requestType.getTaskId());
        vbkDriverGrabDriverDO.setSupplierId(requestType.getSupplierId());
        vbkDriverGrabDriverDO.setDriverId(driverId);
        vbkDriverGrabDriverDO.setGrabStatus(GrabDriverStatus.IN_PROGRESS.getCode());
        return vbkDriverGrabDriverDO;
    }

    protected VBKDriverGrabOrderDO buildVbkDriverGrabOrderDO(VBKGrabTaskDTO requestType, Map<String, DspOrderVO> dspOrderVOMap, String dspOrderId) {
        try{
            logger.info("AbstractVBKGrabTaskExeCmd_buildVbkDriverGrabOrderDO_dspOrderId", dspOrderId);
            VBKDriverGrabOrderDO vbkDriverGrabOrderDO = new VBKDriverGrabOrderDO();
            vbkDriverGrabOrderDO.setVbkGrabTaskId(requestType.getTaskId());
            vbkDriverGrabOrderDO.setSupplierId(requestType.getSupplierId());
            vbkDriverGrabOrderDO.setDspOrderId(dspOrderId);
            vbkDriverGrabOrderDO.setGrabStatus(GrabTaskStatus.IN_PROGRESS.getCode());
            DspOrderVO dspOrderVO = dspOrderVOMap.get(dspOrderId);
            if (Objects.isNull(dspOrderVO)) {
                return null;
            }
            if (!Objects.equals(requestType.getSupplierId().intValue(), dspOrderVO.getSupplierId())) {
                throw ErrorCode.SERVER_ERROR.getBizException();
            }
            vbkDriverGrabOrderDO.setUserOrderId(dspOrderVO.getUserOrderId());
            vbkDriverGrabOrderDO.setCityId(dspOrderVO.getCityId());
            vbkDriverGrabOrderDO.setOrderStatus(dspOrderVO.getOrderStatus());
            vbkDriverGrabOrderDO.setCategoryCode(dspOrderVO.getCategoryCode().getType());
            vbkDriverGrabOrderDO.setOrderSourceCode(dspOrderVO.getOrderSourceCode());
            String extendInfo = dspOrderVO.getExtendInfo();
            if(StringUtils.isBlank(extendInfo)){
                throw ErrorCode.SETTLEMENTS_WAY_NOT_CORRECT.getBizException();
            }
            HashMap extendHashMap = JsonUtil.fromJson(extendInfo, HashMap.class);
            if(Objects.isNull(extendHashMap)){
                extendHashMap = new HashMap();
            }
            Integer settleToDriver = MapUtil.getInt(extendHashMap, "settleToDriver");
            if(Objects.isNull(settleToDriver)){
                throw ErrorCode.SETTLEMENTS_WAY_NOT_CORRECT.getBizException();

            }
            Integer payForDriver = requestType.getPayForDriver();
            if(!Objects.equals(payForDriver, settleToDriver)){
                throw ErrorCode.SETTLEMENTS_WAY_NOT_CORRECT.getBizException();
            }
            //抢单初始金额和币种
            buildInitialSettlement(requestType, vbkDriverGrabOrderDO, dspOrderVO);
            //抢单奖励金额和币种
            buildRewardsSettlement(requestType, vbkDriverGrabOrderDO, dspOrderVO);

            Date lastConfirmCarTime = dspOrderVO.getLastConfirmCarTime();
            Date lastConfirmCarTimeBj = dspOrderVO.getLastConfirmCarTimeBj();
            Date estimatedUseTime = dspOrderVO.getEstimatedUseTime();
            //抢单截止时间计算
            buildGrabLimitTime(requestType, vbkDriverGrabOrderDO, lastConfirmCarTime, lastConfirmCarTimeBj, estimatedUseTime);
            //加价截止时间计算
            buildGrabRewardTime(requestType, vbkDriverGrabOrderDO, lastConfirmCarTime, lastConfirmCarTimeBj, estimatedUseTime);
            logger.info("QueryDspOrderServiceImpl_buildVbkDriverGrabOrderDO_vbkDriverGrabOrderDO", LocalJsonUtils.toJson(vbkDriverGrabOrderDO));
            return vbkDriverGrabOrderDO;
        }catch (BizException ex){
            logger.error("QueryDspOrderServiceImpl_buildVbkDriverGrabOrderDO", ex);
            throw ex;
        }
    }


    protected void buildGrabRewardTime(VBKGrabTaskDTO requestType, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, Date lastConfirmCarTime, Date lastConfirmCarTimeBj, Date estimatedUseTime) {
        String makeUpEffectTime = requestType.getMakeUpEffectTime();
        //非携程单有可能都是空的
        if (Objects.nonNull(makeUpEffectTime)) {
            BigDecimal makeUpEffectTimeDecimal = new BigDecimal(makeUpEffectTime);
            int makeUpEffectTimeMinutes = makeUpEffectTimeDecimal.multiply(new BigDecimal("60")).setScale(0, RoundingMode.HALF_UP).intValue() * -1;
            if (Objects.nonNull(lastConfirmCarTime)) {
                Date date = DateUtil.addMinutes(lastConfirmCarTime, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeLocal(new Timestamp(date.getTime()));
            }else {
                Date date = DateUtil.addMinutes(estimatedUseTime, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeLocal(new Timestamp(date.getTime()));
            }
            if (Objects.nonNull(lastConfirmCarTimeBj)) {
                Date date = DateUtil.addMinutes(lastConfirmCarTimeBj, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeBJ(new Timestamp(date.getTime()));
            }
        }
    }


    protected void buildGrabLimitTime(VBKGrabTaskDTO requestType, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, Date lastConfirmCarTime, Date lastConfirmCarTimeBj, Date estimatedUseTime) {
        //非携程单有可能都是空的
        BigDecimal grabEndTimeDecimal = new BigDecimal(requestType.getGrabEndTime());
        int grabEndTimeMinutes = grabEndTimeDecimal.multiply(new BigDecimal("60")).setScale(0, RoundingMode.HALF_UP).intValue() * -1;
        if (Objects.nonNull(lastConfirmCarTime)) {
            Date date = DateUtil.addMinutes(lastConfirmCarTime, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeLocal(new Timestamp(date.getTime()));
        }else {
            Date date = DateUtil.addMinutes(estimatedUseTime, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeLocal(new Timestamp(date.getTime()));
        }
        if (Objects.nonNull(lastConfirmCarTimeBj)) {
            Date date = DateUtil.addMinutes(lastConfirmCarTimeBj, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeBJ(new Timestamp(date.getTime()));
        }
    }

    public void buildRewardsSettlement(VBKGrabTaskDTO requestType, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, DspOrderVO dspOrderVO) {
        VBKGrabTaskSettlement rewards = requestType.getRewards();
        Integer type = rewards.getType();
        //固定金额
        if (GrabSettlementEnum.VALUE.getType().equals(type)) {
            //如果是司机结算并且固定金额加价，X<=司机结算价*2，由于该校验需要查询每个订单的金额，所以下沉的41593，而不是在26121校验
            if(requestType.getPayForDriver().equals(SettleToDriverEnum.SETTLE_TO_DRIVER.getCode())){
                BigDecimal preDriverSettleAmount = dspOrderVO.getPreDriverSettleAmount();
                if (Objects.nonNull(preDriverSettleAmount)) {
                    BigDecimal multiply = preDriverSettleAmount.multiply(new BigDecimal("2"));
                    int num = rewards.getValue().compareTo(multiply);
                    if(num > 0){
                        throw ErrorCode.DRIVER_SETTLEMENT_TWO_MULTIPLE.getBizException();
                    }
                }else{
                    throw ErrorCode.SERVER_ERROR.getBizException();
                }
            }
            vbkDriverGrabOrderDO.setRewardsAmount(rewards.getValue());
            vbkDriverGrabOrderDO.setRewardsCurrency(vbkDriverGrabOrderDO.getInitialCurrency());
        } else if (GrabSettlementEnum.RATE.getType().equals(type)) {
            BigDecimal amount;
            if(requestType.getPayForDriver().equals(SettleToDriverEnum.SETTLE_TO_DRIVER.getCode())){
                amount = dspOrderVO.getPreDriverSettleAmount();
            }else{
                amount = dspOrderVO.getPreSupplierSettleAmount();
            }
            if(Objects.nonNull(amount)){
                BigDecimal rate = new BigDecimal("0.01").multiply(rewards.getRate());
                BigDecimal multiply = amount.multiply(rate);
                vbkDriverGrabOrderDO.setRewardsAmount(multiply.setScale(2, RoundingMode.HALF_UP));
                vbkDriverGrabOrderDO.setRewardsCurrency(vbkDriverGrabOrderDO.getInitialCurrency());
            }else{
                throw ErrorCode.SERVER_ERROR.getBizException();
            }
        }
    }

    public void buildInitialSettlement(VBKGrabTaskDTO requestType, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, DspOrderVO dspOrderVO) {
        //给司机结算
        Integer payForDriver = requestType.getPayForDriver();
        if (SettleToDriverEnum.SETTLE_TO_DRIVER.getCode().equals(payForDriver)) {
            vbkDriverGrabOrderDO.setInitialAmount(dspOrderVO.getPreDriverSettleAmount());
            vbkDriverGrabOrderDO.setInitialCurrency(dspOrderVO.getPreDriverSettleCurrency());
        } else {
            VBKGrabTaskSettlement initial = requestType.getInitial();
            Integer type = initial.getType();
            //固定金额
            if (GrabSettlementEnum.VALUE.getType().equals(type)) {
                vbkDriverGrabOrderDO.setInitialAmount(initial.getValue());
                vbkDriverGrabOrderDO.setInitialCurrency(initial.getCurrency());
            } else if (GrabSettlementEnum.RATE.getType().equals(type)) {
                BigDecimal preSupplierSettleAmount = dspOrderVO.getPreSupplierSettleAmount();
                BigDecimal rate = new BigDecimal("0.01").multiply(initial.getRate());
                if (Objects.nonNull(preSupplierSettleAmount)) {
                    BigDecimal multiply = preSupplierSettleAmount.multiply(rate);
                    vbkDriverGrabOrderDO.setInitialAmount(multiply.setScale(2, RoundingMode.HALF_UP));
                    vbkDriverGrabOrderDO.setInitialCurrency(dspOrderVO.getPreSupplierSettleCurrency());
                }else{
                    throw ErrorCode.SERVER_ERROR.getBizException();
                }
            }
        }
    }

    protected VBKDriverGrabTaskDO buildVBKDriverGrabTaskDO(VBKGrabTaskDTO requestType){
        VBKDriverGrabTaskDO vbkDriverGrabTaskDO = new VBKDriverGrabTaskDO();
        vbkDriverGrabTaskDO.setVbkGrabTaskId(requestType.getTaskId());
        vbkDriverGrabTaskDO.setSupplierId(requestType.getSupplierId());
        vbkDriverGrabTaskDO.setCityId(requestType.getCityId());
        vbkDriverGrabTaskDO.setTaskStatus(GrabTaskStatus.IN_PROGRESS.getCode());
        vbkDriverGrabTaskDO.setCategoryCode(StringUtils.join(requestType.getCategoryCodeList(), ","));
        vbkDriverGrabTaskDO.setOrderSourceCode(requestType.getOrderSourceCode());
        VBKGrabTaskSettlement initial = requestType.getInitial();
        vbkDriverGrabTaskDO.setInitialType(initial.getType());
        vbkDriverGrabTaskDO.setInitialValue(initial.getValue());
        vbkDriverGrabTaskDO.setInitialRate(initial.getRate());
        vbkDriverGrabTaskDO.setInitialCurrency(initial.getCurrency());
        VBKGrabTaskSettlement rewards = requestType.getRewards();
        vbkDriverGrabTaskDO.setRewardsType(rewards.getType());
        vbkDriverGrabTaskDO.setRewardsValue(rewards.getValue());
        vbkDriverGrabTaskDO.setRewardsRate(rewards.getRate());
        vbkDriverGrabTaskDO.setRewardsCurrency(initial.getCurrency());
        vbkDriverGrabTaskDO.setGrabLimitHours(new BigDecimal(requestType.getGrabEndTime()));
        if(Objects.nonNull(requestType.getMakeUpEffectTime())){
            vbkDriverGrabTaskDO.setGrabRewardHours(new BigDecimal(requestType.getMakeUpEffectTime()));
        }
        return vbkDriverGrabTaskDO;
    }

    public int getVBKGrabDspTaskDelay(){
        try{
            Integer vbkGrabDspTaskDelay = commonConfConfig.getInteger("vbkGrabDspTaskDelay");
            logger.info("AbstractVBKGrabTaskExeCmd_getVBKGrabDspTaskDelay", JsonUtils.toJson(vbkGrabDspTaskDelay));
            return vbkGrabDspTaskDelay == null ? 1000 : vbkGrabDspTaskDelay;
        }catch (Exception ex){
            logger.error("AbstractVBKGrabTaskExeCmd_getVBKGrabDspTaskDelay", ex);
        }
        return 1000;
    }

    public void sendVBKGrabDspTask(List<VBKDriverGrabOrderDO> vbkDriverGrabOrderDOList) {
        logger.info("CreateVBKGrabTaskExeCmd_sendVBKGrabDspTask", JsonUtils.toJson(vbkDriverGrabOrderDOList));
        if(CollectionUtils.isEmpty(vbkDriverGrabOrderDOList)){
            return;
        }
        int vbkGrabDspTaskDelay = getVBKGrabDspTaskDelay();
        long delay = 3L;
        for (VBKDriverGrabOrderDO vbkDriverGrabOrderDO : vbkDriverGrabOrderDOList) {
            sendEvent(vbkDriverGrabOrderDO, delay);
            delay += vbkGrabDspTaskDelay;
        }
    }
    public void sendEvent(VBKDriverGrabOrderDO vbkDriverGrabOrderDO, long delay) {
        try{
            CategoryCodeEnum categoryCodeEnum = CategoryCodeEnum.getByType(vbkDriverGrabOrderDO.getCategoryCode()).orElse(null);
            if(categoryCodeEnum == null){
                return;
            }
            VBKGrabDspTaskEvent vbkGrabDspTaskEvent = new VBKGrabDspTaskEvent(delay, vbkDriverGrabOrderDO.getVbkGrabTaskId(), vbkDriverGrabOrderDO.getSupplierId(),
                    vbkDriverGrabOrderDO.getDspOrderId(), vbkDriverGrabOrderDO.getCityId(), categoryCodeEnum.parentType);
            logger.info("CreateVBKGrabTaskExeCmd_sendVBKGrabDspTask_event", LocalJsonUtils.toJson(vbkGrabDspTaskEvent));
            messageProducer.send(vbkGrabDspTaskEvent);
        }catch (Exception ex){
            logger.error("CreateVBKGrabTaskExeCmd_sendVBKGrabDspTask_event", ex);
        }
    }
}
