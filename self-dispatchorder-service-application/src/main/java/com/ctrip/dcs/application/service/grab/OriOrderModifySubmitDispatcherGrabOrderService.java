package com.ctrip.dcs.application.service.grab;

import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.constants.VbkOperateRecordConstant;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 原单修改需商家待确认，同意处理服务
 */
@Component("oriOrderModifySubmitDispatcherGrabOrderService")
public class OriOrderModifySubmitDispatcherGrabOrderService implements SubmitDispatcherGrabOrderService {
    private static final Logger logger = LoggerFactory.getLogger(OriOrderModifySubmitDispatcherGrabOrderService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;
    @Autowired
    private DispatcherGrabOrderDao dispatcherGrabOrderDao;
    @Autowired
    private MessageProviderService messageProviderService;
    @Autowired
    private QueryTransportGroupService queryTransportGroupService;
    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;
    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;
    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;
    @Autowired
    private DateZoneConvertGateway dateZoneConvertGateway;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;
    @Autowired
    private PurchaseSupplyOrderGateway purchaseSupplyOrderGateway;
    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Override
    public void submit(SubmitDispatcherGrabOrderCommand command) {
        String logTitile = "oriOrderModifySubmit_" + command.getDspOrderId();
        logger.info(logTitile, "submit start");
        if (StringUtils.isAnyBlank(command.getLastConfirmTime(), command.getLastConfirmTimeBj(), command.getEstimatedUseTime())) {
            throw new IllegalArgumentException("time param is empty");
        }
        // 查询待抢单信息
        DispatcherGrabOrderDO order = dispatcherGrabOrderGateway.query(command.getPkId(), command.getSupplierId());
        if (Objects.isNull(order)) {
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        if (order.isGrab()) {
            logger.warn(logTitile, "dispatcher order isGrab!");
            // 已抢，直接返回
            return;
        }
        if (!order.isInit()) {
            logger.warn(logTitile, "dispatcher order status is not Init!");
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        // 查询派发单
        DspOrderVO dspOrderVO = queryDspOrderService.query(order.getDspOrderId());
        if (Objects.isNull(dspOrderVO)) {
            logger.warn(logTitile, "query dspOrder is null");
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        if (dspOrderVO.isCancel()) {
            throw new BizException("502", "order has canceled");
        }
        // 查询运力组
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(order.getTransportGroupId());
        if (Objects.isNull(transportGroup) || Objects.isNull(transportGroup.getTransportGroupMode()) || !transportGroup.getTransportGroupMode().isManual()) {
            throw new BizException("401", "transport group is not manual");
        }
        try {
            Integer confirmStatus = DispatcherGrabOrderStatusEnum.APPROVE.getCode();
            // 校验最晚同意时间 是否 小于最新的预估用车时间（接机场景，容易倒挂，直接发起系统撤回）
            Date estimatedUseTime = DateUtil.parseDateStr2Date(command.getEstimatedUseTime());
            if (estimatedUseTime.compareTo(order.getLastConfirmTime()) < 0) {
                confirmStatus = DispatcherGrabOrderStatusEnum.CANCEL.getCode();
                logger.warn(logTitile, "check estimatedUseTime is before lastAgreeTime, start system revoke");
            }
            // 回调平台层商家操作结果
            boolean result = purchaseSupplyOrderGateway.confirmModifiedJntOrder(order.getUserOrderId(), order.getModifyVersion(), confirmStatus, command.getLastConfirmTime(), command.getLastConfirmTimeBj());
            logger.info(logTitile, "confirmModifiedJntOrder result=" + result);
            if (!result) {
                throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
            }
            if (confirmStatus.equals(DispatcherGrabOrderStatusEnum.CANCEL.getCode())) {
                // 系统撤回
                CancelDispatcherGrabOrderCommand cancelDispatcherGrabOrderCommand = new CancelDispatcherGrabOrderCommand(order.getUserOrderId(), 2, order.getModifyVersion(), 9, "SYSTEM_REVOKE_FOR_LAST_CONFIRM_TIME_OVER_USE_TIME");
                cancelDispatcherGrabOrderExeCmd.cancelForOriModify(cancelDispatcherGrabOrderCommand);
                // 抛出错误提示
                if (confirmStatus.equals(DispatcherGrabOrderStatusEnum.CANCEL.getCode())) {
                    throw ErrorCode.SUBMIT_GRAB_ORDER_CHANGE_CANCEL.getBizException();
                }
            }
            // 同意成功状态更新
            dispatcherGrabOrderDao.submit(order.getId(), command.getSource(), confirmStatus);
            // 原单修改修改订单且需要商家同意-客服工作台通知
            WorkBenchLogMessage oriOrderModifyMessage = workBenchLogMessageFactory.createOriModifySupplierAgreeConfirmWorkBenchLog(order);
            workBenchLogGateway.sendWorkBenchLogMessage(oriOrderModifyMessage);
            // 原单修改修改订单且需要商家同意-vbk工作台日志
            insertVbkWorkLogOriOrderModify(dspOrderVO, order);
            logger.info(logTitile, "confirmModifiedJntOrder success");
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("OriginalOrderModifySubmitDispatcherGrabOrderServiceError", e);
            throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
    }

    private void insertVbkWorkLogOriOrderModify(DspOrderVO dspOrderVO, DispatcherGrabOrderDO order) {
        Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderVO.getCityId());
        // 构建VO
        VBKOperationRecordVO recordVO = VBKOperationRecordVO.builder()
                .supplyOrderId(order.getDspOrderId())
                .userOrderId(order.getUserOrderId())
                .vendorOrderId("")
                .sysUserAccount("system")
                .operUserName("user")
                .operUserType("user")
                .operateType(VbkOperateRecordConstant.ORI_MODIFY_SUPPLIER_AGREE)
                .operateName(businessTemplateInfoConfig.getVbkOperationName(VbkOperateRecordConstant.ORI_MODIFY_SUPPLIER_AGREE))
                .operateLocalTime(localTimeNow.getTime())
                .recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW)
                .comment(commonConfConfig.getString("ori_modify_supplier_agree_vbk"))
                .supplierId(order.getSupplierId().intValue())
                .orderSourceCode(String.valueOf(dspOrderVO.getOrderSourceCode()))
                .timeZone(dspOrderVO.getTimeZone() == null ? 0D : dspOrderVO.getTimeZone().doubleValue())
                .beforeOperateData(String.valueOf(dspOrderVO.getOrderStatus()))
                .afterOperateData(String.valueOf(OrderStatusEnum.ORDER_CANCEL.getCode()))
                .build();
        vbkOperationRecordGateway.record(recordVO);
    }
}
