package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SaaSOtherAssignDriverValidator extends AbstractSaaSOperateValidator {

    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
        if(!OrderStatusEnum.DISPATCH_CONFIRMED.getCode().equals(dspOrderVO.getOrderStatus())){
            throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
        }
        //订单、操作人的供应商一致
        Long supplierId = cmd.getSupplierId();
        if(!dspOrderVO.getSupplierId().toString().equals(supplierId.toString())){
            throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
        }
    }
}
