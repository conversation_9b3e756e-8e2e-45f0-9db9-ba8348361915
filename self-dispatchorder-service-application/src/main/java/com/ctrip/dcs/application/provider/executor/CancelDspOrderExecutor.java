package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CancelDspOrderExeCmd;
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 取消派发单
 */
@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
public class CancelDspOrderExecutor extends AbstractRpcExecutor<CancelDspOrderRequestType, CancelDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CancelDspOrderExecutor.class);

    @Autowired
    private CancelDspOrderExeCmd cancelDspOrderExeCmd;

    @Override
    public CancelDspOrderResponseType execute(CancelDspOrderRequestType requestType) {
        CancelDspOrderResponseType responseType = new CancelDspOrderResponseType();
        if (StringUtils.isEmpty(requestType.getUserOrderId()) || StringUtils.isEmpty(requestType.getSupplyOrderId())
                || StringUtils.isEmpty(requestType.getCancelReasonId())) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            cancelDspOrderExeCmd.execute(CancelDspOrderConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("cancelDspOrderException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


}
