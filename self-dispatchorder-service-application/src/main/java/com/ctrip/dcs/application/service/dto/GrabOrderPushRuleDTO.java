package com.ctrip.dcs.application.service.dto;


import java.util.List;

public class GrabOrderPushRuleDTO {

    public Long id;

    public Long supplierId;

    public Long cityId;

    private List<Long> cityIds;

    public String categoryCode;

    public String supplierName;

    public List<Long> vehicleGroupIdList;

    public Integer ruleType;

    public Integer immediatePushTime;

    public String fixedPushTime;

    public String startBookTime;

    public String endBookTime;

    public String bookTime;

    public String operateUser;

    public Integer deleteFlag;

    private String priority;

    private String rewards;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<Long> getVehicleGroupIdList() {
        return vehicleGroupIdList;
    }

    public void setVehicleGroupIdList(List<Long> vehicleGroupIdList) {
        this.vehicleGroupIdList = vehicleGroupIdList;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getImmediatePushTime() {
        return immediatePushTime;
    }

    public void setImmediatePushTime(Integer immediatePushTime) {
        this.immediatePushTime = immediatePushTime;
    }

    public String getFixedPushTime() {
        return fixedPushTime;
    }

    public void setFixedPushTime(String fixedPushTime) {
        this.fixedPushTime = fixedPushTime;
    }

    public String getStartBookTime() {
        return startBookTime;
    }

    public void setStartBookTime(String startBookTime) {
        this.startBookTime = startBookTime;
    }

    public String getEndBookTime() {
        return endBookTime;
    }

    public void setEndBookTime(String endBookTime) {
        this.endBookTime = endBookTime;
    }

    public String getBookTime() {
        return bookTime;
    }

    public void setBookTime(String bookTime) {
        this.bookTime = bookTime;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public List<Long> getCityIds() {
        return cityIds;
    }

    public void setCityIds(List<Long> cityIds) {
        this.cityIds = cityIds;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getRewards() {
        return rewards;
    }

    public void setRewards(String rewards) {
        this.rewards = rewards;
    }
}
