package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverVehicleTypeModifyEvent;
import com.ctrip.dcs.application.event.impl.DriverVehicleTypeModifyEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverVehicleTypeModifyTestService")
public class DriverVehicleTypeModifyTestService implements ITestDspOrderService{
    @Autowired
    private DriverVehicleTypeModifyEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverVehicleTypeModifyEvent event = new DriverVehicleTypeModifyEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setAccountType(params.get("accountType"));
        event.setVehicleId(Long.valueOf(params.get("vehicleId")));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
