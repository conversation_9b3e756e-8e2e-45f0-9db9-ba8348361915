package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

/**
 * 司机提交抢单消息
 * <AUTHOR>
 */
@Component
public class DriverCarConfirmSyncPurchaseListener {

    private static final Logger logger = LoggerFactory.getLogger(DriverCarConfirmSyncPurchaseListener.class);

    @Autowired
    protected PurchaseSupplyOrderGateway purchaseSupplyOrderGateway;

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_SYNC_PURCHASE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try{
            if(message.times() > 20){
                MetricsUtil.recordValue(MetricsConstants.DRIVER_CAR_CONFIRM_SYNC_PURCHASE_ERROR_COUNT);
                return;
            }
            String dspOrderConfirmRecord = message.getStringProperty("dspOrderConfirmRecord");
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("DriverCarConfirmSyncPurchaseListener_dspOrderConfirmRecord", dspOrderConfirmRecord);
            if(StringUtils.isBlank(dspOrderConfirmRecord)){
                return;
            }
            DspOrderConfirmRecordVO dspOrderConfirmRecordVO = JsonUtil.fromJson(dspOrderConfirmRecord, DspOrderConfirmRecordVO.class);
            if(Objects.isNull(dspOrderConfirmRecordVO)){
                return;
            }
            DspOrderDO dspOrder = null;
            if (StringUtils.isNotBlank(dspOrderId)) {
                dspOrder = dspOrderRepository.find(dspOrderId);
            }
            purchaseSupplyOrderGateway.confirm(dspOrder, dspOrderConfirmRecordVO);
            MetricsUtil.recordValue(MetricsConstants.DRIVER_CAR_CONFIRM_SYNC_PURCHASE_COUNT);
        } catch(Exception ex){
            MetricsUtil.recordValue(MetricsConstants.DRIVER_CAR_CONFIRM_SYNC_PURCHASE_COUNT);
            logger.warn("DriverCarConfirmSyncPurchaseListener", ex);
            throw new NeedRetryException(System.currentTimeMillis() + 500, "DriverCarConfirmSyncPurchaseListener");
        }
    }
}
