package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CheckDspOrderInventoryExeCmd;
import com.ctrip.dcs.application.command.api.CheckDspOrderInventoryCommand;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDspOrderInventoryRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDspOrderInventoryResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CheckDspOrderInventoryDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class CheckDspOrderInventoryExecutor extends AbstractRpcExecutor<CheckDspOrderInventoryRequestType, CheckDspOrderInventoryResponseType> implements Validator<CheckDspOrderInventoryRequestType> {
//hebing
    @Autowired
    private CheckDspOrderInventoryExeCmd cmd;

    @Override
    public CheckDspOrderInventoryResponseType execute(CheckDspOrderInventoryRequestType requestType) {
        List<CheckModel> list = cmd.execute(new CheckDspOrderInventoryCommand(requestType.getDspOrderId(), requestType.getSubSkuId()));
        return toCheckDspOrderInventoryResponseType(list);
    }

    @Override
    public void validate(AbstractValidator<CheckDspOrderInventoryRequestType> validator) {
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("subSkuId").notNull();
    }

    public CheckDspOrderInventoryResponseType toCheckDspOrderInventoryResponseType(List<CheckModel> list) {
        CheckDspOrderInventoryResponseType responseType = new CheckDspOrderInventoryResponseType();
        responseType.setCheckList(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(list)) {
            List<CheckDspOrderInventoryDTO> result = list.stream().map(this::toCheckDspOrderInventoryDTO).filter(Objects::nonNull).collect(Collectors.toList());
            responseType.setCheckList(result);
        }
        return responseType;
    }

    public CheckDspOrderInventoryDTO toCheckDspOrderInventoryDTO(CheckModel model) {
        if (model == null || model.getModel() == null || model.getModel().getOrder() == null || model.getCheckCode() == null) {
            return null;
        }
        CheckDspOrderInventoryDTO dto = new CheckDspOrderInventoryDTO();
        dto.setDspOrderId(model.getModel().getOrder().getDspOrderId());
        if (model.getModel().getDriver() != null) {
            dto.setDriverId(model.getModel().getDriver().getDriverId());
        }
        if (model.getModel().getTransportGroup() != null) {
            dto.setTransportGroupId(model.getModel().getTransportGroup().getTransportGroupId());
        }
        dto.setCheckCode(model.getCheckCode().getCode());
        return dto;
    }
}
