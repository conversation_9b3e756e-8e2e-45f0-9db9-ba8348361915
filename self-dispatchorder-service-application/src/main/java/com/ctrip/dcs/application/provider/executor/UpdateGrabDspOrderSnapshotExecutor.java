package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderSnapshotDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO;
import com.ctrip.dcs.self.dispatchorder.interfaces.GrabDspOrderSnapshotDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class UpdateGrabDspOrderSnapshotExecutor extends AbstractRpcExecutor<UpdateGrabDspOrderSnapshotRequestType, UpdateGrabDspOrderSnapshotResponseType> implements Validator<UpdateGrabDspOrderSnapshotRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabDspOrderSnapshotExecutor.class);

    @Autowired
    private GrabDspOrderSnapshotDao grabDspOrderSnapshotDao;

    @Override
    public UpdateGrabDspOrderSnapshotResponseType execute(UpdateGrabDspOrderSnapshotRequestType requestType) {
        for (GrabDspOrderSnapshotDTO dto : requestType.getSnapshots()) {
            try {
                GrabDspOrderSnapshotPO po = grabDspOrderSnapshotDao.query(dto.getDspOrderId());
                if (po != null) {
                    po.setGrabStatus(dto.getGrabStatus());
                    grabDspOrderSnapshotDao.update(po);
                }
            } catch (Exception e) {
                logger.warn("UpdateGrabDspOrderSnapshotExecutorError", e);
            }
        }
        return ServiceResponseUtils.success(new UpdateGrabDspOrderSnapshotResponseType());
    }

    @Override
    public void validate(AbstractValidator<UpdateGrabDspOrderSnapshotRequestType> validator) {
        validator.ruleFor("snapshots").notNull().notEmpty();
    }
}
