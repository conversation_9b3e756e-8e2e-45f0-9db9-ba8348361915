package com.ctrip.dcs.application.provider.executor;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.application.command.CancelVBKGrabTaskExeCmd;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@ServiceLogTagPair(key = "taskId", alias = "taskId")
public class CancelVBKGrabTaskExecutor extends AbstractRpcExecutor<CancelVBKGrabTaskRequestType, CancelVBKGrabTaskResponseType> implements Validator<CancelVBKGrabTaskRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(CancelVBKGrabTaskExecutor.class);


    @Autowired
    private CancelVBKGrabTaskExeCmd cancelVBKGrabTaskExeCmd;

    @Override
    public CancelVBKGrabTaskResponseType execute(CancelVBKGrabTaskRequestType requestType) {
        CancelVBKGrabTaskResponseType responseType = new CancelVBKGrabTaskResponseType();
        try {
            Pair<Integer, Integer> pair = cancelVBKGrabTaskExeCmd.execute(requestType);
            responseType.setSuccessNum(pair.getKey());
            responseType.setFailNum(pair.getValue());
            return ServiceResponseUtils.success(responseType);
        }catch (BizException e) {
            MetricsUtil.recordValue(MetricsConstants.VBK_CANCEL_VBK_GRAB_TASK_EXECUTOR_ERROR_COUNT);
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            MetricsUtil.recordValue(MetricsConstants.VBK_CANCEL_VBK_GRAB_TASK_EXECUTOR_ERROR_COUNT);

            logger.error("CancelVBKGrabTaskExecutor", ex);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    @Override
    public void validate(AbstractValidator<CancelVBKGrabTaskRequestType> validator) {
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("operatorName").notNull();
        validator.ruleFor("taskId").notNull();
    }
}
