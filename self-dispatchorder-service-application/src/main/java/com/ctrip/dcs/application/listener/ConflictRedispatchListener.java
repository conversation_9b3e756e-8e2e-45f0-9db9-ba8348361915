package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.event.IConflictRedispatchEventHandler;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.HashMap;
import java.util.Map;

/**
 * 冲突自动改派事件
 */
@Component
public class ConflictRedispatchListener {
    private static final Logger logger = LoggerFactory.getLogger(ConflictRedispatchListener.class);
    @Autowired
    private IConflictRedispatchEventHandler handler;

    @QmqLogTag(tagKeys = {"driverId", "redispatchDspOrderId","conflictDspOrderId","redispatchDriverOrderId","redispatchUserOrderId"})
    @QmqConsumer(prefix = EventConstants.QMQ_SUBJECT_CONFLICT_REDISPATCH, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void redispatch(Message message) {
        logger.info("ConflictRedispatchListener_msg", LocalJsonUtils.toJson(message));
        String conflictDspOrderId = message.getStringProperty("conflictDspOrderId");
        String redispatchDspOrderId = message.getStringProperty("redispatchDspOrderId");
        String driverId = message.getStringProperty("driverId");
        String redispatchDriverOrderId = message.getStringProperty("redispatchDriverOrderId");
        String redispatchUserOrderId = message.getStringProperty("redispatchUserOrderId");
        boolean result = handler.redispatch(driverId,redispatchDspOrderId,conflictDspOrderId,redispatchDriverOrderId,redispatchUserOrderId);
        Map<String,String> tag = new HashMap<>();
        tag.put("conflictDspOrderId",conflictDspOrderId);
        tag.put("redispatchDspOrderId",redispatchDspOrderId);
        tag.put("driverId",driverId);
        tag.put("redispatchDriverOrderId",redispatchDriverOrderId);
        tag.put("redispatchResult",String.valueOf(result));
        tag.put("redispatchUserOrderId",redispatchUserOrderId);
        logger.info("ConflictRedispatchListener_result", LocalJsonUtils.toJson(tag),tag);
        //埋点
        MetricsUtil.conflictRedispatch("redispatchResult_"+result);
    }
}
