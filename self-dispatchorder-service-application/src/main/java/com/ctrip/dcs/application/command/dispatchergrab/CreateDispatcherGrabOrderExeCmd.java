package com.ctrip.dcs.application.command.dispatchergrab;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.ctrip.dcs.domain.common.enums.JntModifyOrderHandleScene;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.schedule.event.CreateDispatcherGrabOrderEvent;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.application.command.api.CreateDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.converter.DispatcherGrabOrderDOConverter;
import com.ctrip.dcs.application.command.dto.DispatcherGrabOrderDTO;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DispatcherGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.process.impl.RefuseAssignTransportProcess;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
@Component
public class CreateDispatcherGrabOrderExeCmd {

    private final static Logger logger = LoggerFactory.getLogger(CreateDispatcherGrabOrderExeCmd.class);

    @Autowired
    private DispatcherGrabOrderDOConverter dispatcherGrabOrderConverter;

    @Autowired
    private RefuseAssignTransportProcess process;

    @Autowired
    DspOrderRepository dspOrderRepository;

    @Autowired
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    DispatcherGrabOrderRepository dispatcherGrabOrderRepository;

    @Autowired
    private MessageProviderService messageProviderService;

    public void execute(CreateDispatcherGrabOrderCommand command) {
        List<DispatcherGrabOrderDO> orders = Lists.newArrayList();
        // 原单修改场景-创建待确认数据
        if (SupplierConfirmSceneEnum.ORI_MODIFY.getScene().equals(command.getConfirmScene())) {
            createOriModifySupplierConfirm(command, orders);
        } else {
            // 抢单、急单场景
            orders = dispatcherGrabOrderConverter.toDispatcherGrabOrderPO(command.getOrders());
            process.execute(orders);
        }
    }

    private void createOriModifySupplierConfirm(CreateDispatcherGrabOrderCommand command, List<DispatcherGrabOrderDO> orders) {
        try {
            // 查询有效的派发单
            DispatcherGrabOrderDTO dispatcherGrabOrderDTO = command.getOrders().getFirst();
            DspOrderDO dspOrderDO = getDspOrderDO(dispatcherGrabOrderDTO);
            if (dspOrderDO == null || dspOrderDO.getConfirmRecordId() == null) {
                Cat.logEvent("dcs.self.createOriModifySupplierConfirm.queryDspOorder.null", "1");
                throw new IllegalStateException("dspOrderDO is null or dspOrderDO.getConfirmRecordId is null");
            }
            // 原派发单特殊标识，在原列表页展示【已修改待确认】标签，屏蔽操作按钮
            dspOrderDO.setSpecialCancelScene(JntModifyOrderHandleScene.TO_BE_SUPPLIER_CONFIRM.getType());

            // 查询应单记录
            DspOrderConfirmRecordVO dspOrderConfirmRecordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
            if (dspOrderConfirmRecordVO == null) {
                throw new IllegalStateException("dspOrderConfirmRecordVO is null");
            }
            DispatcherGrabOrderDO modifyOrder = DispatcherGrabOrderDO.builder()
                    .dspOrderId(dspOrderDO.getDspOrderId())
                    .userOrderId(dspOrderDO.getUserOrderId())
                    .supplierId(dspOrderDO.getSupplierId().longValue())
                    .transportGroupId(dspOrderConfirmRecordVO.getDriverInfo() == null ? 0 : dspOrderConfirmRecordVO.getDriverInfo().getTransportGroupId())
                    .subSku(0)
                    .duid("")
                    .grabStatus(DispatcherGrabOrderStatusEnum.INIT)
                    .confirmScene(SupplierConfirmSceneEnum.ORI_MODIFY.getScene())
                    .orderSource("CTRIP")
                    .lastConfirmTime(dispatcherGrabOrderDTO.getLastConfirmTime())
                    .modifyVersion(dispatcherGrabOrderDTO.getModifyVersion())
                    .build();
            orders.add(modifyOrder);
            Long id = dispatcherGrabOrderRepository.createForOriModify(modifyOrder, dspOrderDO);
            // 发送消息
            messageProviderService.send(new CreateDispatcherGrabOrderEvent(Lists.newArrayList(id)));
            Cat.logEvent("dcs.self.dsp.createOriModifySupplierConfirm", "success");
        } catch (SQLException e) {
            logger.error("createForOriModify_exp", e);
            Cat.logEvent("dcs.self.dsp.createOriModifySupplierConfirm", "fail");
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询已应单的派发单
     *
     * @param dispatcherGrabOrderDTO
     * @return
     */
    private DspOrderDO getDspOrderDO(DispatcherGrabOrderDTO dispatcherGrabOrderDTO) {
        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(dispatcherGrabOrderDTO.getUserOrderId());
        return Optional.ofNullable(dspOrderDOS).orElse(Collections.emptyList()).stream().filter(d ->
                OrderStatusEnum.DISPATCH_CONFIRMED.getCode().equals(d.getOrderStatus())
                        || OrderStatusEnum.DRIVER_CONFIRMED.getCode().equals(d.getOrderStatus())
                        || OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode().equals(d.getOrderStatus())).findFirst().orElse(null);
    }
}
