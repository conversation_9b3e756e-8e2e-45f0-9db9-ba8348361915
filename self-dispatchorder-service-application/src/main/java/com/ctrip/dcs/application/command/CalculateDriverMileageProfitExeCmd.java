package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand;
import com.ctrip.dcs.domain.common.enums.CalculateDriverMileageProfitEventType;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.LocationBasedService;
import com.ctrip.dcs.domain.common.service.OrderFeePriorityService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.mileage.entity.DriverMileageProfitDO;
import com.ctrip.dcs.domain.mileage.factory.DriverMileageProfitFactory;
import com.ctrip.dcs.domain.mileage.repository.DriverMileageProfitRepository;
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 计算司机完成里程价值
 * <AUTHOR>
 */
@Component
public class CalculateDriverMileageProfitExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CalculateDriverMileageProfitExeCmd.class);

    @Qualifier("completeDriverMileageProfitRepositoryImpl")
    @Autowired
    private DriverMileageProfitRepository completeDriverMileageProfitRepositoryImpl;

    @Qualifier("expectDriverMileageProfitRepositoryImpl")
    @Autowired
    private DriverMileageProfitRepository expectDriverMileageProfitRepositoryImpl;

    @Qualifier("todayDriverMileageProfitRepositoryImpl")
    @Autowired
    private DriverMileageProfitRepository todayDriverMileageProfitRepositoryImpl;

    @Autowired
    private DriverMileageProfitFactory driverMileageProfitFactory;

    @Autowired
    private LocationBasedService locationBasedService;

    @Autowired
    private OrderFeePriorityService orderFeePriorityService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    public void execute(CalculateDriverMileageProfitCommand command) {
        Map<String, String> tag = ImmutableMap.of("driverId", command.getDriverVO().getDriverId().toString());
        // 1、查询时段内已接订单
        List<DspOrderVO> orders = queryOrders(command.getDriverVO(), command.getWorkTime());
        // 派发单取消，过滤掉这单（原单场景，派发单取消，司机单hold住不取消）
        if(CalculateDriverMileageProfitEventType.DSP_ORDER_CANCEL.equals(command.getCalculateDriverMileageProfitEventType())) {
            orders.removeIf(dspOrderVO -> dspOrderVO.getDspOrderId().equals(command.getDspOrderId()));
        }
        // 2、查询城市订单分位信息
        Map<Integer, DspOrderFeeQuantileVO> quantileMap = queryOrderFeeQuantile(command.getDriverVO().getCityId());
        for (DriverMileageProfitTypeVO type : command.getDriverMileageProfitType()) {
            List<DspOrderVO> list = orders.stream().filter(o -> type.getFilter().apply(o)).collect(Collectors.toList());
            DriverMileageProfitDO driverMileageProfitDO = driverMileageProfitFactory.create(command.getDriverVO(), command.getWorkTime());
            // 3、 查询空驶
            List<EmptyDrivingVO> emptyDriving = queryEmptyDriving(command.getDriverVO(), list);
            // 4、计算预期里程收益
            driverMileageProfitDO.calculate(list, emptyDriving, quantileMap);
            logger.info("CalculateDriverMileageProfit", JacksonSerializer.INSTANCE().serialize(driverMileageProfitDO), tag);
            // 5、保存
            DriverMileageProfitRepository driverMileageProfitRepository = getDriverMileageProfitRepository(type);
            if (driverMileageProfitRepository != null) {
                driverMileageProfitRepository.save(driverMileageProfitDO);
            }
        }

    }

    private DriverMileageProfitRepository getDriverMileageProfitRepository(DriverMileageProfitTypeVO type) {
        if (DriverMileageProfitTypeVO.COMPLETE == type) {
            return completeDriverMileageProfitRepositoryImpl;
        }
        if (DriverMileageProfitTypeVO.EXPECT == type) {
            return expectDriverMileageProfitRepositoryImpl;
        }
        if (DriverMileageProfitTypeVO.TODAY == type) {
            return todayDriverMileageProfitRepositoryImpl;
        }
        return null;
    }

    private List<EmptyDrivingVO> queryEmptyDriving(DriverVO driver, List<DspOrderVO> orders) {
        List<PairLocationVO> locations = DriverMileageProfitDO.location(driver, orders);
        List<LocationRouteVO> routes = locationBasedService.query(locations);
        return routes.stream()
                .map(r -> new EmptyDrivingVO(r.getDistance(), r.getDuration()))
                .collect(Collectors.toList());
    }

    private Map<Integer /*carTypeId*/, DspOrderFeeQuantileVO> queryOrderFeeQuantile(Long cityId) {
        List<DspOrderFeeQuantileVO> quatile = orderFeePriorityService.queryOrderFeeQuantile(cityId.intValue());
        return quatile.stream().collect(Collectors.toMap(
                DspOrderFeeQuantileVO::getCarTypeId,
                q -> q,
                (q1, q2) -> q2
        ));
    }

    private List<DspOrderVO> queryOrders(DriverVO driver, DriverWorkTimeVO workTime) {
        return queryDspOrderService.queryOrderListWithDriverAndTime(
                driver,
                workTime.getStart(),
                workTime.getEnd(),
                Lists.newArrayList(
                        OrderStatusEnum.DRIVER_CONFIRMED,
                        OrderStatusEnum.DRIVER_CAR_CONFIRMED,
                        OrderStatusEnum.DRIVER_TO_MEET,
                        OrderStatusEnum.DRIVER_ARRIVE,
                        OrderStatusEnum.DRIVER_SERVICE_START,
                        OrderStatusEnum.DRIVER_SERVICE_END,
                        OrderStatusEnum.ORDER_FINISH
                ), true, true
        );
    }

}
