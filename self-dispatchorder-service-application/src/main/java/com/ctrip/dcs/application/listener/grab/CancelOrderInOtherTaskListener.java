package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 派发任务执行完成消息
 * <AUTHOR>
 */
@Component
public class CancelOrderInOtherTaskListener extends MqListener {

    private static final Logger logger = LoggerFactory.getLogger(CancelOrderInOtherTaskListener.class);

    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;

    @QmqConsumer(prefix = EventConstants.CANCEL_ORDER_IN_OTHER_TASK_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.CANCEL_ORDER_IN_OTHER_EXECUTE_SCHEDULE_ERROR_COUNT) == 0){
                return;
            }
            String vbkGrabTaskId = message.getStringProperty("vbkGrabTaskId");
            String orderIdStr = message.getStringProperty("orderIdStr");
            if(StringUtils.isBlank(orderIdStr)){
                return;
            }
            if(StringUtils.isBlank(vbkGrabTaskId)){
                return;
            }
            String[] orderId = orderIdStr.split(",");
            Map<String, String> map = new HashMap();
            map.put("vbkGrabTaskId", vbkGrabTaskId);
            map.put("orderId", JsonUtils.toJson(orderId));
            for (String dspOrderId : orderId) {
                List<VBKDriverGrabOrderDO> list = vbkDriverGrabOrderRepository.queryByDspOrderId(dspOrderId, GrabTaskStatus.IN_PROGRESS.getCode());
                logger.info("CancelOrderInOtherTaskListener_queryByDspOrderId", JsonUtils.toJson(list));
                if(CollectionUtils.isEmpty(list)){
                    continue;
                }
                List<String> vbkGrabTaskIdList = list.stream().map(VBKDriverGrabOrderDO::getVbkGrabTaskId).filter(x -> !x.equals(vbkGrabTaskId)).collect(Collectors.toList());
                logger.info("CancelOrderInOtherTaskListener_vbkGrabTaskIdList", JsonUtils.toJson(vbkGrabTaskIdList));
                vbkGrabTaskIdList.forEach(x -> finishGrabTask(x, dspOrderId));
            }
            logger.info("CancelOrderInOtherTaskListener_param", JsonUtils.toJson(map));
            int num = vbkDriverGrabOrderRepository.deleteOrderInOtherTask(vbkGrabTaskId, Arrays.asList(orderId));
            logger.info("CancelOrderInOtherTaskListener_res", JsonUtils.toJson(num));
        } catch (Exception e) {
            logger.error("CancelOrderInOtherTaskListener", e);
            throw new NeedRetryException("CancelOrderInOtherTaskListener");
        }
    }

    private void finishGrabTask(String x, String dspOrderId) {
        List<VBKDriverGrabOrderDO> list = vbkDriverGrabOrderRepository.queryByTaskIdAndStatus(x, GrabTaskStatus.IN_PROGRESS.getCode());
        logger.info("CancelOrderInOtherTaskListener_finishGrabTask_orderNum", JsonUtils.toJson(list));
        if(CollectionUtils.isEmpty(list) || (list.size() == 1 && list.get(0).getDspOrderId().equals(dspOrderId))){
            int finishNum = vbkDriverGrabTaskRepository.finishTaskBatch(Lists.newArrayList(x), GrabTaskStatus.FINISH.getCode());
            logger.info("CancelOrderInOtherTaskListener_finishGrabTask_finishNum", JsonUtils.toJson(finishNum));
        }
    }
}
