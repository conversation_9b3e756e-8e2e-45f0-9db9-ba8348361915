package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.SaaSUpdateDspOrderCommand;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.XproductVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.domain.dsporder.entity.FromPoiDTO;
import com.ctrip.dcs.domain.dsporder.entity.ToPoiDTO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtendInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtraFlightInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSFeeInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSPoiInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSUserCountInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSXproductInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaasUpdateDspOrderInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class SaaSUpdateDspOrderConverter {

    public static SaaSUpdateDspOrderCommand converter(SaasUpdateDspOrderRequestType requestType) {
        SaaSUpdateDspOrderCommand cmd = new SaaSUpdateDspOrderCommand();

        SaasUpdateDspOrderInfo updateDspOrderInfo = requestType.getUpdateDspOrderInfo();
        SaaSFeeInfo feeInfo = updateDspOrderInfo.getFeeInfo();
        SaaSXproductInfo xproductInfo = updateDspOrderInfo.getXproductInfo();
        SaaSUserCountInfo userCount = updateDspOrderInfo.getUserCount();
        SaaSExtraFlightInfo extraFlightInfo = updateDspOrderInfo.getExtraFlightInfo();

        SaaSPoiInfo toPoi = updateDspOrderInfo.getToPoi();
        SaaSPoiInfo fromPoi = updateDspOrderInfo.getFromPoi();
        SaaSExtendInfo extendInfo = updateDspOrderInfo.getExtendInfo();

        cmd.setSupplierId(requestType.getSupplierId());
        cmd.setDspOrderDO(buildOrder(updateDspOrderInfo, fromPoi, toPoi, xproductInfo));
        cmd.setDspOrderDetailDO(buildOrderDetail(updateDspOrderInfo, extendInfo, extraFlightInfo, xproductInfo, userCount));
        cmd.setDspOrderFeeDO(buildFee(feeInfo, updateDspOrderInfo));
        cmd.setNewProcess(updateDspOrderInfo.getNewProcess());
        return cmd;
    }


    private static DspOrderDO buildOrder(SaasUpdateDspOrderInfo updateDspOrderInfo, SaaSPoiInfo fromPoi, SaaSPoiInfo toPoi,
                                         SaaSXproductInfo xproductInfo) {
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDO.setDspOrderId(updateDspOrderInfo.getDspOrderId());
        dspOrderDO.setVbkOrderId(updateDspOrderInfo.getVbkOrderId());
        dspOrderDO.setUserOrderId(updateDspOrderInfo.getUserOrderId());
        dspOrderDO.setOrderSourceCode(updateDspOrderInfo.getOrderSourceCode());
        dspOrderDO.setVehicleGroupName(updateDspOrderInfo.getVehicleGroupName());
        dspOrderDO.setEstimatedUseTime(new Timestamp(DateUtil.parseDateStr2Date(updateDspOrderInfo.getEstimatedUseTime()).getTime()));
        dspOrderDO.setEstimatedKm(updateDspOrderInfo.getEstimatedKm());
        FromPoiDTO fromPoiDTO = new FromPoiDTO();
        fromPoiDTO.setCityId(fromPoi.getCityId());
        fromPoiDTO.setFromAddress(fromPoi.getAddress());
        dspOrderDO.setFromPoiDTO(fromPoiDTO);
        ToPoiDTO toPoiDTO = new ToPoiDTO();
        toPoiDTO.setCityId(toPoi.getCityId());
        toPoiDTO.setToAddress(toPoi.getAddress());
        dspOrderDO.setToPoiDTO(toPoiDTO);
        if(Objects.nonNull(xproductInfo)){
            dspOrderDO.setProductName(xproductInfo.getPackageName());
        }
        if (StringUtils.isNotBlank(updateDspOrderInfo.getLastConfirmCarTime())) {
            dspOrderDO.setLastConfirmCarTime(new Timestamp(DateUtil.parseDateStr2Date(updateDspOrderInfo.getLastConfirmCarTime()).getTime()));
        }
        return dspOrderDO;
    }


    private static DspOrderDetailDO buildOrderDetail(SaasUpdateDspOrderInfo updateDspOrderInfo,
                                                     SaaSExtendInfo extendInfo,
                                                     SaaSExtraFlightInfo extraFlightInfo,
                                                     SaaSXproductInfo xproductInfo,
                                                     SaaSUserCountInfo userCount) {
        DspOrderDetailDO dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setDspOrderId(updateDspOrderInfo.getDspOrderId());
        dspOrderDetailDO.setUserOrderId(updateDspOrderInfo.getUserOrderId());
        if(Objects.nonNull(extendInfo)){
            dspOrderDetailDO.setRemark(extendInfo.getRemark());
            dspOrderDetailDO.setDriverRemark(extendInfo.getDriverRemark());
            dspOrderDetailDO.setVbkDistributorName(extendInfo.getVbkDistributorName());
            dspOrderDetailDO.setVbkDriverSettlePrice(extendInfo.getVbkDriverSettlePrice());
            dspOrderDetailDO.setVbkDriverSettleCurrency(extendInfo.getVbkDriverSettleCurrency());
        }
        if(Objects.nonNull(extraFlightInfo)){
            dspOrderDetailDO.setFlightInfo(LocalJsonUtils.toJson(extraFlightInfo));
        }
        XproductVO xproductVO = new XproductVO();
        if(Objects.nonNull(xproductInfo)){
            xproductVO.setAdditionalServices(xproductInfo.getAdditionalServices());
            dspOrderDetailDO.setXproductInfo(LocalJsonUtils.toJson(Lists.newArrayList(xproductVO)));
        }
        if(Objects.nonNull(userCount)){
            dspOrderDetailDO.setUserCount(LocalJsonUtils.toJson(userCount));
        }
//        if (Objects.nonNull(passengerInfo)) {
//            dspOrderDetailDO.setPassengerInfo(LocalJsonUtils.toJson(passengerInfo));
//        }
//        if (Objects.nonNull(updateDspOrderInfo.getOrderCreateTime())) {
//            dspOrderDetailDO.setOrderCreateTime(new Timestamp(DateUtil.parseDateStr2Date(updateDspOrderInfo.getOrderCreateTime()).getTime()));
//            dspOrderDetailDO.setOrderCreateTimeBj(new Timestamp(DateUtil.parseDateStr2Date(updateDspOrderInfo.getOrderCreateTimeBj()).getTime()));
//        }
        return dspOrderDetailDO;
    }

    private static DspOrderFeeDO buildFee(SaaSFeeInfo feeInfo, SaasUpdateDspOrderInfo updateDspOrderInfo) {
        DspOrderFeeDO dspOrderFeeDO = new DspOrderFeeDO();
        dspOrderFeeDO.setDspOrderId(updateDspOrderInfo.getDspOrderId());
        dspOrderFeeDO.setUserOrderId(updateDspOrderInfo.getUserOrderId());
        if(Objects.nonNull(feeInfo)){
            dspOrderFeeDO.setCostAmount(Objects.isNull(feeInfo.getCostAmount()) ? BigDecimal.ZERO : feeInfo.getCostAmount());
            dspOrderFeeDO.setUserCurrency(Objects.isNull(feeInfo.getUserCurrency()) ? StringUtils.EMPTY : feeInfo.getUserCurrency());
            dspOrderFeeDO.setSupplierCurrency(Objects.isNull(feeInfo.getSupplierCurrency()) ? StringUtils.EMPTY : feeInfo.getSupplierCurrency());
        }
        return dspOrderFeeDO;
    }
}
