package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ReDispatchConfirmCommand;
import com.ctrip.dcs.application.listener.DspOrderBookPostListener;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ReDispatchConfirmExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(ReDispatchConfirmExeCmd.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    public void execute(ReDispatchConfirmCommand command) {
        try {
            DspOrderVO dspOrder = queryDspOrderService.query(command.getDspOrderId());
            if (dspOrder == null || !dspOrder.isDispatching()) {
                logger.info("ReDispatchConfirmExeCmd", "dspOrder is null or not dispatching, dspOrderId: " + command.getDspOrderId());
                return;
            }
            DspOrderVO parentDspOrder = queryDspOrderService.queryBase(command.getParentDspOrderId());
            if (parentDspOrder == null || parentDspOrder.getConfirmRecordId() == null) {
                logger.info("ReDispatchConfirmExeCmd", "parentDspOrder is null, parentDspOrderId: " + command.getParentDspOrderId());
                return;
            }
            DspOrderConfirmRecordVO parentConfirmRecord = dspOrderConfirmRecordRepository.find(parentDspOrder.getConfirmRecordId());
            if (parentConfirmRecord == null || parentConfirmRecord.getDriverInfo() == null || parentConfirmRecord.getDriverInfo().getTransportGroupId() == null || parentConfirmRecord.getDuid() == null) {
                logger.info("ReDispatchConfirmExeCmd", "parentConfirmRecord is null, parentDspOrderId: " + command.getParentDspOrderId());
                return;
            }
            DuidVO parenDuid = DuidVO.of(parentConfirmRecord.getDuid());
            TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(parentConfirmRecord.getDriverInfo().getTransportGroupId());
            DispatcherConfirmVO confirmVO = DispatcherConfirmVO.builder()
                    .dspOrder(dspOrder)
                    .serviceProvider(new ServiceProviderVO(dspOrder.getSpId()))
                    .supplier(new SupplierVO(transportGroup.getSupplierId()))
                    .transportGroup(transportGroup)
                    .duid(DuidVO.of(dspOrder.getDspOrderId(), parenDuid.getSubSkuId(), parenDuid.getDspType(), parenDuid.getTakenType()))
                    .event(OrderStatusEvent.SYSTEM_ASSIGN)
                    .operator(OperatorVO.systemOperator())
                    .build();
            confirmDspOrderService.confirm(confirmVO);
        } catch (Exception e) {
            logger.warn("ReDispatchConfirmExeCmd", "confirmDspOrderService.confirm error, dspOrderId: " + command.getDspOrderId(), e);
            throw ErrorCode.BUFFER_CHANGE_ERROR.getBizException();
        }
    }
}
