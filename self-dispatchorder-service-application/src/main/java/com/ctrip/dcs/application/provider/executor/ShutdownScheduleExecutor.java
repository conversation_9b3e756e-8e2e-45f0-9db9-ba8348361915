package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.ShutdownScheduleService;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class ShutdownScheduleExecutor extends AbstractRpcExecutor<ShutdownScheduleRequestType, ShutdownScheduleResponseType> implements Validator<ShutdownScheduleRequestType> {

    @Autowired
    private ShutdownScheduleService shutdownScheduleService;

    @Override
    public ShutdownScheduleResponseType execute(ShutdownScheduleRequestType requestType) {
        shutdownScheduleService.shutdown(requestType.getScheduleIds(), ScheduleEventType.MANUAL_SHUTDOWN);
        return ServiceResponseUtils.success(new ShutdownScheduleResponseType());
    }

    @Override
    public void validate(AbstractValidator<ShutdownScheduleRequestType> validator) {
        validator.ruleFor("scheduleIds").notNull().notEmpty();
    }
}
