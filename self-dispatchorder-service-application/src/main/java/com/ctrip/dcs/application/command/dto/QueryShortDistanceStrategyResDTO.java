package com.ctrip.dcs.application.command.dto;

import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO;
import com.ctrip.igt.PaginationDTO;
import lombok.Getter;

import java.util.List;

@Getter
public class QueryShortDistanceStrategyResDTO {
    List<ShortDistanceStrategyDO> shortDistanceStrategyList;
    PaginationDTO pagination;

    public QueryShortDistanceStrategyResDTO(List<ShortDistanceStrategyDO> list, PaginationDTO pagination) {
        this.shortDistanceStrategyList = list;
        this.pagination = pagination;
    }
}
