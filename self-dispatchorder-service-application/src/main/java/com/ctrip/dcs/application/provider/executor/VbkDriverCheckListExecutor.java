package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.VbkDriverCheckListExeCmd;
import com.ctrip.dcs.application.command.api.OperateDriverCheckListCommand;
import com.ctrip.dcs.application.command.dto.DriverCheckListResDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.DriverCheckDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.DriverCheckListRes;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class VbkDriverCheckListExecutor extends AbstractRpcExecutor<VbkDriverCheckListRequestType, VbkDriverCheckListResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(VbkDriverCheckListExecutor.class);

    @Autowired
    private VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd;

    @Override
    public VbkDriverCheckListResponseType execute(VbkDriverCheckListRequestType vbkDriverCheckListRequestType) {
        VbkDriverCheckListResponseType vbkDriverCheckListResponseType = new VbkDriverCheckListResponseType();
        try {
            DriverCheckListResDTO checkListResDTO = vbkDriverCheckListExeCmd.execute(buildReq(vbkDriverCheckListRequestType));
            convert(vbkDriverCheckListResponseType, checkListResDTO);
            return ServiceResponseUtils.success(vbkDriverCheckListResponseType);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(vbkDriverCheckListResponseType, e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("driverCheckList error", e);
            return ServiceResponseUtils.fail(vbkDriverCheckListResponseType);
        }
    }

    private OperateDriverCheckListCommand buildReq(VbkDriverCheckListRequestType vbkDriverCheckListRequestType) {
        OperateDriverCheckListCommand cmd = new OperateDriverCheckListCommand();
        BeanUtils.copyProperties(vbkDriverCheckListRequestType, cmd);
        cmd.setNewProcess(vbkDriverCheckListRequestType.isNewProcess());
        return cmd;
    }

    private void convert(VbkDriverCheckListResponseType vbkDriverCheckListResponseType, DriverCheckListResDTO resDTO) {
        DriverCheckListRes driverCheckListRes = new DriverCheckListRes();
        BeanUtils.copyProperties(resDTO, driverCheckListRes);
        if (CollectionUtils.isNotEmpty(resDTO.getList())) {
            List<DriverCheckDTO> checkDTOList = resDTO.getList().stream()
                    .map(driverCheckDTO -> {
                        DriverCheckDTO checkDTO = new DriverCheckDTO();
                        BeanUtils.copyProperties(driverCheckDTO, checkDTO);
                        checkDTO.setIsAboard(driverCheckDTO.getIsAboard());
                        checkDTO.setDriverLevel(driverCheckDTO.getDriveLevel() == null ?null :driverCheckDTO.getDriveLevel().toString() );
                        return checkDTO;
                    })
                    .collect(Collectors.toList());
            driverCheckListRes.setList(checkDTOList);
        }
        vbkDriverCheckListResponseType.setDriverCheckListResDTO(driverCheckListRes);
    }

}
