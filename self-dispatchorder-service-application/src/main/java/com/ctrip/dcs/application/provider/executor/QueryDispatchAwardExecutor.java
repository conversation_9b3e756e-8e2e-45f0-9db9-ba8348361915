package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDispatchAwardRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDispatchAwardResponseType;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class QueryDispatchAwardExecutor extends AbstractRpcExecutor<BatchQueryDispatchAwardRequestType, BatchQueryDispatchAwardResponseType> {

    @Autowired
    ScheduleTaskRepository scheduleTaskRepository;

    @Override
    public BatchQueryDispatchAwardResponseType execute(BatchQueryDispatchAwardRequestType requestType) {
        BatchQueryDispatchAwardResponseType responseType = new BatchQueryDispatchAwardResponseType();
        //筛选新老版本的duid,老版本查询老的接口，新版本查询新的数据库
        if (CollectionUtils.isEmpty(requestType.getDuIds())) {
            return ServiceResponseUtils.success(responseType);
        }
        List<String> queryDuidsAll = requestType.getDuIds().stream().distinct().collect(Collectors.toList());

        Map<String, BigDecimal> resMap = Maps.newHashMap();

        List<String> oldDuidList = queryDuidsAll.stream().filter(duid -> duid.contains("v2")).collect(Collectors.toList());
        List<String> newDuidList = queryDuidsAll.stream().filter(duid -> !oldDuidList.contains(duid)).collect(Collectors.toList());
        Map<String, BigDecimal> newDriverRewardMap = scheduleTaskRepository.batchQueryRewardByDuid(newDuidList);
        if (MapUtils.isNotEmpty(newDriverRewardMap)) {
            resMap.putAll(newDriverRewardMap);
        }
        responseType.setDispatchAwardInfo(resMap);
        return ServiceResponseUtils.success(responseType);
    }

}
