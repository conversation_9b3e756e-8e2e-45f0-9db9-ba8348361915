package com.ctrip.dcs.application.command.converter;

import com.ctrip.dcs.application.command.dto.DispatcherGrabOrderDTO;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DispatcherGrabOrderDOConverter {

    public List<DispatcherGrabOrderDO> toDispatcherGrabOrderPO(List<DispatcherGrabOrderDTO> dtos) {
        return dtos.stream()
                .map(dto -> {
                    return DispatcherGrabOrderDO.builder()
                            .dspOrderId(dto.getDspOrderId())
                            .userOrderId(dto.getUserOrderId())
                            .supplierId(dto.getSupplierId())
                            .transportGroupId(dto.getTransportGroupId())
                            .subSku(dto.getSubSkuId())
                            .duid(dto.getDuid())
                            .grabStatus(DispatcherGrabOrderStatusEnum.INIT)
                            .confirmScene(dto.getConfirmScene())
                            .orderSource(StringUtils.isNotEmpty(dto.getOrderSource())?dto.getOrderSource():"QUNAR")
                            .lastConfirmTime(dto.getLastConfirmTime())
                            .build();
                })
                .collect(Collectors.toList());
    }
}
