package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.processor.PushSettlementProcessor;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.event.DspOrderDoneEvent;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.repository.DriverAttendanceRepository;
import com.ctrip.dcs.infrastructure.common.constants.PushSettlementTypeEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.sql.SQLException;
import java.util.Date;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

/**
 * 司机单完成消息
 *
 * <AUTHOR>
 */
@Component
public class DriverOrderFinishListener {

    private static final Logger logger = LoggerFactory.getLogger(DriverOrderFinishListener.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private DriverAttendanceRepository driverAttendanceRepository;

    @Autowired
    private PushSettlementProcessor pushSettlementProcessor;

    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_FINISH_TOPIC, consumerGroup = "100041593_driver_attendance", idempotentChecker = "redisIdempotentChecker")
    public void onMessageDriverAttendance(Message message) {
        logger.info("DriverOrderFinishListener.onMessageDriverAttendance begin", JacksonUtil.serialize(message));
        long sysExpectBookTimeBj = message.getLongProperty("sysExpectBookTimeBJ");
        String driverId = message.getStringProperty("driverId");
        if (sysExpectBookTimeBj == 0 || StringUtils.isBlank(driverId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DriverOrderFinishListener_onMessageDriverAttendance retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            Date date = new Date(sysExpectBookTimeBj);
            String str = DateUtil.formatDate(date, DateUtil.DATETIME_FORMAT);
            Date parse = DateUtil.parse(str, DateUtil.DATETIME_FORMAT);
            driverAttendanceRepository.save(driverId,parse);
        } catch (Exception e) {
            logger.error("onMessageDriverAttendance.error", e);
            throw new NeedRetryException("onMessageDriverAttendance");
        }
    }


    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_FINISH_TOPIC, consumerGroup = "100041593_dsporder_done", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) throws SQLException {
        logger.info("DriverOrderFinishListener.onMessage begin", JacksonUtil.serialize(message));
        //"drvOrderId":"司机单号","dspOrderId":"派发单号",
        String dspOrderId = message.getStringProperty("dspOrderId");
        String drvOrderId = message.getStringProperty("driverOrderId");

        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DriverOrderFinishListener_dsporder_complete qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            DspOrderConfirmRecordVO confirmRecordVO = dspOrderConfirmRecordRepository.findByDspOrderId(dspOrderId, drvOrderId);
            if (dspOrderDO == null || confirmRecordVO == null) {
                logger.info("DriverOrderFinishListener_dsporder_complete dspOrder is null,dspOrderId=" + dspOrderId);
                return;
            }
            if (OrderStatusEnum.ORDER_FINISH.getCode().equals(dspOrderDO.getOrderStatus()) || OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus())) {
                logger.info("DriverOrderFinishListener_dsporder_complete dspOrder Illegal status,dspOrderStr=" + JacksonUtil.serialize(dspOrderDO));
                return;
            }
            dspOrderDO.setOrderStatus(OrderStatusEnum.ORDER_FINISH.getCode());
            dspOrderRepository.update(dspOrderDO);
            //发送完成消息
            messageProducer.send(new DspOrderDoneEvent(dspOrderId, dspOrderDO.getUserOrderId(), DateUtil.formatDate(dspOrderDO.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT), confirmRecordVO.getDriverInfo().getTransportGroupId(), 0L));
            // 推送完单 费项消息
            pushSettlementProcessor.process(dspOrderId, confirmRecordVO.getId(), drvOrderId,
                    PushSettlementTypeEnum.SUPPLY_ORDER_COMPLETE);
        } catch (Exception e) {
            logger.error("DriverOrderFinishListener.error", e);
            throw e;
        }
    }

    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_SOP_EVENT_SUBJECT, consumerGroup = "100041593_dsporder_done", idempotentChecker = "redisIdempotentChecker")
    public void onMessageNonCtripOrder(Message message) {
        logger.info("nonCtrioDriverOrderSopEventListener.onMessage begin", JacksonUtil.serialize(message));
        //"drvOrderId":"司机单号","dspOrderId":"派发单号",
        String dspOrderId = message.getStringProperty("dspOrderId");
        String drvOrderId = message.getStringProperty("driverOrderId");
        Integer driverOrderStatus = message.getIntProperty("orderStatus");

        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DriverOrderFinishListener_dsporder_complete qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            if(!OrderStatusEnum.ORDER_FINISH.getCode().equals(driverOrderStatus)){
                logger.info("nonCtrioDriverOrderSopEventListener", " not finish,driverOrder status is " + driverOrderStatus);
                return;
            }
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            DspOrderConfirmRecordVO confirmRecordVO = dspOrderConfirmRecordRepository.findByDspOrderId(dspOrderId, drvOrderId);
            if (dspOrderDO == null || confirmRecordVO == null) {
                logger.info("DriverOrderFinishListener_dsporder_complete dspOrder is null,dspOrderId=" + dspOrderId);
                return;
            }
            if (OrderStatusEnum.ORDER_FINISH.getCode().equals(dspOrderDO.getOrderStatus()) || OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus())) {
                logger.info("DriverOrderFinishListener_dsporder_complete dspOrder Illegal status,dspOrderStr=" + JacksonUtil.serialize(dspOrderDO));
                return;
            }
            dspOrderDO.setOrderStatus(OrderStatusEnum.ORDER_FINISH.getCode());
            dspOrderRepository.update(dspOrderDO);

        } catch (Exception e) {
            logger.error("DriverOrderFinishListener.error", e);
            throw e;
        }
    }




}
