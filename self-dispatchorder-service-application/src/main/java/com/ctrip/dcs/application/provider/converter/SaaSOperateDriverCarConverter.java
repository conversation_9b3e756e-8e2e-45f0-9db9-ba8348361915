package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum;
import com.ctrip.dcs.domain.common.enums.ForceAssignEnum;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.VehicleVO;
import com.ctrip.dcs.domain.common.value.saas.AssignResLogVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasChangeDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSCarInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSDriverInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOperatorInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOrderInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSSupplierInfo;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


public class SaaSOperateDriverCarConverter {



    public static SaaSOperateDriverCarCommand converter(SaasAssignDriverRequestType request) {
        Assert.notNull(request);
        SaaSOperateDriverCarCommand cmd = new SaaSOperateDriverCarCommand();

        SaaSOrderInfo order = request.getOrder();
        cmd.setDspOrderId(order.getDspOrderId());

        Assert.notNull(order.getOrderSourceCode());
        OrderSourceCodeEnum orderSourceCodeEnum = OrderSourceCodeEnum.fromCode(order.getOrderSourceCode());
        Assert.notNull(orderSourceCodeEnum);
        cmd.setOrderSourceCode(orderSourceCodeEnum);

        SaaSDriverInfo driver = request.getDriver();
        cmd.setCityId(driver.getCityId());
        cmd.setIsSelfDriver(driver.getIsSelfDriver());
        cmd.setDriverMobileCountryCode(driver.getDriverMobileCountryCode());
        cmd.setDriverOtherContract(driver.getDriverOtherContract());
        cmd.setDriverLanguage(driver.getDriverLanguage());
        cmd.setDriverMobile(driver.getDriverMobile());
        cmd.setDriverName(driver.getDriverName());
        cmd.setDriverId(driver.getDriverId());


        SaaSCarInfo car = request.getCar();
        cmd.setCarSeriesId(car.getCarSeriesId());
        cmd.setCarColorId(car.getCarColorId());
        cmd.setCarBrandId(car.getCarBrandId());
        cmd.setCarDesc(car.getCarDesc());
        cmd.setCarColor(car.getCarColor());
        cmd.setCarLicense(car.getCarLicense());
        cmd.setCarTypeName(car.getCarTypeName());
        cmd.setCarTypeId(car.getCarTypeId());
        cmd.setCarId(car.getCarId());



        SaaSSupplierInfo supplier = request.getSupplier();
        cmd.setSupplierName(supplier.getSupplierName());
        cmd.setSupplierId(supplier.getSupplierId());

        SaaSOperatorInfo operator = request.getOperator();
        cmd.setOperatorUserType(operator.getOperatorUserType());
        cmd.setOperatorUserName(operator.getOperatorUserName());
        cmd.setOperatorUserAccount(operator.getOperatorUserAccount());


        cmd.setTkeSource("pc");
        cmd.setExtraFee(new BigDecimal("0"));
        cmd.setEvent(OrderStatusEvent.SAAS_ASSIGN);
        cmd.setCheckCode(request.getCheckCode());
        cmd.setNewProcess(Boolean.FALSE);

        if(Boolean.TRUE.equals(request.isNewProcess())){
            cmd.setNewProcess(request.isNewProcess());
            cmd.setDriverSettleCurrency(driver.getDriverSettleCurrency());
            cmd.setDriverSettlePrice(driver.getDriverSettlePrice());
            cmd.setCheckCode(request.getCheckCode());
            cmd.setEvent(OrderStatusEvent.VBK_ASSIGN);
            cmd.setTkeSource(StringUtils.isEmpty(operator.getTkeSource())? "pc" :operator.getTkeSource() );
            cmd.setDriverVisibleRemark(driver.getDriverVisibleRemark());
            cmd.setSupplierRemark(supplier.getSupplierRemark());
            cmd.setBatchAssign(Boolean.TRUE.equals(operator.isBatchAssign()));
            cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
            cmd.setTransportGroupId(driver.getTransportGroupId());
            cmd.setNotUpdateSettlePriceAndCurrency(Boolean.TRUE.equals(operator.isNotUpdateSettlePriceAndCurrency()));
            cmd.setNotUpdateDriverVisibleRemark(Boolean.TRUE.equals(operator.isNotUpdateDriverVisibleRemark()));
            cmd.setNotUpdateSupplierRemark(Boolean.TRUE.equals(operator.isNotUpdateSupplierRemark()));
            cmd.setAssignResLogVO(buildResLogVO(cmd));



        }

        return cmd;
    }


    public static SaaSOperateDriverCarCommand converter(SaasChangeDriverRequestType request) {
        Assert.notNull(request);
        SaaSOperateDriverCarCommand cmd = new SaaSOperateDriverCarCommand();

        SaaSOrderInfo order = request.getOrder();
        cmd.setDspOrderId(order.getDspOrderId());

        Assert.notNull(order.getOrderSourceCode());
        OrderSourceCodeEnum orderSourceCodeEnum = OrderSourceCodeEnum.fromCode(order.getOrderSourceCode());
        Assert.notNull(orderSourceCodeEnum);
        cmd.setOrderSourceCode(orderSourceCodeEnum);

        SaaSDriverInfo driver = request.getDriver();
        if(Objects.nonNull(driver)){
            cmd.setCityId(driver.getCityId());
            cmd.setDriverId(driver.getDriverId());
            cmd.setDriverName(driver.getDriverName());
            cmd.setDriverMobile(driver.getDriverMobile());
            cmd.setDriverLanguage(driver.getDriverLanguage());
            cmd.setDriverOtherContract(driver.getDriverOtherContract());
            cmd.setDriverMobileCountryCode(driver.getDriverMobileCountryCode());
            cmd.setIsSelfDriver(driver.getIsSelfDriver());
        }

        SaaSCarInfo car = request.getCar();
        cmd.setCarId(car.getCarId());
        cmd.setCarTypeId(car.getCarTypeId());
        cmd.setCarTypeName(car.getCarTypeName());
        cmd.setCarLicense(car.getCarLicense());
        cmd.setCarColor(car.getCarColor());
        cmd.setCarDesc(car.getCarDesc());
        cmd.setCarBrandId(car.getCarBrandId());
        cmd.setCarColorId(car.getCarColorId());
        cmd.setCarSeriesId(car.getCarSeriesId());



        SaaSSupplierInfo supplier = request.getSupplier();
        cmd.setSupplierId(supplier.getSupplierId());
        cmd.setSupplierName(supplier.getSupplierName());

        SaaSOperatorInfo operator = request.getOperator();
        cmd.setOperatorUserAccount(operator.getOperatorUserAccount());
        cmd.setOperatorUserName(operator.getOperatorUserName());
        cmd.setOperatorUserType(operator.getOperatorUserType());



        cmd.setExtraFee(new BigDecimal("0"));
        cmd.setTkeSource("PC");
        cmd.setCheckCode(ForceAssignEnum.ORDINARY_ASSIGN.getCode());
        cmd.setEvent(OrderStatusEvent.SAAS_UPDATE_DRIVER);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SAAS);


        if(Boolean.TRUE.equals(request.isNewProcess())){
            cmd.setNewProcess(request.isNewProcess());
            cmd.setDriverSettleCurrency(driver.getDriverSettleCurrency());
            cmd.setDriverSettlePrice(driver.getDriverSettlePrice());
            cmd.setDriverVisibleRemark(driver.getDriverVisibleRemark());
            cmd.setCheckCode(request.getCheckCode());
            cmd.setEvent(OrderStatusEvent.VBK_UPDATE_DRIVER);
            cmd.setTkeSource(StringUtils.isEmpty(operator.getTkeSource())? "pc" :operator.getTkeSource() );
            cmd.setSupplierRemark(supplier.getSupplierRemark());
            cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
            cmd.setTransportGroupId(driver.getTransportGroupId());
            cmd.setBatchAssign(Boolean.TRUE.equals(operator.isBatchAssign()));
            cmd.setNotUpdateSettlePriceAndCurrency(Boolean.TRUE.equals(operator.isNotUpdateSettlePriceAndCurrency()));
            cmd.setAssignResLogVO(buildResLogVO(cmd));
            cmd.setNotUpdateSupplierRemark(Boolean.TRUE.equals(operator.isNotUpdateSupplierRemark()));

        }

        return cmd;
    }


    public static SaaSOperateDriverCarCommand converter(SaasBindCarRequestType request) {
        Assert.notNull(request);
        SaaSOperateDriverCarCommand cmd = new SaaSOperateDriverCarCommand();

        SaaSOrderInfo order = request.getOrder();
        cmd.setDspOrderId(order.getDspOrderId());

        Assert.notNull(order.getOrderSourceCode());
        OrderSourceCodeEnum orderSourceCodeEnum = OrderSourceCodeEnum.fromCode(order.getOrderSourceCode());
        Assert.notNull(orderSourceCodeEnum);
        cmd.setOrderSourceCode(orderSourceCodeEnum);

        SaaSCarInfo car = request.getCar();
        cmd.setCarId(car.getCarId());
        cmd.setCarTypeId(car.getCarTypeId());
        cmd.setCarTypeName(car.getCarTypeName());
        cmd.setCarLicense(car.getCarLicense());
        cmd.setCarColor(car.getCarColor());
        cmd.setCarDesc(car.getCarDesc());
        cmd.setCarBrandId(car.getCarBrandId());
        cmd.setCarColorId(car.getCarColorId());
        cmd.setCarSeriesId(car.getCarSeriesId());



        SaaSSupplierInfo supplier = request.getSupplier();
        cmd.setSupplierId(supplier.getSupplierId());
        cmd.setSupplierName(supplier.getSupplierName());

        SaaSOperatorInfo operator = request.getOperator();
        cmd.setOperatorUserAccount(operator.getOperatorUserAccount());
        cmd.setOperatorUserName(operator.getOperatorUserName());
        cmd.setOperatorUserType(operator.getOperatorUserType());
        cmd.setNotUpdateSupplierRemark(Optional.ofNullable(operator.isNotUpdateSupplierRemark()).orElse(Boolean.FALSE));
        cmd.setNotUpdateDriverVisibleRemark(Optional.ofNullable(operator.isNotUpdateDriverVisibleRemark()).orElse(Boolean.FALSE));
        cmd.setNotUpdateSettlePriceAndCurrency(Optional.ofNullable(operator.isNotUpdateSettlePriceAndCurrency()).orElse(Boolean.FALSE));



        cmd.setExtraFee(new BigDecimal("0"));
        cmd.setTkeSource("PC");
        cmd.setCheckCode(ForceAssignEnum.ORDINARY_ASSIGN.getCode());
        cmd.setEvent(OrderStatusEvent.SAAS_BIND_CAR);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SAAS);
        if(Boolean.TRUE.equals(request.isNewProcess())){
            cmd.setNewProcess(request.isNewProcess());
            cmd.setEvent(OrderStatusEvent.VBK_BIND_CAR);
            cmd.setTkeSource(StringUtils.isEmpty(operator.getTkeSource())? "pc" :operator.getTkeSource() );
            cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
            cmd.setBatchAssign(Boolean.TRUE.equals(operator.isBatchAssign()));
            cmd.setAssignResLogVO(buildResLogVO(cmd));

        }

        return cmd;
    }




    public static VehicleVO converterVehicleVO(SaaSOperateDriverCarCommand cmd) {
        VehicleVO vehicleVO = new VehicleVO();
        vehicleVO.setCarTypeId(cmd.getCarTypeId());
        vehicleVO.setCarTypeName(cmd.getCarTypeName());
        vehicleVO.setCarLicense(cmd.getCarLicense());
        Integer carColorId = cmd.getCarColorId();
        vehicleVO.setCarColorId(Objects.isNull(carColorId) ? null : Long.valueOf(carColorId));
        vehicleVO.setCarColor(cmd.getCarColor());
        Integer carBrandId = cmd.getCarBrandId();
        vehicleVO.setCarBrandId(Objects.isNull(carBrandId) ? null : Long.valueOf(carBrandId));
        Integer carSeriesId = cmd.getCarSeriesId();
        vehicleVO.setCarSeriesId(Objects.isNull(carSeriesId) ? null : Long.valueOf(carSeriesId));
        Integer carId = cmd.getCarId();
        vehicleVO.setCarId(Objects.isNull(carId) ? null : Long.valueOf(carId));
        vehicleVO.setCarDsc(cmd.getCarDesc());
        return vehicleVO;

    }


    public static DriverVO converterDriverVO(SaaSOperateDriverCarCommand cmd) {
        DriverVO driverVO = new DriverVO();
        driverVO.setDriverName(cmd.getDriverName());
        driverVO.setDriverPhone(cmd.getDriverMobile());
        if(Objects.nonNull(cmd.getCityId())){
            driverVO.setCityId(cmd.getCityId().longValue());
        }
        driverVO.setDriverLanguage(cmd.getDriverLanguage());
        driverVO.setDriverPhoneAreaCode(cmd.getDriverMobileCountryCode());
        driverVO.setDriverId(LocalStringUtils.isEmpty(cmd.getDriverId()) ? null : Long.valueOf(cmd.getDriverId()));
        return driverVO;
    }

    public static AssignResLogVO buildResLogVO(SaaSOperateDriverCarCommand command){
            AssignResLogVO resLogVO = new AssignResLogVO();
            resLogVO.setDspOrderId(command.getDspOrderId());
            resLogVO.setOrderSourceCode(command.getOrderSourceCode().getCode());
            resLogVO.setSupplierId(command.getSupplierId());
            resLogVO.setAssignEvent(command.getEvent().getCode());
            resLogVO.setTkeSource(command.getTkeSource());
            resLogVO.setAssignWay(command.isBatchAssign() ? 1 : 2);
            return resLogVO;
    }
}
