package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.TransportGroupMode;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.SupplierVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.value.BroadcastInfoVO;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component("SerializeBroadcastInfoService")
public class SerializeBroadcastInfoService implements ITestDspOrderService{

    @Override
    public String test(Map<String, String> params) {
        String dspOrderId = params.get("dspOrderId");
        String duid = params.get("duid");
        String transportGroupId = params.get("transportGroupId");
        String transportGroupMode = params.get("transportGroupMode");
        String transportGroupName = params.get("transportGroupName");
        String supplierId = params.get("supplierId");
        TransportGroupVO transportGroup = new TransportGroupVO();
        transportGroup.setTransportGroupId(Long.valueOf(transportGroupId));
        transportGroup.setTransportGroupMode(TransportGroupMode.getInstance(Integer.valueOf(transportGroupMode)));
        transportGroup.setTransportGroupName(transportGroupName);
        SupplierVO supplier = new SupplierVO(Long.valueOf(supplierId));
        BroadcastInfoVO vo = new BroadcastInfoVO(dspOrderId, duid, transportGroup, supplier);
        return JsonUtil.toJson(vo);
    }
}
