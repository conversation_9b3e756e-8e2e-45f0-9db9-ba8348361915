package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.OperateDriverCheckListCommand;
import com.ctrip.dcs.application.command.dto.DriverCheckDTO;
import com.ctrip.dcs.application.command.dto.DriverCheckListResDTO;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.gateway.CarTypeRelationGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.QueryDriverInfoCondition;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.adapter.carconfig.SubSkuEnforcePassConfig;
import com.ctrip.dcs.infrastructure.adapter.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.self.order.query.api.QueryDrvTakenNumRequestType;
import com.ctrip.dcs.self.order.query.api.QueryDrvTakenNumResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.dcs.self.order.query.dto.DrvTakenModel;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.Collator;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
public class VbkDriverCheckListExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(VbkDriverCheckListExeCmd.class);

    private static final Splitter TIME_SPLITTER = Splitter.on("~").trimResults().omitEmptyStrings();

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private SelfOrderQueryServiceProxy selfOrderQueryServiceProxy;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private CarTypeRelationGateway carTypeRelationGateway;

    @Autowired
    private CheckService checkService;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    protected DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;

    @Autowired
    private SubSkuEnforcePassConfig subSkuEnforcePassConfig;

    @Autowired
    protected QueryDspOrderService orderQueryService;

    @Autowired
    @Qualifier("driverTakenNumThreadPool")
    protected ExecutorService executorService;
    
    @Autowired
    @Qualifier("driverCheckListThreadPool")
    private ExecutorService driverCheckListThreadPool;

    public DriverCheckListResDTO execute(OperateDriverCheckListCommand command) {
        // 查询订单信息
        DspOrderVO dspOrder = queryDspOrderService.query(command.getDspOrderId());
        if (null == dspOrder) {
            logger.info("VbkDriverCheckListExeCmd", "dspOrder is null");
            return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
        }
        if (!dspOrder.getDspOrderId().equals(command.getDspOrderId())) {
            throw ErrorCode.ORDER_NUMBERS_DO_NOT_MATCH.getBizException();
        }
        logger.info("VbkDriverCheckListExeCmd_" + command.getDspOrderId(), "OperateDriverCheckListCommand,dspOrderId={}", command.getDspOrderId());
        //查询可指派司机列表
        DriverCheckListResDTO driverCheckListResDTO = queryDriverCheckListResDTO(command,dspOrder);
        //产线
        driverCheckListResDTO.setCategoryCode(dspOrder.getCategoryCode().getType());
        return driverCheckListResDTO;
    }

    /**
     * 查询可指派司机列表
     * @param command
     * @param dspOrder
     * @return
     */
    public DriverCheckListResDTO queryDriverCheckListResDTO(OperateDriverCheckListCommand command,DspOrderVO dspOrder){
        //新流程
        if(Boolean.TRUE.equals(command.getNewProcess())){
            // 查询运力组信息
            List<TransportGroupVO> transportGroups = queryTransportGroupService.queryTransportGroups(dspOrder, command.getSupplierId(), command.getTransportGroupId());
            List<DriverVO> drvInfos = queryDriverService.queryDriversForVbk(getQueryDriverInfoCondition(command, dspOrder, transportGroups));
            if (CollectionUtils.isEmpty(drvInfos)) {
                logger.info("VbkDriverCheckListExeCmd", "drvInfos is empty");
                return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
            }
            //携程订单
            if(OrderSourceCodeEnum.TRIP.getCode().equals(dspOrder.getOrderSourceCode())){
                // 过滤运力组
                transportGroups = filterTransportGroup(command, transportGroups, drvInfos);
                if (CollectionUtils.isEmpty(transportGroups)) {
                    logger.info("VbkDriverCheckListExeCmd", "transportGroups is empty");
                    return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
                }
                return filterDriverCheckList(command,dspOrder,drvInfos,transportGroups);
            }
            //非携程订单
            return getDriverCheckListResDTONonCtrip(command,dspOrder,drvInfos);

        }
        return getDriverCheckListResDTO(command, dspOrder);
    }
    /**
     * 非携程单获取结果
     * @param command
     * @param dspOrder
     * @param drvInfos
     * @return
     */
    private DriverCheckListResDTO getDriverCheckListResDTONonCtrip(OperateDriverCheckListCommand command, DspOrderVO dspOrder, List<DriverVO> drvInfos) {
        // 创建duid
        DuidVO duid = DuidVO.of(dspOrder.getDspOrderId(), 0, DspType.VBK_ASSIGN.getCode(), TakenType.SUPPLIER.getCode());
        // 司机待执行订单数量
        Map<Integer /*driverId*/, Integer /*司机接单数量*/> driverCountMap = batchQueryDriverCountAsync(drvInfos,command.getNewProcess());
        // 转换结果
        List<DriverCheckDTO> list = convertDriverCheckListNonCtrip(command.getDspOrderId(), drvInfos, driverCountMap, duid);
        // 排序
        list = filterAndSort(list, dspOrder, command.getAvailable());
        // 分页
        return page(command, list);
    }


    private QueryDriverInfoCondition getQueryDriverInfoCondition(OperateDriverCheckListCommand command, DspOrderVO dspOrder, List<TransportGroupVO> transportGroup) {
        return QueryDriverInfoCondition.builder()
                .driverId(command.getDriverId())
                .dspOrderId(command.getDspOrderId())
                .supplierId(command.getSupplierId().longValue())
                .carLicense(command.getCarLicense())
                .carTypeId(command.getCarTypeId())
                .categoryCode(dspOrder.getCategoryCode())
                .driverName(command.getDriverName())
                .driverPhone(command.getDriverPhone())
//                .driverGuideGraySupplier(driverGuideGraySupplier)
                .transportGroupId(command.getTransportGroupId())
                .transportGroupIdList(transportGroup.stream().map(TransportGroupVO::getTransportGroupId).collect(Collectors.toList()))
                .build();
    }

    private DriverCheckListResDTO getDriverCheckListResDTO(OperateDriverCheckListCommand command, DspOrderVO dspOrder) {
        // 查询司机信息
        Long supplierId = dspOrder.getSupplierId() == null ? 0L : dspOrder.getSupplierId().longValue();
        List<DriverVO> drvInfos = queryDriverService.queryDriversByCondition(command.getTransportGroupId(), command.getDriverName(), command.getDriverPhone(), CategoryUtils.selfGetParentType(dspOrder), supplierId);
        if (CollectionUtils.isEmpty(drvInfos)) {
            logger.info("VbkDriverCheckListExeCmd", "drvInfos is empty");
            return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
        }
        // 查询运力组信息
        List<TransportGroupVO> transportGroups = queryTransportGroupService.queryTransportGroups(dspOrder.getSkuId(), command.getSupplierId(), command.getTransportGroupId());
        // 过滤运力组
        transportGroups = filterTransportGroup(command, transportGroups, drvInfos);
        if (CollectionUtils.isEmpty(transportGroups)) {
            logger.info("VbkDriverCheckListExeCmd", "transportGroups is empty");
            return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
        }
        return filterDriverCheckList(command, dspOrder, drvInfos, transportGroups);
    }

    private DriverCheckListResDTO filterDriverCheckList(OperateDriverCheckListCommand command, DspOrderVO dspOrder, List<DriverVO> drvInfos, List<TransportGroupVO> transportGroups) {
        logger.info("VbkDriverCheckListExeCmd", "param:" + JsonUtil.toJson(command) + ",Filtered query capacity group results:" + JsonUtil.toJson(transportGroups));
        // 创建duid
        DuidVO duid = initDuid(dspOrder, transportGroups);
        //手动检查 和 司机接单数量 并行处理
        CountDownLatch latch = new CountDownLatch(2);
        Map<String, Object> contextHolder = buildContext(command, dspOrder, drvInfos, transportGroups, duid, latch);
        // 手动检查
        List<CheckModel> drvCheckResults = (List<CheckModel>) contextHolder.get("checkModels");
        if (CollectionUtils.isEmpty(drvCheckResults)) {
            logger.info("VbkDriverCheckListExeCmd", "drvCheckResults is empty");
            return new DriverCheckListResDTO(command.getPageNo(), command.getPageSize());
        }
        // 司机信息
        logger.info("VbkDriverCheckListExeCmd_filterDriverCheckList_drvInfos", JsonUtil.toJson(drvInfos.size()));
        Map<Long, DriverVO> driverMap = drvInfos.stream().filter(Objects::nonNull).collect(Collectors.toMap(DriverVO::getDriverId, o -> o, (o1, o2) -> o2));
        logger.info("VbkDriverCheckListExeCmd_filterDriverCheckList_driverMap", JsonUtil.toJson(driverMap.size()));
        // 运力组
        Map<Long, TransportGroupVO> transportGroupInfoMap = transportGroups.stream()
                .collect(Collectors.toMap(TransportGroupVO::getTransportGroupId, transportGroupInfo -> transportGroupInfo));
        // 司机待执行订单数量
        Map<Integer /*driverId*/, Integer /*司机接单数量*/> driverCountMap = (Map<Integer, Integer>) contextHolder.get("driverCountMap");
        // 转换结果
        List<DriverCheckDTO> list = convertDriverCheckList(command.getDspOrderId(), drvCheckResults, driverMap, driverCountMap, transportGroupInfoMap, duid);
        // 排序
        list = filterAndSort(list, dspOrder, command.getAvailable());
        // 分页
        return page(command, list);
    }
    
    public Map<String, Object> buildContext(OperateDriverCheckListCommand command, DspOrderVO dspOrder, List<DriverVO> drvInfos, List<TransportGroupVO> transportGroups, DuidVO duid, CountDownLatch latch) {
        Map<String, Object> contextHolder = Maps.newHashMap();
        String globalTraceId = Optional.ofNullable(LoggerContext.getCurrent()).map(LoggerContext::getGlobalTraceId).orElse(UUID.randomUUID().toString());
        driverCheckListThreadPool.submit(() -> {
            // 手动检查
            checkDrivers(dspOrder, drvInfos, transportGroups, duid, globalTraceId, latch, contextHolder);
        });
        driverCheckListThreadPool.submit(() -> {
            // 司机待执行订单数量
            batchQueryDriverCountAsync(drvInfos, command.getNewProcess(), globalTraceId, latch, contextHolder);
        });
        try {
            latch.await(3000, TimeUnit.MILLISECONDS);
            return contextHolder;
        } catch (Exception e) {
            logger.error("VbkDriverCheckListExeCmd_latch_wait_error", "latch await timeout", e, Maps.newHashMap());
            return Maps.newHashMap();
        }
    }
    
    
    public void batchQueryDriverCountAsync(List<DriverVO> drvInfos, Boolean newProcess, String globalTraceId, CountDownLatch latch, Map<String, Object> contextHolder) {
        try {
            LoggerContext.newContext().withGlobalTraceId(Optional.ofNullable(globalTraceId).orElse(UUID.randomUUID().toString())).init();
            Map<Integer /*driverId*/, Integer /*司机接单数量*/> driverCountMap = batchQueryDriverCountAsync(drvInfos, newProcess);
            contextHolder.put("driverCountMap", driverCountMap);
        } finally {
            latch.countDown();
        }
    }
    
    public void checkDrivers(DspOrderVO dspOrder, List<DriverVO> drvInfos, List<TransportGroupVO> transportGroups, DuidVO duid, String globalTraceId, CountDownLatch latch, Map<String, Object> contextHolder) {
        try {
            LoggerContext.newContext().withGlobalTraceId(Optional.ofNullable(globalTraceId).orElse(UUID.randomUUID().toString())).init();
            List<CheckModel> checkModels = checkDrivers(dspOrder, drvInfos, transportGroups, duid);
            contextHolder.put("checkModels", checkModels);
        } finally {
            latch.countDown();
        }
    }
    
    private DuidVO initDuid(DspOrderVO order, List<TransportGroupVO> transportGroups) {
        Integer transportGroupMode = transportGroups.size() == 1 ? transportGroups.get(0).getTransportGroupMode().getCode() : ManualSubSkuConf.DEFAULT_TRANSPORT_GROUP_MODE;
        Integer subSkuId = manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, transportGroupMode, SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        return DuidVO.of(order.getDspOrderId(), subSkuId, DspType.VBK_ASSIGN.getCode(), TakenType.SUPPLIER.getCode());
    }

    private DriverCheckListResDTO page(OperateDriverCheckListCommand param, List<DriverCheckDTO> list) {
        DriverCheckListResDTO.PageInfo pageInfo = convertPageInfo(param.getPageNo(), list.size(), param.getPageSize());
        int fromIndex = Math.min((pageInfo.getPage() - 1) * pageInfo.getPageSize(), pageInfo.getTotalRecord());
        int toIndex = Math.min(pageInfo.getPage() * pageInfo.getPageSize(), pageInfo.getTotalRecord());
        return new DriverCheckListResDTO(list.subList(fromIndex, toIndex), pageInfo);
    }

    /**
     * @param page
     * @param count
     * @param pageSize
     * @return
     */
    private DriverCheckListResDTO.PageInfo convertPageInfo(Integer page, int count, Integer pageSize) {
        DriverCheckListResDTO.PageInfo pageInfo = new DriverCheckListResDTO.PageInfo();
        pageInfo.setPage(page);
        pageInfo.setTotalPage((int) Math.ceil(count * 1.0 / pageSize));
        pageInfo.setTotalRecord(count);
        pageInfo.setPageSize(pageSize);
        return pageInfo;
    }

    /**
     * 检查结果排序
     *
     * @return
     */
    public List<DriverCheckDTO> filterAndSort(List<DriverCheckDTO> list, DspOrderVO order, Integer available) {
        Long driverId = getDriverId(order);
        logger.info("filterAndSort", "filterAndSort START");
        Collator collator = Collator.getInstance(Locale.CHINA);
        if (available == YesOrNo.YES.getCode()) {
            // 车型关系  非携程单下单车型是0
            Map<Integer /*carTypeId*/, Integer /*与订单车型的等级关系*/> carRelationMap = Integer.valueOf(0).equals(order.getCarTypeId()) ? Maps.newHashMap() : queryCarLevelRelation(order.getCarTypeId());
            return list.stream()
                    .filter(dto -> !dto.getDriverId().equals(driverId) && dto.getCheckCode() >= 0)
                    .sorted((r1, r2) -> {
                        if (r2.getCheckCode() != null && r1.getCheckCode()!= null && !r1.getCheckCode().equals(r2.getCheckCode())) {
                            return Integer.compare(r2.getCheckCode(), r1.getCheckCode());   // 通过>部分通过>不通过
                        }
                        int c1 = carRelationMap.computeIfAbsent(r1.getCarTypeId(), v -> 0);
                        int c2 = carRelationMap.computeIfAbsent(r2.getCarTypeId(), v -> 0);
                        if (!Objects.equals(c1, c2)) {
                            return Integer.compare(c2, c1);   // 车型平派>车型上派
                        }
                        if (!Objects.equals(r1.getRaisingPickUp(), r2.getRaisingPickUp()) || !Objects.equals(r1.getChildSeat(), r2.getChildSeat())) { // 有附加服务的优先展示
                            int v1 = (r1.getRaisingPickUp() ? 2 : 0) + (r1.getChildSeat() ? 1 : 0);
                            int v2 = (r2.getRaisingPickUp() ? 2 : 0) + (r2.getChildSeat() ? 1 : 0);
                            return Integer.compare(v2, v1);
                        }
                        if (!Objects.equals(r1.getOrderCounts(), r2.getOrderCounts())) {
                            return Integer.compare(r1.getOrderCounts(), r2.getOrderCounts());     // 待执行订单从小到大排列
                        }

                        int driverLevel1 =  r1.getDriveLevel() == null ? 0 : r1.getDriveLevel();
                        int driverLevel2 =  r2.getDriveLevel() == null ? 0 : r2.getDriveLevel();
                        if (!Objects.equals(driverLevel1, driverLevel2)) {
                            return Integer.compare(driverLevel2,driverLevel1);     // 司机等级由高到低排列
                        }
                        return collator.compare(r1.getDriverName(), r2.getDriverName());  // 司机姓名首字母升序排列
                    }).collect(Collectors.toList());
        } else {
            return list.stream()
                    .filter(dto -> !dto.getDriverId().equals(driverId) && dto.getCheckCode() < 0)
                    .sorted((r1, r2) -> {
                        if (!Objects.equals(r1.getRaisingPickUp(), r2.getRaisingPickUp()) || !Objects.equals(r1.getChildSeat(), r2.getChildSeat())) { // 有附加服务的优先展示
                            int v1 = (r1.getRaisingPickUp() ? 2 : 0) + (r1.getChildSeat() ? 1 : 0);
                            int v2 = (r2.getRaisingPickUp() ? 2 : 0) + (r2.getChildSeat() ? 1 : 0);
                            return Integer.compare(v2, v1);
                        }

                        int driverLevel1 =  r1.getDriveLevel() == null ? 0 : r1.getDriveLevel();
                        int driverLevel2 =  r2.getDriveLevel() == null ? 0 : r2.getDriveLevel();
                        if (!Objects.equals(driverLevel1, driverLevel2)) {
                            return Integer.compare(driverLevel2, driverLevel1);     // 司机等级由高到低排列
                        }
                        return collator.compare(r1.getDriverName(), r2.getDriverName());  // 司机姓名首字母升序排列
                    }).collect(Collectors.toList());
        }
    }

    /**
     * 查询订单车型等级关系
     *
     * @param orderCarId
     * @return
     */
    public Map<Integer /*carTypeId*/, Integer /*与订单车型的等级关系*/> queryCarLevelRelation(Integer orderCarId) {
        logger.info("queryCarLevelRelation_" + orderCarId, "SOA queryCarTypeLevelRelations START");
        CarTypeLevelRelationsVO carTypeLevelRelationsVO = carTypeRelationGateway.queryCarTypeLevelRelations(orderCarId);
        if (null == carTypeLevelRelationsVO) {
            return Maps.newHashMap();
        }
        Map<Integer /*carTypeId*/, Integer /*与订单车型的等级关系*/> result = Maps.newHashMap();
        Map<Long, CarTypeLevelRelation> map = carTypeLevelRelationsVO.getOtherCarTypeRelationMap();
        for (Map.Entry<Long, CarTypeLevelRelation> entry : map.entrySet()) {
            if (Objects.equals(CarTypeLevelRelation.UNKOWN, entry.getValue())) {
                continue;
            }
            result.put(entry.getKey().intValue(), entry.getValue().getCode());
        }
        return result;
    }

    /**
     * 获取订单绑定的司机id
     */
    public Long getDriverId(DspOrderVO order) {
        if (null == order || StringUtils.isBlank(order.getDriverOrderId())) {
            return 0L;
        }
        QueryOrderDetailRequestType requestType = new QueryOrderDetailRequestType();
        requestType.setDriverOrderId(order.getDriverOrderId());
        DataSwitch dataSwitch = new DataSwitch();
        dataSwitch.setBaseDetailSwitch(Boolean.TRUE);
        requestType.setDataSwitch(dataSwitch);
        QueryOrderDetailResponseType responseType = selfOrderQueryServiceProxy.queryOrderDetail(requestType);
        return Optional.ofNullable(responseType)
                .map(QueryOrderDetailResponseType::getOrderDetail)
                .map(OrderDetail::getBaseDetail)
                .map(BaseDetail::getDrvId)
                .orElse(0L);

    }


    private List<DriverCheckDTO> convertDriverCheckList(String orderId,
                                                       List<CheckModel> list,
                                                       Map<Long, DriverVO> driverMap,
                                                       Map<Integer, Integer> driverCountMap,
                                                       Map<Long, TransportGroupVO> transportGroupInfoMap,
                                                       DuidVO duid) {
        return list.stream()
                .filter(checkResult -> null != checkResult.getModel().getDriver() && driverMap.containsKey(checkResult.getModel().getDriver().getDriverId()))
                .map(checkResult -> {
                    DspModelVO model = checkResult.getModel();
                    Long driverId = model.getDriver().getDriverId();
                    DriverVO drvInfo = driverMap.get(driverId);
                    CarVO car = drvInfo.getCar();
                    DriverCheckDTO dto = new DriverCheckDTO();
                    dto.setSubOrderId(orderId);
                    dto.setDuid(duid.toString());
                    CheckCode checkCode = checkResult.getCheckCode();
                    dto.setCheckCode(convrtToTaken(checkResult, duid));
                    dto.setCheckResultCode(checkCode.getCode());
                    dto.setCheckResult(checkCode.getDesc());
                    dto.setDriverId(driverId);
                    TransportGroupVO transportGroupInfo = getTransportGroupInfo(drvInfo, transportGroupInfoMap);
                    if (transportGroupInfo != null) {
                        dto.setTransportGroupName(transportGroupInfo.getTransportGroupName());
                        dto.setTransportGroupId(transportGroupInfo.getTransportGroupId().intValue());
                    }
                    dto.setCarId(car.getCarId().toString());
                    dto.setCarTypeId(car.getCarTypeId().intValue());
                    dto.setCarColor(car.getCarColor());
                    dto.setCarDesc(car.getCarBrandName());
                    dto.setCarLicense(car.getCarLicense());
                    dto.setCarTypeName(car.getCarTypeName());
                    dto.setDriverPhoneAreaCode(drvInfo.getDriverPhoneAreaCode());
                    dto.setDriverPhone(drvInfo.getDriverPhone());
                    dto.setDriverName(drvInfo.getDriverName());
                    dto.setWorkTime(JsonUtil.toJson(mergeDriverWorkTime(drvInfo.getWorkTimes().getPeriodStrs())));
                    dto.setDriverLanguage(drvInfo.getDriverLanguage());
                    dto.setBindCar(StringUtils.isNotBlank(car.getCarLicense()) ? "YES" : "NO");
                    int orderCounts = driverCountMap.get(driverId.intValue()) == null ? 0 : driverCountMap.get(driverId.intValue());
                    dto.setOrderCounts(orderCounts);
                    dto.setCarBrandSeriesName(String.join("", car.getCarBrandName() != null ? car.getCarBrandName() : "", car.getCarSeriesName() != null ? car.getCarSeriesName() : ""));
                    if (drvInfo.getRaisingPickUp() != null) {
                        dto.setRaisingPickUp(drvInfo.getRaisingPickUp());
                    }
                    if (drvInfo.getChildSeat() != null) {
                        dto.setChildSeat(drvInfo.getChildSeat());
                    }
                    dto.setIsAboard(drvInfo.getIsAbroad());
                    dto.setDriveLevel(drvInfo.getDriverLevel());
                    return dto;
                })
                .collect(Collectors.toList());
    }


    private List<DriverCheckDTO> convertDriverCheckListNonCtrip(String orderId,
                                                                List<DriverVO> list,
                                                        Map<Integer, Integer> driverCountMap,
                                                        DuidVO duid) {
        return list.stream()
                .map(drvInfo -> {
                    CarVO car = drvInfo.getCar();
                    DriverCheckDTO dto = new DriverCheckDTO();
                    dto.setSubOrderId(orderId);
                    dto.setDuid(duid.toString());
                    Integer checkCodeInt = drvInfo.getDriverStatus().equals(1) ?  ToTakenEnum.PASS.getCode(): ToTakenEnum.NO_PASS.getCode();
                    CheckCode checkCode = ToTakenEnum.PASS.getCode().equals(checkCodeInt) ? CheckCode.PASS : CheckCode.DRIVER_NO_ACTIVE;
                    dto.setCheckCode(checkCodeInt);
                    dto.setCheckResultCode(checkCode.getCode());
                    dto.setCheckResult(checkCode.getDesc());
                    dto.setDriverId(drvInfo.getDriverId());
                    dto.setCarId(car.getCarId().toString());
                    dto.setCarTypeId(car.getCarTypeId().intValue());
                    dto.setCarColor(car.getCarColor());
                    dto.setCarDesc(car.getCarBrandName());
                    dto.setCarLicense(car.getCarLicense());
                    dto.setCarTypeName(car.getCarTypeName());
                    dto.setDriverPhoneAreaCode(drvInfo.getDriverPhoneAreaCode());
                    dto.setDriverPhone(drvInfo.getDriverPhone());
                    dto.setDriverName(drvInfo.getDriverName());
                    dto.setWorkTime(JsonUtil.toJson(mergeDriverWorkTime(drvInfo.getWorkTimes().getPeriodStrs())));
                    dto.setDriverLanguage(drvInfo.getDriverLanguage());
                    dto.setBindCar(StringUtils.isNotBlank(car.getCarLicense()) ? "YES" : "NO");
                    int orderCounts = driverCountMap.get(drvInfo.getDriverId().intValue()) == null ? 0 : driverCountMap.get(drvInfo.getDriverId().intValue());
                    dto.setOrderCounts(orderCounts);
                    dto.setCarBrandSeriesName(String.join("", car.getCarBrandName() != null ? car.getCarBrandName() : "", car.getCarSeriesName() != null ? car.getCarSeriesName() : ""));
                    if (drvInfo.getRaisingPickUp() != null) {
                        dto.setRaisingPickUp(drvInfo.getRaisingPickUp());
                    }
                    if (drvInfo.getChildSeat() != null) {
                        dto.setChildSeat(drvInfo.getChildSeat());
                    }
                    dto.setIsAboard(drvInfo.getIsAbroad());
                    if(CollectionUtils.isNotEmpty(drvInfo.getTransportGroups())){
                        dto.setTransportGroupId(drvInfo.getTransportGroups().get(0).getTransportGroupId().intValue());
                        dto.setTransportGroupName(drvInfo.getTransportGroups().get(0).getTransportGroupName());
                    }
                    dto.setDriveLevel(drvInfo.getDriverLevel());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 司机绑定的运力组与反查的运力组取交集
     * @param drvInfo
     * @param transportGroupInfoMap
     * @return
     */
    public TransportGroupVO getTransportGroupInfo(DriverVO drvInfo, Map<Long, TransportGroupVO> transportGroupInfoMap) {
        if (CollectionUtils.isNotEmpty(drvInfo.getTransportGroups())) {
            for (TransportGroupVO transportGroup : drvInfo.getTransportGroups()) {
                if (transportGroupInfoMap.containsKey(transportGroup.getTransportGroupId())) {
                    return transportGroupInfoMap.get(transportGroup.getTransportGroupId());
                }
            }
        }
        return null;
    }

    /**
     * 设置接单标志
     * @param checkResult
     * @param duid
     * @return
     */
    public Integer convrtToTaken(CheckModel checkResult, DuidVO duid) {
        Long diffMinute = DateUtil.getDiffMinute(new Date(), checkResult.getModel().getOrder().getEstimatedUseTimeBj());
        if (duid != null && !checkResult.getCheckCode().isPass()) {
            // 强制指派判断
            List<String> values = subSkuEnforcePassConfig.get(Lists.newArrayList(new SubSkuEnforcePassConfig.Key(duid.getSubSkuId()))).stream().map(SubSkuEnforcePassConfig.Value::getCheckCode).collect(Collectors.toList());
            if (values.contains(String.valueOf(checkResult.getCheckCode().getCode())) &&
                    diffMinute < commonConfConfig.getInteger(ConfigKey.FORCE_ASSIGN_TIME_DIFF, 720)) {
                return ToTakenEnum.FORCED_ASSIGNMENT.getCode();
            } else {
                // 检查失败并且不是强制通过枚举的，标识设置为-1返回给vbk
                return ToTakenEnum.NO_PASS.getCode();
            }
        } else {
            return ToTakenEnum.PASS.getCode();
        }
    }

    /**
     * 合并司机的工作时间
     * @param segWorkTime
     * @return
     */
    private static List<String> mergeDriverWorkTime(List<String> segWorkTime) {
        if (CollectionUtils.isEmpty(segWorkTime)) {
            return Lists.newArrayList();
        }
        List<String> res = Lists.newArrayList();
        try {
            // 把时间转化为时间类型
            List<List<Date>> times = segWorkTime.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(s -> {
                        List<Date> ans = new ArrayList<>();
                        try {
                            List<String> list = TIME_SPLITTER.splitToList(s);
                            if (list.size() == 2) {
                                ans = list.stream().map(h -> DateUtil.parse(h, DateUtil.TIME_FORMAT)).collect(Collectors.toList());
                            }
                        } catch (Exception e) {
                            logger.error("mergeDriverWorkTime_error", "segWorkTime:" + s, e);
                        }
                        return ans;
                    })
                    .filter(CollectionUtils::isNotEmpty)
                    .sorted(Comparator.comparing(v -> v.get(0)))
                    .collect(Collectors.toList());
            // 根据开始时间进行排序
            List<Date> dates = times.get(0);
            String start = DateUtil.getHourMinStr(dates.get(0));
            for (int i = 1; i < times.size(); i++) {
                Instant date = dates.get(1).toInstant().plus(1, ChronoUnit.MINUTES);
                if (date.isBefore(times.get(i).get(0).toInstant())) {
                    String s = DateUtil.getHourMinStr(dates.get(0)) + "~" + DateUtil.getHourMinStr(dates.get(1));
                    res.add(s);
                    dates = times.get(i);
                } else {    // 工作时间连续，则合并
                    if (dates.get(1).before(times.get(i).get(1))) {
                        dates.set(1, times.get(i).get(1));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(res) && "23:59".equals(DateUtil.getHourMinStr(dates.get(1))) && "00:00".equals(start)) {    //跨天
                String replace = res.get(0).replace("00:00", DateUtil.getHourMinStr(dates.get(0)));
                res.set(0, replace);
            } else {
                String s = DateUtil.getHourMinStr(dates.get(0)) + "~" + DateUtil.getHourMinStr(dates.get(1));
                res.add(s);
            }
        } catch (Exception e) {
            logger.error("mergeDriverWorkTime_error", "segWorkTime:" + JsonUtil.toJson(segWorkTime), e);
        }
        return res;
    }

    /**
     * 查询司机待执行订单数量
     */
    public Map<Integer, Integer> queryDriverCount(List<DriverVO> drvInfos,Boolean newProcess) {
        List<Integer> driverIds = drvInfos.stream()
                .map(drv -> drv.getDriverId().intValue())
                .collect(Collectors.toList());
        QueryDrvTakenNumRequestType requestType = new QueryDrvTakenNumRequestType();
        requestType.setDriverIds(driverIds);
        requestType.setContainAllOrderSource(newProcess);
        QueryDrvTakenNumResponseType responseType = selfOrderQueryServiceProxy.queryDrvTakenNum(requestType);
        if (responseType == null || CollectionUtils.isEmpty(responseType.getDrvTakenModels())) {
            return Collections.emptyMap();
        }
        return responseType.getDrvTakenModels().stream()
                .collect(Collectors.toMap(DrvTakenModel::getDrvId, DrvTakenModel::getTakenNum));
    }

    /**
     * drvInfos分批处理，100个一批
     * 每一批使用线程池executorService，异步调用queryDriverCount()方法
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=3499325360
     * @param drvInfos
     * @param newProcess
     * @return
     */
    public Map<Integer, Integer> batchQueryDriverCountAsync(List<DriverVO> drvInfos,Boolean newProcess) {
        List<List<DriverVO>> partition = Lists.partition(drvInfos, 100);
        Map<Integer, Integer> result = Maps.newHashMap();
        List<Future<Map<Integer, Integer>>> futures = Lists.newArrayList();
        for (List<DriverVO> list : partition) {
            Future<Map<Integer, Integer>> future = executorService.submit(() -> queryDriverCount(list, newProcess));
            futures.add(future);
        }
        for (Future<Map<Integer, Integer>> future : futures) {
            try {
                Map<Integer, Integer> map = Maps.newHashMap();
                if (future != null) {
                    map = future.get(2000, TimeUnit.MILLISECONDS);
                }
                if (MapUtils.isNotEmpty(map)) {
                    result.putAll(map);
                }
            } catch (Exception e) {
                logger.warn("batchQueryDriverCountAsync_error", e);
            }
        }
        return result;
    }

    /**
     * 手动检查
     *
     * @param dspOrder
     * @param drivers
     */
    public List<CheckModel> checkDrivers(DspOrderVO dspOrder, List<DriverVO> drivers, List<TransportGroupVO> transportGroups, DuidVO duid) {
        SubSkuVO subSku = subSkuRepository.find(duid.getSubSkuId());
        DspCheckCommand command = CollectionUtils.isNotEmpty(transportGroups) ? new DspCheckCommand(dspOrder, subSku, drivers, transportGroups.get(0), duid) : new DspCheckCommand(dspOrder, subSku, drivers, duid);
        return checkService.check(command);
    }

    /**
     * 过滤运力组
     *
     * @param param
     * @param transportGroups
     * @param drvInfos
     * @return
     */
    private List<TransportGroupVO> filterTransportGroup(OperateDriverCheckListCommand param, List<TransportGroupVO> transportGroups, List<DriverVO> drvInfos) {
        if (CollectionUtils.isEmpty(transportGroups)) {
            return Collections.emptyList();
        }
        // 取参数中的运力组id与司机的运力组和订单可服务的运力组交集
        if (StringUtils.isNotBlank(param.getDriverName()) || StringUtils.isNotBlank(param.getDriverPhone())) {
            // 根据司机过滤。司机的运力组与订单运力组取交集
            Set<Long> set = Sets.newHashSet();
            drvInfos.forEach(d -> set.addAll(d.getTransportGroups().stream().map(TransportGroupVO::getTransportGroupId).collect(Collectors.toSet())));
            transportGroups = transportGroups.stream()
                    .filter(t -> set.contains(t.getTransportGroupId()))
                    .collect(Collectors.toList());
        }
        if (param.getTransportGroupId() != null) {    // 根据运力组id过滤
            transportGroups = transportGroups.stream()
                    .filter(tg -> param.getTransportGroupId().equals(tg.getTransportGroupId().intValue()))
                    .collect(Collectors.toList());
        }
        return transportGroups;
    }

}
