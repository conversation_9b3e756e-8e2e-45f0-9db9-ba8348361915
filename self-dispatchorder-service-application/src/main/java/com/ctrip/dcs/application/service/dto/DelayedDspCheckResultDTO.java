package com.ctrip.dcs.application.service.dto;

import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * 订单是否延后派检查结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/2 11:39:34
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DelayedDspCheckResultDTO {
    /* 是否延后派司机 */
    private boolean isDelayedDispatch = false;// 默认false
    /* 订单确认类型 */
    private ConfirmType orderConfirmType;
    /* 最晚派车时间(当地) */
    private String lastConfirmCarTime;
    /* 最晚派车时间(北京) */
    private String lastConfirmCarTimeBj;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DelayedDspCheckResultDTO)) return false;
        DelayedDspCheckResultDTO that = (DelayedDspCheckResultDTO) o;
        return isDelayedDispatch() == that.isDelayedDispatch() && getOrderConfirmType() == that.getOrderConfirmType() && Objects.equals(getLastConfirmCarTime(), that.getLastConfirmCarTime()) && Objects.equals(getLastConfirmCarTimeBj(), that.getLastConfirmCarTimeBj());
    }

    @Override
    public int hashCode() {
        return Objects.hash(isDelayedDispatch(), getOrderConfirmType(), getLastConfirmCarTime(), getLastConfirmCarTimeBj());
    }
}
