package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.DriverOrderConfirmFailCommand;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DriverOrderConfirmFailExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(DriverOrderConfirmFailExeCmd.class);

    /**
     * 取消司机单CODE
     */
    protected static final int CANCEL_DRIVER_ORDER_CODE = 100001;

    /**
     * 取消司机单角色 2-系统
     */
    protected static final int CANCEL_DRIVER_ORDER_ROLE = 2;

    /**
     * 取消司机单原因-接单失败取消
     */
    protected static final String CANCEL_DRIVER_ORDER_REASON = "confirm fail";

    @Autowired
    protected DriverOrderGateway driverOrderGateway;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    public void execute(DriverOrderConfirmFailCommand command) {
        DspOrderVO dspOrderVO = queryDspOrderService.queryBase(command.getDspOrderId());
        if (dspOrderVO != null && Objects.equals(dspOrderVO.getDriverOrderId(), command.getDriverOrderId())) {
            logger.info("DriverOrderConfirmFailInfo", "dspOrderId:{}, driverOrderId:{}", command.getDspOrderId(), command.getDriverOrderId());
            return;
        }
        driverOrderGateway.cancel(
                command.getDspOrderId(),
                command.getDriverOrderId(),
                command.getDriverId(),
                CANCEL_DRIVER_ORDER_ROLE,
                CANCEL_DRIVER_ORDER_CODE,
                CANCEL_DRIVER_ORDER_REASON, Boolean.TRUE,false
        );
    }
}
