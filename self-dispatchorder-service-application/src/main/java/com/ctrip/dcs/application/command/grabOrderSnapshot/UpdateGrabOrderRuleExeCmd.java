package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.UpdateGrabOrderRuleCommand;
import com.ctrip.dcs.application.command.api.UpdateGrabOrderSnapshotCommand;
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand;
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDetailDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderDetailPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.handler.DispatcherGrabOrderPushRuleHandler;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class UpdateGrabOrderRuleExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabOrderRuleExeCmd.class);

    @Autowired
    UpdateGrabOrderSnapshotExeCmd updateGrabOrderSnapshotExeCmd;

    @Autowired
    private DispatcherGrabOrderPushRuleHandler dispatcherGrabOrderPushRuleHandler;

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @Autowired
    private UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DspOrderDao dspOrderDao;

    @Autowired
    private DspOrderDetailDao dspOrderDetailDao;

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    public void execute(UpdateGrabOrderRuleCommand command) {
        try {
            List<DspOrderPO> dspOrderPOList = dspOrderDao.queryDspOrdersBySupplierId(command.getSupplierId(), command.getCityId(), command.getCategoryCodes(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode());
            if (CollectionUtils.isEmpty(dspOrderPOList)) {
                return;
            }
            List<String> dspOrderIds = dspOrderPOList.stream().map(DspOrderPO::getDspOrderId).distinct().toList();
            List<GrabDspOrderSnapshotDO> snapshots = grabDspOrderSnapshotRepository.query(dspOrderIds, command.getSupplierId(), Lists.newArrayList(GrabDspOrderSnapshotStatusEnum.INIT.getCode(), GrabDspOrderSnapshotStatusEnum.GRAB.getCode()));
            if (CollectionUtils.isNotEmpty(snapshots)) {
                for (GrabDspOrderSnapshotDO snapshot : snapshots) {
                    updateGrabOrderSnapshotExeCmd.execute(snapshot, GrabDspOrderSnapshotEventEnum.CHANGE_GRAB_RULE);
                }
            }
            // 发单规则变更，未创建快照的调度确认订单，创建调度
            Set<String> snapshotDspOrderIds = Optional.ofNullable(snapshots).orElse(Collections.emptyList()).stream().map(GrabDspOrderSnapshotDO::getDspOrderId).collect(Collectors.toSet());
            List<DspOrderPO> list = dspOrderPOList.stream().filter(po -> !snapshotDspOrderIds.contains(po.getDspOrderId())).toList();
            for (DspOrderPO po : list) {
                createSchedule(po);
            }
        } catch (Exception e) {
            logger.warn("UpdateGrabOrderSnapshotError", e);
            throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
        }
    }

    public void createSchedule(DspOrderPO po) {
        try {
            logger.info("ExecuteDispatcherConfirmOrderInfo", "createSchedule, dspOrderId: {}", po.getDspOrderId());
            GrabDspOrderPushRuleDO rule = dispatcherGrabOrderPushRuleHandler.queryGrabOrderPushRule(po.getSupplierId().longValue(), po.getCityId().longValue(), po.getCategoryCode(), po.getVehicleGroupId().longValue());
            if (Objects.isNull(rule)) {
                logger.info("ExecuteDispatcherConfirmOrderInfo", "rules is empty, supplierId: {}, dspOrderId: {}", po.getSupplierId(), po.getDspOrderId());
                return;
            }
            DspOrderDetailPO dspOrderDetailPO = dspOrderDetailDao.find(po.getDspOrderId());
            if (Objects.isNull(dspOrderDetailPO)) {
                logger.info("ExecuteDispatcherConfirmOrderInfo", "dspOrderDetail is empty, dspOrderId: {}", po.getDspOrderId());
                return;
            }
            HashMap<String, Object> extendInfo = JacksonSerializer.INSTANCE().deserialize(dspOrderDetailPO.getExtendInfo(), new TypeReference<HashMap<String, Object>>() {
            });
            if (!extendInfo.containsKey("preDriverGuideAmount") || !extendInfo.containsKey("preDriverGuideCurrency")) {
                Integer integer = MapUtils.getInteger(extendInfo, "settleToDriver");
                updateOrderSettlementBeforeTakenCmd.execute(new UpdateOrderSettlementBeforeTakenCommand(po.getUserOrderId(), po.getDspOrderId(), po.getVehicleGroupId(), po.getCategoryCode(), po.getSupplierId().longValue(), po.getSpId(), po.getOrderSourceCode(), OrderVersionEnum.CTRIP.getVersion(), integer, po.getCityId()));
            }
            // 若发单规则不为空，则创建调度
            createScheduleExeCmd.execute(new CreateScheduleCommand(po.getDspOrderId(), ScheduleEventType.VBK_GRAB_SYSTEM));
        } catch (Exception e) {
            logger.warn("ExecuteDispatcherConfirmOrderError", e);
        }
    }
}
