package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.feature.impl.AbstractFeature;
import com.ctrip.dcs.domain.schedule.sort.feature.impl.NearOrderFeature;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Component
public class TestNearOrderSortFeature implements ITestDspOrderService{
    @Autowired
    @Qualifier("sortConfig")
    private ConfigService sortConfig;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private DspContextService dspContextService;
    @Override
    public String test(Map<String, String> params) {
        boolean near = "true".equals(params.get("nearOrderFlag"));
        Date startTime = near?new Date():LocalDateUtils.addDays(new Date(),2);
        AbstractFeature nearOrderFeature = new NearOrderFeature();
        DspOrderVO dspOrderVO = queryDspOrderService.queryOrderDetail(params.get("dspOrderId"));
        dspOrderVO.setEstimatedUseTimeBj(startTime);
        SortModel sortModel = new SortModel(new DspModelVO(dspOrderVO,new DriverVO(3452498L)));
        DspContext dspContext = new DspContext(dspContextService);
        SortContext context = new SortContext(dspOrderVO,dspContext,sortConfig);
        if(context.getDriverEmptyDurationMap() != null){
            context.getDriverEmptyDurationMap().put(3452498L,600D);
        }
        List<Value> values = nearOrderFeature.value(Arrays.asList(sortModel),context);
        return LocalJsonUtils.toJson(values);
    }
}
