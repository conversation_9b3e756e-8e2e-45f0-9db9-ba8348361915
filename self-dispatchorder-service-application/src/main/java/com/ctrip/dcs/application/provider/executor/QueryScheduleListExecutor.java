package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryScheduleListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryScheduleListResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ScheduleInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/7 11:31
 */
@Component
@ServiceLogTagPair(key = "dspOrderId")
public class QueryScheduleListExecutor extends AbstractRpcExecutor<QueryScheduleListRequestType, QueryScheduleListResponseType> {
    private static Logger logger = LoggerFactory.getLogger(QueryScheduleListExecutor.class);
    
    @Resource
    ExecuteScheduleExeCmd cmd;
    
    @Override
    public QueryScheduleListResponseType execute(QueryScheduleListRequestType requestType) {
        QueryScheduleListResponseType responseType = new QueryScheduleListResponseType();
        try {
            if (StringUtils.isBlank(requestType.getDspOrderId())) {
                throw new BizException("dspOrderId is null");
            }
            List<ScheduleInfo> list = cmd.queryScheduleList(requestType.getDspOrderId());
            responseType.setList(list);
            return ServiceResponseUtils.success(responseType);
        } catch (Exception ex) {
            logger.error("query_scheduleList_error_" + requestType.getDspOrderId(), ex);
            return ServiceResponseUtils.fail(responseType);
        }
    }
}
