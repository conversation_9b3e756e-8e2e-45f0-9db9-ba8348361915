package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CheckDriverTiredLimitExeCmd;
import com.ctrip.dcs.application.command.api.CheckDriverTiredLimitCommand;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverTiredLimitVO;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDriverTiredLimitRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDriverTiredLimitResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CheckDriverTiredLimitDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class CheckDriverTiredLimitExecutor extends AbstractRpcExecutor<CheckDriverTiredLimitRequestType, CheckDriverTiredLimitResponseType> implements Validator<CheckDriverTiredLimitRequestType> {

    @Autowired
    private CheckDriverTiredLimitExeCmd cmd;

    @Override
    public CheckDriverTiredLimitResponseType execute(CheckDriverTiredLimitRequestType requestType) {
        CheckDriverTiredLimitCommand command = toCheckDriverTiredLimitCommand(requestType);
        List<DriverTiredLimitVO> list = cmd.execute(command);
        CheckDriverTiredLimitResponseType responseType = toCheckDriverTiredLimitResponseType(list);
        return ServiceResponseUtils.success(responseType);
    }

    private CheckDriverTiredLimitCommand toCheckDriverTiredLimitCommand(CheckDriverTiredLimitRequestType requestType) {
        Date estimatedUseTime = DateUtil.parseDateStr2Date(requestType.getEstimatedUseTime());
        Set<Long> driverIds = Sets.newHashSet(requestType.getDriverIds());
        return new CheckDriverTiredLimitCommand(estimatedUseTime, driverIds);
    }

    private CheckDriverTiredLimitResponseType toCheckDriverTiredLimitResponseType(List<DriverTiredLimitVO> list) {
        CheckDriverTiredLimitResponseType responseType = new CheckDriverTiredLimitResponseType();
        if (CollectionUtils.isNotEmpty(list)) {
            List<CheckDriverTiredLimitDTO> driverTiredLimitList = list.stream().map(vo -> {
                CheckDriverTiredLimitDTO dto = new CheckDriverTiredLimitDTO();
                dto.setDriverId(vo.getDriverId());
                dto.setIsLimit(vo.getTired());
                return dto;
            }).collect(Collectors.toList());
            responseType.setDriverTiredLimitList(driverTiredLimitList);
        }
        return responseType;
    }

    @Override
    public void validate(AbstractValidator<CheckDriverTiredLimitRequestType> validator) {
        validator.ruleFor("estimatedUseTime").notNull().notEmpty();
        validator.ruleFor("driverIds").notNull().notEmpty();
    }
}
