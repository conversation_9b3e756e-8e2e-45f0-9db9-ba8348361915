package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.RightAndPointCmd;
import com.ctrip.dcs.application.provider.converter.ReDispatchedConverter;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.infrastructure.adapter.monitoring.SelfDspOrderMetric;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointResponseType;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询改派权益
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/5/16 19:35
 */
@Component
@ServiceLogTagPair(key = "userOrderId")
public class RightAndPointExecutor extends AbstractRpcExecutor<RightAndPointRequestType, RightAndPointResponseType> implements Validator<RightAndPointRequestType> {

    @Resource
    private RightAndPointCmd rightAndPointCmd;

    @Override
    public RightAndPointResponseType execute(RightAndPointRequestType req) {
        RightAndPointResponseType responseType = new RightAndPointResponseType();
        Result<RightAndPointVO> rightRes = rightAndPointCmd.execute(ReDispatchedConverter.buildCommand(req));
        if (rightRes.isSuccess() && rightRes.getData() != null) {
            SelfDspOrderMetric.selfDspOrderRightAndPointResultRecordInc(Boolean.TRUE);
            ReDispatchedConverter.assembleResp(responseType, rightRes.getData());
            return ServiceResponseUtils.success(responseType);
        }
        SelfDspOrderMetric.selfDspOrderRightAndPointResultRecordInc(Boolean.FALSE);
        return ServiceResponseUtils.fail(responseType, rightRes.getCode(), rightRes.getMsg());
    }

    @Override
    public void validate(AbstractValidator<RightAndPointRequestType> validator) {
        validator.ruleFor("roleId").notNull().notEmpty();
        validator.ruleFor("userOrderId").notNull().notEmpty();
        validator.ruleFor("reasonDetailId").notNull().notEmpty();
    }

}