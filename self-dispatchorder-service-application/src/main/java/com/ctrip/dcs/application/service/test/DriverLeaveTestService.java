package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.IDriverLeaveEventHandler;
import com.ctrip.dcs.application.event.dto.DriverLeaveEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverLeaveTestService")
public class DriverLeaveTestService implements ITestDspOrderService{
    @Autowired
    private IDriverLeaveEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        Long driverId = Long.valueOf(params.get("driverId"));
        String startTime = params.get("startTime");
        String endTime = params.get("endTime");
        String accountType = params.get("accountType");
        DriverLeaveEvent event = new DriverLeaveEvent(driverId,startTime,endTime,accountType);
        boolean result = handler.handle(event, null);
        return String.valueOf(result);
    }
}
