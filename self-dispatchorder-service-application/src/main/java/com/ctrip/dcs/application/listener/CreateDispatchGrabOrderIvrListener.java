package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.api.DispatcherGrabOrderIvrCommand;
import com.ctrip.dcs.application.command.dispatchergrab.DispatcherGrabOrderIvrExeCmd;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

@Component
public class CreateDispatchGrabOrderIvrListener {


    @Autowired
    private DispatcherGrabOrderIvrExeCmd dispatcherGrabOrderIvrExeCmd;

    @QmqConsumer(prefix = EventConstants.DCS_DSP_DISPATCH_GRAB_ORDER_IVR_CREATE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "userOrderId", alias = "userOrderId")
    public void onMessage(Message message) {
        Long dispatcherGrabOrderId = message.getLongProperty("dispatcherGrabOrderId");
        if (dispatcherGrabOrderId != null) {
            DispatcherGrabOrderIvrCommand command = new DispatcherGrabOrderIvrCommand(dispatcherGrabOrderId);
            dispatcherGrabOrderIvrExeCmd.execute(command);
        }
    }

}
