package com.ctrip.dcs.application.provider.executor.dispatchergrab;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class CancelDispatcherGrabOrdersExecutor extends AbstractRpcExecutor<CancelDispatcherGrabOrdersRequestType, CancelDispatcherGrabOrdersResponseType> implements Validator<CancelDispatcherGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(CancelDispatcherGrabOrdersExecutor.class);

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cmd;

    @Override
    public CancelDispatcherGrabOrdersResponseType execute(CancelDispatcherGrabOrdersRequestType requestType) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(requestType.getUserOrderId(), requestType.getCancelScene(),requestType.getModifyVersion(),requestType.getCancelReason(),requestType.getCancelReasonDes());
            cmd.execute(command);
            return ServiceResponseUtils.success(new CancelDispatcherGrabOrdersResponseType());
        } catch (BizException e) {
            logger.error("CancelDispatcherGrabOrdersExecutorError", e);
            return ServiceResponseUtils.fail(new CancelDispatcherGrabOrdersResponseType(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("CancelDispatcherGrabOrdersExecutorError", e);
            return ServiceResponseUtils.fail(new CancelDispatcherGrabOrdersResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<CancelDispatcherGrabOrdersRequestType> validator) {
        validator.ruleFor("userOrderId").notNull().notEmpty();
    }
}
