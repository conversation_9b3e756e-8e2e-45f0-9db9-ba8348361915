package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.impl.ConflictRedispatchEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
public class TestConflictRedispatchEventHandlerService implements ITestDspOrderService {
    @Autowired
    private ConflictRedispatchEventHandler handler;
    @Override
    public String test(Map<String, String> params) {
        String driverId = params.get("driverId");
        String redispatchDspOrderId = params.get("redispatchDspOrderId");
        String conflictDspOrderId = params.get("conflictDspOrderId");
        String redispatchDriverOrderId = params.get("redispatchDriverOrderId");
        String redispatchUserOrderId = params.get("redispatchUserOrderId");
        boolean result = handler.redispatch(driverId,redispatchDspOrderId,conflictDspOrderId,redispatchDriverOrderId,redispatchUserOrderId);
        return String.valueOf(result);
    }
}
