package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.api.CancelGrabOrderSnapshotCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class CancelGrabOrderSnapshotExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CancelGrabOrderSnapshotExeCmd.class);

    public void execute(CancelGrabOrderSnapshotCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, command.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
            }
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(command.getDspOrderId());
            if (snapshot == null) {
                return;
            }
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(command.getDspOrderId());
            updateGrabDspOrderSnapshot(snapshot);
            // 更是司机抢单大厅
            updateOtherGrabDriverIndex(dspOrder);
        } catch (Exception e) {
            logger.warn("UpdateGrabOrderSnapshotError", e);
            throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }

    public void updateGrabDspOrderSnapshot(GrabDspOrderSnapshotDO snapshot) {
        if (Objects.isNull(snapshot) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.SUCCESS)) {
            return;
        }
        List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(snapshot.getDspOrderId());
        // 取消
        snapshot.cancel(indexes);
        // 更新db
        saveOrUpdate(snapshot, indexes);
    }
}
