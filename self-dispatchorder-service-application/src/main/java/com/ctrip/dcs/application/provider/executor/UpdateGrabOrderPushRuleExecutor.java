package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class UpdateGrabOrderPushRuleExecutor extends AbstractRpcExecutor<UpdateGrabOrderPushRuleRequestType, UpdateGrabOrderPushRuleResponseType> implements Validator<UpdateGrabOrderPushRuleRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabOrderPushRuleExecutor.class);
    @Autowired
    private IGrabOrderPushRuleService grabOrderPushRuleService;
    @Override
    public UpdateGrabOrderPushRuleResponseType execute(UpdateGrabOrderPushRuleRequestType requestType) {
        try{
            boolean result = grabOrderPushRuleService.update(convertToDTO(requestType));
            if(result){
                return ServiceResponseUtils.success(new UpdateGrabOrderPushRuleResponseType());
            }
            return ServiceResponseUtils.fail(new UpdateGrabOrderPushRuleResponseType());
        }catch (BizException e){
            logger.warn("UpdateGrabOrderPushRuleExecutorExp","UpdateGrabOrderPushRuleExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new UpdateGrabOrderPushRuleResponseType(), e.getCode(), e.getMessage());
        }catch (Exception e){
            logger.error("UpdateGrabOrderPushRuleExecutorExp","UpdateGrabOrderPushRuleExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new UpdateGrabOrderPushRuleResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<UpdateGrabOrderPushRuleRequestType> validator) {
        validator.ruleFor("rule").notNull();
        validator.ruleFor("operateUser").notNull().notEmpty();
    }
    /**
     * 封装参数
     * @param requestType
     * @return
     */
    public GrabOrderPushRuleDTO convertToDTO(UpdateGrabOrderPushRuleRequestType requestType){
        GrabOrderPushRuleSoaDTO soaDTO = requestType.getRule();
        GrabOrderPushRuleDTO ruleDTO = new GrabOrderPushRuleDTO();
        ruleDTO.setId(soaDTO.getId());
        ruleDTO.setCityId(soaDTO.getCityId());
        ruleDTO.setRuleType(soaDTO.getRuleType());
        ruleDTO.setCategoryCode(soaDTO.getCategoryCode());
        ruleDTO.setEndBookTime(soaDTO.getEndBookTime());
        ruleDTO.setStartBookTime(soaDTO.getStartBookTime());
        ruleDTO.setBookTime(soaDTO.getBookTime());
        ruleDTO.setFixedPushTime(soaDTO.getFixedPushTime());
        ruleDTO.setImmediatePushTime(soaDTO.getImmediatePushTime());
        ruleDTO.setSupplierId(soaDTO.getSupplierId());
        ruleDTO.setSupplierName(soaDTO.getSupplierName());
        ruleDTO.setVehicleGroupIdList(soaDTO.getVehicleGroupIdList());
        ruleDTO.setOperateUser(requestType.getOperateUser());
        ruleDTO.setPriority(soaDTO.getPriority() == null ? StringUtils.EMPTY : JacksonSerializer.INSTANCE().serialize(soaDTO.getPriority()));
        ruleDTO.setRewards(soaDTO.getRewards() == null ? StringUtils.EMPTY : JacksonSerializer.INSTANCE().serialize(soaDTO.getRewards()));
        return ruleDTO;
    }
}
