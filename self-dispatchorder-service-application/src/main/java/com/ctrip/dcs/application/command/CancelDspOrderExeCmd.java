package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand;
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter;
import com.ctrip.dcs.application.provider.converter.CancelOldSelfOrderConverter;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.*;
import com.ctrip.dcs.domain.dsporder.event.DriverOrderStatusDelayCheckEvent;
import com.ctrip.dcs.domain.dsporder.event.DspOrderCancelEvent;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.adapter.http.OldSelfOrderClient;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class CancelDspOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CancelDspOrderExeCmd.class);


    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderOperateRepository dspOrderOperateRepository;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private OldSelfOrderClient oldSelfOrderClient;

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private DriverOrderGateway driverOrderGateway;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;


    public void execute(CancelDspOrderCommand cmd) throws SQLException {
        checkArgs(cmd);
        DspOrderDO dspOrderDO = dspOrderRepository.queryByDspOrderId(cmd.getDspOrderId());
        if (dspOrderDO == null) {
            Cat.logEvent("CancelDspOrderExeCmdDspOrder", "Empty");
            cancelSelfOldOrder(cmd);
            logger.info("cancel_dsp_order_info", "The dsp order is null,userOrderId=" + cmd.getUserOrderId());
            return;
        }
        Integer orderStatus = dspOrderDO.getOrderStatus();
        if (OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus())) {
            logger.info("cancel_dsp_order_info", "The order status does not conform,userOrderId=" + cmd.getUserOrderId());
            return;
        }

        //状态检查：客服取消状态宽松，用户取消状态严格(需要查司机单状态)，other:状态不允许取消
        checkLegalStatus(cmd, dspOrderDO);
        cmd.setOrderCurrentStatus(orderStatus);

        dspOrderDO.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getCode());
        DspOrderFeeDO dspOrderFeeDO = CancelDspOrderConverter.buildDspOrderFeeDO(cmd, dspOrderDO);
        DspOrderCancelDO cancelDO = CancelDspOrderConverter.buildDspOrderCancelDO(cmd);
        cancelDO.setDspOrderId(dspOrderDO.getDspOrderId());
        //更新状态为取消、更新费用、插入取消记录、更新接单记录为无效
        dspOrderOperateRepository.cancelDspOrderHandle(dspOrderDO, dspOrderFeeDO, cancelDO);
        //取消司机单
        cancelDriverOrder(dspOrderDO, cmd, needHoldOnDriverOrder(cmd));

        //发送客服日志
        WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.cancelDspOrderWorkBenchLog(dspOrderDO);
        workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);

        String cancelReason = StringUtils.isBlank(cmd.getCancelReason()) ? ""+cmd.getCancelReasonId():cmd.getCancelReason();
        //发送取消消息,发送取消记录消息
        messageProvider.send(new DspOrderCancelEvent(dspOrderDO.getDspOrderId(), cmd.getUserOrderId(), dspOrderDO.getDriverOrderId(), cmd.getCancelReasonId(), cmd.getCancelRole(), cancelReason, CancelTypeEnum.FORCE.getCode().equals(cmd.getCheckType()), 0L, needHoldOnDriverOrder(cmd)));
        // 延迟检查
        messageProvider.send(new DriverOrderStatusDelayCheckEvent(dspOrderDO.getDspOrderId(), dspOrderDO.getDriverOrderId()));

        //5. 构建操作日志,写入ck
        this.sendVBKOperationRecord(dspOrderDO, cmd,orderStatus);
        MetricsUtil.recordValue("cancel.dsporder.count",getMetricsTags(dspOrderDO,"200",String.valueOf(cancelDO.getCancelReasonId())));

    }

    protected boolean needHoldOnDriverOrder(CancelDspOrderCommand cmd) {
        return cmd.getOriModifyCancelScene() != null && cmd.getOriModifyCancelScene().equals(JntModifyOrderHandleScene.NEED_HOLD_DRIVER_ORDER.getType());
    }

    private Map<String, String> getMetricsTags(DspOrderDO dspOrderDO, String resCode,String cancelReasonId) {
        Map<String, String> tags = new HashMap<>();
        try {
            tags.put("bizAreaType", String.valueOf(dspOrderDO.getBizAreaType()));
            tags.put("categoryCode", dspOrderDO.getCategoryCode());
            tags.put("cancelReasonId", cancelReasonId);
            tags.put("resCode", resCode);
        } catch (Exception e) {
            logger.warn("getMetricsTags_error", e);
        }
        return tags;
    }

    private void checkArgs(CancelDspOrderCommand cmd) {
        if (StringUtils.isBlank(cmd.getCancelReason())) {
            String cancelReason = businessTemplateInfoConfig.getCancelReasonMap().get(cmd.getCancelReasonId().toString());
            cmd.setCancelReason(cancelReason);
        }
    }


    public void sendVBKOperationRecord(DspOrderDO dspOrderDO, CancelDspOrderCommand cmd,Integer orderStatus) {
        dspOrderDO.setOrderStatus(orderStatus);

        try {
            String comment = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(cmd.getCancelReason())) {
                comment = String.format(SysConstants.OperationRecordLog.COMM_CANCEL_REASON, cmd.getCancelReason());
            }
            if (cmd.getCancelReasonId().equals(CancelReasonIdEnum.BUFFER_CHANGE_CANCEL.getCode())) {
                comment = SysConstants.OperationRecordLog.BUFFER_CANCEL_REASON;
            }
            Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderDO.getCityId());
            VBKOperationRecordVO record = vbkOperationRecordFactory.createCancelDspOrderOperationRecord(dspOrderDO
                    , OperateUserInfoDTO.buildInstance(SystemOperateUserType.SYSTEM.type, SystemOperateUserType.SYSTEM.desc, SystemOperateUserType.SYSTEMUSER)
                    ,comment,localTimeNow);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    private void checkLegalStatus(CancelDspOrderCommand cmd,DspOrderDO dspOrderDO) {
        if (CancelTypeEnum.FORCE.getCode().equals(cmd.getCheckType())) {
            return;
        }
        BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetail(cmd.getUserOrderId(), cmd.getDspOrderId());
        if (CancelRoleEnum.USERCANCEL.getCode().equals(cmd.getCancelRole()) && baseDetailVO.getOrderStatus() > DriverOrderStatusEnum.ARRIVE.getCode()
        ) {
            MetricsUtil.recordValue("cancel.dsporder.count",getMetricsTags(dspOrderDO,ErrorCode.ERROR_STATE_CHG.getCode(),String.valueOf(cmd.getCancelReasonId())));
            throw ErrorCode.ERROR_STATE_CHG.getBizException();
        }
    }

    private void cancelSelfOldOrder(CancelDspOrderCommand cmd) {
        boolean f = false;
        cmd.setqCancelUrlId(QCancelUrlIdEnum.self_cancel.getCode());
        if (QCancelUrlIdEnum.self_cancel.getCode().equals(cmd.getqCancelUrlId())) {
            f = oldSelfOrderClient.cancelSelfOrder(CancelOldSelfOrderConverter.converter(cmd));
        }
        if (!f) {
            MetricsUtil.recordValue(MetricsConstants.CANCEL_SELF_OLD_ORDER_ERROR_COUNT);
            throw ErrorCode.SERVER_ERROR.getBizException();
        }
    }

    private void cancelDriverOrder(DspOrderDO dspOrderDO, CancelDspOrderCommand cmd, boolean needHoldOnDriverOrder) {
        try {
            driverOrderGateway.cancel(dspOrderDO.getDspOrderId(), dspOrderDO.getDriverOrderId(), null, cmd.getCancelRole(), cmd.getCancelReasonId(), cmd.getCancelReason(), CancelTypeEnum.FORCE.getCode().equals(cmd.getCheckType()), needHoldOnDriverOrder);
        } catch (Exception e) {
            logger.error("cancelDriverOrder_ex", e);
        }
    }

}
