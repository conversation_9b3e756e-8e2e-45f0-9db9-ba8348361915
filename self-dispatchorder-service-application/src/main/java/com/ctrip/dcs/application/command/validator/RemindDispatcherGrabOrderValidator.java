package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class RemindDispatcherGrabOrderValidator {

    private static final Logger logger = LoggerFactory.getLogger(RemindDispatcherGrabOrderValidator.class);

    public boolean validate(DispatcherGrabOrderDO order, DspOrderVO dspOrderVO, TransportGroupVO transportGroupVO) {
        if (!order.isInit()) {
            logger.info("RemindDispatcherGrabOrderExeCmdInfo", "order is not init, dspOrderId={}, supplierId={}", order.getDspOrderId(), order.getSupplierId());
            return false;
        }
        if (dspOrderVO == null) {
            logger.info("RemindDispatcherGrabOrderExeCmdInfo", "dspOrder is null, dspOrderId={}, supplierId={}", order.getDspOrderId(), order.getSupplierId());
            return false;
        }
        if (!dspOrderVO.isDispatching() && !Objects.equals(SupplierConfirmSceneEnum.ORI_MODIFY.getScene(), order.getConfirmScene())) {
            logger.info("RemindDispatcherGrabOrderExeCmdInfo", "dspOrder is not dispatching, dspOrderId={}, supplierId={}", order.getDspOrderId(), order.getSupplierId());
            return false;
        }
        if (transportGroupVO == null) {
            logger.info("RemindDispatcherGrabOrderExeCmdInfo", "transportGroup is null, dspOrderId={}, supplierId={}", order.getDspOrderId(), order.getSupplierId());
            return false;
        }
        return true;
    }
}
