package com.ctrip.dcs.application.listener.grab.record;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.enums.GrabSettlementEnum;
import com.ctrip.dcs.domain.common.enums.SettleToDriverEnum;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component(value = VBKGrabOperationTypeEnum.CREATE_SERVICE)
public class CreateGrabTaskOperateTaskRecord extends AbstractOperateTaskRecord{

    private static final Logger logger = LoggerFactory.getLogger(CreateGrabTaskOperateTaskRecord.class);


    @Override
    protected String buildRecordContent(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        logger.info("CreateGrabTaskOperateTaskRecord_enter", JsonUtils.toJson(vbkGrabTaskOperateDTO));
        String content = String.format(map.getString(DRIVER_NUM, DRIVER_NUM_STR), vbkGrabTaskOperateDTO.getDriverNum());

        GrabSettlementEnum grabSettlementEnum = GrabSettlementEnum.getByType(vbkGrabTaskOperateDTO.getInitialType()).orElse(null);
        if(grabSettlementEnum != null){
            if(Objects.equals(vbkGrabTaskOperateDTO.getPayForDriver(), SettleToDriverEnum.SETTLE_TO_DRIVER.getCode())){
                content += String.format(map.getString(INIT_NUM, INIT_NUM_STR), grabSettlementEnum.getDesc(), "");
            }else{
                if(!grabSettlementEnum.equals(GrabSettlementEnum.DISPLAY)) {
                    String value = vbkGrabTaskOperateDTO.getInitialValue() == null ? vbkGrabTaskOperateDTO.getInitialRate() + "%" : vbkGrabTaskOperateDTO.getInitialValue().toString();
                    content += String.format(map.getString(INIT_NUM, INIT_NUM_STR), grabSettlementEnum.getDesc(), value);
                }
            }
        }

        content += String.format(map.getString(GRAB_LIMIT, GRAB_LIMIT_STR), vbkGrabTaskOperateDTO.getGrabEndTime());

        GrabSettlementEnum rewardsGrabSettlementEnum = GrabSettlementEnum.getByType(vbkGrabTaskOperateDTO.getRewardsType()).orElse(null);
        if(rewardsGrabSettlementEnum != null){
            if(!rewardsGrabSettlementEnum.equals(GrabSettlementEnum.DISPLAY)){
                String value = vbkGrabTaskOperateDTO.getRewardsValue() == null ? vbkGrabTaskOperateDTO.getRewardsRate() + "%" : vbkGrabTaskOperateDTO.getRewardsValue().toString();
                content += String.format(map.getString(REWARD_NUM, REWARD_NUM_STR), rewardsGrabSettlementEnum.getDesc(), value);
            }
        }


        if(Objects.nonNull(vbkGrabTaskOperateDTO.getMakeUpEffectTime())){
            content += String.format(map.getString(REWARD_LIMIT, REWARD_LIMIT_STR), vbkGrabTaskOperateDTO.getMakeUpEffectTime());
        }
        logger.info("CreateGrabTaskOperateTaskRecord_exit", content);
        return content;
    }
}
