package com.ctrip.dcs.application.listener.grab.record;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.enums.GrabSettlementEnum;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Component(value = VBKGrabOperationTypeEnum.UPDATE_SERVICE)
public class UpdateGrabTaskOperateTaskRecord extends AbstractOperateTaskRecord{

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabTaskOperateTaskRecord.class);

    @Override
    protected String buildRecordContent(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        logger.info("UpdateGrabTaskOperateTaskRecord_enter", JsonUtils.toJson(vbkGrabTaskOperateDTO));
        String content = String.format(map.getString(UPDATE_DRIVER_NUM, UPDATE_DRIVER_NUM_STR), vbkGrabTaskOperateDTO.getOriginDriverNum(), vbkGrabTaskOperateDTO.getDriverNum());

        Integer initialType = vbkGrabTaskOperateDTO.getInitialType();
        BigDecimal initialValue = vbkGrabTaskOperateDTO.getInitialValue();
        BigDecimal initialRate = vbkGrabTaskOperateDTO.getInitialRate();

        Integer initialTypeOrigin = vbkGrabTaskOperateDTO.getInitialTypeOrigin();
        BigDecimal initialValueOrigin = vbkGrabTaskOperateDTO.getInitialValueOrigin();
        BigDecimal initialRateOrigin = vbkGrabTaskOperateDTO.getInitialRateOrigin();
        content += buildInitOrRewardValue(UPDATE_INIT_NUM, UPDATE_INIT_NUM_STR, initialType, initialTypeOrigin, initialValue, initialValueOrigin, initialRate, initialRateOrigin);

        String grabEndTime = vbkGrabTaskOperateDTO.getGrabEndTime();
        String grabEndTimeOrigin = vbkGrabTaskOperateDTO.getGrabEndTimeOrigin();
        content += buildGrabTime(UPDATE_GRAB_LIMIT, UPDATE_GRAB_LIMIT_STR, grabEndTime, grabEndTimeOrigin);


        Integer rewardsType = vbkGrabTaskOperateDTO.getRewardsType();
        BigDecimal rewardsValue = vbkGrabTaskOperateDTO.getRewardsValue();
        BigDecimal rewardsRate = vbkGrabTaskOperateDTO.getRewardsRate();

        Integer rewardsTypeOrigin = vbkGrabTaskOperateDTO.getRewardsTypeOrigin();
        BigDecimal rewardsValueOrigin = vbkGrabTaskOperateDTO.getRewardsValueOrigin();
        BigDecimal rewardsRateOrigin = vbkGrabTaskOperateDTO.getRewardsRateOrigin();
        content += buildInitOrRewardValue(UPDATE_REWARD_NUM, UPDATE_REWARD_NUM_STR, rewardsType, rewardsTypeOrigin, rewardsValue, rewardsValueOrigin, rewardsRate, rewardsRateOrigin);



        String makeUpEffectTimeOrigin = vbkGrabTaskOperateDTO.getMakeUpEffectTimeOrigin();
        String makeUpEffectTime = vbkGrabTaskOperateDTO.getMakeUpEffectTime();
        content += buildGrabTime(UPDATE_REWARD_LIMIT, UPDATE_REWARD_LIMIT_STR, makeUpEffectTime, makeUpEffectTimeOrigin);
        logger.info("UpdateGrabTaskOperateTaskRecord_exit", content);
        return content;
    }


    public String buildGrabTime(String key, String defaultKey, String grabEndTime, String grabEndTimeOrigin) {
        if(StringUtils.isBlank(grabEndTime) && StringUtils.isBlank(grabEndTimeOrigin)){
            return StringUtils.EMPTY;
        } else if (StringUtils.isBlank(grabEndTime) || StringUtils.isBlank(grabEndTimeOrigin)) {
            String defaultStr = map.getString(DISPLAY, DISPLAY_STR);
            grabEndTime = StringUtils.isBlank(grabEndTime) ? defaultStr : grabEndTime;
            grabEndTimeOrigin = StringUtils.isBlank(grabEndTimeOrigin) ? defaultStr : grabEndTimeOrigin;
            return String.format(map.getString(key, defaultKey), grabEndTimeOrigin, grabEndTime);
        }else if(new BigDecimal(grabEndTime).compareTo(new BigDecimal(grabEndTimeOrigin)) != 0){
            return String.format(map.getString(key, defaultKey), grabEndTimeOrigin, grabEndTime);
        }
        return StringUtils.EMPTY;
    }


    public String buildInitOrRewardValue(String key, String defaultKey, Integer type, Integer typeOrigin, BigDecimal value, BigDecimal valueOrigin, BigDecimal rate, BigDecimal rateOrigin){
        GrabSettlementEnum grabSettlementEnumOrigin = GrabSettlementEnum.getByType(typeOrigin).orElse(null);
        if(type.equals(typeOrigin)){
            if(grabSettlementEnumOrigin != null && !grabSettlementEnumOrigin.equals(GrabSettlementEnum.DISPLAY)){
                if(GrabSettlementEnum.VALUE.equals(grabSettlementEnumOrigin) && value.compareTo(valueOrigin) != 0){
                    return String.format(map.getString(key, defaultKey), grabSettlementEnumOrigin.getDesc() + valueOrigin, grabSettlementEnumOrigin.getDesc() + value);
                } else if (GrabSettlementEnum.RATE.equals(grabSettlementEnumOrigin)) {
                    if(Objects.nonNull(rate) && Objects.nonNull(rateOrigin)){
                        if(rateOrigin.compareTo(rate) != 0){
                            return String.format(map.getString(key, defaultKey), grabSettlementEnumOrigin.getDesc() + rateOrigin + "%", grabSettlementEnumOrigin.getDesc() + rate + "%");
                        }
                    }else if(Objects.nonNull(rate) || Objects.nonNull(rateOrigin)){
                        return String.format(map.getString(key, defaultKey), grabSettlementEnumOrigin.getDesc() + ((Objects.nonNull(rateOrigin) ? rateOrigin : "")) + "%", grabSettlementEnumOrigin.getDesc() + (Objects.nonNull(rate) ? rate : "") + "%");
                    }
                }
            }
        }else{
            String valueOriginTemp = map.getString(DISPLAY, DISPLAY_STR), valueTemp = map.getString(DISPLAY, DISPLAY_STR);
            if(grabSettlementEnumOrigin != null){
                if(grabSettlementEnumOrigin.equals(GrabSettlementEnum.VALUE)) {
                    String valueOriginStr = valueOrigin.toString();
                    valueOriginTemp = grabSettlementEnumOrigin.getDesc() + valueOriginStr;
                } else if (grabSettlementEnumOrigin.equals(GrabSettlementEnum.RATE)) {
                    valueOriginTemp = grabSettlementEnumOrigin.getDesc() +  (Objects.nonNull(rateOrigin) ? rateOrigin : "") + "%" ;
                }
            }
            GrabSettlementEnum grabSettlementEnum = GrabSettlementEnum.getByType(type).orElse(null);

            if(grabSettlementEnum != null){
                if(grabSettlementEnum.equals(GrabSettlementEnum.VALUE)) {
                    String valueStr = value.toString();
                    valueTemp = grabSettlementEnum.getDesc() + valueStr;
                } else if (grabSettlementEnum.equals(GrabSettlementEnum.RATE)) {
                    valueTemp = grabSettlementEnum.getDesc() +  (Objects.nonNull(rate) ? rate : "") + "%" ;
                }
            }
            return String.format(map.getString(key, defaultKey), valueOriginTemp, valueTemp);
        }
        return StringUtils.EMPTY;
    }
}
