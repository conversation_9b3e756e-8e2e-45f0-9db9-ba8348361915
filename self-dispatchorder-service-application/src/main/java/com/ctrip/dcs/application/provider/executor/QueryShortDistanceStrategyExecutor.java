package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd;
import com.ctrip.dcs.application.command.dto.QueryShortDistanceStrategyResDTO;
import com.ctrip.dcs.application.provider.converter.ShortDistanceStrategyConverter;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryShortDistanceStrategyExecutor extends AbstractRpcExecutor<QueryShortDistanceStrategyRequestType, QueryShortDistanceStrategyResponseType> implements Validator<QueryShortDistanceStrategyRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryShortDistanceStrategyExecutor.class);

    @Autowired
    private ShortDistanceStrategyExeCmd strategyExeCmd;

    @Autowired
    private ShortDistanceStrategyConverter shortDistanceStrategyConverter;

    @Override
    public QueryShortDistanceStrategyResponseType execute(QueryShortDistanceStrategyRequestType requestType) {
        try {
            QueryShortDistanceStrategyResDTO resDTO = strategyExeCmd.query(shortDistanceStrategyConverter.convert(requestType));
            return ServiceResponseUtils.success(shortDistanceStrategyConverter.convert(resDTO));
        } catch (Exception e) {
            logger.error("QueryShortDistanceStrategyExecutor_Exp", e);
            return ServiceResponseUtils.fail(new QueryShortDistanceStrategyResponseType());
        }
    }
}
