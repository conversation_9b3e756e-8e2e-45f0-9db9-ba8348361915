package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dto.DispatcherGrabSubmitResDTO;
import com.ctrip.dcs.application.service.grab.SubmitDispatcherGrabOrderService;
import com.ctrip.dcs.application.service.grab.SubmitDispatcherGrabOrderServiceContext;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SubmitDispatcherGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SubmitDispatcherGrabOrderExeCmd.class);

    private static final String DISPATCHER_GRAB_ORDER_SUBMIT_PREFIX = SysConstants.APP_ID + "_DISPATCHER_GRAB_ORDER_SUBMIT_%s";

    @Autowired
    private DistributedLockService distributedLockService;

    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private SubmitDispatcherGrabOrderServiceContext submitDispatcherGrabOrderServiceContext;

    public Result<DispatcherGrabSubmitResDTO> execute(SubmitDispatcherGrabOrderCommand command) {
        DistributedLockService.DistributedLock lock = getLock(command);
        try {
            if (!lock.tryLock()) {
                throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_LOCK_ERROR.getBizException();
            }
            // 判断订单来源
            SubmitDispatcherGrabOrderService service = submitDispatcherGrabOrderServiceContext.get(command);
            if (Objects.isNull(service)) {
                throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
            }
            Cat.logEvent("dcs.self.dispatcher.grab.order.submit.type", command.getScene() + "");
            // 执行对应抢单操作
            service.submit(command);
            // 构建返回数据
            DispatcherGrabSubmitResDTO dispatcherGrabSubmitResDTO = Optional.ofNullable(command.getPkId())
                    .map(id -> dispatcherGrabOrderGateway.query(id, command.getSupplierId()))
                    .map(DO -> {
                        DispatcherGrabSubmitResDTO resDTO = new DispatcherGrabSubmitResDTO();
                        resDTO.setGrabStatus(DO.getGrabStatus().getCode());
                        resDTO.setId(DO.getId());
                        return resDTO;
                    })
                    .orElse(new DispatcherGrabSubmitResDTO());
            return Result.Builder.<DispatcherGrabSubmitResDTO>newResult().withData(dispatcherGrabSubmitResDTO).success().build();
        } catch (DistributedLockRejectedException e) {
            logger.error("SubmitDispatcherGrabOrderExeCmdError", e);
            throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }


    private DistributedLockService.DistributedLock getLock(SubmitDispatcherGrabOrderCommand command) {
        return distributedLockService.getLock(generateKey(command.getUserOrderId()));
    }

    public String generateKey(String userOrderId) {
        return String.format(DISPATCHER_GRAB_ORDER_SUBMIT_PREFIX, userOrderId);
    }

}
