package com.ctrip.dcs.application.command.dto;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;

import java.util.List;

public class DriverCheckListResDTO {

    /**
     * 检查结果集合
     */
    private List<DriverCheckDTO> list;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 分页条数
     */
    private int pageSize;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 总记录数
     */
    private int totalSize;

    /**
     * 品类编码
     * 这里应该控制不能为错误的值且不能为空
     */
    private String categoryCode;

    public DriverCheckListResDTO() {
    }

    public DriverCheckListResDTO(Integer pageNo, Integer pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public DriverCheckListResDTO(List<DriverCheckDTO> list, PageInfo pageInfo) {
        this.list = list;
        this.pageNo = pageInfo.getPage();
        this.pageSize = pageInfo.getPageSize();
        this.totalPages = pageInfo.getTotalPage();
        this.totalSize = pageInfo.getTotalRecord();
    }

    public List<DriverCheckDTO> getList() {
        return list;
    }

    public void setList(List<DriverCheckDTO> list) {
        this.list = list;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public Integer getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(Integer totalSize) {
        this.totalSize = totalSize;
    }

    public static class PageInfo {

        private int page; // 页数

        private int totalPage; // 总页数

        private int totalRecord; // 总记录数

        private int pageSize; // 每页大小

        public Integer getPage() {
            return page;
        }

        public void setPage(Integer page) {
            this.page = page;
        }

        public Integer getTotalPage() {
            return totalPage;
        }

        public void setTotalPage(Integer totalPage) {
            this.totalPage = totalPage;
        }

        public Integer getTotalRecord() {
            return totalRecord;
        }

        public void setTotalRecord(Integer totalRecord) {
            this.totalRecord = totalRecord;
        }

        public Integer getPageSize() {
            return pageSize;
        }

        public void setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
        }
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }
}
