package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.processor.CreateDriverChangeRecordProcessor;
import com.ctrip.dcs.application.processor.PushSettlementProcessor;
import com.ctrip.dcs.application.service.DriverCarConfirmedService;
import com.ctrip.dcs.application.service.DriverConfirmedService;
import com.ctrip.dcs.application.service.SpContractInfoService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.infrastructure.common.constants.PushSettlementTypeEnum;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@Component
public class DriverAndCarConfirmListener {

    @Autowired
    private DriverCarConfirmedService driverCarConfirmedService;

    @Autowired
    private SpContractInfoService spContractInfoService;

    @Autowired
    private DriverConfirmedService driverConfirmedService;

    @Autowired
    private DspDrvOrderLimitTakenRecordGateway dspDrvOrderLimitTakenRecordGateway;

    @Autowired
    CreateDriverChangeRecordProcessor createDriverChangeRecordProcessor;

    @Autowired
    PushSettlementProcessor pushSettlementProcessor;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        driverCarConfirmedService.confirmed(dspOrderId,  confirmRecordId);
//        spContractInfoService.updateContractInfo(dspOrderId);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = EventConstants.DRIVER_CHANGE_RECORD_CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onDriverChangeRecordMessage(Message message) {
        // 原应单记录id为空 非改派司机场景
        long originalConfirmRecordId = message.getLongProperty("originalConfirmRecordId");
        if( originalConfirmRecordId == 0L ) {
            return;
        }
        int statusMachineEventCode = message.getIntProperty("statusMachineEventCode");
        // 只有当供应商更改司机时才需要记录司机变更记录 eventCode: 21
        if( OrderStatusEvent.VBK_UPDATE_DRIVER.getCode() == statusMachineEventCode ) {
            // 获取新应单记录id
            long newConfirmRecordId = message.getLongProperty("confirmRecordId");
            // 执行真正落表逻辑
            createDriverChangeRecordProcessor.process(originalConfirmRecordId, newConfirmRecordId);
        }
    }

    /**
     * 推送结算消息
     * @param message
     * @throws SQLException
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = EventConstants.PUSH_SETTLEMENT_CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onPushSettlementMessage(Message message) throws SQLException {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        String driverOrderId = message.getStringProperty("driverOrderId");
        pushSettlementProcessor.process(dspOrderId, confirmRecordId, driverOrderId, PushSettlementTypeEnum.SUPPLY_ORDER_TAKEN);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "self_order_contract", idempotentChecker = "redisIdempotentChecker")
    public void contractInfoUpdate(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        spContractInfoService.updateContractInfo(dspOrderId);
    }

    @QmqLogTag(tagKeys = {"confirmRecordId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "self_driver_Level", idempotentChecker = "redisIdempotentChecker")
    public void contractUpdateDrvLevel(Message message) {
        Long driverId = message.getLongProperty("driverId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");

        if(driverId == null || confirmRecordId == null){
            return;
        }
        driverConfirmedService.contractUpdateDrvLevel(driverId,confirmRecordId);
        dspDrvOrderLimitTakenRecordGateway.takenOrder(confirmRecordId);
    }
}
