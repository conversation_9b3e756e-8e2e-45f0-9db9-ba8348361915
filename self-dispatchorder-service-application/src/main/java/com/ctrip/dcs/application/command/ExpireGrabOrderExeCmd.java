package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ExpireGrabOrderCommand;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.process.impl.RefuseAssignTransportProcess;
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ExpireGrabOrderExeCmd {
    private static final Logger logger = LoggerFactory.getLogger(ExpireGrabOrderExeCmd.class);


    @Autowired
    private BroadcastRepository broadcastRepository;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    public void execute(ExpireGrabOrderCommand command) {
        try{
            GrabOrderDO order = broadcastRepository.find(command.getDuid(), command.getDriverId());
            if (isExpire(command, order)) {
                broadcastRepository.delete(command.getDuid(), command.getDriverId());
                grabOrderDetailRepository.deleteByDuidAndDriverId(command.getDuid(), command.getDriverId());
            }
            order = grabCentreRepository.find(command.getDspOrderId(), command.getDriverId());
            if (isExpire(command, order)) {
                grabCentreRepository.delete(command.getDspOrderId(), command.getDriverId());
                grabOrderDetailRepository.delete(command.getDspOrderId(), command.getDriverId());
            }
        }catch (Exception ex){
            Map<String, String> map = new HashMap<>();
            map.put("command", JsonUtil.toJson(command));
            logger.error("ExpireGrabOrderExeCmd_execute", ex, map);
        }
    }

    private boolean isExpire(ExpireGrabOrderCommand command, GrabOrderDO order) {
        if (Objects.isNull(order)) {
            return false;
        }
        return order.getExpire() <= command.getExpire();
    }
}
