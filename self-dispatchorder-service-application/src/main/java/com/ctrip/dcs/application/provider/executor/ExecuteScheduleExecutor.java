package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd;
import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "scheduleId")
public class ExecuteScheduleExecutor extends AbstractRpcExecutor<ExecuteScheduleRequestType, ExecuteScheduleResponseType> implements Validator<ExecuteScheduleRequestType> {

    @Autowired
    private ExecuteScheduleExeCmd cmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Override
    public ExecuteScheduleResponseType execute(ExecuteScheduleRequestType requestType) {
        ScheduleDO scheduleDO = scheduleRepository.find(requestType.getScheduleId());
        ExecuteScheduleCommand command = new ExecuteScheduleCommand(scheduleDO.getDspOrderId(), scheduleDO.getScheduleId(), scheduleDO.getRound() + 1, ScheduleEventType.SCHEDULE_RETRY_JOB);
        cmd.execute(command);
        return ServiceResponseUtils.success(new ExecuteScheduleResponseType());
    }

    @Override
    public void validate(AbstractValidator<ExecuteScheduleRequestType> validator) {
        validator.ruleFor("scheduleId").notNull();
    }
}
