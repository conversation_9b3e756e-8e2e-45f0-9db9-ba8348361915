package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd;
import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.CalculateDriverMileageProfitEventType;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Optional;

/**
 * 派发单完成消息
 *
 * <AUTHOR>
 */
@Component
public class DspOrderDoneListener {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderDoneListener.class);


    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;


    @Autowired
    private CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DONE_TOPIC, consumerGroup = "100041593_message", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        logger.info("DspOrderDoneListener.onMessage begin", JacksonUtil.serialize(message));
        String dspOrderId = message.getStringProperty("dspOrderId");
        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        try {
            DspOrderVO dspOrderVO = queryDspOrderService.queryBase(dspOrderId);
            DspOrderConfirmRecordVO dspOrderConfirmRecordDO = dspOrderConfirmRecordRepository.find(dspOrderVO.getConfirmRecordId());
            // 计算里程价值
            calculateDriverMileageProfit(dspOrderVO, dspOrderConfirmRecordDO);
        } catch (Exception e) {
            logger.error("DspOrderDoneListener.error", e);
            throw e;
        }
    }

    private void calculateDriverMileageProfit(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecordDO) {
        try {
            if (dspOrder != null && confirmRecordDO != null && confirmRecordDO.getDriverInfo() != null) {
                Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : 0L;
                DriverVO driverVO = queryDriverService.queryDriver(confirmRecordDO.getDriverInfo().getDriverId(), CategoryUtils.selfGetParentType(dspOrder), supplierId);
                DriverWorkTimeVO driverWorkTimeVO = DriverWorkTimeUtil.getDriverWorkTime(driverVO, dspOrder.getEstimatedUseTime());
                if (driverWorkTimeVO == null) {
                    return;
                }
                List<DriverMileageProfitTypeVO> driverMileageProfitType = Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY, DriverMileageProfitTypeVO.COMPLETE);
                CalculateDriverMileageProfitCommand command = new CalculateDriverMileageProfitCommand(driverVO, driverWorkTimeVO, driverMileageProfitType, dspOrder.getDspOrderId(), CalculateDriverMileageProfitEventType.DSP_ORDER_DONE);
                calculateDriverMileageProfitExeCmd.execute(command);
            }
        } catch (Exception e) {
            logger.warn("calculate_driver_mileage_profit_error", e);
        }
    }

}
