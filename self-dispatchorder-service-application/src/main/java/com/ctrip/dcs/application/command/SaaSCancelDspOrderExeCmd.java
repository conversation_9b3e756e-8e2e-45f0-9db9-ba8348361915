package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand;
import com.ctrip.dcs.application.provider.converter.SaaSCancelDspOrderConverter;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.constants.VbkOperateRecordConstant;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderCancelDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.Date;
import java.util.Objects;

@Component
public class SaaSCancelDspOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SaaSCancelDspOrderExeCmd.class);
    private static final String CANCEL_ORDER = "CANCEL_ORDER";

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderOperateRepository dspOrderOperateRepository;

    @Autowired
    private DriverOrderGateway driverOrderGateway;

    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;

    @Autowired
    protected DistributedLockService distributedLockService;
    @Resource
    VBKOperationRecordGateway vbkOperationRecordGateway;
    @Resource
    BusinessTemplateInfoConfig businessTemplateInfoConfig;


    public void execute(CancelDspOrderCommand cmd) throws SQLException {
        logger.info("SaaSCancelDspOrderExeCmd_execute", LocalJsonUtils.toJson(cmd));
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, cmd.getDspOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            DspOrderDO dspOrderDO = dspOrderRepository.find(cmd.getDspOrderId());
            if(Objects.isNull(dspOrderDO)){
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            if (OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus())) {
                logger.info("SaaSCancelDspOrderExeCmd_execute_valid_ORDER_CANCEL", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }
            if (OrderStatusEnum.ORDER_FINISH.getCode().equals(dspOrderDO.getOrderStatus())) {
                logger.info("SaaSCancelDspOrderExeCmd_execute_valid_ORDER_FINISH", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }

            if (dspOrderDO.getOrderSourceCode().equals(OrderSourceCodeEnum.TRIP.getCode())) {
                logger.info("SaaSCancelDspOrderExeCmd_execute_valid_OrderSourceCode", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.CHECK_VALIDATE_ORDER_SOURCE_TYPE_ERROR.getBizException();
            }

            if (!dspOrderDO.getSupplierId().toString().equals(cmd.getSupplierId().toString())) {
                logger.info("SaaSCancelDspOrderExeCmd_execute_valid_SupplierId", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.CHECK_VALIDATE_SUPPLIERID_ERROR.getBizException();
            }
            //获取当前订单状态
            Integer currentOrderStatus = dspOrderDO.getOrderStatus();
            dspOrderDO.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getCode());

            DspOrderCancelDO cancelDO = SaaSCancelDspOrderConverter.buildDspOrderCancelDO(cmd);

            //更新状态为取消、更新费用、插入取消记录、更新接单记录为无效
            dspOrderOperateRepository.cancelDspOrderHandle(dspOrderDO, null, cancelDO);
            //取消司机单
            cancelDriverOrder(dspOrderDO, cmd);
            //保存操作历史日志
            insertSaasCancelOrderOperationRecord(dspOrderDO, cancelDO, currentOrderStatus);

        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        }catch (BizException e){
            throw e;
        } catch (Exception e) {
            logger.error(e);
            throw ErrorCode.SERVER_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }
    
    private void insertSaasCancelOrderOperationRecord(DspOrderDO dspOrderDO, DspOrderCancelDO cancelDO, Integer currentOrderStatus) {
        try {
            VBKOperationRecordVO recordVO = buildCancelOrderVBKOperationRecordVO(dspOrderDO, cancelDO, currentOrderStatus, new Date().getTime());
            vbkOperationRecordGateway.record(recordVO);
        } catch(Exception ex) {
            logger.error("insertSaasCancelOrderOperationRecord", "insert operate record is failed.",ex, Maps.newHashMap());
        }
    }
    
    private VBKOperationRecordVO buildCancelOrderVBKOperationRecordVO(DspOrderDO dspOrderDO, DspOrderCancelDO cancelDO, Integer currentOrderStatus, Long operateLocalTime) {
        String supplierDesc = businessTemplateInfoConfig.getOperateUserNameDesc(CommonConstants.SUPPLIER_OPERATE_NAME_DESC);
        return VBKOperationRecordVO.builder().supplyOrderId(dspOrderDO.getDspOrderId()).userOrderId(dspOrderDO.getUserOrderId()).vendorOrderId(dspOrderDO.getDriverOrderId()).sysUserAccount(cancelDO.getOperator()).operUserName(String.format(supplierDesc, cancelDO.getOperator())).operUserType("systemUser").operateType(CANCEL_ORDER).operateName(businessTemplateInfoConfig.getVbkOperationName(CANCEL_ORDER))
                .operateLocalTime(operateLocalTime).recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW).beforeOperateData(String.valueOf(currentOrderStatus)).afterOperateData(String.valueOf(OrderStatusEnum.ORDER_CANCEL.getCode())).comment(StringUtils.EMPTY).supplierId(dspOrderDO.getSupplierId()).orderSourceCode(String.valueOf(dspOrderDO.getOrderSourceCode())).build();
    }
    
    private void cancelDriverOrder(DspOrderDO dspOrderDO, CancelDspOrderCommand cmd) {
        try {
            driverOrderGateway.saasCancel(dspOrderDO.getDspOrderId(), dspOrderDO.getDriverOrderId(), null, cmd.getCancelRole(), cmd.getCancelReasonId(), cmd.getCancelReason());
        } catch (Exception e) {
            logger.error("SaaSCancelDspOrderExeCmd_cancelDriverOrder_ex", e);
            MetricsUtil.recordValue(MetricsConstants.SAAS_CANCEL_DRIVER_ORDER_ERROR_COUNT);
            throw ErrorCode.SAAS_CANCEL_DRIVER_ORDER_EXCEPTION.getBizException();
        }
    }

}
