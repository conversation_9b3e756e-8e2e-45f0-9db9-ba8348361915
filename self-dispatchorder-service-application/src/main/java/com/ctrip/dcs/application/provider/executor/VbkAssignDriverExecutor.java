package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.VbkAssignDriverExeCmd;
import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 * vbk指派
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class VbkAssignDriverExecutor extends AbstractRpcExecutor<VbkAssignDriverRequestType, VbkAssignDriverResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(VbkAssignDriverExecutor.class);

    @Autowired
    private VbkAssignDriverExeCmd vbkAssignDriverExeCmd;

    @Override
    public VbkAssignDriverResponseType execute(VbkAssignDriverRequestType requestType) {
        VbkAssignDriverResponseType responseType = new VbkAssignDriverResponseType();
        if (!validatePrm(requestType)) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

        }
        try {
            vbkAssignDriverExeCmd.execute(CustomerAssignDriverConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            logger.error("vbkAssignDriverException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


    private boolean validatePrm(VbkAssignDriverRequestType requestType) {
        return org.apache.commons.lang.StringUtils.isNotBlank(requestType.getDspOrderId())
                && Objects.nonNull(requestType.getDriverId())
                && requestType.getDriverId() > 0L
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserName())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getSysUserAccount())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserType())
                && Objects.nonNull(requestType.getTransportGroupId())
                && Objects.nonNull(requestType.getSupplierId());
    }
}
