package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.service.SpContractInfoService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.gateway.ChannelGateway;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.gateway.BusinessTravelServiceGateway;
import com.ctrip.dcs.domain.schedule.process.impl.SystemAssignProcess;
import com.ctrip.dcs.domain.schedule.process.impl.SystemAssignTransportProcess;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component("SpContractInfoTestService")
public class SpContractInfoTestService implements ITestDspOrderService {
    @Autowired
    private SpContractInfoService service;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;
    @Autowired
    BusinessTravelServiceGateway businessTravelServiceGateway;

    @Autowired
    private SystemAssignTransportProcess systemAssignTransportProcess;


    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private ScheduleTaskRepository taskRepository;
    @Autowired
    private ChannelGateway channelGateway;

    @Override
    public String test(Map<String, String> params) {//已测试
//        service.updateContractInfo(params.get("dspOrderId"));
        if (!StringUtils.isEmpty(params.get("taskId"))) {
//            workBenchLogMessageFactory.createServiceProviderWorkBenchLog(null, null);
//            workBenchLogMessageFactory.createDispatcherConfirmWorkBenchLog(null, DspOrderConfirmRecordVO.builder().build());
//            workBenchLogMessageFactory.createDriverCarConfirmWorkBenchLog(null, DspOrderConfirmRecordVO.builder().build());
            DspOrderVO dspOrderVO = queryDspOrderService.query(params.get("dspOrderId"));
            ScheduleTaskDO task = taskRepository.find(Long.parseLong(params.get("taskId")), params.get("dspOrderId"));
            systemAssignTransportProcess.execute(task, dspOrderVO);
        }
        if (!StringUtils.isEmpty(params.get("userOrderId"))) {
            businessTravelServiceGateway.queryBusinessTravelBlackList(params.get("userOrderId"),new Date());

        }
        if (!StringUtils.isEmpty(params.get("distributionChannelId"))) {
            channelGateway.findDistributionChannel(Integer.valueOf(params.get("distributionChannelId")));

        }
        return "ok";
    }
}
