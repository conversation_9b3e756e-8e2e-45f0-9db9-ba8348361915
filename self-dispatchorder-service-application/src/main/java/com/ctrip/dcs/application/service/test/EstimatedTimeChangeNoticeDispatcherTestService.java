package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.listener.UseTimeChangeListener;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("EstimatedTimeChangeNoticeDispatcherTestService")
public class EstimatedTimeChangeNoticeDispatcherTestService implements ITestDspOrderService {

    @Autowired
    UseTimeChangeListener useTimeChangeListener;

    @Autowired
    SelfOrderQueryGateway selfOrderQueryGateway;

    @Override
    public String test(Map<String, String> params) {
        String dspOrderId = params.get("dspOrderId");
        BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetail(dspOrderId);
        useTimeChangeListener.judgeSwitchAndFindEmail(baseDetailVO);
        return "ok";
    }
}
