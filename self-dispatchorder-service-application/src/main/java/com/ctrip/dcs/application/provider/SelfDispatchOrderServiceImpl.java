package com.ctrip.dcs.application.provider;

import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.UpdateDspOrderUseTimeRequestType;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.UpdateDspOrderUseTimeResponseType;
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaRequestType;
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaResponseType;
import com.ctrip.dcs.self.dispatchorder.client.SelfDispatchOrderService;
import com.ctrip.dcs.self.dispatchorder.interfaces.AddGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.AddGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.AllowAuthRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.AllowAuthResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchClearCacheRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchClearCacheResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDispatchAwardRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDispatchAwardResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDriverGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDriverGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDriverTiredLimitRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDriverTiredLimitResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDspOrderInventoryRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckDspOrderInventoryResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDispatcherGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateVBKGrabTaskRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateVBKGrabTaskResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckDriversRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckDriversResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckSingleDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckSingleDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteShortDistanceStrategyResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockSyncDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockSyncDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.OrderPushRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.OrderPushResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsAndAllowAuthRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsAndAllowAuthResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryScheduleListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryScheduleListResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QunarCheckDriverForDspRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QunarCheckDriverForDspResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasChangeDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasChangeDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SortDspOrderInventoryRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SortDspOrderInventoryResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.TestDspOrderServiceRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.TestDspOrderServiceResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateCancelRateRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateCancelRateResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderDriverIndexRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderDriverIndexResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateVBKGrabTaskRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateVBKGrabTaskResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListResponseType;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.stereotype.Component;

/**
 * 派发单接口
 */
@Component
public class SelfDispatchOrderServiceImpl implements SelfDispatchOrderService {


    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType checkHealthRequestType) throws Exception {
        return ServiceResponseUtils.getDefaultCheckHealthResponse();
    }

    @Override
    public CreateDspOrderResponseType createDspOrder(CreateDspOrderRequestType createDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(createDspOrderRequestType, CreateDspOrderResponseType.class);
    }

    @Override
    public CancelDspOrderResponseType cancelDspOrder(CancelDspOrderRequestType cancelDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(cancelDspOrderRequestType, CancelDspOrderResponseType.class);
    }

    @Override
    public CustomerCheckDriversResponseType checkDrivers(CustomerCheckDriversRequestType customerCheckDriversRequestType) throws Exception {
        return ServiceExecutors.execute(customerCheckDriversRequestType,CustomerCheckDriversResponseType.class);
    }

    @Override
    public CustomerCheckSingleDriverResponseType checkSingleDriver(CustomerCheckSingleDriverRequestType customerCheckSingleDriverRequestType) throws Exception {
        return ServiceExecutors.execute(customerCheckSingleDriverRequestType, CustomerCheckSingleDriverResponseType.class);
    }

    @Override
    public CustomerAssignDriverResponseType assignDriver(CustomerAssignDriverRequestType customerAssignDriverRequestType) throws Exception {
        return ServiceExecutors.execute(customerAssignDriverRequestType,CustomerAssignDriverResponseType.class);
    }

    @Override
    public VbkAssignDriverResponseType vbkAssignDriver(VbkAssignDriverRequestType vbkAssignDriverRequestType) throws Exception {
        return ServiceExecutors.execute(vbkAssignDriverRequestType,VbkAssignDriverResponseType.class);
    }

    @Override
    public VbkBindCarAndTakenResponseType bingCarAndTaken(VbkBindCarAndTakenRequestType vbkBindCarAndTakenRequestType) throws Exception {
        return ServiceExecutors.execute(vbkBindCarAndTakenRequestType,VbkBindCarAndTakenResponseType.class);
    }

    @Override
    public VbkChangeDriverResponseType vbkChangeDriver(VbkChangeDriverRequestType vbkChangeDriverRequestType) throws Exception {
        return ServiceExecutors.execute(vbkChangeDriverRequestType,VbkChangeDriverResponseType.class);
    }

    @Override
    public RightAndPointResponseType rightAndPoint(RightAndPointRequestType request) throws Exception {
        return ServiceExecutors.execute(request, RightAndPointResponseType.class);
    }

    @Override
    public ReDispatchSubmitResponseType reDispatchSubmit(ReDispatchSubmitRequestType request) throws Exception {
        return ServiceExecutors.execute(request,ReDispatchSubmitResponseType.class);
    }

    @Override
    public QueryReasonsResponseType queryReasons(QueryReasonsRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryReasonsResponseType.class);
    }

    @Override
    public AllowAuthResponseType allowAuth(AllowAuthRequestType request) throws Exception {
        return ServiceExecutors.execute(request, AllowAuthResponseType.class);
    }

    @Override
    public QueryReasonsAndAllowAuthResponseType queryReasonsAndAllowAuth(QueryReasonsAndAllowAuthRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryReasonsAndAllowAuthResponseType.class);
    }

    @Override
    public VbkDriverCheckListResponseType driverCheckList(VbkDriverCheckListRequestType request) throws Exception {
        return ServiceExecutors.execute(request, VbkDriverCheckListResponseType.class);
    }

    @Override
    public ConfirmDelayDspOrderResponseType confirmDelayDspOrder(ConfirmDelayDspOrderRequestType request) throws Exception {
        return ServiceExecutors.execute(request, ConfirmDelayDspOrderResponseType.class);
    }

    @Override
    public QueryAvailableDriversResponseType queryAvailableDrivers(QueryAvailableDriversRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryAvailableDriversResponseType.class);
    }

    @Override
    public UpdateDspOrderUseTimeResponseType updateDspOrderUseTime(UpdateDspOrderUseTimeRequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateDspOrderUseTimeResponseType.class);
    }

    @Override
    public SupplierDiversionListResponseType querySupplierDiversion(SupplierDiversionListRequestType supplierDiversionListRequestType) throws Exception {
        return ServiceExecutors.execute(supplierDiversionListRequestType, SupplierDiversionListResponseType.class);
    }

    @Override
    public MockConfirmDspOrderResponseType mockConfirmDspOrder(MockConfirmDspOrderRequestType mockConfirmDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(mockConfirmDspOrderRequestType,MockConfirmDspOrderResponseType.class);
    }

    @Override
    public MockSyncDspOrderResponseType mockSyncDspOrder(MockSyncDspOrderRequestType mockSyncDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(mockSyncDspOrderRequestType,MockSyncDspOrderResponseType.class);
    }

    @Override
    public CreateIvrRecordResponseType createIvrRecord(CreateIvrRecordRequestType request) throws Exception {
        return ServiceExecutors.execute(request,CreateIvrRecordResponseType.class);
    }

    @Override
    public QueryIvrRecordResponseType queryIvrRecord(QueryIvrRecordRequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryIvrRecordResponseType.class);
    }

    /**
     * 批量查询派发奖励
     */
    @Override
    public BatchQueryDispatchAwardResponseType queryDispatchAward(BatchQueryDispatchAwardRequestType request) throws Exception {
        return ServiceExecutors.execute(request,BatchQueryDispatchAwardResponseType.class);
    }

    @Override
    public CreateDispatcherGrabOrdersResponseType createDispatcherGrabOrders(CreateDispatcherGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request,CreateDispatcherGrabOrdersResponseType.class);
    }

    @Override
    public SubmitDispatcherGrabOrdersResponseType submitDispatcherGrabOrders(SubmitDispatcherGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request,SubmitDispatcherGrabOrdersResponseType.class);
    }

    @Override
    public RefuseDispatcherGrabOrdersResponseType refuseDispatcherGrabOrders(RefuseDispatcherGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request,RefuseDispatcherGrabOrdersResponseType.class);
    }

    @Override
    public CancelDispatcherGrabOrdersResponseType cancelDispatcherGrabOrders(CancelDispatcherGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request,CancelDispatcherGrabOrdersResponseType.class);
    }

    @Override
    public UpdateCancelRateResponseType updateCancelRate(UpdateCancelRateRequestType request) throws Exception {
        return ServiceExecutors.execute(request,UpdateCancelRateResponseType.class);
    }

    @Override
    public TestDspOrderServiceResponseType testDspOrderService(TestDspOrderServiceRequestType request) throws Exception {
        return ServiceExecutors.execute(request,TestDspOrderServiceResponseType.class);
    }

    @Override
    public HighLevelUserResponseType highLevelUserCheck(HighLevelUserRequestType request) throws Exception {
        return ServiceExecutors.execute(request,HighLevelUserResponseType.class);
    }

    @Override
    public HighLevelDriverResponseType highLevelDriverCheck(HighLevelDriverRequestType request) throws Exception {
        return ServiceExecutors.execute(request,HighLevelDriverResponseType.class);
    }

    @Override
    public CheckDriverTiredLimitResponseType checkDriverTiredLimit(CheckDriverTiredLimitRequestType checkDriverTiredLimitRequestType) throws Exception {
        return ServiceExecutors.execute(checkDriverTiredLimitRequestType, CheckDriverTiredLimitResponseType.class);
    }

    @Override
    public CreateDriverGrabOrdersResponseType createDriverGrabOrders(CreateDriverGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CreateDriverGrabOrdersResponseType.class);
    }

    @Override
    public CancelDriverGrabOrdersResponseType cancelDriverGrabOrders(CancelDriverGrabOrdersRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CancelDriverGrabOrdersResponseType.class);
    }

    @Override
    public CheckDspOrderInventoryResponseType checkDspOrderInventory(CheckDspOrderInventoryRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDspOrderInventoryResponseType.class);
    }

    @Override
    public QueryDriverOrCarInServiceOrderResponseType queryDriverOrCarInServiceOrderCount(QueryDriverOrCarInServiceOrderRequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryDriverOrCarInServiceOrderResponseType.class);
    }

    @Override
    public SortDspOrderInventoryResponseType sortDspOrderInventory(SortDspOrderInventoryRequestType request) throws Exception {
        return ServiceExecutors.execute(request, SortDspOrderInventoryResponseType.class);
    }

    /**
     * 查询自营订单是否延后派单
     */
    @Override
    public CheckVBKOrderDelayedDispatchResponseType checkVBKOrderDelayedDispatch(CheckVBKOrderDelayedDispatchRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckVBKOrderDelayedDispatchResponseType.class);
    }

    @Override
    public SaasCreateDspOrderResponseType saaSCreateDspOrder(SaasCreateDspOrderRequestType saasCreateDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(saasCreateDspOrderRequestType, SaasCreateDspOrderResponseType.class);
    }

    @Override
    public SaasUpdateDspOrderResponseType saaSUpdateDspOrder(SaasUpdateDspOrderRequestType saasUpdateDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(saasUpdateDspOrderRequestType, SaasUpdateDspOrderResponseType.class);
    }

    @Override
    public SaasCancelDspOrderResponseType saaSCancelDspOrder(SaasCancelDspOrderRequestType saasCancelDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(saasCancelDspOrderRequestType, SaasCancelDspOrderResponseType.class);

    }

    @Override
    public SaasAssignDriverResponseType saaSAssignDriver(SaasAssignDriverRequestType saasAssignDriverRequestType) throws Exception {
        return ServiceExecutors.execute(saasAssignDriverRequestType, SaasAssignDriverResponseType.class);
    }

    @Override
    public SaasChangeDriverResponseType saasChangeDriver(SaasChangeDriverRequestType saasChangeDriverRequestType) throws Exception {
        return ServiceExecutors.execute(saasChangeDriverRequestType, SaasChangeDriverResponseType.class);
    }

    @Override
    public SaasBindCarResponseType saaSBindCar(SaasBindCarRequestType saasBindCarRequestType) throws Exception {
        return ServiceExecutors.execute(saasBindCarRequestType, SaasBindCarResponseType.class);
    }

    @Override
    public QunarCheckDriverForDspResponseType qunarCheckDriverForDsp(QunarCheckDriverForDspRequestType qunarCheckDriverForDspRequestType) throws Exception {
        return ServiceExecutors.execute(qunarCheckDriverForDspRequestType, QunarCheckDriverForDspResponseType.class);
    }

    @Override
    public ExecuteScheduleResponseType executeSchedule(ExecuteScheduleRequestType executeScheduleRequestType) throws Exception {
        return ServiceExecutors.execute(executeScheduleRequestType, ExecuteScheduleResponseType.class);
    }

    @Override
    public ShutdownScheduleResponseType shutdownSchedule(ShutdownScheduleRequestType shutdownScheduleRequestType) throws Exception {
        return ServiceExecutors.execute(shutdownScheduleRequestType, ShutdownScheduleResponseType.class);
    }

    @Override
    public CreateVBKGrabTaskResponseType createVBKGrabTask(CreateVBKGrabTaskRequestType createVBKGrabTaskRequestType) throws Exception {
        return ServiceExecutors.execute(createVBKGrabTaskRequestType, CreateVBKGrabTaskResponseType.class);
    }

    @Override
    public UpdateVBKGrabTaskResponseType updateVBKGrabTask(UpdateVBKGrabTaskRequestType updateVBKGrabTaskRequestType) throws Exception {
        return ServiceExecutors.execute(updateVBKGrabTaskRequestType, UpdateVBKGrabTaskResponseType.class);
    }

    @Override
    public CancelVBKGrabTaskResponseType cancelVBKGrabTask(CancelVBKGrabTaskRequestType cancelVBKGrabTaskRequestType) throws Exception {
        return ServiceExecutors.execute(cancelVBKGrabTaskRequestType, CancelVBKGrabTaskResponseType.class);
    }

    @Override
    public UpdateOrderDetailSettlementResponseType updateOrderDetailSettlement(UpdateOrderDetailSettlementRequestType updateOrderDetailSettlementRequestType) throws Exception {
        return ServiceExecutors.execute(updateOrderDetailSettlementRequestType, UpdateOrderDetailSettlementResponseType.class);
    }

    @Override
    public UpdateOrderRemarkResponseType updateOrderRemark(UpdateOrderRemarkRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, UpdateOrderRemarkResponseType.class);
    }

    @Override
    public QuerySupplierCanChangeCarListSoaResponseType querySupplierCanChangeCarList(QuerySupplierCanChangeCarListSoaRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, QuerySupplierCanChangeCarListSoaResponseType.class);
    }

    @Override
    public OrderPushResponseType orderPush(OrderPushRequestType request) throws Exception {
        return ServiceExecutors.execute(request, OrderPushResponseType.class);
    }

    @Override
    public ModifyCharteredLineOrderResponseType modifyCharteredLineOrder(ModifyCharteredLineOrderRequestType modifyCharteredLineOrderRequestType) throws Exception {
        return ServiceExecutors.execute(modifyCharteredLineOrderRequestType, ModifyCharteredLineOrderResponseType.class);
    }
    
    @Override
    public QueryScheduleListResponseType queryScheduleList(QueryScheduleListRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, QueryScheduleListResponseType.class);
    }

    @Override
    public AddGrabOrderPushRuleResponseType addGrabOrderPushRule(AddGrabOrderPushRuleRequestType request) throws Exception {
        return ServiceExecutors.execute(request, AddGrabOrderPushRuleResponseType.class);
    }

    @Override
    public DeleteGrabOrderPushRuleResponseType deleteGrabOrderPushRule(DeleteGrabOrderPushRuleRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DeleteGrabOrderPushRuleResponseType.class);
    }

    @Override
    public UpdateGrabOrderPushRuleResponseType updateGrabOrderPushRule(UpdateGrabOrderPushRuleRequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateGrabOrderPushRuleResponseType.class);
    }

    @Override
    public QueryGrabOrderPushRuleResponseType queryGrabOrderPushRule(QueryGrabOrderPushRuleRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryGrabOrderPushRuleResponseType.class);
    }

    @Override
    public QueryGrabOrderPushRuleRecordResponseType queryGrabOrderPushRuleRecord(QueryGrabOrderPushRuleRecordRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryGrabOrderPushRuleRecordResponseType.class);
    }

    @Override
    public QueryGrabOrderPushRuleDetailResponseType queryGrabOrderPushRuleDetail(QueryGrabOrderPushRuleDetailRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryGrabOrderPushRuleDetailResponseType.class);
    }

    @Override
    public UpdateGrabDspOrderSnapshotResponseType updateGrabDspOrderSnapshot(UpdateGrabDspOrderSnapshotRequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateGrabDspOrderSnapshotResponseType.class);
    }

    @Override
    public UpdateGrabDspOrderDriverIndexResponseType updateGrabDspOrderDriverIndex(UpdateGrabDspOrderDriverIndexRequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateGrabDspOrderDriverIndexResponseType.class);
    }

    @Override
    public QueryShortDistanceStrategyResponseType queryShortDistanceStrategy(QueryShortDistanceStrategyRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryShortDistanceStrategyResponseType.class);
    }

    @Override
    public SaveShortDistanceStrategyResponseType saveShortDistanceStrategy(SaveShortDistanceStrategyRequestType request) throws Exception {
        return ServiceExecutors.execute(request, SaveShortDistanceStrategyResponseType.class);
    }

    @Override
    public DeleteShortDistanceStrategyResponseType deleteShortDistanceStrategy(DeleteShortDistanceStrategyRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DeleteShortDistanceStrategyResponseType.class);
    }

    @Override
    public BatchUpdateSupplierResponseType batchUpdateSupplier(BatchUpdateSupplierRequestType request) throws Exception {
        return ServiceExecutors.execute(request, BatchUpdateSupplierResponseType.class);
    }

    @Override
    public BatchQueryDspOrderResponseType batchQueryDspOrderByUserOrderIds(BatchQueryDspOrderRequestType request) throws Exception {
        return ServiceExecutors.execute(request, BatchQueryDspOrderResponseType.class);
    }

    @Override
    public BatchClearCacheResponseType batchClearCache(BatchClearCacheRequestType batchClearCacheRequestType) throws Exception {
        return ServiceExecutors.execute(batchClearCacheRequestType, BatchClearCacheResponseType.class);
    }
}
