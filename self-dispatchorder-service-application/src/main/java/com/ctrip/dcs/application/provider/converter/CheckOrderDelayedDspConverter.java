package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.CreateDspOrderExeCmd;
import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.FixedLocationType;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.XproductVO;
import com.ctrip.dcs.domain.dsporder.entity.ActivityInfoDTO;
import com.ctrip.dcs.domain.dsporder.value.UseDays;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ActivityInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SupplierBorneInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/29 11:15:59
 */
public class CheckOrderDelayedDspConverter {

    /**
     * request to dto
     * @param request 原始请求
     * @return DTO
     */
    public static CheckOrderDelayedDspDTO toDTO(CheckVBKOrderDelayedDispatchRequestType request) {
        CheckOrderDelayedDspDTO dto = new CheckOrderDelayedDspDTO();
        dto.setUserOrderId(request.getUserOrderId());
        dto.setCityId(request.getCityId());
        dto.setCategoryCode(request.getCategoryCode());
        dto.setVehicleGroupid(request.getVehicleGroupid());
        dto.setEstimatedUseTime(request.getEstimatedUseTime());
        dto.setEstimatedUseTimeBj(request.getEstimatedUseTimeBj());
        dto.setUseDays(request.getUseDays());
        dto.setFixedLocationCode(request.getFixedLocationCode());
        dto.setSkuId(request.getSkuId());
        dto.setFromLocation(request.getFromLocation());
        dto.setToLocation(request.getToLocation());
        dto.setEstimatedDistance(request.getEstimatedDistance());
        dto.setEstimatedDuration(request.getEstimatedDuration());
        dto.setDistributionChannel(request.getDistributionChannel());
        dto.setOrderPackageServiceCodes(request.getOrderPackageServiceCodes());
        dto.setUrgentOrder(request.isUrgentOrder());
        dto.setHighGradeOrder(request.isHighGradeOrder());
        setRatePlanId(dto, request.getSupplierBornePrice());
        return dto;
    }

    public static void setRatePlanId(CheckOrderDelayedDspDTO dto, SupplierBorneInfo supplierBorneInfo) {
        Integer ratePlanId = Optional.ofNullable(supplierBorneInfo)
                .map(SupplierBorneInfo::getActivity)
                .orElse(Collections.emptyList())
                .stream()
                .filter(a -> CreateDspOrderExeCmd.ACTIVITY_TYPE_RATE_PLAN.equals(a.getActivityType()))
                .findFirst()
                .map(ActivityInfo::getActivityId)
                .map(Long::intValue)
                .orElse(null);
        dto.setRatePlanId(ratePlanId);
    }

    public static DspOrderVO toDspOrderVO(CheckOrderDelayedDspDTO dto) {
        DspOrderVO vo = new DspOrderVO(
                dto.getUserOrderId(),
                CategoryCodeEnum.getByType(dto.getCategoryCode()).isPresent() ? CategoryCodeEnum.getByType(dto.getCategoryCode()).get() : null,
                dto.getCityId().intValue(),
                dto.getFromLocation().getCityId(),
                dto.getToLocation().getCityId(),
                dto.getVehicleGroupid(),
                DateUtil.parseDateStr2Date(dto.getEstimatedUseTime()),
                DateUtil.parseDateStr2Date(dto.getEstimatedUseTimeBj()),
                new UseDays(dto.getUseDays()),
                dto.getEstimatedDistance(),
                dto.getEstimatedDuration(),
                dto.getFixedLocationCode(),
                getFixedLocationType(dto.getCategoryCode(), ""),
                BigDecimal.valueOf(dto.getFromLocation().getLongitude()),
                BigDecimal.valueOf(dto.getFromLocation().getLatitude()),
                dto.getFromLocation().getCoord(),
                dto.getFromLocation().getCarPlaceId(),
                BigDecimal.valueOf(dto.getToLocation().getLongitude()),
                BigDecimal.valueOf(dto.getToLocation().getLatitude()),
                dto.getToLocation().getCoord(),
                dto.getToLocation().getCarPlaceId(),
                dto.getSkuId().intValue(),
                CollectionUtils.isEmpty(dto.getOrderPackageServiceCodes()) ? Lists.newArrayList() : dto.getOrderPackageServiceCodes().stream().map(XproductVO::new).collect(Collectors.toList()),
                dto.getDistributionChannel(),
                dto.getUrgentOrder() ? 1 : 0,
                dto.getHighGradeOrder() ? 1 : 0,
                Long.valueOf(dto.getVehicleGroupid())
                );
        vo.setRatePlanId(dto.getRatePlanId());
        return vo;
    }

    private static Integer getFixedLocationType(String categoryCode, String terminalId) {
        if (StringUtils.isNotBlank(terminalId) && Integer.valueOf(terminalId) != 0 && EnumSet.of(CategoryCodeEnum.FROM_AIRPORT, CategoryCodeEnum.TO_AIRPORT).contains(CategoryCodeEnum.getByType(categoryCode).get())) {
            return FixedLocationType.TERMINAL.getCode();
        } else if (EnumSet.of(CategoryCodeEnum.TO_STATION, CategoryCodeEnum.FROM_STATION).contains(CategoryCodeEnum.getByType(categoryCode).get())) {
            return FixedLocationType.STATION.getCode();
        } else if (StringUtils.isBlank(terminalId)  && EnumSet.of(CategoryCodeEnum.FROM_AIRPORT, CategoryCodeEnum.TO_AIRPORT).contains(CategoryCodeEnum.getByType(categoryCode).get())) {
            return FixedLocationType.AIRPORT.getCode();
        }
        return 0;
    }

}
