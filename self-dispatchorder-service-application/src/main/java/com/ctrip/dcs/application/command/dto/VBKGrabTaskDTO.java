package com.ctrip.dcs.application.command.dto;

import java.util.List;

public class VBKGrabTaskDTO {

    private String taskId;
    private Integer cityId;
    private List<String> categoryCodeList;
    private Integer orderSourceCode;
    private List<String> driverIdList;
    private VBKGrabTaskSettlement initial;
    private VBKGrabTaskSettlement rewards;
    private String grabEndTime;
    private String makeUpEffectTime;
    private Integer payForDriver;
    private Long supplierId;
    private String operatorName;
    private List<String> orderIdList;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public List<String> getCategoryCodeList() {
        return categoryCodeList;
    }

    public void setCategoryCodeList(List<String> categoryCodeList) {
        this.categoryCodeList = categoryCodeList;
    }

    public Integer getOrderSourceCode() {
        return orderSourceCode;
    }

    public void setOrderSourceCode(Integer orderSourceCode) {
        this.orderSourceCode = orderSourceCode;
    }

    public List<String> getDriverIdList() {
        return driverIdList;
    }

    public void setDriverIdList(List<String> driverIdList) {
        this.driverIdList = driverIdList;
    }

    public VBKGrabTaskSettlement getInitial() {
        return initial;
    }

    public void setInitial(VBKGrabTaskSettlement initial) {
        this.initial = initial;
    }

    public VBKGrabTaskSettlement getRewards() {
        return rewards;
    }

    public void setRewards(VBKGrabTaskSettlement rewards) {
        this.rewards = rewards;
    }

    public String getGrabEndTime() {
        return grabEndTime;
    }

    public void setGrabEndTime(String grabEndTime) {
        this.grabEndTime = grabEndTime;
    }

    public String getMakeUpEffectTime() {
        return makeUpEffectTime;
    }

    public void setMakeUpEffectTime(String makeUpEffectTime) {
        this.makeUpEffectTime = makeUpEffectTime;
    }

    public Integer getPayForDriver() {
        return payForDriver;
    }

    public void setPayForDriver(Integer payForDriver) {
        this.payForDriver = payForDriver;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public List<String> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<String> orderIdList) {
        this.orderIdList = orderIdList;
    }
}
