package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.ConfirmDelayDspOrderCommand;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderRequestType;

/**
 * <AUTHOR>
 */
public class ConfirmDelayDspOrderConverter {

    public static ConfirmDelayDspOrderCommand toConfirmDelayDspOrderCommand(ConfirmDelayDspOrderRequestType requestType) {
        return new ConfirmDelayDspOrderCommand(requestType.getDspOrderId(), requestType.getDriverId(), requestType.getTransportGroupId(), requestType.getDuid());
    }
}
