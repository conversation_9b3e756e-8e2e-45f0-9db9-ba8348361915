package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverVehicleInfoModifyEvent;
import com.ctrip.dcs.application.event.impl.DriverVehicleInfoModifyEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverVehicleInfoModifyTestService")
public class DriverVehicleInfoModifyTestService implements ITestDspOrderService{
    @Autowired
    private DriverVehicleInfoModifyEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverVehicleInfoModifyEvent event = new DriverVehicleInfoModifyEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setAccountType(params.get("accountType"));
        event.setVehicleId(Long.valueOf(params.get("vehicleId")));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
