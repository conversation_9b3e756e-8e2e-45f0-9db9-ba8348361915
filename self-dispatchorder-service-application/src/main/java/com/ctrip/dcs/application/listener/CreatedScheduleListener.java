package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd;
import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 调度已创建
 * <AUTHOR>
 */
@Component
public class CreatedScheduleListener {

    private static final Logger logger = LoggerFactory.getLogger(CreatedScheduleListener.class);

    @Autowired
    private ExecuteScheduleExeCmd executeScheduleExeCmd;

    @QmqLogTag(tagKeys = {"scheduleId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.CREATED_SCHEDULE_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            ExecuteScheduleCommand command = MessageConverter.INSTANCE.toExecuteScheduleCommand(message);
            executeScheduleExeCmd.execute(command);
        } catch (Exception e) {
            logger.error("ExecuteScheduleListenerError", e);
            // 埋点
            MetricsUtil.recordValue(MetricsConstants.EXECUTE_SCHEDULE_ERROR_COUNT);
            throw new NeedRetryException("ExecuteScheduleListenerError");
        }
    }

}
