package com.ctrip.dcs.application.command.dto;

import java.math.BigDecimal;

public class VBKGrabTaskOperateDTO {

    private String taskId;
    
    private Long supplierId;
    
    private String operatorName;
    
    private int successNum;
    
    private Integer operatorType;

    private String operatorDesc;

    private String currentDateStr;

    private Integer cityId;

    private String grabEndTime;

    private String makeUpEffectTime;

    private String grabEndTimeOrigin;

    private String makeUpEffectTimeOrigin;

    private Integer initialType;

    private BigDecimal initialValue;

    private BigDecimal initialRate;

    private String initialCurrency;

    private Integer initialTypeOrigin;

    private BigDecimal initialValueOrigin;

    private BigDecimal initialRateOrigin;

    private String initialCurrencyOrigin;
    
    private Integer rewardsType;

    private BigDecimal rewardsValue;

    private BigDecimal rewardsRate;

    private String rewardsCurrency;

    private Integer rewardsTypeOrigin;

    private BigDecimal rewardsValueOrigin;

    private BigDecimal rewardsRateOrigin;

    private String rewardsCurrencyOrigin;

    private Long driverNum;

    private Long originDriverNum;

    private Integer payForDriver;

    public Integer getPayForDriver() {
        return payForDriver;
    }

    public void setPayForDriver(Integer payForDriver) {
        this.payForDriver = payForDriver;
    }

    public String getOperatorDesc() {
        return operatorDesc;
    }

    public void setOperatorDesc(String operatorDesc) {
        this.operatorDesc = operatorDesc;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public int getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(int successNum) {
        this.successNum = successNum;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getCurrentDateStr() {
        return currentDateStr;
    }

    public void setCurrentDateStr(String currentDateStr) {
        this.currentDateStr = currentDateStr;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getGrabEndTime() {
        return grabEndTime;
    }

    public void setGrabEndTime(String grabEndTime) {
        this.grabEndTime = grabEndTime;
    }

    public String getMakeUpEffectTime() {
        return makeUpEffectTime;
    }

    public void setMakeUpEffectTime(String makeUpEffectTime) {
        this.makeUpEffectTime = makeUpEffectTime;
    }

    public String getGrabEndTimeOrigin() {
        return grabEndTimeOrigin;
    }

    public void setGrabEndTimeOrigin(String grabEndTimeOrigin) {
        this.grabEndTimeOrigin = grabEndTimeOrigin;
    }

    public String getMakeUpEffectTimeOrigin() {
        return makeUpEffectTimeOrigin;
    }

    public void setMakeUpEffectTimeOrigin(String makeUpEffectTimeOrigin) {
        this.makeUpEffectTimeOrigin = makeUpEffectTimeOrigin;
    }

    public Integer getInitialType() {
        return initialType;
    }

    public void setInitialType(Integer initialType) {
        this.initialType = initialType;
    }

    public String getInitialCurrency() {
        return initialCurrency;
    }

    public void setInitialCurrency(String initialCurrency) {
        this.initialCurrency = initialCurrency;
    }

    public Integer getInitialTypeOrigin() {
        return initialTypeOrigin;
    }

    public void setInitialTypeOrigin(Integer initialTypeOrigin) {
        this.initialTypeOrigin = initialTypeOrigin;
    }

    public String getInitialCurrencyOrigin() {
        return initialCurrencyOrigin;
    }

    public void setInitialCurrencyOrigin(String initialCurrencyOrigin) {
        this.initialCurrencyOrigin = initialCurrencyOrigin;
    }

    public Integer getRewardsType() {
        return rewardsType;
    }

    public void setRewardsType(Integer rewardsType) {
        this.rewardsType = rewardsType;
    }

    public String getRewardsCurrency() {
        return rewardsCurrency;
    }

    public void setRewardsCurrency(String rewardsCurrency) {
        this.rewardsCurrency = rewardsCurrency;
    }

    public Integer getRewardsTypeOrigin() {
        return rewardsTypeOrigin;
    }

    public void setRewardsTypeOrigin(Integer rewardsTypeOrigin) {
        this.rewardsTypeOrigin = rewardsTypeOrigin;
    }

    public String getRewardsCurrencyOrigin() {
        return rewardsCurrencyOrigin;
    }

    public void setRewardsCurrencyOrigin(String rewardsCurrencyOrigin) {
        this.rewardsCurrencyOrigin = rewardsCurrencyOrigin;
    }

    public Long getOriginDriverNum() {
        return originDriverNum;
    }

    public void setOriginDriverNum(Long originDriverNum) {
        this.originDriverNum = originDriverNum;
    }

    public BigDecimal getInitialValue() {
        return initialValue;
    }

    public void setInitialValue(BigDecimal initialValue) {
        this.initialValue = initialValue;
    }

    public BigDecimal getInitialRate() {
        return initialRate;
    }

    public void setInitialRate(BigDecimal initialRate) {
        this.initialRate = initialRate;
    }

    public BigDecimal getRewardsValue() {
        return rewardsValue;
    }

    public void setRewardsValue(BigDecimal rewardsValue) {
        this.rewardsValue = rewardsValue;
    }

    public BigDecimal getRewardsRate() {
        return rewardsRate;
    }

    public void setRewardsRate(BigDecimal rewardsRate) {
        this.rewardsRate = rewardsRate;
    }

    public BigDecimal getInitialValueOrigin() {
        return initialValueOrigin;
    }

    public void setInitialValueOrigin(BigDecimal initialValueOrigin) {
        this.initialValueOrigin = initialValueOrigin;
    }

    public BigDecimal getInitialRateOrigin() {
        return initialRateOrigin;
    }

    public void setInitialRateOrigin(BigDecimal initialRateOrigin) {
        this.initialRateOrigin = initialRateOrigin;
    }

    public BigDecimal getRewardsValueOrigin() {
        return rewardsValueOrigin;
    }

    public void setRewardsValueOrigin(BigDecimal rewardsValueOrigin) {
        this.rewardsValueOrigin = rewardsValueOrigin;
    }

    public BigDecimal getRewardsRateOrigin() {
        return rewardsRateOrigin;
    }

    public void setRewardsRateOrigin(BigDecimal rewardsRateOrigin) {
        this.rewardsRateOrigin = rewardsRateOrigin;
    }

    public Long getDriverNum() {
        return driverNum;
    }

    public void setDriverNum(Long driverNum) {
        this.driverNum = driverNum;
    }
}
