package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd;
import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand;
import com.ctrip.dcs.application.listener.grab.VBKGrabOrderCancelListener;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DriverMileageProfitListener {

    private static final Logger logger = LoggerFactory.getLogger(DriverMileageProfitListener.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd;

    @QmqLogTag(tagKeys = {"orderId", "driverId"})
    @QmqConsumer(prefix = EventConstants.OLD_DSP_ORDER_CANCEL_TOPIC, consumerGroup = "DriverMileageProfit", idempotentChecker = "redisIdempotentChecker")
    public void onCancel(Message message) {
        String oldSupplyOrderIds = message.getStringProperty("supplyOrderIds");
        long driverId = message.getLongProperty("driverId");
        logger.info("DriverMileageProfitListenerInfo", oldSupplyOrderIds);
        if (Strings.isBlank(oldSupplyOrderIds) || driverId == 0) {
            return;
        }
        List<String> oldSupplyOrderIdList = Splitter.on(",").splitToList(oldSupplyOrderIds);
        for (String supplyOrderId : oldSupplyOrderIdList) {
            calculateDriverMileageProfit(supplyOrderId, driverId, Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY));
        }
    }


    @QmqLogTag(tagKeys = {"orderId", "driverId"})
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN, consumerGroup = "DriverMileageProfit", idempotentChecker = "redisIdempotentChecker")
    public void onTaken(Message message) {
        String oldSupplyOrderIds = message.getStringProperty("supplyOrderIds");
        long driverId = message.getLongProperty("driverId");
        logger.info("DriverMileageProfitListenerInfo", oldSupplyOrderIds);
        if (Strings.isBlank(oldSupplyOrderIds) || driverId == 0) {
            return;
        }
        List<String> oldSupplyOrderIdList = Splitter.on(",").splitToList(oldSupplyOrderIds);
        for (String supplyOrderId : oldSupplyOrderIdList) {
            calculateDriverMileageProfit(supplyOrderId, driverId, Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY));
        }
    }

    @QmqLogTag(tagKeys = {"orderId", "driverId"})
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_FINISH, consumerGroup = "DriverMileageProfit", idempotentChecker = "redisIdempotentChecker")
    public void onFinish(Message message) {
        String oldSupplyOrderIds = message.getStringProperty("supplyOrderIds");
        long driverId = message.getLongProperty("driverId");
        logger.info("DriverMileageProfitListenerInfo", oldSupplyOrderIds);
        if (Strings.isBlank(oldSupplyOrderIds) || driverId == 0) {
            return;
        }
        List<String> oldSupplyOrderIdList = Splitter.on(",").splitToList(oldSupplyOrderIds);
        for (String supplyOrderId : oldSupplyOrderIdList) {
            calculateDriverMileageProfit(supplyOrderId, driverId, Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY, DriverMileageProfitTypeVO.COMPLETE));
        }
    }


    public void calculateDriverMileageProfit(String dspOrderId, Long driverId, List<DriverMileageProfitTypeVO> types) {
        try {
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(dspOrderId);
            DriverVO driver = queryDriverService.queryDriver(driverId, CategoryUtils.selfGetParentType(dspOrder), dspOrder.getSupplierId().longValue());
            DriverWorkTimeVO driverWorkTimeVO = DriverWorkTimeUtil.getDriverWorkTime(driver, dspOrder.getEstimatedUseTime());
            if (driverWorkTimeVO == null) {
                return;
            }
            CalculateDriverMileageProfitCommand command = new CalculateDriverMileageProfitCommand(driver, driverWorkTimeVO, types, dspOrderId, null);
            calculateDriverMileageProfitExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("DriverMileageProfitListenerError", e);
        }
    }
}
