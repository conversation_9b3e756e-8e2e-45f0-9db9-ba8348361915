package com.ctrip.dcs.application.service.risk;

import com.ctrip.dcs.domain.common.enums.LateRiskTypeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.gateway.LateRiskRemindGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/7 11:52
 */
@Service
public class LateRiskTypeBeforeArriveStrategy extends AbstractLateRiskTypeStrategy {
    private static Logger logger = LoggerFactory.getLogger(LateRiskTypeBeforeArriveStrategy.class);
    
    @Resource
    LateRiskRemindGateway lateRiskRemindGateway;
    
    @Override
    public Integer getType() {
        return LateRiskTypeEnum.BEFORE_ARRIVE.getType();
    }
    
    @Override
    public void sendEmail(DspOrderVO dspOrder, TransportGroupVO transportGroupVO) {
        if (StringUtils.isBlank(transportGroupVO.getInformEmail())) {
            logger.info("late_risk_before_arrive_send_email_" + dspOrder.getDspOrderId(), "receiver email is empty, no need send");
            return;
        }
        lateRiskRemindGateway.pushLateRiskBeforeArriveForEmail(dspOrder, transportGroupVO);
    }
    
    @Override
    public void sendPcStationLetter(DspOrderVO dspOrder, TransportGroupVO transportGroup) {
        lateRiskRemindGateway.pushLateRiskBeforeArriveForPcSite(dspOrder, transportGroup);
    }
    
    @Override
    public void sendMobileSupplierRemind(DspOrderVO dspOrder, TransportGroupVO transportGroup) {
        lateRiskRemindGateway.pushLateRiskBeforeArriveForApp(dspOrder, transportGroup);
    }
    
    @Override
    public void sendLateRiskIVR(DspOrderVO dspOrderVO, TransportGroupVO transportGroup) {
        lateRiskRemindGateway.pushLateRiskBeforeArriveForIVR(dspOrderVO, transportGroup);
    }
}
