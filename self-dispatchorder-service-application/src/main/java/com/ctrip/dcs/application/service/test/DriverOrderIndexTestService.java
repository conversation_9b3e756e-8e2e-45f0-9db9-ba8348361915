package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DrvOrderIndexDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderIndexPO;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class DriverOrderIndexTestService implements ITestDspOrderService {

    @Autowired
    private DrvOrderIndexDao drvOrderIndexDao;

    @Override
    public String test(Map<String, String> params) {
        String driverId = params.get("driverId");
        String status = params.get("status");
        try {
            List<DrvOrderIndexPO> list = drvOrderIndexDao.query(Lists.newArrayList(driverId), Integer.valueOf(status));
            return toDrvOrderIndexPO(list);
        } catch (Exception e) {
            throw new BizException(e);
        }
    }

    public String toDrvOrderIndexPO(List<DrvOrderIndexPO> list) {
        List<DrvOrderIndexPO> result = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(item -> {
            DrvOrderIndexPO po = new DrvOrderIndexPO();
            po.setId(item.getId());
            po.setDrvOrderId(item.getDrvOrderId());
            po.setDspOrderId(item.getDspOrderId());
            po.setUserOrderId(item.getUserOrderId());
            po.setSupplyOrderId(item.getSupplyOrderId());
            po.setDrvId(item.getDrvId());
            po.setOrderStatus(item.getOrderStatus());
            po.setSysExpectBookTimeBj(item.getSysExpectBookTimeBj());
            po.setSysExpectBookTimeLocal(item.getSysExpectBookTimeLocal());
            po.setFinishTimeBj(item.getFinishTimeBj());
            po.setFinishTimeLocal(item.getFinishTimeLocal());
            po.setCancelTimeBj(item.getCancelTimeBj());
            po.setCancelTimeLocal(item.getCancelTimeLocal());
            po.setDatachangeLasttime(item.getDatachangeLasttime());
            po.setDatachangeCreatetime(item.getDatachangeCreatetime());
            po.setIsAvailable(item.getIsAvailable());
            po.setCategoryCode(item.getCategoryCode());
            po.setOrderSourceCode(item.getOrderSourceCode());
            po.setTakenTimeBj(item.getTakenTimeBj());
            po.setTakenTimeLocal(item.getTakenTimeLocal());
            po.setConfirmMoneyTimeLocal(item.getConfirmMoneyTimeLocal());
            po.setConfirmMoneyTimeBj(item.getConfirmMoneyTimeBj());
            po.setIsGuideOrder(item.getIsGuideOrder());
            return po;
        }).toList();
        return JacksonSerializer.INSTANCE().serialize(result);
    }
}
