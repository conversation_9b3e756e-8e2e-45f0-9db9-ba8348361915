package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.tool.BatchUpdateSupplierExeCmd;
import com.ctrip.dcs.infrastructure.common.util.NumberUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BatchUpdateSupplierExecutor extends AbstractRpcExecutor<BatchUpdateSupplierRequestType, BatchUpdateSupplierResponseType>
        implements Validator<BatchUpdateSupplierRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(BatchUpdateSupplierExecutor.class);
    @Autowired
    private BatchUpdateSupplierExeCmd batchUpdateSupplierExeCmd;

    @Override
    public BatchUpdateSupplierResponseType execute(BatchUpdateSupplierRequestType requestType) {
        BatchUpdateSupplierResponseType response = new BatchUpdateSupplierResponseType();
        try {
            response = batchUpdateSupplierExeCmd.execute(requestType);
            return response;
        } catch (BizException e) {
            return ServiceResponseUtils.fail(response, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("BatchUpdateSupplier_Error", ex);
            return ServiceResponseUtils.fail(response);
        }
    }

    @Override
    public void validate(AbstractValidator<BatchUpdateSupplierRequestType> validator, BatchUpdateSupplierRequestType req) {
        validator.ruleFor("supplierInfoList").notNull().notEmpty();
        if (CollectionUtils.isNotEmpty(req.getSupplierInfoList())) {
            for (SupplierInfo supplierInfo : req.getSupplierInfoList()) {
                if (!NumberUtil.isPositive(supplierInfo.getSupplierId())) {
                    throw new BizException("supplierId must be positive integer");
                }
                if (StringUtils.isEmpty(supplierInfo.getUserOrderId())) {
                    throw new BizException("userOrderId must not null or empty");
                }
                if (!NumberUtil.isPositive(supplierInfo.getSkuId())) {
                    throw new BizException("skuId must be positive integer");
                }
                if (!NumberUtil.isPositive(supplierInfo.getSpId())) {
                    throw new BizException("spId must be positive integer");
                }
                if (!NumberUtil.isPositive(supplierInfo.getTransportGroupId())) {
                    throw new BizException("transportGroupId must be positive integer");
                }
            }
        }
    }
}
