package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SortDspOrderInventoryCommand;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspSortCommand;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.service.RecommendServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SortDspOrderInventoryExeCmd {

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private SubSkuRepository subSkuRepository;
    
    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private SortService sortService;

    @Autowired
    private RecommendServiceImpl recommendService;

    @Autowired
    private DspContextService dspContextService;

    public List<SortModel> execute(SortDspOrderInventoryCommand command) {
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(command.getDspOrderId());
        if (dspOrderVO == null) {
            return Collections.emptyList();
        }
        SubSkuVO subSkuVO = subSkuRepository.find(command.getSubSkuId());
        if (subSkuVO == null) {
            return Collections.emptyList();
        }
        Long supplierId = dspOrderVO.getSupplierId() != null ? dspOrderVO.getSupplierId().longValue() : 0L;
        List<DriverVO> drivers = queryDriverService.queryDriver(command.getDriverIds(), CategoryUtils.selfGetParentType(dspOrderVO), supplierId);
        if (CollectionUtils.isEmpty(drivers)) {
            return Collections.emptyList();
        }
        DuidVO duid = DuidVO.of(dspOrderVO.getDspOrderId(), subSkuVO.getSubSkuId(), subSkuVO.getDspType().getCode(), subSkuVO.getTakenType().getCode());
        List<CheckModel> list = drivers.stream().map(d -> new CheckModel(new DspModelVO(dspOrderVO, d))).toList();
        return recommendService.sort(dspOrderVO, subSkuVO, duid, new DspContext(dspContextService), list);
    }
    
}
