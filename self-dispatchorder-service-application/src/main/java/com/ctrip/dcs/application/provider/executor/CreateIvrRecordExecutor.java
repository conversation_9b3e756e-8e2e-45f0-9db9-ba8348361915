package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CreateIvrRecordExeCmd;
import com.ctrip.dcs.application.provider.converter.CreateIvrRecordConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTagPair(key = "data.userOrderId", alias = "userOrderId")
public class CreateIvrRecordExecutor extends AbstractRpcExecutor<CreateIvrRecordRequestType, CreateIvrRecordResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CreateIvrRecordExecutor.class);

    @Autowired
    private CreateIvrRecordExeCmd createIvrRecordExeCmd;

    @Override
    public CreateIvrRecordResponseType execute(CreateIvrRecordRequestType requestType) {
        CreateIvrRecordResponseType responseType = new CreateIvrRecordResponseType();
        if (requestType == null || requestType.getData() == null || StringUtils.isEmpty(requestType.getData().getUserOrderId())
                || StringUtils.isEmpty(requestType.getData().getSupplyOrderId()) || StringUtils.isEmpty(requestType.getData().getRecordGuid())) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
              createIvrRecordExeCmd.execute(CreateIvrRecordConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("ivr_record_create_error", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }
}
