package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.processor.CreateDriverChangeRecordProcessor;
import com.ctrip.dcs.application.service.DriverConfirmedService;
import com.ctrip.dcs.application.service.SpContractInfoService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class DriverConfirmListener {

    @Autowired
    private DriverConfirmedService driverConfirmedService;

    @Autowired
    private SpContractInfoService spContractInfoService;

    @Autowired
    private CreateDriverChangeRecordProcessor createDriverChangeRecordProcessor;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        driverConfirmedService.confirmed(dspOrderId, confirmRecordId);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup = EventConstants.DRIVER_CHANGE_RECORD_CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onDriverChangeRecordMessage(Message message) {
        // 原应单记录id为空 非改派司机场景
        long originalConfirmRecordId = message.getLongProperty("originalConfirmRecordId");
        if( originalConfirmRecordId == 0L ) {
            return;
        }
        int statusMachineEventCode = message.getIntProperty("statusMachineEventCode");
        // 只有当供应商更改司机时才需要记录司机变更记录 eventCode: 21
        if( OrderStatusEvent.VBK_UPDATE_DRIVER.getCode() == statusMachineEventCode ) {
            // 获取新应单记录id
            long newConfirmRecordId = message.getLongProperty("confirmRecordId");
            // 执行真正落表逻辑
            createDriverChangeRecordProcessor.process(originalConfirmRecordId, newConfirmRecordId);
        }
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup  =  "self_order_contract", idempotentChecker = "redisIdempotentChecker")
    public void contractInfoUpdate(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        spContractInfoService.updateContractInfo(dspOrderId);
    }

    @QmqLogTag(tagKeys = {"confirmRecordId","driverId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup  =  "self_driver_Level", idempotentChecker = "redisIdempotentChecker")
    public void contractUpdateDrvLevel(Message message) {
        Long driverId = message.getLongProperty("driverId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        if(driverId == null || confirmRecordId == null){
            return;
        }
        driverConfirmedService.contractUpdateDrvLevel(driverId,confirmRecordId);
    }

}
