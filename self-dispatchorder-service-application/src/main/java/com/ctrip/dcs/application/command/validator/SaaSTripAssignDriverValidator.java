package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class SaaSTripAssignDriverValidator extends AbstractSaaSOperateValidator {

    public static final Logger logger = LoggerFactory.getLogger(SaaSTripAssignDriverValidator.class);


    /**
     * 查询携程订单用
     */
    @Autowired
    protected DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;


    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();

        //订单状态为220
        if(!OrderStatusEnum.DISPATCH_CONFIRMED.getCode().equals(saaSBusinessVO.getDspOrderVO().getOrderStatus())){
            logger.warn("SaaSTripAssignDriverValidator_order_status_un_support_assign_error", LocalJsonUtils.toJson(dspOrderVO.getOrderStatus()));
            throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
        }

        //包车需要车
        if (CategoryCodeEnum.isCharterOrder(dspOrderVO.getCategoryCode().getType()) && StringUtils.isEmpty(cmd.getCarLicense())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }

        //特惠订单不允许指派
        if (dspOrderVO.isSpecialSaleOrder()) {
            throw ErrorCode.SPECIAL_ORDER_NOT_ASSIGN.getBizException();
        }

        validateSuppliers(cmd, saaSBusinessVO);

        UserOrderDetail userOrderDetail = saaSBusinessVO.getUserOrderDetail();
        MetricsUtil.recordValue("SaaSTripAssignDriverValidator_sum");
        //1代表使用用户单的灰度，0代表全量,2代表老逻辑
        Integer useTimeNoDriverCancelBufferSwitch = commonConfConfig.getInteger("useTimeNoDriverCancelBufferSwitch", 1);
        logger.info("SaaSTripAssignDriverValidator_useTimeNoDriverCancelBufferSwitch", LocalJsonUtils.toJson(useTimeNoDriverCancelBufferSwitch));
        if(useTimeNoDriverCancelBufferSwitch == 2){
            validLastConfirmCarTimeOld(saaSBusinessVO, dspOrderVO);
            return;
        }
        if(useTimeNoDriverCancelBufferSwitch == 0) {
            validLastConfirmCarTimeNew(dspOrderVO);
            return;
        }
        if(Objects.isNull(userOrderDetail) || Objects.isNull(userOrderDetail.getBookInfo())){
            validLastConfirmCarTimeNew(dspOrderVO);
            return;
        }
        if(!userOrderDetail.getBookInfo().getUseTimeNoDriverCancelGrayTag()){
            validLastConfirmCarTimeOld(saaSBusinessVO, dspOrderVO);
            return;
        }
        validLastConfirmCarTimeNew(dspOrderVO);
    }

    private void validLastConfirmCarTimeNew(DspOrderVO dspOrderVO) {
        int buffer = 0;
        String extendInfo = dspOrderVO.getExtendInfo();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(extendInfo)){
            HashMap hashMap = JsonUtils.fromJson(extendInfo, HashMap.class);
            Integer o = MapUtils.getInteger(hashMap,SysConstants.Order.USE_TIME_NO_DRIVER_CANCEL_BUFFER,0);
            if(Objects.nonNull(o)){
                buffer = o;
            }
        }
        long time = DateUtil.addMinutes(dspOrderVO.getEstimatedUseTimeBj(), buffer).getTime();
        long currentTimeMillis = System.currentTimeMillis();
        if (time < currentTimeMillis) {
            Map<String, String> map = new HashMap<>();
            map.put("validWay", "new");
            MetricsUtil.recordValue("SaaSTripAssignDriverValidator_error", map);
            logger.warn("SaaSTripAssignDriverValidator_EstimatedUseTimeBj", LocalJsonUtils.toJson(time));
            throw ErrorCode.CURRENT_TIME_AFTER_USE_TIME_ERROR.getBizException();
        }
    }

    private static void validLastConfirmCarTimeOld(SaaSBusinessVO saaSBusinessVO, DspOrderVO dspOrderVO) {
        logger.info("SaaSTripAssignDriverValidator_validLastConfirmCarTimeOld", LocalJsonUtils.toJson(saaSBusinessVO));
        if (CategoryCodeEnum.isCharterOrder(dspOrderVO.getCategoryCode().getType())) {
            //当前时间<用车时间
            long time = saaSBusinessVO.getDspOrderVO().getEstimatedUseTimeBj().getTime();
            long currentTimeMillis = System.currentTimeMillis();
            // 包车订单不校验最晚派遣时间，使用用车时间进行拦截
            if (currentTimeMillis >= time) {
                Map<String, String> map = new HashMap<>();
                map.put("validWay", "old");
                MetricsUtil.recordValue("SaaSTripAssignDriverValidator_error", map);
                logger.warn("SaaSTripAssignDriverValidator_EstimatedUseTimeBj", LocalJsonUtils.toJson(time));
                throw ErrorCode.CURRENT_TIME_AFTER_USE_TIME_ERROR.getBizException();
            }
        } else {
            //当前时间<最晚派遣司机车辆时间
            long time = saaSBusinessVO.getDspOrderVO().getLastConfirmCarTimeBj().getTime();
            long currentTimeMillis = System.currentTimeMillis();
            if(currentTimeMillis >= time){
                Map<String, String> map = new HashMap<>();
                map.put("validWay", "old");
                MetricsUtil.recordValue("SaaSTripAssignDriverValidator_error", map);
                logger.warn("SaaSTripAssignDriverValidator_getLastConfirmCarTimeBj", LocalJsonUtils.toJson(time));
                throw ErrorCode.CURRENT_TIME_AFTER_LAST_CONFIRM_CAR_TIME_ERROR.getBizException();
            }
        }
    }

}
