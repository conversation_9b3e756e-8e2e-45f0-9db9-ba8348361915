package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverInfoModifyEvent;
import com.ctrip.dcs.application.event.impl.DriverInfoModifyEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverInfoModifyTestService")
public class DriverInfoModifyTestService implements ITestDspOrderService{
    @Autowired
    private DriverInfoModifyEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//报错
        DriverInfoModifyEvent event = new DriverInfoModifyEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        // false 就是 接送机
        event.setFromDrvGuide(false);
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
