package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.service.HighLevelCheckService;
import com.ctrip.dcs.self.dispatchorder.interfaces.GoldDriverInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverResponseType;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class HighLevelDriverCheckExecutor extends AbstractRpcExecutor<HighLevelDriverRequestType, HighLevelDriverResponseType> {

    @Autowired
    private HighLevelCheckService highLevelCheckService;

    @Override
    public HighLevelDriverResponseType execute(HighLevelDriverRequestType requestType) {
        HighLevelDriverResponseType responseType = new HighLevelDriverResponseType();
        List<GoldDriverInfo> goldDriverInfoList = highLevelCheckService.checkHighLevelDriver(requestType.getHighLevelDriver());
        responseType.setData(goldDriverInfoList);
        return ServiceResponseUtils.success(responseType);
    }
}
