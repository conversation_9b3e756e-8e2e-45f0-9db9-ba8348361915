package com.ctrip.dcs.application.listener.driver;

import com.ctrip.dcs.application.event.IDriverFreezeEventHandler;
import com.ctrip.dcs.application.event.IDriverOfflineEventHandler;
import com.ctrip.dcs.application.event.dto.DriverFreezeEvent;
import com.ctrip.dcs.application.event.dto.DriverOfflineEvent;
import com.ctrip.dcs.application.listener.driver.handler.TourDriverQMQHandler;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 司机状态变更消息 dcs.tms.transport.driver.state.changed
 * 司机冻结 tag_driverstate_freeze
 * 司机下线 tag_driverstate_offline
 */
@Component
public class DriverStatusModifyListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverStatusModifyListener.class);
    @Autowired
    private IDriverOfflineEventHandler driverOfflineEventHandler;
    @Autowired
    private IDriverFreezeEventHandler driverFreezeEventHandler;

    @QmqLogTag(tagKeys = {"driverId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_STATUS_MODIFY_TOPIC,
            tagType = TagType.OR,
            tags = {"tag_driverstate_freeze","tag_driverstate_offline"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handle(Message message){
        logger.info("DriverStatusModifyListener_msg", LocalJsonUtils.toJson(message));
        //司机冻结
        if(message.getTags().contains("tag_driverstate_freeze")){
            handleDriverFreeze(message);
            return;
        }
        //司机请假
        if(message.getTags().contains("tag_driverstate_offline")){
            handleDriverOffline(message);
        }
    }

    /**
     * 司导平台供应链消息监听处理
     *
     * @param message 消息主体
     */
    @QmqLogTag
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_DRIVER_STATUS_CHANGED,
            tagType = TagType.OR,
            tags = {"tag_driver_offline","tag_driver_freeze"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handleTourDriverPlatformQmq(Message message){
        // 公共参数获取
        long driverId = message.getLongProperty("driverId");// 司导ID
        long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
        String accountType = message.getStringProperty("operateFrom"); // 操作来源(1.供应商,2.BD)
        //String ctripUID = message.getStringProperty("ctripUID"); // 操作者UID
        String strDriverProductLineList = message.getStringProperty("driverProductLineList"); // 司机所属产线列表，多个产线以逗号分割

        // 参数校验
        if (driverId <= 0) {// 检查是否有效的司机ID
            logger.error(message.getSubject() + "_" + message.getMessageId(), "driverId is invalid: " + driverId);
            return;
        }
        if (supplierId <= 0) {// 检查是否有效的供应商ID
            logger.error(message.getSubject() + "_" + message.getMessageId(), "supplierId is invalid: " + supplierId);
            return;
        }
        // 检查司机产线是否含有包车，没有则不处理直接返回
        if (!TourDriverQMQHandler.isCharteredDriverCarChanged(strDriverProductLineList)) {
            logger.info(message.getSubject() + "_" + message.getMessageId(), "driverProductLineList don't contain charteredCar, return. driverProductLineList: " + strDriverProductLineList);
            return;
        }

        //司机冻结处理
        if(message.getTags().contains("tag_driver_freeze")){
            String startFreezeTime = message.getStringProperty("startFreezeTime"); // 开始冻结时间
            String endFreezeTime = message.getStringProperty("endFreezeTime"); // 结束冻结时间
            if (StringUtils.isBlank(startFreezeTime) || StringUtils.isBlank(endFreezeTime)) {
                logger.error(message.getSubject() + "_" + message.getMessageId(), "freezeTime is invalid. startFreezeTime=" + startFreezeTime + ", endFreezeTime=" + endFreezeTime);
                return;
            }

            DriverFreezeEvent event = new DriverFreezeEvent();
            event.setAccountType(accountType);
            event.setChgAllOrder("1"); // 1-改派一定时间范围的订单
            event.setDriverId(driverId);
            event.setSupplierId(supplierId);
            event.setEndTime(endFreezeTime);
            event.setStartTime(startFreezeTime);
            event.setIsToSendType("1");// 1-不改派
            event.setFromDrvGuide(true);
            driverFreezeEventHandler.handle(event);

            return;
        }

        //司机下线
        if(message.getTags().contains("tag_driver_offline")){
            DriverOfflineEvent event = new DriverOfflineEvent();
            event.setDriverId(driverId);
            event.setAccountType(accountType);
            event.setSupplierId(supplierId);
            event.setIsFromPenalty("0");
            event.setFromDrvGuide(true);
            driverOfflineEventHandler.handle(event);
        }
    }

    /**
     * 司机冻结
     * @param message
     */
    public void handleDriverFreeze(Message message){
        // 1.供应商,2.BD
        String accountType = message.getStringProperty("accountType");
        /** 1是不改派 2.自动改派*/
        String isToSendType = message.getStringProperty("isToSendType");
        /**改派的司机id*/
        String drvId = message.getStringProperty("drvId");
        /** 冻结开始时间*/
        String startTime = message.getStringProperty("startTime");
        /** 冻结结束时间*/
        String endTime = message.getStringProperty("endTime");
        /** 0:改派所有订单, 1:改派一定时间范围的订单*/
        String chgAllOrder = message.getStringProperty("chgAllOrder");
        long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
        //司机id有效性检查
        if (StringUtils.isEmpty(drvId) || drvId.equalsIgnoreCase("0") || StringUtils.isEmpty(chgAllOrder)) {
            logger.error("handleDriverFreeze_param_error",LocalJsonUtils.toJson(message));
            return;
        }
        //指定不改派
        if (isToSendType.equalsIgnoreCase("1")) {
            logger.info("handleDriverFreeze_not_dispatch", LocalJsonUtils.toJson(message));
            return;
        }
        //1:改派一定时间范围的订单 时间必须有值
        if("1".equals(chgAllOrder)){
            if(StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)){
                logger.info("freeze_time_null", LocalJsonUtils.toJson(message));
                return;
            }
        }
        DriverFreezeEvent event = new DriverFreezeEvent();
        event.setAccountType(accountType);
        event.setChgAllOrder(chgAllOrder);
        event.setDriverId(Long.valueOf(drvId));
        event.setSupplierId(supplierId);
        event.setEndTime(endTime);
        event.setStartTime(startTime);
        event.setIsToSendType(isToSendType);
        driverFreezeEventHandler.handle(event);
    }

    /**
     * 司机下线
     * @param message
     */
    public void handleDriverOffline(Message message){
        String drvId = message.getStringProperty("drvId");
        String accountType = message.getStringProperty("accountType");
        String isFromPenalty = message.getStringProperty("isFromPenalty");
        long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
        if (StringUtils.isEmpty(drvId) || drvId.equalsIgnoreCase("0")) {
            logger.error("handleDriverOffline_param_error",LocalJsonUtils.toJson(message));
            return;
        }
        DriverOfflineEvent event = new DriverOfflineEvent();
        event.setIsFromPenalty(isFromPenalty);
        event.setDriverId(Long.valueOf(drvId));
        event.setSupplierId(supplierId);
        event.setAccountType(accountType);
        driverOfflineEventHandler.handle(event);
    }
}
