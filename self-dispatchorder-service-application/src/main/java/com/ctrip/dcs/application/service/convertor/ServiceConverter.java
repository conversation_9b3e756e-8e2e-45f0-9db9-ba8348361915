package com.ctrip.dcs.application.service.convertor;

import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DispatchRightVO;
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO;
import com.ctrip.dcs.infrastructure.common.constants.DescribeConstants;

/**
 * 服务转换器
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/9/22 17:13
 */
public class ServiceConverter {

    public static DispatchRightVO buildRight(DriverRightsVO driverRightsVO, ReassignTaskEnum.RightTypeEnum rightTypeEnum) {
        DispatchRightVO dispatchRightVO = new DispatchRightVO();
        dispatchRightVO.setRightId(String.valueOf(rightTypeEnum.getCode()));
        dispatchRightVO.setRightName(rightTypeEnum.getDesc());
        // fixme review 点 这个属性的赋值怎么这么怪。。。biz.DispatchRightInfo.135
        dispatchRightVO.setCycleType(rightTypeEnum.getCode());
        dispatchRightVO.setCycleStartTime(DateUtil.formatDate(rightTypeEnum == ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT ? DateUtil.getWeekStartTime() : DateUtil.getMonthStartTime(), DateUtil.DATETIME_FORMAT));
        dispatchRightVO.setCycleEndTime(DateUtil.formatDate(rightTypeEnum == ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT ? DateUtil.getWeekEndTime() : DateUtil.getMonthEndTime(), DateUtil.DATETIME_FORMAT));
        dispatchRightVO.setTimes(driverRightsVO.getReassignUsedByWeek());
        dispatchRightVO.setRightDetailDesc(String.format(rightTypeEnum == ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT ? DescribeConstants.FREE_RIGHTS : DescribeConstants.LEAVE_RIGHTS, driverRightsVO.getReassignUsedByWeek()));
        return dispatchRightVO;
    }
}