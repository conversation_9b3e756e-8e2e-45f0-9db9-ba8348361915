package com.ctrip.dcs.application.command;

import cn.hutool.core.math.Money;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.command.validator.SaaSTripBindCarValidator;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.UpdateDspOrderConfirmRecordService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail;
import com.ctrip.dcs.domain.dsporder.event.BindCarAndTakenEvent;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmRollBackEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;


@Component
public class SaaSTripBindCarExeCmd extends AbstractSaaSOperateExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SaaSTripBindCarExeCmd.class);

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;
    @Autowired
    private SaaSTripBindCarValidator saaSTripBindCarValidator;
    @Autowired
    private DriverOrderGateway driverOrderGateway;
    @Autowired
    private UpdateDspOrderConfirmRecordService updateDspOrderConfirmRecordService;
    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;
    @Autowired
    protected MessageProviderService messageProducer;
    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;


    @Override
    public String execute(SaaSOperateDriverCarCommand cmd) {
        logger.info("SaaSTripBindCarExeCmd_execute", LocalJsonUtils.toJson(cmd));
        SaaSBusinessVO saaSBusinessVO = this.buildSaaSBusinessVO(cmd);
        logger.info("SaaSTripBindCarExeCmd_buildSaaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
        //构建日志参数-vbk融合项目
        cmd.setAssignResLogVO(buildResLogVO(saaSBusinessVO,cmd));
        //业务校验
        this.validBusiness(cmd, saaSBusinessVO);

        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, cmd.getDspOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
            // 原单修改待确认时不允许任何操作
            OrderModifyHelper.assertNotToBeConfirmed(dspOrderVO.getDspOrderId());
            Long supplierId = Objects.nonNull(dspOrderVO.getSupplierId()) ? Long.valueOf(dspOrderVO.getSupplierId()) : null;
            //匹配策略
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
            logger.info("SaaSTripBindCarExeCmd_matchAssignStrategy_enter", LocalJsonUtils.toJson(objectObjectHashMap));
            ScheduleTaskDO scheduleTaskDO = this.matchAssignStrategy(cmd, saaSBusinessVO);
            logger.info("SaaSTripBindCarExeCmd_matchAssignStrategy_exit", LocalJsonUtils.toJson(scheduleTaskDO));
            //派单检查
            CheckModel checkModel = super.preCheck(saaSBusinessVO, scheduleTaskDO, dspOrderVO);
            if(!checkModel.getCheckCode().isPass()){
                logger.warn("checkModel.getCheckCode().isPass()", LocalJsonUtils.toJson(checkModel));
                CheckCode checkCode = checkModel.getCheckCode();
                throw new BizException(String.valueOf(checkCode.getCode()), checkCode.getDesc());
            }
            saaSBusinessVO.setTransportGroupVO(checkModel.getModel().getTransportGroup());
            //判断新老订单
            logger.info("SaaSTripBindCarExeCmd_queryOrderIsNew_enter", LocalJsonUtils.toJson(dspOrderVO));
            Long driverId = saaSBusinessVO.getDriverVO().getDriverId();
            VehicleVO vehicleVO = saaSBusinessVO.getVehicleVO();
            //订单状态依据是否  >司机确认  执行不同逻辑
            if(Objects.equals(dspOrderVO.getOrderStatus(), OrderStatusEnum.DRIVER_CONFIRMED.getCode())){
                // OrderStatus == 司机确认
                // 派发单司机车辆确认,由司机单确认车辆信息
                HashMap<Object, String> objectObjectHashMapLog = new HashMap<>();
                objectObjectHashMapLog.put("dspOrderVO", LocalJsonUtils.toJson(dspOrderVO));
                objectObjectHashMapLog.put("driverId", LocalJsonUtils.toJson(driverId));
                objectObjectHashMapLog.put("vehicleVO", LocalJsonUtils.toJson(vehicleVO));
                logger.info("SaaSTripBindCarExeCmd_driverOrderGateway.confirmCar", LocalJsonUtils.toJson(objectObjectHashMapLog));
                driverOrderGateway.confirmCar(dspOrderVO.getDspOrderId(), dspOrderVO.getDriverOrderId(), driverId, vehicleVO);

                DriverOrderVO driverOrderVO = new DriverOrderVO(dspOrderVO.getDriverOrderId(), dspOrderVO.getDspOrderId(), driverId);
                return assign(scheduleTaskDO, saaSBusinessVO, cmd, supplierId, driverOrderVO);
            }else{
                //更新司机单
                HashMap<Object, String> objectObjectHashMapLog = new HashMap<>();
                objectObjectHashMapLog.put("dspOrderVO", LocalJsonUtils.toJson(dspOrderVO));
                objectObjectHashMapLog.put("driverId", LocalJsonUtils.toJson(driverId));
                objectObjectHashMapLog.put("vehicleVO", LocalJsonUtils.toJson(vehicleVO));
                logger.info("SaaSTripBindCarExeCmd_driverOrderGateway.confirmCar->>230", LocalJsonUtils.toJson(objectObjectHashMapLog));
                OperatorVO operatorVO = new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(), cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode());
                driverOrderGateway.confirmCar(dspOrderVO.getDspOrderId(), dspOrderVO.getDriverOrderId(), driverId, vehicleVO);
                // 更新应单记录的车辆和操作人信息
                updateDspOrderConfirmRecordService.update(dspOrderVO, vehicleVO, operatorVO);
                // vbk操作记录
                saveVBKOperateRecord(dspOrderVO, operatorVO);
                // 发送绑定车辆事件
                messageProducer.send(new BindCarAndTakenEvent(dspOrderVO.getDspOrderId(), dspOrderVO.getDriverOrderId(), driverId, vehicleVO.getCarId(), operatorVO));
                return dspOrderVO.getDspOrderId();
            }
        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } finally {
            lock.unlock();
        }
    }

    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        saaSTripBindCarValidator.validate(cmd, saaSBusinessVO);
    }

    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, SaaSOperateDriverCarCommand cmd){
        if(cmd.isNewProcess()){
            return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, null, SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        }
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, null, SysConstants.AssignRole.SYS_ROLE_SAAS);
    }

    @Override
    protected SaaSBusinessVO buildSaaSBusinessVO(SaaSOperateDriverCarCommand cmd) {
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(cmd.getDspOrderId());
        ParentCategoryEnum parentCategoryEnum = CategoryUtils.selfGetParentType(dspOrderVO);
        VehicleVO vehicleVO = queryVehicleService.query(Long.valueOf(cmd.getCarId()),parentCategoryEnum);
        if(Objects.isNull(dspOrderVO)){
            return new SaaSBusinessVO(null, null, vehicleVO, null);
        }
        Long supplierId = dspOrderVO.getSupplierId() != null ? dspOrderVO.getSupplierId().longValue() : 0L;
        DriverVO driverVO = queryDriverService.queryDriver(dspOrderVO.getDriverId(), parentCategoryEnum, supplierId);
        UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(dspOrderVO.getUserOrderId());
        TransportGroupVO transportGroupVO = queryTransportGroupService.queryTransportGroup(dspOrderVO.getTransportGroupId());
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO(dspOrderVO, driverVO, vehicleVO, userOrderDetail);
        saaSBusinessVO.setTransportGroupVO(transportGroupVO);
        return saaSBusinessVO;
    }


    /**
     * 匹配策略
     * @param cmd
     * @param saaSBusinessVO
     * @return
     */
    @Override
    protected ScheduleTaskDO matchAssignStrategy(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        //匹配指派策略
        SubSkuVO subSku = subSkuRepository.find(getSubskuId(saaSBusinessVO.getDspOrderVO(), saaSBusinessVO.getTransportGroupVO(), cmd));
        ScheduleTaskDO task = ScheduleTaskDO.builder()
                .subSku(subSku)
                .dspOrderId(cmd.getDspOrderId())
                .status(ScheduleTaskStatus.INIT)
                .reward(new Money(cmd.getExtraFee()))
                .scheduleId(System.currentTimeMillis())
                .taskId(NumberUtils.LONG_ONE)
                .round(NumberUtils.INTEGER_ONE)
                .dspRewardStrategyId(NumberUtils.LONG_ZERO)
                .build();
        return task;
    }

    @Override
    protected String assign(ScheduleTaskDO task, SaaSBusinessVO saaSBusinessVO, SaaSOperateDriverCarCommand cmd,
                             Long supplierId, DriverOrderVO driverOrder) {
        DspOrderVO order = saaSBusinessVO.getDspOrderVO();
        DriverVO driver = saaSBusinessVO.getDriverVO();
        TransportGroupVO transportGroup = saaSBusinessVO.getTransportGroupVO();
        VehicleVO vehicleVO = saaSBusinessVO.getVehicleVO();
        Long oldCarId = order.getCarId();
        VehicleVO oldVehicleVO = queryVehicleService.query(oldCarId,CategoryUtils.selfGetParentType(order));
        boolean confirm = false;
        try {
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(new SupplierVO(supplierId))
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicleVO)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(cmd.getEvent())
                    .operator(new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(),
                            cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                    .otherInfoFromSupplier(new OtherInfoFromSupplierVO(cmd.getDriverVisibleRemark(),cmd.getDriverSettlePrice(),cmd.getDriverSettleCurrency(),cmd.getSupplierRemark(),isNeedUpdateSupplierRemark(cmd.isNotUpdateSupplierRemark(),cmd.isBatchAssign()), cmd.getNotUpdateSettlePriceAndCurrency(), cmd.isNotUpdateDriverVisibleRemark()))
                    .build();
            confirmDspOrderService.confirm(confirmVO);
            // 应单成功
            confirm = true;
        } catch (Exception e) {
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("task", LocalJsonUtils.toJson(task));
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            objectObjectHashMap.put("driverOrder", LocalJsonUtils.toJson(driverOrder));
            logger.error("SaaSTripBindCarExeCmd_confirm", LocalJsonUtils.toJson(objectObjectHashMap));
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_AFTER_ERR.getBizException();
        } finally {
            if (!confirm) {
                // 数据库更新失败通知回滚
                messageProducer.send(new DriverOrderConfirmRollBackEvent(driverOrder.getDriverOrderId(),
                        driverOrder.getDspOrderId(), driverOrder.getDriverId(), oldVehicleVO, vehicleVO));
            }
        }
        return order.getDspOrderId();
    }

    private void saveVBKOperateRecord(DspOrderVO dspOrder, OperatorVO operator) {
        try {
            VBKOperationRecordVO record = vbkOperationRecordFactory.createSaasBindCarOperationRecord(dspOrder, operator);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.error("saveVBKOperateRecordError", e);
        }
    }

}
