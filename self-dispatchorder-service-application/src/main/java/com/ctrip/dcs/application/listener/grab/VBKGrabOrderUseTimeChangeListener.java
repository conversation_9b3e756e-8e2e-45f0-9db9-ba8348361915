package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class VBKGrabOrderUseTimeChangeListener extends MqListener{
    private static final Logger logger = LoggerFactory.getLogger(VBKGrabOrderUseTimeChangeListener.class);


    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    protected MessageProviderService messageProducer;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;



    @QmqLogTag(tagKeys = { "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void useTimeChange(Message message) {
        if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_USE_TIME_CHANGE_ERROR_COUNT) == 0){
            return;
        }
        String dspOrderId = message.getStringProperty("dspOrderId");
        logger.info("VBKGrabOrderUseTimeChangeListener_useTimeChange_dspOrderId", dspOrderId);
        try {
            useTimeChange(dspOrderId);
        } catch (Exception e) {
            logger.error("VBKGrabOrderUseTimeChangeListener_useTimeChange", e);
            throw new NeedRetryException("VBKGrabOrderUseTimeChangeListener_useTimeChange");
        }
    }


    @QmqLogTag(tagKeys = { "supplyOrderIds"})
    @QmqConsumer(prefix = EventConstants.QMQ_ORDER_SYS_EXPECT_BOOK_TIME_CHANGE, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void oldUseTimeChange(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_USE_TIME_CHANGE_ERROR_COUNT) == 0){
                return;
            }
            String supplyOrderIds = message.getStringProperty("supplyOrderIds");
            logger.info("VBKGrabOrderUseTimeChangeListener_oldUseTimeChange_dspOrderIds", supplyOrderIds);
            if(StringUtils.isBlank(supplyOrderIds)){
                return;
            }
            String[] split = supplyOrderIds.split(",");
            for (int i = 0; i < split.length; i++) {
                useTimeChange(split[i]);
            }
        } catch (Exception e) {
            logger.error("VBKGrabOrderUseTimeChangeListener_oldUseTimeChange", e);
            throw new NeedRetryException("VBKGrabOrderUseTimeChangeListener_oldUseTimeChange");
        }
    }


    public void useTimeChange(String dspOrderId) throws Exception {
        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrderForVBKGrab(dspOrderId,false);
        if(dspOrderVO == null){
            return;
        }
        //获取所有待抢订单
        List<VBKDriverGrabOrderDO> list = vbkDriverGrabOrderRepository.queryByDspOrderId(dspOrderId, GrabTaskStatus.IN_PROGRESS.getCode());
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Optional<Long> first = list.stream().map(VBKDriverGrabOrderDO::getSupplierId).findFirst();
        if(first.isEmpty()){
            return;
        }
        Long supplierId = first.get();
        logger.info("VBKGrabOrderUseTimeChangeListener_useTimeChange_supplierId", JsonUtils.toJson(supplierId));
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_GRAB_TASK_WITH_DSP_ORDER_ID, supplierId, dspOrderId);
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try{
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            List<VBKDriverGrabOrderDO> vbkDriverGrabOrderDOs = buildVbkDriverGrabOrderDO(list, dspOrderVO);
            vbkDriverGrabOrderRepository.updateBatchForUseTimeChange(vbkDriverGrabOrderDOs);
        } finally {
            lock.unlock();
        }
    }

    public List<VBKDriverGrabOrderDO> buildVbkDriverGrabOrderDO(List<VBKDriverGrabOrderDO> list, DspOrderVO dspOrderVO) throws Exception {
        try {
            Set<String> taskIds = list.stream().map(VBKDriverGrabOrderDO::getVbkGrabTaskId).collect(Collectors.toSet());
            List<VBKDriverGrabTaskDO> vbkDriverGrabTaskDOS = vbkDriverGrabTaskRepository.queryByVBKGrabTaskIds(Lists.newCopyOnWriteArrayList(taskIds), GrabTaskStatus.IN_PROGRESS.getCode());
            logger.info("VBKGrabOrderUseTimeChangeListener_buildVbkDriverGrabOrderDO_queryByVBKGrabTaskIds", LocalJsonUtils.toJson(vbkDriverGrabTaskDOS));
            Map<String, VBKDriverGrabTaskDO> vbkDriverGrabTaskDOMap = vbkDriverGrabTaskDOS.stream().collect(Collectors.toMap(VBKDriverGrabTaskDO::getVbkGrabTaskId, o -> o, (o1, o2) -> o2));
            if (vbkDriverGrabTaskDOMap.size() == 0) {
                return Lists.newArrayList();
            }
            Date lastConfirmCarTime = dspOrderVO.getLastConfirmCarTime();
            Date lastConfirmCarTimeBj = dspOrderVO.getLastConfirmCarTimeBj();
            Date estimatedUseTime = dspOrderVO.getEstimatedUseTime();
            List<VBKDriverGrabOrderDO> newVBKDriverGrabOrderDOs = new ArrayList<>();

            for (VBKDriverGrabOrderDO vbkDriverGrabOrderDO : list) {
                VBKDriverGrabTaskDO vbkDriverGrabTaskDO = vbkDriverGrabTaskDOMap.get(vbkDriverGrabOrderDO.getVbkGrabTaskId());
                if (vbkDriverGrabTaskDO == null) {
                    continue;
                }
                VBKDriverGrabOrderDO vbkDriverGrabOrderTemp = new VBKDriverGrabOrderDO();
                vbkDriverGrabOrderTemp.setVbkGrabTaskId(vbkDriverGrabOrderDO.getVbkGrabTaskId());
                vbkDriverGrabOrderTemp.setDspOrderId(dspOrderVO.getDspOrderId());
                //抢单截止时间计算
                buildGrabLimitTime(vbkDriverGrabTaskDO, vbkDriverGrabOrderTemp, lastConfirmCarTime, lastConfirmCarTimeBj, estimatedUseTime);
                //加价截止时间计算
                buildGrabRewardTime(vbkDriverGrabTaskDO, vbkDriverGrabOrderTemp, lastConfirmCarTime, lastConfirmCarTimeBj, estimatedUseTime);
                newVBKDriverGrabOrderDOs.add(vbkDriverGrabOrderTemp);
            }
            logger.info("VBKGrabOrderUseTimeChangeListener_buildVbkDriverGrabOrderDO_vbkDriverGrabOrderDO", LocalJsonUtils.toJson(newVBKDriverGrabOrderDOs));
            return newVBKDriverGrabOrderDOs;
        }catch (Exception ex){
            logger.error("VBKGrabOrderUseTimeChangeListener_buildVbkDriverGrabOrderDO", ex);
            throw ex;
        }
    }


    public void buildGrabRewardTime(VBKDriverGrabTaskDO vbkDriverGrabTaskDO, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, Date lastConfirmCarTime, Date lastConfirmCarTimeBj, Date estimatedUseTime) {
        BigDecimal makeUpEffectTime = vbkDriverGrabTaskDO.getGrabRewardHours();
        //非携程单有可能都是空的
        if (Objects.nonNull(makeUpEffectTime)) {
            int makeUpEffectTimeMinutes = makeUpEffectTime.multiply(new BigDecimal("60")).setScale(0, RoundingMode.HALF_UP).intValue() * -1;
            if (Objects.nonNull(lastConfirmCarTime)) {
                Date date = DateUtil.addMinutes(lastConfirmCarTime, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeLocal(new Timestamp(date.getTime()));
            }else {
                Date date = DateUtil.addMinutes(estimatedUseTime, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeLocal(new Timestamp(date.getTime()));
            }
            if (Objects.nonNull(lastConfirmCarTimeBj)) {
                Date date = DateUtil.addMinutes(lastConfirmCarTimeBj, makeUpEffectTimeMinutes);
                vbkDriverGrabOrderDO.setGrabRewardTimeBJ(new Timestamp(date.getTime()));
            }
        }
    }

    public void buildGrabLimitTime(VBKDriverGrabTaskDO vbkDriverGrabTaskDO, VBKDriverGrabOrderDO vbkDriverGrabOrderDO, Date lastConfirmCarTime, Date lastConfirmCarTimeBj, Date estimatedUseTime) {
        //非携程单有可能都是空的
        BigDecimal grabEndTimeDecimal = vbkDriverGrabTaskDO.getGrabLimitHours();
        int grabEndTimeMinutes = grabEndTimeDecimal.multiply(new BigDecimal("60")).setScale(0, RoundingMode.HALF_UP).intValue() * -1;
        if (Objects.nonNull(lastConfirmCarTime)) {
            Date date = DateUtil.addMinutes(lastConfirmCarTime, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeLocal(new Timestamp(date.getTime()));
        } else {
            Date date = DateUtil.addMinutes(estimatedUseTime, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeLocal(new Timestamp(date.getTime()));
        }
        if (Objects.nonNull(lastConfirmCarTimeBj)) {
            Date date = DateUtil.addMinutes(lastConfirmCarTimeBj, grabEndTimeMinutes);
            vbkDriverGrabOrderDO.setGrabLimitTimeBJ(new Timestamp(date.getTime()));
        }
    }
}
