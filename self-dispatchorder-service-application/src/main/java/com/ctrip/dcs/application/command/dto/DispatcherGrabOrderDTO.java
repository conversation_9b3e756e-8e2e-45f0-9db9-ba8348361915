package com.ctrip.dcs.application.command.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DispatcherGrabOrderDTO {

    private String dspOrderId;
    private String userOrderId;
    private Long supplierId;
    private Long transportGroupId;
    private Integer subSkuId;
    private String duid;
    private Integer confirmScene;
    private Date lastConfirmTime;

    private String orderSource;

    private Integer modifyVersion;
    /**
     * 修改角色  1、用户 2、客服
     */
    private Integer modifyRole;
}
