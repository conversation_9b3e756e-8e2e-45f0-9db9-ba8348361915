package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.command.api.UpdateGrabOrderSnapshotCommand;
import com.ctrip.dcs.application.command.grabOrderSnapshot.UpdateGrabOrderSnapshotExeCmd;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component("UpdateGrabOrderSnapshotTestService")
public class UpdateGrabOrderSnapshotTestService implements ITestDspOrderService {

    @Autowired
    private UpdateGrabOrderSnapshotExeCmd updateGrabOrderSnapshotExeCmd;

    @Override
    public String test(Map<String, String> params) {
        String dspOrderId = params.get("dspOrderId");
        updateGrabOrderSnapshotExeCmd.execute(new UpdateGrabOrderSnapshotCommand(dspOrderId, GrabDspOrderSnapshotEventEnum.CHANGE_USE_TIME));
        return LocalJsonUtils.toJson("success");
    }
}
