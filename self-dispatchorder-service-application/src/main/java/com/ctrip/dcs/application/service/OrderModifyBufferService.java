package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.AppPushOrderRemindRecord;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.factory.PushOrderRemindRecordFactory;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.dto.UserOrderModifyDTO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.infrastructure.adapter.soa.TourVendorNoticeServiceProxy;
import com.ctrip.dcs.infrastructure.common.converter.SoaConverter;
import com.ctrip.dcs.infrastructure.common.util.LocaleUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.tour.vendor.noticesvc.soa.v1.service.type.SendNoticeResponseType;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class OrderModifyBufferService {
    
    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    PushOrderRemindRecordFactory pushOrderRemindRecordFactory;

    @Autowired
    TourVendorNoticeServiceProxy tourVendorNoticeServiceProxy;

    @Resource
    CityRepository cityRepositoryImpl;


    private static final Logger logger = LoggerFactory.getLogger(OrderModifyBufferService.class);


    public void orderModifyBuffer(UserOrderModifyDTO orderModifyDTO){
        logger.info("orderModifyBuffer" + orderModifyDTO.getUserOrderId(),"orderModifyDTO="+ JacksonUtil.serialize(orderModifyDTO));
        //用户修改buffer有效的派发单应该只有一个
        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(orderModifyDTO.getUserOrderId());
        if(CollectionUtils.isEmpty(dspOrderDOS)){
            logger.info("orderModifyBuffer" + orderModifyDTO.getUserOrderId(),"dspOrder is empty");
            return;
        }
        DspOrderDO dspOrderDO = dspOrderDOS.get(0);
        BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetail(dspOrderDO.getDspOrderId());
        if(Objects.isNull(baseDetailVO) || OrderStatusEnum.isCancel(baseDetailVO.getOrderStatus())){
            logger.info("orderModifyBuffer"+orderModifyDTO.getUserOrderId(),"dspOrderId is cancel or null ,return");
            return;
        }
        //如果没有供应商或者运力组，则不需要推送
        if(baseDetailVO.getSupplierId() == null || baseDetailVO.getSupplierId().equals(0)  || baseDetailVO.getTransportGroupId() == null || baseDetailVO.getTransportGroupId().equals(0L)){
            logger.info("orderModifyBuffer" + orderModifyDTO.getUserOrderId(),"getSupplierId or transportGroupId is illegal,return");
            return;
        }
        TransportGroupVO transportGroupDetail = queryTransportGroupService.queryTransportGroup(baseDetailVO.getTransportGroupId());
        if (transportGroupDetail == null) {
            logger.info("orderModifyBuffer" + orderModifyDTO.getUserOrderId(),"transportGroupDetail is empty");
            throw new BizException("query transport group is failed");
        }
        //订单是境内订单
        if(cityRepositoryImpl.isChineseMainland(baseDetailVO.getCityId().longValue())){
            logger.info("orderModifyBuffer" + orderModifyDTO.getUserOrderId(),"order is domestic");
            return;
        }
        //境外订单发送站内信和邮件
        sendAppPush(baseDetailVO,transportGroupDetail);
        sendEmail(baseDetailVO,transportGroupDetail,orderModifyDTO);
    }

    private void sendAppPush(BaseDetailVO baseDetailVO, TransportGroupVO transportGroup) {

        try {
            Map<String, String> messageProperties = ImmutableMap.<String, String>builder()
                    .put("OrderID", baseDetailVO.getUserOrderId())
                    .build();
            //发送push
            String locale = LocaleUtil.getLocale(baseDetailVO.getBizAreaType(), transportGroup);
            AppPushOrderRemindRecord record = pushOrderRemindRecordFactory.createOrderModifyBufferAppPush(baseDetailVO, messageProperties, locale);
            SendNoticeResponseType orderPushResponseType = tourVendorNoticeServiceProxy.sendNotice(SoaConverter.toSendNoticeRequestType(record));
            if (orderPushResponseType == null || Strings.isNullOrEmpty(orderPushResponseType.getNoticeId())) {
                logger.error("orderModifyBuffer_app_push_" + baseDetailVO.getDspOrderId(), "orderModifyBuffer_app_push error");
                MetricsUtil.recordValue("orderModifyBuffer_app_push.error");
            }
        } catch (Exception e) {
            logger.error("orderModifyBuffer" + baseDetailVO.getDspOrderId(), e);
            MetricsUtil.recordValue("orderModifyBuffer.error");
        }

    }

    private void sendEmail(BaseDetailVO baseDetailVO, TransportGroupVO transportGroup,UserOrderModifyDTO orderModifyDTO) {
        try {
            // 通知开关是否打开
            if (transportGroup.getInformSwitch() == null || transportGroup.getInformSwitch().intValue() != 1) {
                logger.warn("orderModifyBuffer_email"+orderModifyDTO.getUserOrderId(), "transportGroupId switch close transportId=" + transportGroup.getTransportGroupId());
                return;
            }
            if (StringUtils.isBlank(transportGroup.getInformEmail())) {
                logger.warn("orderModifyBuffer_email" + baseDetailVO.getUserOrderId(), "transportGroup.getInformEmail() is null,return");
                return;
            }
            Map<String, String> messageProperties = ImmutableMap.<String, String>builder()
                    .put("OrderID", baseDetailVO.getUserOrderId())
                    .put("RealOrderappearancebuffer", orderModifyDTO.getUserChoseBufferMinutes().toString())
                    .put("usingTime", orderModifyDTO.getOrignalSysExpectBookTime())
                    .put("realusingTime", orderModifyDTO.getSysExpectBookTime())
                    .build();
            //发送push
            String locale = LocaleUtil.getLocale(baseDetailVO.getBizAreaType(), transportGroup);
            AppPushOrderRemindRecord record = pushOrderRemindRecordFactory.createOrderModifyBufferEmail(baseDetailVO, messageProperties, locale, transportGroup);
            SendNoticeResponseType orderPushResponseType = tourVendorNoticeServiceProxy.sendNotice(SoaConverter.toSendNoticeRequestType(record));

            if (orderPushResponseType == null || Strings.isNullOrEmpty(orderPushResponseType.getNoticeId())) {
                logger.error("orderModifyBufferEmailRemind" + baseDetailVO.getDspOrderId(), "orderModifyBufferEmailRemind error");
                MetricsUtil.recordValue("orderModifyBufferEmailRemind.error");
            }
        } catch (Exception e) {
            logger.error("orderModifyBufferEmailRemind" + baseDetailVO.getDspOrderId(), e);
            MetricsUtil.recordValue("orderModifyBufferEmailRemind.error");
        }
    }
}
