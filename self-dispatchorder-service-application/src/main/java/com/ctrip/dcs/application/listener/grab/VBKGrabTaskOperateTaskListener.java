package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.application.listener.grab.record.OperateTaskRecordService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;
import java.util.Objects;

@Component
public class VBKGrabTaskOperateTaskListener extends MqListener{

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabTaskOperateTaskListener.class);

    @Autowired
    private Map<String, OperateTaskRecordService> agentMap;

    @QmqConsumer(prefix = EventConstants.VBK_GRAB_TASK_OPERATE_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_OPERATE_SCHEDULE_ERROR_COUNT) == 0){
                return;
            }
            String jsonStr = message.getStringProperty("jsonStr");
            logger.info("VBKGrabTaskOperateTaskListener_onMessage", jsonStr);
            if(StringUtils.isBlank(jsonStr)){
                return;
            }
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = JsonUtils.fromJson(jsonStr, VBKGrabTaskOperateDTO.class);
            if(Objects.isNull(vbkGrabTaskOperateDTO)){
                return;
            }
            int operatorType = vbkGrabTaskOperateDTO.getOperatorType();
            VBKGrabOperationTypeEnum.OperationTypeEnum operationTypeEnum = VBKGrabOperationTypeEnum.OperationTypeEnum.fromCode(operatorType);
            if(operationTypeEnum == null){
                return;
            }
            if(Objects.isNull(agentMap)){
                return;
            }
            OperateTaskRecordService agent = agentMap.get(operationTypeEnum.getOperateService());
            if(Objects.nonNull(agent)){
                agent.buildAndInsertRecord(vbkGrabTaskOperateDTO);
                MetricsUtil.recordValue(MetricsConstants.VBK_GRAB_TASK_OPERATE_SCHEDULE_COUNT);
            }
//            MetricsUtil.recordValue(MetricsConstants.VBK_GRAB_TASK_OPERATE_SCHEDULE_ERROR_COUNT);
            // 埋点
        } catch (Exception e) {
            logger.error("VBKGrabTaskOperateTaskListener", e);
            // 埋点
//            MetricsUtil.recordValue(MetricsConstants.VBK_GRAB_TASK_OPERATE_SCHEDULE_ERROR_COUNT);
            throw new NeedRetryException("VBKGrabTaskOperateTaskListener");
        }
    }
}
