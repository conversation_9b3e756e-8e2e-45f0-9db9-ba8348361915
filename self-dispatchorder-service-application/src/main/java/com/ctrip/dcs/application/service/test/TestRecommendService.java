package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("TestRecommendService")
public class TestRecommendService implements ITestDspOrderService{
    @Autowired
    private RecommendService recommendService;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    protected SubSkuRepository subSkuRepository;
    @Autowired
    private QueryDriverService queryDriverService;


    @Override
    public String test(Map<String, String> params) {
        DspOrderVO dspOrder = queryDspOrderService.query(params.get("dspOrderId"));
        SubSkuVO subSkuVO = subSkuRepository.find(Integer.valueOf(params.get("subSkuId")));
        List<String> driverIds = Arrays.asList(params.get("driverIds").split(","));
        Set<Long> driverIdsSet = driverIds.stream().map(id->Long.valueOf(id)).collect(Collectors.toSet());
        Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : 0L;
        List<DriverVO> drivers = queryDriverService.queryDriver(driverIdsSet, CategoryUtils.selfGetParentType(dspOrder), supplierId);
        List<SortModel> sortModels = drivers.stream()
                .filter(Objects::nonNull)
                .map(driver -> new SortModel(new DspModelVO(dspOrder, driver)))
                .collect(Collectors.toList());
//        List<SortModel> result =  recommendService.testRecommend(dspOrder, subSkuVO, DuidVO.of(params.get("duid")),sortModels);

        List<SortModel> result =  recommendService.recommend(dspOrder,subSkuVO,DuidVO.of(params.get("duid")));
        return String.valueOf(!LocalCollectionUtils.isEmpty(result));
    }
}
