package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.VbkBindCarAndTakenExeCmd;
import com.ctrip.dcs.application.command.VbkChangeDriverExeCmd;
import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 客服更改司机
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class VbkChangeDriverExecutor extends AbstractRpcExecutor<VbkChangeDriverRequestType, VbkChangeDriverResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(VbkChangeDriverExecutor.class);

    @Autowired
    private VbkChangeDriverExeCmd vbkChangeDriverExeCmd;

    @Autowired
    private VbkBindCarAndTakenExeCmd vbkBindCarAndTakenExeCmd;

    @Override
    public VbkChangeDriverResponseType execute(VbkChangeDriverRequestType requestType) {
        VbkChangeDriverResponseType responseType = new VbkChangeDriverResponseType();
        if (!validatePrm(requestType)) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

        }
        try {
            Boolean isOnlyUpdateCar = vbkChangeDriverExeCmd.isOnlyUpdateCar(requestType.getDspOrderId(), requestType.getDriverId(), requestType.getCarLicense());
            if (isOnlyUpdateCar) {
                // 更换司机，如果司机相同，车辆不同，则走更换车辆流程
                vbkBindCarAndTakenExeCmd.execute(CustomerAssignDriverConverter.converter(requestType));
            } else {
                vbkChangeDriverExeCmd.execute(CustomerAssignDriverConverter.converter(requestType));
            }
        } catch (BizException e) {
            logger.warn("vbkChangeDriverBizException", e);
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            logger.error("vbkChangeDriverException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


    private boolean validatePrm(VbkChangeDriverRequestType requestType) {
        return org.apache.commons.lang.StringUtils.isNotBlank(requestType.getDspOrderId())
                && Objects.nonNull(requestType.getDriverId())
                && requestType.getDriverId() > 0L
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserName())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getSysUserAccount())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserType())
                && Objects.nonNull(requestType.getTransportGroupId())
                && Objects.nonNull(requestType.getSupplierId());
    }
}
