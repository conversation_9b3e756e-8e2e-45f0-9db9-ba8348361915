package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.ResetScheduleTaskExeCmd;
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.ResetScheduleTaskCommand;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class ExecuteVBKDriverGrabTaskListener {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteVBKDriverGrabTaskListener.class);

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @Autowired
    private ResetScheduleTaskExeCmd resetScheduleTaskExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.VBK_GRAB_DSP_TASK_TOPIC, consumerGroup = "100041593_create_schedule", idempotentChecker = EventConstants.IDEMPOTENT_CHECKER)
    public void onMessage(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            ScheduleDO vbkSystemSchedule = getExecutingScheduleByType(dspOrderId, ScheduleType.VBK_SYSTEM);
            if (vbkSystemSchedule != null) {
                logger.info("ExecuteVBKDriverGrabTaskListenerInfo_shutdown", "shutdown vbk system schedule, scheduleId:{}", vbkSystemSchedule.getScheduleId());
                shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(vbkSystemSchedule, ScheduleEventType.MANUAL_SHUTDOWN));
            }
            ScheduleDO schedule = getExecutingScheduleByType(dspOrderId, ScheduleType.VBK);
            if (schedule == null) {
                // 创建调度
                CreateScheduleCommand command = MessageConverter.INSTANCE.toCreateScheduleCommandByVBKGrabTask(message);
                createScheduleExeCmd.execute(command);
                // 埋点
                MetricsUtil.recordValue(MetricsConstants.CREATE_VBK_SCHEDULE_COUNT);
            } else {
                resetScheduleTaskExeCmd.execute(new ResetScheduleTaskCommand(schedule.getScheduleId()));
            }
        } catch (Exception e) {
            logger.error("ExecuteVBKDriverGrabTaskListenerError", e);
            MetricsUtil.recordValue(MetricsConstants.CREATE_SCHEDULE_ERROR_COUNT);
            throw ErrorCode.SCHEDULE_CREATE_ERROR.getBizException();
        }
    }

    public ScheduleDO getExecutingScheduleByType(String dspOrderId, ScheduleType scheduleType) {
        List<ScheduleDO> schedules = scheduleRepository.query(dspOrderId);
        for (ScheduleDO scheduleDO : schedules) {
            if (!scheduleDO.isShutdown() && scheduleDO.getType() == scheduleType) {
                return scheduleDO;
            }
        }
        return null;
    }
}
