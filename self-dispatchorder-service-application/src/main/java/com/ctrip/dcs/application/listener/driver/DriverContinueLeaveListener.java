package com.ctrip.dcs.application.listener.driver;

import com.ctrip.dcs.application.event.IDriverLeaveEventHandler;
import com.ctrip.dcs.application.event.converter.EventConverter;
import com.ctrip.dcs.application.event.dto.DriverContinueLeaveEvent;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.infrastructure.adapter.monitoring.SelfDspOrderMetric;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;

/**
 * 司机请假接力消费者
 * 详见：http://conf.ctripcorp.com/pages/viewpage.action?pageId=1957200779
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/11/9 19:50
 */
@Component
public class DriverContinueLeaveListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverContinueLeaveListener.class);
    @Resource
    private IDriverLeaveEventHandler driverLeaveEventHandler;

    @QmqConsumer(prefix = EventConstants.DRIVER_LEAVE_CONTINUE_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP)
    @QmqLogTag(tagKeys = {"driverId"})
    public void continueLeave(Message message) {
        logger.info("DriverContinueLeaveListener_msg", LocalJsonUtils.toJson(message));
        Result<DriverContinueLeaveEvent> eventResult = EventConverter.buildEvent(message);
        if (!eventResult.isSuccess()) {
            SelfDspOrderMetric.selfDspOrderDriverContinueLeaveResultRecordInc(Boolean.FALSE);
            return;
        }
        DriverContinueLeaveEvent event = eventResult.getData();
        Boolean res = driverLeaveEventHandler.handle(event.getDriverLeaveEvent(), event.getLeaveRights());
        if (res) {
            SelfDspOrderMetric.selfDspOrderDriverContinueLeaveResultRecordInc(Boolean.TRUE);
        } else {
            SelfDspOrderMetric.selfDspOrderDriverContinueLeaveResultRecordInc(Boolean.FALSE);
        }
    }

}