package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.provider.converter.CheckOrderDelayedDspConverter;
import com.ctrip.dcs.application.query.CheckDelayedDspExeQry;
import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO;
import com.ctrip.dcs.application.service.dto.DelayedDspCheckResultDTO;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.HighLevelCheckService;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 查询检查VBK订单是否延后派司机、
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Component
@ServiceLogTagPair(key = "userOrderId")
public class CheckVBKOrderDelayedDispatchExecutor extends AbstractRpcExecutor<CheckVBKOrderDelayedDispatchRequestType, CheckVBKOrderDelayedDispatchResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CheckVBKOrderDelayedDispatchExecutor.class);

    @Autowired
    private CheckDelayedDspExeQry checkDelayedDspExeQry;

    @Autowired
    private HighLevelCheckService highLevelCheckService;


    /**
     * 查询VBK订单是否延后派司机
     *
     * @param request 请求参数
     * @return 检查结果 {@link CheckVBKOrderDelayedDispatchResponseType}
     */
    @Override
    public CheckVBKOrderDelayedDispatchResponseType execute(CheckVBKOrderDelayedDispatchRequestType request) {
        CheckVBKOrderDelayedDispatchResponseType responseType = new CheckVBKOrderDelayedDispatchResponseType();

        try {
            CheckOrderDelayedDspDTO checkReq = CheckOrderDelayedDspConverter.toDTO(request);
            logger.info("CheckVBKOrderDelayedDispatchExecutorInfo", JacksonSerializer.INSTANCE().serialize(checkReq));
            DelayedDspCheckResultDTO resultDTO = checkDelayedDspExeQry.checkOrderDelayedDispatch(checkReq);
            responseType.setIsDelayedDispatch(resultDTO.isDelayedDispatch());
            responseType.setConfirmType(resultDTO.getOrderConfirmType() != null ? resultDTO.getOrderConfirmType().getCode() : "");
            responseType.setLastConfirmCarTime(resultDTO.getLastConfirmCarTime());
            responseType.setLastConfirmCarTimeBj(resultDTO.getLastConfirmCarTimeBj());
            return ServiceResponseUtils.success(responseType);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("checkVBKOrderDelayedDispatch error", e);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    /**
     * 前置逻辑处理
     *
     * @param request 请求参数
     */
    @Override
    public void onExecuting(CheckVBKOrderDelayedDispatchRequestType request) {
        if (StringUtils.isBlank(request.getUserOrderId())
                || request.getCityId() == null
                || CategoryCodeEnum.getByType(request.getCategoryCode()).isEmpty()
                || request.getVehicleGroupid() == null
                || StringUtils.isBlank(request.getEstimatedUseTime())
                || StringUtils.isBlank(request.getEstimatedUseTimeBj())
                || request.getUseDays() == null || BigDecimal.ZERO.compareTo(request.getUseDays()) >= 0
                || request.getSkuId() == null
                || request.getFromLocation() == null || request.getFromLocation().getLatitude() == null || request.getFromLocation().getLongitude() == null
                || request.getToLocation() == null || request.getToLocation().getLatitude() == null || request.getToLocation().getLongitude() == null
                || request.getDistributionChannel() == null
                || request.isUrgentOrder() == null
        ) {
            throw new ServiceValidationException("invalid arguments");
        }

        // 高等级用户处理
        if (request.isHighGradeOrder() == null) {
            request.setHighGradeOrder(highLevelCheckService.isHighLevelUserOrder(request.getCityId().intValue(), request.getVipGrade()));
        }

    }
}
