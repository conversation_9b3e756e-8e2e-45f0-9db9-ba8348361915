package com.ctrip.dcs.application.command;

import cn.hutool.core.math.Money;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.command.validator.SaaSTripChangeDriverValidator;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class SaaSTripChangeDriverExeCmd extends AbstractSaaSOperateExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SaaSTripChangeDriverExeCmd.class);

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;
    @Autowired
    private SaaSTripChangeDriverValidator saaSTripChangeDriverValidator;

    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        saaSTripChangeDriverValidator.validate(cmd, saaSBusinessVO);
    }

    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, SaaSOperateDriverCarCommand cmd){
        if(cmd.isNewProcess()){
            return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, null, SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        }
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, null, SysConstants.AssignRole.SYS_ROLE_SAAS);
    }

    @Override
    protected SaaSBusinessVO buildSaaSBusinessVO(SaaSOperateDriverCarCommand cmd) {
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(cmd.getDspOrderId());
        if(Objects.isNull(dspOrderVO)){
            return new SaaSBusinessVO(null, null, null, null);
        }
        dspOrderVO.setDriverVisibleRemark(cmd.getDriverVisibleRemark());
        Long supplierId = dspOrderVO.getSupplierId() != null ? dspOrderVO.getSupplierId().longValue() : 0L;
        ParentCategoryEnum parentCategoryEnum = CategoryUtils.selfGetParentType(dspOrderVO);
        VehicleVO vehicleVO = queryVehicleService.query(cmd.getCarId() == null ? 0 : Long.valueOf(cmd.getCarId()), parentCategoryEnum);
        DriverVO driverVO = queryDriverService.queryDriver(Long.valueOf(cmd.getDriverId()), parentCategoryEnum, supplierId);
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO(dspOrderVO, driverVO, vehicleVO);
        return saaSBusinessVO;
    }


    /**
     * 匹配策略
     * @param cmd
     * @param saaSBusinessVO
     * @return
     */
    @Override
    protected ScheduleTaskDO matchAssignStrategy(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        //匹配指派策略
        SubSkuVO subSku = subSkuRepository.find(getSubskuId(saaSBusinessVO.getDspOrderVO(), saaSBusinessVO.getTransportGroupVO(), cmd));
        ScheduleTaskDO task = ScheduleTaskDO.builder()
                .subSku(subSku)
                .dspOrderId(cmd.getDspOrderId())
                .status(ScheduleTaskStatus.INIT)
                .reward(new Money(cmd.getExtraFee()))
                .scheduleId(System.currentTimeMillis())
                .taskId(NumberUtils.LONG_ONE)
                .round(NumberUtils.INTEGER_ONE)
                .dspRewardStrategyId(NumberUtils.LONG_ZERO)
                .build();
        return task;
    }

    @Override
    protected String assign(ScheduleTaskDO task, SaaSBusinessVO saaSBusinessVO, SaaSOperateDriverCarCommand cmd,
                             Long supplierId, DriverOrderVO driverOrder) {
        DspOrderVO order = saaSBusinessVO.getDspOrderVO();
        DriverVO driver = saaSBusinessVO.getDriverVO();
        TransportGroupVO transportGroup = saaSBusinessVO.getTransportGroupVO();
        VehicleVO vehicleVO = saaSBusinessVO.getVehicleVO();
        boolean confirm = false;
        try {
            //三期会出现只有司机的情况，如果司机身上没有车，参数里也未指定车辆信息，则走司机确认流程
            if(cmd.isNewProcess()){
                if((Objects.isNull(driver.getCar()) || StringUtils.isEmpty(driver.getCar().getCarLicense())) && vehicleVO == null){
                    DriverConfirmVO confirmVO = DriverConfirmVO.builder()
                            .dspOrder(saaSBusinessVO.getDspOrderVO())
                            .serviceProvider(new ServiceProviderVO(saaSBusinessVO.getDspOrderVO().getSpId()))
                            .supplier(new SupplierVO(supplierId))
                            .transportGroup(saaSBusinessVO.getTransportGroupVO())
                            .driver(driver)
                            .duid(DuidVO.of(task))
                            .driverOrderId(driverOrder.getDriverOrderId())
                            .rewardAmount("0")
                            .event(cmd.getEvent())
                            .operator(new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(), cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                            .otherInfoFromSupplier(new OtherInfoFromSupplierVO(cmd.getDriverVisibleRemark(), cmd.getDriverSettlePrice(),cmd.getDriverSettleCurrency(),cmd.getSupplierRemark(),isNeedUpdateSupplierRemark(cmd.isNotUpdateSupplierRemark(),cmd.isBatchAssign()), cmd.getNotUpdateSettlePriceAndCurrency(), cmd.isNotUpdateDriverVisibleRemark()))
                            .build();
                    confirmDspOrderService.confirm(confirmVO);
                    // 应单成功
                    confirm = true;
                    return order.getDspOrderId();
                }
            }
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(new SupplierVO(supplierId))
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicleVO)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(cmd.getEvent())
                    .operator(new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(),
                            cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                    .otherInfoFromSupplier(new OtherInfoFromSupplierVO(cmd.getDriverVisibleRemark(), cmd.getDriverSettlePrice(),cmd.getDriverSettleCurrency(),cmd.getSupplierRemark(),!cmd.isNotUpdateSupplierRemark(), cmd.getNotUpdateSettlePriceAndCurrency(), cmd.isNotUpdateDriverVisibleRemark()))
                    .build();
            confirmDspOrderService.confirm(confirmVO);
            // 应单成功
            confirm = true;
        } catch (Exception e) {
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("task", LocalJsonUtils.toJson(task));
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            objectObjectHashMap.put("driverOrder", LocalJsonUtils.toJson(driverOrder));
            logger.error("SaaSTripChangeDriverExeCmd_confirm", LocalJsonUtils.toJson(objectObjectHashMap));
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_AFTER_ERR.getBizException();
        } finally {
            if (!confirm) {
                // 未接单成功，取消司机单
                messageProducer.send(new DriverOrderConfirmFailEvent(driverOrder.getDriverOrderId(), driverOrder.getDspOrderId(), driverOrder.getDriverId()));
            }
        }
        return order.getDspOrderId();
    }
}
