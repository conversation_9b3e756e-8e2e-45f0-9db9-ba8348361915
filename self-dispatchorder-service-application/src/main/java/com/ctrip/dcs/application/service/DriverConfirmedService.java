package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.PushOrderRemindExeCmd;
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.PushOrderRemindCommand;
import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.PushOrderRemindType;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.event.ImNoticeEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.adapter.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.infrastructure.common.constants.NoticeEnum;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.service.QueryDriverServiceImpl;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 司机确认后处理服务
 * <AUTHOR>
 */
@Component
public class DriverConfirmedService {

    private static final Logger logger = LoggerFactory.getLogger(DriverConfirmedService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;
    @Autowired
    private PushOrderRemindExeCmd pushOrderRemindExeCmd;

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    private TmsTransportServiceProxy tmsTransportServiceProxy;

    @Autowired
    private DriverPointsGateway driverPointsGateway;
    
    @Resource
    private QueryDriverServiceImpl queryDriverService;

    public void confirmed(String dspOrderId, Long confirmRecordId) {
        DspOrderVO dspOrder = queryDspOrderService.query(dspOrderId);
        DspOrderConfirmRecordVO confirmRecord = dspOrderConfirmRecordRepository.find(confirmRecordId);
        // VBK操作记录
        sendVBKOperationRecord(dspOrder, confirmRecord);
        //境外订单调度确认进单提醒
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(confirmRecord.getDriverInfo().getTransportGroupId());
        pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrder, transportGroup, Lists.newArrayList(PushOrderRemindType.IGT_ORDER_DISPATCHER_IVR)));

        messageProducer.send(new ImNoticeEvent(dspOrder.getUserOrderId(), "", NoticeEnum.TAKEN.getCode(), "", "", confirmRecord.querySupplierIdStr(), confirmRecord.queryDriverIdStr()));

        // 终止调度
        shutdownSchedule(dspOrder);
        // 取消调度抢单
        cancelDispatcherGrabOrder(dspOrder);
        // 取消抢单大厅
        cancelGrabCentre(dspOrderId);
    }

    private void cancelGrabCentre(String dspOrderId) {
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);
    }

    private void sendVBKOperationRecord(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        try {
            VBKOperationRecordVO record = vbkOperationRecordFactory.createOperationRecord(dspOrder, confirmRecord);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void shutdownSchedule(DspOrderVO dspOrder) {
        try {
            List<ScheduleDO> schedules = scheduleRepository.query(dspOrder.getDspOrderId());
            if (CollectionUtils.isEmpty(schedules)) {
                return;
            }
            for (ScheduleDO schedule : schedules) {
                shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(schedule, ScheduleEventType.DSP_ORDER_NOT_DISPATCHING));
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void cancelDispatcherGrabOrder(DspOrderVO dspOrderDO) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(dspOrderDO.getUserOrderId());
            cancelDispatcherGrabOrderExeCmd.execute(command);
        } catch (Exception e) {
            logger.error("cancel_dispatcher_grab_order_error", String.format("cancel_dispatcher_grab_order_error,userOrderId=%s", dspOrderDO.getUserOrderId()), e);
        }
    }

    //司机/车辆确认后，更新司机等级
    public Boolean contractUpdateDrvLevel(Long driverId, Long confirmRecordId) {
       try {
           //判断确认信息是否存在
           DspOrderConfirmRecordVO confirmRecord = dspOrderConfirmRecordRepository.find(confirmRecordId);
           if(Objects.isNull(confirmRecord)){
               logger.warn("contractUpdateDrvLevel_confirmRecord_isEmpty", "confirmRecordId:{}", confirmRecordId);
               return Boolean.FALSE;
           }
           //包车司机查询司机等级
           if(driverPointsGateway.isQueryGuideLevel(confirmRecord.getCategoryCode())){
               Map<Long, Integer> result = driverPointsGateway.batchQueryDriverGuideLevel(Arrays.asList(driverId));
               if(!LocalCollectionUtils.isEmpty(result)){
                   Integer driverLevel = result.get(driverId);
                   if(driverLevel != null){
                       updateDriverLevel(confirmRecord,driverLevel.toString(),driverId,confirmRecordId);
                   }
               }
               return true;
           }
           //查询供应链司机信息  包车品类上面已经拦截了，下面都是非包车品类单
           String udl = Optional.ofNullable(confirmRecord.getDriverInfo()).map(DspOrderConfirmRecordVO.DriverRecord::getDriverUdl).orElse(null);
           QueryDrvDetailSOARequestType requestType = new QueryDrvDetailSOARequestType();
           requestType.setDrvId(driverId);
           QueryDrvDetailSOAResponseType responseType = queryDriverService.queryDrvDetail(requestType, udl);
           if (responseType == null || responseType.getData() == null) {
               logger.warn("contractUpdateDrvLevel_driver_isEmpty", "driverId:{}", driverId);
               return Boolean.FALSE;
           }
           //境内司机不做处理
           QueryDrvDetailDTOSOA detailDTOSOA = responseType.getData();
           if(detailDTOSOA.getAreaScope() == null){
               return Boolean.TRUE;
           }

           if(detailDTOSOA.getAreaScope().intValue() == 0){
               return Boolean.TRUE;
           }
           //查询司机等级
           Map<Long, String> driverLevelMap = driverPointsGateway.queryDriverLevel(Arrays.asList(driverId),null);
           //等级为空，不处理
           if(MapUtils.isEmpty(driverLevelMap)){
               return Boolean.TRUE;
           }
           String driverLevel = StringUtils.isEmpty(driverLevelMap.get(driverId)) ? "" : driverLevelMap.get(driverId);
           //更新司机等级
           updateDriverLevel(confirmRecord,driverLevel,driverId,confirmRecordId);
           return Boolean.TRUE;
       }catch (Exception e){
           logger.error("contractUpdateDrvLevel_error", "confirmRecordId:{},driverId:{}", confirmRecordId,driverId,e);
       }
        return Boolean.FALSE;
    }

    /**
     * 更新司机等级
     * @param confirmRecord
     * @param driverLevel
     * @param driverId
     * @param confirmRecordId
     * @return
     */
    public boolean updateDriverLevel(DspOrderConfirmRecordVO confirmRecord,String driverLevel,Long driverId, Long confirmRecordId){
        //更新等级
        DspOrderConfirmRecordVO.ConfirmSnapShotInfo confirmSnapShotInfo =  confirmRecord.getConfirmSnapshotInfo();
        if (Objects.isNull(confirmSnapShotInfo)) {
            confirmSnapShotInfo = new  DspOrderConfirmRecordVO.ConfirmSnapShotInfo();
        }
        confirmSnapShotInfo.setDriverLevel(driverLevel);
        String snapShotInfoJson = JsonUtil.toJson(confirmSnapShotInfo);
        return dspOrderConfirmRecordRepository.updateDriverLevelToSnapInfo(confirmRecordId, driverId, snapShotInfoJson);
    }
}
