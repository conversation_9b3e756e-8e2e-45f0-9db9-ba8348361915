package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CreateIvrRecordCommand;
import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordRequestType;
import org.springframework.beans.BeanUtils;

public class CreateIvrRecordConverter {

    public static CreateIvrRecordCommand converter(CreateIvrRecordRequestType request) {
        Assert.notNull(request);
        Assert.notNull(request.getData());
        CreateIvrRecordCommand cmd = new CreateIvrRecordCommand();
        IvrRecordDO ivrRecordDO = new IvrRecordDO();
        BeanUtils.copyProperties(request.getData(),ivrRecordDO);
        cmd.setIvrRecordDO(ivrRecordDO);
        return cmd;
    }
}
