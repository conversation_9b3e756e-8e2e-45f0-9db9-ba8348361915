package com.ctrip.dcs.application.service.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class TestDspOrderServiceContext {
    @Autowired
    private GrabOrderCheckTestService grabOrderCheckTestService;
    @Autowired
    private DispatchModifyDriverCheckTestService dispatchModifyDriverCheckTestService;
    @Autowired
    private DriverLeaveTestService driverLeaveTestService;
    @Autowired
    private DriverSupplierModifyTestService driverSupplierModifyTestService;
    @Autowired
    private DriverFreezeTestService driverFreezeTestService;
    @Autowired
    private DriverCityModifyTestService driverCityModifyTestService;
    @Autowired
    private DriverInfoModifyTestService driverInfoModifyTestService;
    @Autowired
    private DriverOfflineTestService driverOfflineTestService;
    @Autowired
    private DriverVehicleChangeTestService driverVehicleChangeTestService;
    @Autowired
    private DriverVehicleInfoModifyTestService driverVehicleInfoModifyTestService;
    @Autowired
    private DriverVehicleTypeModifyTestService driverVehicleTypeModifyTestService;
    @Autowired
    private CharterOrderDispatchModifyTestService charterOrderDispatchModifyTestService;
    @Autowired
    private GrabOrderDetailRepositoryTestService grabOrderDetailRepositoryTestService;
    @Autowired
    private SpContractInfoTestService spContractInfoTestService;
    @Autowired
    private PlatformGeoTestService platformGeoTestService;

    @Autowired
    private ManualCarStatusCheckTestService manualCarStatusCheckTestService;
    @Autowired
    EstimatedTimeChangeNoticeDispatcherTestService estimatedTimeChangeNoticeDispatcherTestService;

    @Autowired
    CarSeriesCheckTestService carSeriesCheckTestService;

    @Autowired
    SerializeBroadcastInfoService serializeBroadcastInfoService;

    @Autowired
    private ConflictGatewayTestService conflictGatewayTestService;

    @Autowired
    private DriverDomainServiceGatewayTestService driverDomainServiceGatewayTestService;

    @Autowired
    private QueryDriverLocationTestService queryDriverLocationTestService;
    @Autowired
    private QueryDriverOrderTestService queryDriverOrderTestService;
    @Autowired
    private TestDriverGuideService testDriverGuideService;
    @Autowired
    private UpdateDspOrderConfirmRecordTestService updateDspOrderConfirmRecordTestService;
    @Autowired
    private TestStaticInventoryVisitor testStaticInventoryVisitor;
    @Autowired
    private DriverOrderIndexTestService driverOrderIndexTestService;
    @Autowired
    private UpdateGrabOrderSnapshotTestService updateGrabOrderSnapshotTestService;
    @Autowired
    private TestRecommendService testRecommendService;
    @Autowired
    private TestDowngradeCarTypeOrderDriverCheckService testDowngradeCarTypeOrderDriverCheckService;
    @Autowired
    private TestSupplierOrderDiversionGatewayService testSupplierOrderDiversionGatewayService;
    @Autowired
    private TestConflictRedispatchEventHandlerService testConflictRedispatchEventHandlerService;
    @Autowired
    private TestNearOrderSortFeature testNearOrderSortFeature;
    @Autowired
    private TestDriverCategorySortFeature testDriverCategorySortFeature;

    private Map<String, ITestDspOrderService> serviceMap = new HashMap<>();
    @PostConstruct
    public void initMap(){
        initServiceMap();
    }

    private void initServiceMap(){
        serviceMap.put("GrabOrderCheckTestService",grabOrderCheckTestService);
        serviceMap.put("DispatchModifyDriverCheckTestService",dispatchModifyDriverCheckTestService);
        serviceMap.put("DriverLeaveTestService",driverLeaveTestService);
        serviceMap.put("DriverSupplierModifyTestService",driverSupplierModifyTestService);
        serviceMap.put("DriverFreezeTestService",driverFreezeTestService);
        serviceMap.put("DriverCityModifyTestService",driverCityModifyTestService);
        serviceMap.put("DriverInfoModifyTestService",driverInfoModifyTestService);
        serviceMap.put("DriverOfflineTestService",driverOfflineTestService);
        serviceMap.put("DriverVehicleChangeTestService",driverVehicleChangeTestService);
        serviceMap.put("DriverVehicleInfoModifyTestService",driverVehicleInfoModifyTestService);
        serviceMap.put("DriverVehicleTypeModifyTestService",driverVehicleTypeModifyTestService);
        serviceMap.put("CharterOrderDispatchModifyTestService",charterOrderDispatchModifyTestService);
        serviceMap.put("GrabOrderDetailRepositoryTestService",grabOrderDetailRepositoryTestService);
        serviceMap.put("SpContractInfoTestService",spContractInfoTestService);
        serviceMap.put("ManualCarStatusCheckTestService",manualCarStatusCheckTestService);
        serviceMap.put("EstimatedTimeChangeNoticeDispatcherTestService",estimatedTimeChangeNoticeDispatcherTestService);
        serviceMap.put("CarSeriesCheckTestService",carSeriesCheckTestService);
        serviceMap.put("SerializeBroadcastInfoService",serializeBroadcastInfoService);
        serviceMap.put("ConflictGatewayTestService",conflictGatewayTestService);
        serviceMap.put("DriverDomainServiceGatewayTestService",driverDomainServiceGatewayTestService);
        serviceMap.put("QueryDriverLocationTestService",queryDriverLocationTestService);
        serviceMap.put("PlatformGeoTestService",platformGeoTestService);
        serviceMap.put("QueryDriverOrderTestService",queryDriverOrderTestService);
        serviceMap.put("TestDriverGuideService",testDriverGuideService);
        serviceMap.put("UpdateDspOrderConfirmRecordTestService", updateDspOrderConfirmRecordTestService);
        serviceMap.put("TestStaticInventoryVisitor",testStaticInventoryVisitor);
        serviceMap.put("DriverOrderIndexTestService",driverOrderIndexTestService);
        serviceMap.put("UpdateGrabOrderSnapshotTestService",updateGrabOrderSnapshotTestService);
        serviceMap.put("TestRecommendService",testRecommendService);
        serviceMap.put("TestDowngradeCarTypeOrderDriverCheckService",testDowngradeCarTypeOrderDriverCheckService);
        serviceMap.put("TestSupplierOrderDiversionGatewayService",testSupplierOrderDiversionGatewayService);
        serviceMap.put("TestConflictRedispatchEventHandlerService", testConflictRedispatchEventHandlerService);
        serviceMap.put("TestNearOrderSortFeature",testNearOrderSortFeature);
        serviceMap.put("TestDriverCategorySortFeature", testDriverCategorySortFeature);
    }
    public ITestDspOrderService getService(String serviceName){
        return serviceMap.get(serviceName);
    }
}
