package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.infrastructure.adapter.soa.TourVendorNoticeServiceProxy;
import com.ctrip.dcs.infrastructure.common.converter.SoaConverter;
import com.ctrip.dcs.self.dispatchorder.interfaces.OrderPushRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.OrderPushResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.tour.vendor.noticesvc.soa.v1.service.type.SendNoticeResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 供应商消息推送通知接口
 *
 * <AUTHOR> ZhangZhen.
 * @create 2024/11/21 15:58
 */
@Component
@ServiceLogTagPair(key = "noticeId")
public class OrderPushExecutor extends AbstractRpcExecutor<OrderPushRequestType, OrderPushResponseType> implements Validator<OrderPushRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(OrderPushExecutor.class);

    @Autowired
    private TourVendorNoticeServiceProxy tourVendorNoticeServiceProxy;

    @Override
    public OrderPushResponseType execute(OrderPushRequestType req) {

        OrderPushResponseType orderPushResponseType = new OrderPushResponseType();

        SendNoticeResponseType responseType = tourVendorNoticeServiceProxy.sendNotice(SoaConverter.toSendNoticeRequestType(req));

        if (responseType != null && !Strings.isNullOrEmpty(responseType.getNoticeId())) {
            orderPushResponseType.setSuccess(Boolean.TRUE);
            return ServiceResponseUtils.success(orderPushResponseType);
        }

        logger.error("orderPushFail", "request:{} responseType:{}", JsonUtil.toJson(req), JsonUtil.toJson(responseType));

        return ServiceResponseUtils.fail(orderPushResponseType);

    }

    @Override
    public void validate(AbstractValidator<OrderPushRequestType> validator, OrderPushRequestType req) {
        validator.ruleFor("bizType").notNull();
        validator.ruleFor("noticeId").notNull().notEmpty();
    }

}