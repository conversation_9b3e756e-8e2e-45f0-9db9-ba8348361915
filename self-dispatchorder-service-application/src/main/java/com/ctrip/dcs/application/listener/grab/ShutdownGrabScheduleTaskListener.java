package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.CancelGrabOrderSnapshotCommand;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.application.command.grabOrderSnapshot.CancelGrabOrderSnapshotExeCmd;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.UUID;

/**
 * 终止抢单派发任务
 * <AUTHOR>
 */
@Component
public class ShutdownGrabScheduleTaskListener extends MqListener{

    private static final Logger logger = LoggerFactory.getLogger(ShutdownGrabScheduleTaskListener.class);

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;
    @Autowired
    private ScheduleRepository scheduleRepository;
    @Autowired
    private GrabCentreRepository grabCentreRepository;
    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;
    @Autowired
    private CancelGrabOrderSnapshotExeCmd cancelGrabOrderSnapshotExeCmd;


    @QmqConsumer(prefix = EventConstants.SHUT_DOWN_GRAB_SCHEDULE_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        if(super.validConsumerTimes(message, MetricsConstants.SHUT_DOWN_GRAB_SCHEDULE_ERROR_COUNT) == 0){
            return;
        }
        try {
            String orderIdStr = message.getStringProperty("orderIdStr");
            logger.info("ShutdownGrabScheduleTaskListener_onMessage", orderIdStr);
            if(StringUtils.isBlank(orderIdStr)){
                return;
            }
            String[] orderIds = orderIdStr.split(",");
            for (String orderId : orderIds) {
                List<ScheduleDO> schedules = scheduleRepository.query(orderId);
                if (CollectionUtils.isNotEmpty(schedules)) {
                    for (ScheduleDO schedule : schedules) {
                        logger.info("ShutdownGrabScheduleTaskListener_schedules", "shutdown schedule id : {}", schedule.getScheduleId());
                        shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(schedule, ScheduleEventType.VBK_GRAB_TASK));
                    }
                }
                deleteGrabCentre(orderId);
                cancelGrabOrderSnapshot(orderId);
            }
            // 埋点
//            MetricsUtil.recordValue(MetricsConstants.SHUT_DOWN_GRAB_SCHEDULE_COUNT);
        } catch (Exception e) {
            logger.error("ShutdownGrabScheduleTaskListener", e);
            // 埋点
//            MetricsUtil.recordValue(MetricsConstants.SHUT_DOWN_GRAB_SCHEDULE_ERROR_COUNT);
            throw e;
        }
    }

    private void cancelGrabOrderSnapshot(String orderId) {
        try {
            cancelGrabOrderSnapshotExeCmd.execute(new CancelGrabOrderSnapshotCommand(orderId));
        } catch (Exception e) {
            logger.warn("ShutdownGrabScheduleTaskListener", e);
        }
    }

    private void deleteGrabCentre(String dspOrderId) {
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);
    }
}
