package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.UpdateOrderSettleToDriverBeforeTakenCommand;
import com.ctrip.dcs.application.service.DriverConfirmedService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.OrderVersionEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.OldDuidVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;



@Component
public class VBKGrabOrderTakenListener extends AbstractUpdateOrderStatusAndFinishTask{

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabOrderTakenListener.class);

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private DriverConfirmedService driverConfirmedService;


    @Autowired
    private UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd;

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    public void driverConfirm(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderTakenListener_driverConfirm_dspOrderId", dspOrderId);
            driverConfirm(dspOrderId);
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_driverConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_driverConfirm");
        }
    }

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    public void driverCarConfirm(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderTakenListener_driverCarConfirm_dspOrderId", dspOrderId);
            driverConfirm(dspOrderId);
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_driverCarConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_driverCarConfirm");
        }
    }


    @QmqConsumer(prefix = EventConstants.SAAS_DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    public void saasDriverCarConfirm(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderTakenListener_saasDriverCarConfirm_dspOrderId", dspOrderId);
            driverConfirm(dspOrderId);
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_saasDriverCarConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_saasDriverCarConfirm");
        }
    }

    @QmqConsumer(prefix = EventConstants.SAAS_DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "vbk_driver_Level" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    public void saasDriverCarConfirmDrvLevel(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderTakenListener_saasDriverCarConfirm_dspOrderId", dspOrderId);
            if (Strings.isBlank(dspOrderId)) {
                return;
            }
            driverConfirmDrvLevel(dspOrderId);
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_saasDriverCarConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_saasDriverCarConfirm");
        }
    }

    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "supplyOrderIds", alias = "supplyOrderIds")
    public void oldDriverCarConfirm(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderIds = message.getStringProperty("supplyOrderIds");
            logger.info("VBKGrabOrderTakenListener_oldDriverCarConfirm_dspOrderIds", dspOrderIds);
            if(StringUtils.isBlank(dspOrderIds)){
                return;
            }
            String[] split = dspOrderIds.split(",");
            for (int i = 0; i < split.length; i++) {
                driverConfirm(split[i]);
            }
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_oldDriverCarConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_oldDriverCarConfirm");
        }
    }

    @QmqLogTagPair(key = "supplyOrderId", alias = "supplyOrderId")
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void oldDriverConfirm(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_TAKEN_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("supplyOrderId");
            logger.info("VBKGrabOrderTakenListener_oldDriverConfirm_dspOrderId", dspOrderId);
            driverConfirm(dspOrderId);
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_oldDriverConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_oldDriverConfirm");
        }
    }

    @QmqLogTagPair(key = "supplyOrderId", alias = "supplyOrderId")
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM, consumerGroup = "vbk_grab_settle_to_driver" + EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void oldDriverConfirmSettleToDriver(Message message) {
        try {
            String dspOrderId = message.getStringProperty("supplyOrderId");
            String dispatcherConfirmed = message.getStringProperty("msg");
            if ("dispatcher_confirmed".equals(dispatcherConfirmed)) {
                DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(dspOrderId);
                if(Objects.isNull(dspOrderVO)){
                    throw new BizException("dspOrderVO_is_null");
                }
                // 调度确认消息
                Long supplierId = message.getLongProperty("supplierId");
                Integer serviceProviderId = message.getIntProperty("serviceProviderId");
                updateOrderDetailSettlementInfo(dspOrderVO, supplierId, serviceProviderId);
            }
        }catch (Exception ex){
            logger.error("VBKGrabOrderTakenListener_oldDriverConfirm", ex);
            throw new NeedRetryException("VBKGrabOrderTakenListener_oldDriverConfirm");
        }
    }



    public void driverConfirm(String dspOrderId) throws Exception {
        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrderForVBKGrab(dspOrderId, true);
        logger.info("VBKGrabOrderTakenListener_driverConfirm_dspOrderVO", JsonUtils.toJson(dspOrderVO));
        if(Objects.isNull(dspOrderVO)){
            throw new NeedRetryException("driverConfirm");
        }
        String driverOrderId = dspOrderVO.getDriverOrderId();
        Integer orderStatus = dspOrderVO.getOrderStatus();
        int grabTaskStatus = GrabTaskStatus.FINISH.getCode();
        grabTaskStatus = getGrabTaskStatus(dspOrderVO, grabTaskStatus);
        Map<String, String> map = new HashMap<>();
        map.put("dspOrderId", dspOrderId);
        map.put("orderStatus", String.valueOf(orderStatus));
        map.put("grabTaskStatus", String.valueOf(grabTaskStatus));
        map.put("driverOrderId", driverOrderId);
        logger.info("VBKGrabOrderTakenListener_driverConfirm", JsonUtils.toJson(map));
        super.dealMessageLogic(dspOrderId, orderStatus, grabTaskStatus, driverOrderId);
    }

    public void driverConfirmDrvLevel(String dspOrderId) throws Exception {
        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrderForVBKGrab(dspOrderId,true);

        if(Objects.isNull(dspOrderVO)){
            throw new NeedRetryException("driverConfirm");
        }
        Long driverId = dspOrderVO.getDriverId();
        Long confirmRecordId = dspOrderVO.getConfirmRecordId();
        if(driverId == null || driverId <= 0 || confirmRecordId == null || confirmRecordId <= 0){
            return;
        }
        driverConfirmedService.contractUpdateDrvLevel(driverId,confirmRecordId);
    }


    public int getGrabTaskStatus(DspOrderVO dspOrderVO, int grabTaskStatus) {
        String orderConfirmRecordDuid = dspOrderVO.getOrderConfirmRecordDuid();
        Integer takenType;
        if (StringUtils.isNotBlank(orderConfirmRecordDuid) && !orderConfirmRecordDuid.contains("null")){
            if(OldDuidVO.isOldDuid(orderConfirmRecordDuid)){
                OldDuidVO oldDuidVO = OldDuidVO.of(orderConfirmRecordDuid);
                takenType = oldDuidVO.getTakenType();
            }else{
                DuidVO duidVO = DuidVO.of(dspOrderVO.getOrderConfirmRecordDuid());
                takenType = duidVO.getTakenType();
            }
            if (Objects.nonNull(takenType) && takenType.equals(TakenType.VBK_DRIVER_GRAB.getCode())) {
                grabTaskStatus = GrabTaskStatus.SUCCESS.getCode();
            }
        }
        return grabTaskStatus;
    }


    public void updateOrderDetailSettlementInfo(DspOrderVO dspOrder, Long supplierId, Integer serviceProviderId) {
        // 查询订单是否给司机结算
        updateOrderSettleToDriverBeforeTakenCmd.execute(new UpdateOrderSettleToDriverBeforeTakenCommand(
                        dspOrder.getUserOrderId(),
                        dspOrder.getDspOrderId(),
                        dspOrder.getCarTypeId(),
                        dspOrder.getCategoryCode().getType(),
                        supplierId,
                        serviceProviderId,
                        dspOrder.getOrderSourceCode(),
                        OrderVersionEnum.QUNAR.getVersion(),
                        dspOrder.getSalesMode(),
                        dspOrder.getCityId()
                )
        );
    }
}
