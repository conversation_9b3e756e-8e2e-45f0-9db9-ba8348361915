package com.ctrip.dcs.application.service.reDispath.roleGroup;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.service.RedispatchRightAndPointService;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/10/8 20:07
 */
@Service(value = ReassignTaskEnum.USER_CLIENT)
public class UserClientHandle implements RightAndPointService {

    @Resource
    private RedispatchRightAndPointService redispatchRightAndPointService;

    @Override
    public Result<RightAndPointVO> queryRightAndPointDTO(RightAndPointCommand command, BaseDetailVO detailVO) {
        Result<RightAndPointVO> voResult = redispatchRightAndPointService.checkPunishReason(command, detailVO);
        if (voResult != null && voResult.isSuccess() && voResult.getData() != null) {
            voResult.getData().setReasonDetail(null);
            return voResult;
        }
        return voResult;
    }

    @Override
    public Result convertAndCheckPrm(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        return null;
    }

}