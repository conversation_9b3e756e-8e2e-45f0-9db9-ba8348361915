package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderExtendAttributeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dsp_order_extend_attribute")
public class DspOrderExtendAttributeDealImpl implements BinlogDeal{

    @Autowired
    private DspOrderExtendAttributeRepository dspOrderExtendAttributeRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        dspOrderExtendAttributeRepository.clearCache(dspOrderId);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> dspOrderExtendAttributeRepository.clearCache(dspOrderId));
    }
}
