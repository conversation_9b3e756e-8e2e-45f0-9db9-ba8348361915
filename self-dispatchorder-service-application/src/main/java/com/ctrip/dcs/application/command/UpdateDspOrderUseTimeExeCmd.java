package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.UpdateDspOrderUseTimeCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderUseTimeVO;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class UpdateDspOrderUseTimeExeCmd {

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderDetailRepository dspOrderDetailRepository;

    public void execute(UpdateDspOrderUseTimeCommand command) {
        dspOrderRepository.updateUseTime(command.getDspOrderUseTimes());
        for (DspOrderUseTimeVO time : command.getDspOrderUseTimes()) {
            if (Objects.isNull(time) || Objects.isNull(time.getSelfBuffer())) {
                continue;
            }
            DspOrderDetailDO orderDetail = dspOrderDetailRepository.find(time.getDspOrderId());
            Map<String, Object> extendInfoMap = ObjectUtils.defaultIfNull(orderDetail.getExtendInfoMap(), Maps.newHashMap());
            extendInfoMap.put(SysConstants.Order.SELF_BUFFER, time.getSelfBuffer());
            orderDetail.setExtendInfo(JacksonSerializer.INSTANCE().serialize(extendInfoMap));
            dspOrderDetailRepository.updateExtendInfo(orderDetail);
        }
    }
}
