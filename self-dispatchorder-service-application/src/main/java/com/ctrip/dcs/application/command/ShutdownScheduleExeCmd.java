package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.value.ScheduleRecordVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 终止调度
 * <AUTHOR>
 */
@Component
public class ShutdownScheduleExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(ShutdownScheduleExeCmd.class);

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private ScheduleRecordRepository scheduleRecordRepository;

    @Autowired
    private ScheduleRecordFactory scheduleRecordFactory;

    public void execute(ShutdownScheduleCommand command) {
        ScheduleDO schedule = command.getSchedule();
        if (schedule.isShutdown()) {
            logger.info("schedule shutdown", "schedule has shutdown.schedule id:{}", schedule.getScheduleId());
            return;
        }
        List<ScheduleTaskDO> tasks = taskRepository.query(schedule.getScheduleId(), schedule.getDspOrderId());
        schedule.shutdown(tasks);
        scheduleRepository.shutdown(schedule);
    }
}
