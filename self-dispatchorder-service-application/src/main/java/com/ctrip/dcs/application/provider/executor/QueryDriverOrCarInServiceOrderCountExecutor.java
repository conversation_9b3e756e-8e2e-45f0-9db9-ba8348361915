package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderResponseType;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.stereotype.Component;

@Component
public class QueryDriverOrCarInServiceOrderCountExecutor extends AbstractRpcExecutor<QueryDriverOrCarInServiceOrderRequestType, QueryDriverOrCarInServiceOrderResponseType> {

    @Override
    public QueryDriverOrCarInServiceOrderResponseType execute(QueryDriverOrCarInServiceOrderRequestType requestType) {
        QueryDriverOrCarInServiceOrderResponseType responseType = new QueryDriverOrCarInServiceOrderResponseType();
        responseType.setOrderNum(0);
        return ServiceResponseUtils.success(responseType);
    }
}
