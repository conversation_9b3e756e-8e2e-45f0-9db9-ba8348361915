package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.QueryIvrRecordCommand;
import com.ctrip.dcs.application.provider.converter.QueryIvrRecordConverter;
import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO;
import com.ctrip.dcs.domain.dsporder.entity.QueryIvrRecordDO;
import com.ctrip.dcs.domain.dsporder.repository.IvrRecordRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class QueryIvrRecordExeCmd {

    @Autowired
    private IvrRecordRepository ivrRecordRepository;

    public List<IvrRecordInfo> execute(QueryIvrRecordCommand cmd){
        QueryIvrRecordDO ivrRecordDO = new QueryIvrRecordDO();
        BeanUtils.copyProperties(cmd,ivrRecordDO);
        List<IvrRecordDO> ivrRecordDOS = ivrRecordRepository.queryIvrRecordList(ivrRecordDO);
        return ivrRecordDOS.stream().map(QueryIvrRecordConverter::converterInfo).collect(Collectors.toList());
    }
}
