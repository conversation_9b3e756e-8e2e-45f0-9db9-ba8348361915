package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class VbkBindCarAndTakenExeCmd extends AbstractAssignDriverExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(VbkBindCarAndTakenExeCmd.class);

    @Autowired
    private DriverOrderGateway driverOrderGateway;

    @Override
    protected void preCheck(DspOrderVO order, DriverVO driverVO, OperateAssignDriverCommand cmd) {
        Long orderSupplierId = 0L;
        DspOrderDO dspOrderDO = dspOrderRepository.find(order.getDspOrderId());
        if (dspOrderDO.getConfirmRecordId() != null && dspOrderDO.getConfirmRecordId() > 0L) {
            DspOrderConfirmRecordVO recordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
            orderSupplierId = recordVO.getSupplierInfo().getSupplierId();
            cmd.setTransportGroupId(recordVO.getDriverInfo().getTransportGroupId());
            //包车需要车
            if (CategoryCodeEnum.isCharterOrder(dspOrderDO.getCategoryCode()) && !Integer.valueOf(YesOrNo.YES.getCode()).equals(driverVO.getDrvTemporaryMark())) {
                if (StringUtils.isEmpty(cmd.getCarLicense())) {
                    throw ErrorCode.ERROR_PARAMS.getBizException();
                }
                //待绑司机身上的车和订单上的车是一样
                if (recordVO.getCarInfo() != null && driverVO.getCar() != null && !StringUtils.isEmpty(driverVO.getCar().getCarLicense())
                        && !StringUtils.isEmpty(recordVO.getCarInfo().getCarLicense()) &&

                        recordVO.getCarInfo().getCarLicense().equals(driverVO.getCar().getCarLicense())) {
                    throw ErrorCode.ERROR_PARAMS.getBizException();
                }
            }
            if (!cmd.getDriverId().equals(recordVO.getDriverInfo().getDriverId())) {
                throw ErrorCode.ERROR_PARAMS.getBizException();
            }
        }

        //canAssistSupplerIds
        Set<String> canAssistSuppliers = Sets.newHashSet(driverVO.getSupplier().getSupplierId().toString());
        canAssistSuppliers.addAll(CollectionUtils.isEmpty(driverVO.getSupplier().getDispatchSupplierIdList()) ? Lists.newArrayList() : driverVO.getSupplier().getDispatchSupplierIdList().stream().map(Object::toString).collect(Collectors.toList()));
        if (!canAssistSuppliers.contains(orderSupplierId.toString())
                || !cmd.getSupplierId().equals(orderSupplierId)
                || !canAssistSuppliers.contains(cmd.getSupplierId().toString())) {
            logger.info("manual_operation_bind_car_error", "Cross supplier assignment not allowed.dspOrderId=" + cmd.getDspOrderId());
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }
    }


    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, OperateAssignDriverCommand cmd) {
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, groupVO.getTransportGroupMode().getCode(), SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
    }

    @Override
    protected boolean assign(ScheduleTaskDO task, DspModelVO model, VehicleVO vehicle, OperateAssignDriverCommand cmd) {
        DspOrderVO order = model.getOrder();
        DriverVO driver = model.getDriver();
        TransportGroupVO transportGroup = model.getTransportGroup();
        // 创建司机单
        driverOrderGateway.confirmCar(order.getDspOrderId(), order.getDriverOrderId(), cmd.getDriverId(), vehicle);

        try {
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(new SupplierVO(order.getSupplierId().longValue()))
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(task))
                    .driverOrderId(order.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(cmd.getEvent())
                    .operator(new OperatorVO(cmd.getSysUserAccount(), cmd.getOperUserName(), cmd.getOperUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                    .build();
            confirmDspOrderService.confirm(confirmVO);
        } catch (Exception e) {
            logger.error("manual_operation_bind_car_error", e);
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_ERR.getBizException();
        }
        return true;
    }
}
