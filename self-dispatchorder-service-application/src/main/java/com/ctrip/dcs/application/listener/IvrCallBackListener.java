package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.dsporder.entity.ivr.CIvrCallback;
import com.ctrip.dcs.domain.dsporder.entity.ivr.OutCallResultDataInfo;
import com.ctrip.dcs.infrastructure.adapter.qconfig.IvrConfig;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.ctrip.dcs.infrastructure.adapter.qconfig.IvrConfig.IVR_LANGUAGE_SKILL_CONFIG_INFO;
import static com.ctrip.dcs.infrastructure.adapter.qconfig.IvrConfig.URGENT_IVR_LANGUAGE_SKILL_CONFIG_INFO;

@Component
public class IvrCallBackListener {
//    private static final Logger logger = LoggerFactory.getLogger(IvrCallBackListener.class);

    @Autowired
    private IvrConfig ivrConfig;

    @Autowired
    private IvrCallService ivrCallService;
    /**
     * ivr结果 回调处理
     * @param msg
     */
    @QmqConsumer(prefix = EventConstants.IVR_CALLBACK, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void ivrCallBack(Message msg) {

        String outCallResultData = msg.getStringProperty("OutCallResultData");
        if (StringUtils.isEmpty(outCallResultData)) {
            return;
        }
        CIvrCallback callback = JacksonUtil.deserialize(outCallResultData, CIvrCallback.class);

        if(callback == null){
            return;
        }
        ivrCallService.ivrCallBackNotice4CHF(callback);
    }

    //ivr回调消息通知
    @QmqConsumer(prefix = EventConstants.IVR_OUT_MSG_NOTICE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "OutCallRecordGUID", alias = "recordGuid")
    public void ivrOutMsgNotice(Message msg) {
        String skillGroupId = msg.getStringProperty("OutCallOriginalID");
        //如果是ivr的技能组ID,则执行ivr流程境外临时派遣
        if (ivrConfig.isIvrSkillGroupId(skillGroupId,IVR_LANGUAGE_SKILL_CONFIG_INFO)) {
            String data = msg.getStringProperty("OutCallResultData");
            if (StringUtils.isNotEmpty(data)) {
                OutCallResultDataInfo outCallResultDataInfo = JacksonUtil.deserialize(data, new TypeReference<OutCallResultDataInfo>() {
                });
                ivrCallService.ivrCallBackNotice4Igt(outCallResultDataInfo);
            }
        }
        //急单ivr
        if (ivrConfig.isIvrSkillGroupId(skillGroupId,URGENT_IVR_LANGUAGE_SKILL_CONFIG_INFO)) {
            String data = msg.getStringProperty("OutCallResultData");
            if (StringUtils.isNotEmpty(data)) {
                OutCallResultDataInfo outCallResultDataInfo = JacksonUtil.deserialize(data, new TypeReference<OutCallResultDataInfo>() {
                });
                ivrCallService.ivrUrgentOrderCallBackNotice4Igt(outCallResultDataInfo);
            }
        }
        //司机未出发/未到达迟到风险ivr
        List<String> lateRiskIvrSkillGroupIds = Arrays.asList(Optional.ofNullable(ivrConfig.getLateRiskIvrSkillGroupId()).orElse(StringUtils.EMPTY).split(","));
        if (lateRiskIvrSkillGroupIds.contains(skillGroupId)) {
            String data = msg.getStringProperty("OutCallResultData");
            if (StringUtils.isNotEmpty(data)) {
                OutCallResultDataInfo outCallResultDataInfo = JacksonUtil.deserialize(data, new TypeReference<OutCallResultDataInfo>() {});
                ivrCallService.lateRiskCallBackNotice(outCallResultDataInfo);
            }
        }
    }

}
