package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 派发加价策略定时任务
 * <AUTHOR>
 */
@Component
public class DspOrderRewardStrategyJob extends BaseJob {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderRewardStrategyJob.class);

    @Autowired
    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository;

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    @QSchedule("com.crtip.dcs.dsp.order.reward.strategy.job")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer size = getProperty(parameter, "size", 300);
        List<DspOrderRewardStrategyDO> strategies = dspOrderRewardStrategyRepository.queryByRewardTime(new Date(), size);
        logger.info("DspOrderRewardStrategyJobInfo", JacksonSerializer.INSTANCE().serialize(strategies));
        if (CollectionUtils.isEmpty(strategies)) {
            return;
        }
        for (DspOrderRewardStrategyDO strategy : strategies) {
            dspOrderRewardStrategyService.activeDspOrderRewardStrategy(strategy);
        }
    }
}
