package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.domain.common.enums.ErrorCode;

/**
 * <AUTHOR>
 */
public class ValidateException extends Exception {

    private ErrorCode errorCode;

    public ValidateException(ErrorCode errorCode) {
        super(errorCode.getDesc());
        this.errorCode = errorCode;

    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }
}
