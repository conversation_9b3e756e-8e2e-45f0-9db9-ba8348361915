package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CheckDspOrderInventoryCommand;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.OldDuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CheckDspOrderInventoryExeCmd {

    @Autowired
    private CheckService checkService;

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private SubSkuRepository subSkuRepository;

    public List<CheckModel> execute(CheckDspOrderInventoryCommand command) {
        DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(command.getDspOrderId());
        if (dspOrderVO == null) {
            return Collections.emptyList();
        }
        SubSkuVO subSkuVO = subSkuRepository.find(command.getSubSkuId());
        if (subSkuVO == null) {
            return Collections.emptyList();
        }
        DuidVO duid = DuidVO.of(dspOrderVO.getDspOrderId(), subSkuVO.getSubSkuId(), subSkuVO.getDspType().getCode(), subSkuVO.getTakenType().getCode());
        return checkService.check(new DspCheckCommand(dspOrderVO, subSkuVO, duid));
    }

}
