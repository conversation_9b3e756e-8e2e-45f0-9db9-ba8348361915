package com.ctrip.dcs.application.scheduler;


import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.driver.domain.account.DriverUDL;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig;
import com.ctrip.dcs.infrastructure.service.QueryDriverServiceImpl;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.utils.Pair;
import com.dianping.cat.utils.StringUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2025/4/10 10:13
 */
@Component
public class SyncDrvUdlAndUidJob {
    private static Logger logger = LoggerFactory.getLogger(SyncDrvUdlAndUidJob.class);
    @Resource
    DspOrderRepository dspOrderRepository;
    @Resource
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;
    @Resource
    QueryDriverServiceImpl queryDriverService;
    @Resource
    SysSwitchConfig sysSwitchConfig;
    @Resource
    BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @QSchedule("com.ctrip.dcs.confirm.record.sync.udl.uid.job")
    public void run(Parameter parameter) {
        Transaction transaction = Cat.newTransaction("SyncDrvUdlAndUidJob", "Start");
        try {
           execute(parameter);
        } catch (Throwable e) {
            logger.error(e);
        } finally {
            transaction.complete();
        }
    }

    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        String globalTraceId = LoggerContext.getCurrent().getGlobalTraceId();
        //yyyy-MM-dd HH:mm:ss
        String startCreateTimeStr = parameter.getProperty("startCreateTime", String.class);
        String endCreateTimeStr = parameter.getProperty("endCreateTime", String.class);
        if (StringUtils.isEmpty(startCreateTimeStr) || StringUtils.isEmpty(endCreateTimeStr)) {
            logger.info("sync_udl_uid_start_" + startCreateTimeStr + endCreateTimeStr, "time param illegal.");
            return;
        }
        try {
            logger.info("sync_udl_uid_start_" + startCreateTimeStr + "_" + endCreateTimeStr, "globalTraceId:" + globalTraceId);
            List<Pair<String, String>> list = splitTime(startCreateTimeStr, endCreateTimeStr);
            for (Pair<String, String> pair : list) {
                if (!sysSwitchConfig.getSyncDrvUdlAndUidSwitch()) {
                    logger.info("sync_udl_uid_start_" + pair.getKey() + "_" + pair.getValue(), "syncDrvUdlAndUid switch off.");
                    return;
                }
                logger.info("sync_udl_uid_perHour_start_" + pair.getKey() + "_" + pair.getValue(), "flush data start");
                //查询派发单
                Timestamp startDate = Timestamp.valueOf(pair.getKey());
                Timestamp endDate = Timestamp.valueOf(pair.getValue());
                List<DspOrderDO> dspOrderDOList = dspOrderRepository.find(startDate, endDate);
                List<String> dspOrderIds = dspOrderDOList.stream().map(DspOrderDO::getDspOrderId).toList();
                for (String dspOrderId : dspOrderIds) {
                    List<DspOrderConfirmRecordVO> confirmRecordVOS = dspOrderConfirmRecordRepository.findByDspOrderId(dspOrderId);
                    for (DspOrderConfirmRecordVO confirmRecord : confirmRecordVOS) {
                        if (!sysSwitchConfig.getSyncDrvUdlAndUidSwitch()) {
                            logger.info("sync_udl_uid_start_" + pair.getKey() + "_" + pair.getValue(), "syncDrvUdlAndUid switch off.");
                            return;
                        }
                        syncDrvUdlAndUid(confirmRecord);
                        //间隔时长
                        Thread.sleep(businessTemplateInfoConfig.getFlushSyncDrvUdlAndUidIntervalTime());
                    }
                }
                logger.info("sync_udl_uid_perHour_end_" + pair.getKey() + "_" + pair.getValue(), "flush data end");
            }
        } catch (Exception ex) {
            logger.error("sync_udl_uid_error_" + startCreateTimeStr + "_" + endCreateTimeStr, "globalTraceId:" + globalTraceId);
            logger.error(ex);
        }
        logger.info("sync_udl_uid_end_" + startCreateTimeStr + "_" + endCreateTimeStr, "globalTraceId:" + globalTraceId);
    }
    
    public void syncDrvUdlAndUid(DspOrderConfirmRecordVO confirmRecord) {
        DspOrderConfirmRecordVO.DriverRecord driverInfo = confirmRecord.getDriverInfo();
        if (Objects.isNull(driverInfo)) {
            return;
        }
        if (StringUtils.isNotEmpty(driverInfo.getDriverUdl())) {
            return;
        }
        if (driverInfo.getDriverId() == null || Objects.equals(driverInfo.getDriverId(), 0L)) {
            return;
        }
        List<DriverUDL> driverUDLS = queryDriverService.getDrvUdls(Lists.newArrayList(String.valueOf(driverInfo.getDriverId())));
        if (CollectionUtils.isEmpty(driverUDLS)) {
            return;
        }
        DriverUDL driverUDL = driverUDLS.getFirst();
        String udl = driverUDL.getUdl();
        String uid = driverUDL.getUid();
        dspOrderConfirmRecordRepository.updateDriverUdlAndUid(confirmRecord.getId(), udl, uid);
    }
    
    
    public List<Pair<String, String>> splitTime(String startTime, String endTime) {
        List<Pair<String, String>> timePairList  = Lists.newArrayList();
        Date startDate = DateUtil.parseDateStr2Date(startTime);
        Date endDate = DateUtil.parseDateStr2Date(endTime);
        do {
            //startDate加1小时
            Date tmpDate = DateUtil.addSeconds(startDate, 60 * 60);
            if (tmpDate.compareTo(endDate) < 0) {
                //还没有到结束时间，则结束时间以tmpDate为准
                Pair<String, String> pair = new Pair<>(DateUtil.getStringDate(startDate), DateUtil.getStringDate(tmpDate));
                timePairList.add(pair);
            } else {
                //已达到结束时间，则结束时间以endDate为准
                Pair<String, String> pair = new Pair<>(DateUtil.getStringDate(startDate), DateUtil.getStringDate(endDate));
                timePairList.add(pair);
                break;
            }
            startDate = tmpDate;
        } while (true);
        return timePairList;
    }
    
    
}
