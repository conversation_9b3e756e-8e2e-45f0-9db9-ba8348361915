package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SaaSOtherBindCarValidator extends AbstractSaaSOperateValidator {


    @Override
    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        //订单、操作人的供应商一致
        DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
        Long supplierId = cmd.getSupplierId();
        //订单状态<司机车辆确认
        if(OrderStatusEnum.DRIVER_CONFIRMED.getCode() >= dspOrderVO.getOrderStatus()){
            throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
        }

        //订单状态>开始服务
        if(OrderStatusEnum.DRIVER_SERVICE_START.getCode() < dspOrderVO.getOrderStatus()){
            throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
        }

        if(!dspOrderVO.getSupplierId().toString().equals(supplierId.toString())){
            throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
        }

    }

    protected void validDriverVONonNull(SaaSBusinessVO saaSBusinessVO) {

    }
}
