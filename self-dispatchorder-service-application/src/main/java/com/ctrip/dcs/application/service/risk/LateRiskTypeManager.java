package com.ctrip.dcs.application.service.risk;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/7 11:55
 */
@Component
public class LateRiskTypeManager {
    
    @Autowired
    List<LateRiskTypeStrategy> strategies;
    
    public LateRiskTypeStrategy get(Integer type) {
        return strategies.stream().filter(t -> Objects.equals(type, t.getType())).findFirst().orElse(null);
    }
    
}
