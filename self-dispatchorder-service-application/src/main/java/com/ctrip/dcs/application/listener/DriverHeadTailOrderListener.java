package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.UpdateDriverHeadTailOrderExeCmd;
import com.ctrip.dcs.application.command.api.UpdateDriverHeadTailOrderCommand;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.infrastructure.common.constants.RedisConstants;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import credis.java.client.CacheProvider;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Date;
import java.util.List;

/**
 * 监听外部时间，更新司机首尾单信息
 * <AUTHOR>
 */
@Component
public class DriverHeadTailOrderListener {

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private UpdateDriverHeadTailOrderExeCmd updateDriverHeadTailOrderExeCmd;

    @Autowired
    @Qualifier("RorCacheProvider")
    private CacheProvider trocksCacheProvider;

    /**
     * 新流程订单-司机确认
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onNewDriverConfirm(Message message) {
        UpdateDriverHeadTailOrderCommand command = toUpdateDriverHeadTailOrderCommand(message);
        updateDriverHeadTailOrderExeCmd.execute(command);
    }

    /**
     * 老流程订单-司机确认
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onOldDriverConfirm(Message message) {
        String driverConfirmed = message.getStringProperty("msg");
        if ("driver_confirmed".equalsIgnoreCase(driverConfirmed)) {
            String dspOrderId = message.getStringProperty("supplyOrderId");
            Long driverId = message.getLongProperty("driverId");
            UpdateDriverHeadTailOrderCommand command = new UpdateDriverHeadTailOrderCommand(driverId, dspOrderId);
            updateDriverHeadTailOrderExeCmd.execute(command);
        }
    }

    /**
     * 新流程订单-司机车辆确认
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onNewDriverCarConfirm(Message message) {
        UpdateDriverHeadTailOrderCommand command = toUpdateDriverHeadTailOrderCommand(message);
        updateDriverHeadTailOrderExeCmd.execute(command);
    }

    /**
     * 老流程订单-司机车辆确认
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onOldDriverCarConfirm(Message message) {
        String dspOrderId = message.getStringProperty("supplyOrderId");
        Long driverId = message.getLongProperty("driverId");
        UpdateDriverHeadTailOrderCommand command = new UpdateDriverHeadTailOrderCommand(driverId, dspOrderId);
        updateDriverHeadTailOrderExeCmd.execute(command);
    }

    /**
     * 新流程订单-司机单取消
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onNewDriverOrderCancel(Message message) {
        UpdateDriverHeadTailOrderCommand command = toUpdateDriverHeadTailOrderCommand(message);
        updateDriverHeadTailOrderExeCmd.execute(command);
    }

    /**
     * 老流程订单-司机单取消
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.OLD_DSP_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onOldDriverOrderCancel(Message message) {
        String supplyOrderIds = message.getStringProperty("supplyOrderIds");
        List<String> list = Splitter.on(",").splitToList(supplyOrderIds);
        for (String dspOrderId : list) {
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(dspOrderId);
            if (dspOrder != null && dspOrder.getDriverId() != null) {
                UpdateDriverHeadTailOrderCommand command = new UpdateDriverHeadTailOrderCommand(dspOrder.getDriverId(), dspOrderId);
                updateDriverHeadTailOrderExeCmd.execute(command);
            }
        }
    }

    /**
     * 新流程订单-用车时间变更
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onNewOrderEstimateTimeChange(Message message) {
        UpdateDriverHeadTailOrderCommand command = toUpdateDriverHeadTailOrderCommand(message);
        updateDriverHeadTailOrderExeCmd.execute(command);
    }

    /**
     * 老流程订单-用车时间变更
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.QMQ_ORDER_SYS_EXPECT_BOOK_TIME_CHANGE, consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onOldOrderEstimateTimeChange(Message message) {
        String supplyOrderIds = message.getStringProperty("supplyOrderIds");
        List<String> list = Splitter.on(",").splitToList(supplyOrderIds);
        for (String dspOrderId : list) {
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(dspOrderId);
            if (dspOrder != null && dspOrder.getDriverId() != null) {
                UpdateDriverHeadTailOrderCommand command = new UpdateDriverHeadTailOrderCommand(dspOrder.getDriverId(), dspOrderId);
                updateDriverHeadTailOrderExeCmd.execute(command);
            }
        }
    }

    /**
     * 监听司机信息变更(只处理工作时间变更)
     * @param message
     */
    @QmqConsumer(prefix = EventConstants.DRIVER_MODIFY_TOPIC,tagType = TagType.OR, tags = {"tag_driverinfo_modify"},
            consumerGroup = "100041593_driver_head_tail", idempotentChecker = "redisIdempotentChecker")
    public void onDriverChange(Message message) {
        int times = message.times();
        if (times > 3) {
            return;
        }
        Long driverId = message.getLongProperty("drvId");
        Long supplierId = message.getLongProperty("supplierId"); // 归属供应商ID
        DriverVO driver = queryDriverService.queryDriver(driverId, ParentCategoryEnum.JNT, supplierId);
        if (driver == null) {
            throw new NeedRetryException("Query driver failed");
        }
        // 与缓存中的司机工作时段做对比，如果发生了变化则清除司机的当天及未来首尾单缓存
        boolean changed = compareAndSaveDriverWorkTime(driver);
        if (!changed) {
            return;
        }

        for (int i = 0; i < 30; i++) {// 暂时清理近30天的缓存
            updateDriverHeadTailOrderExeCmd.delDriverHeadTailOrder(driver, DateUtil.addDays(new Date(), i));
        }
    }

    public UpdateDriverHeadTailOrderCommand toUpdateDriverHeadTailOrderCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long driverId = message.getLongProperty("driverId");
        return new UpdateDriverHeadTailOrderCommand(driverId, dspOrderId);
    }

    private boolean compareAndSaveDriverWorkTime(DriverVO driver) {
        // 实时查询的工作时间
        List<String> newWorkTimes = driver.getWorkTimes().getPeriodStrs();

        // 缓存中的工作时间
        List<String> oldWorkTimes = null;
        String cacheKey = getDriverWorkTimeCacheKey(driver.getDriverId());
        String cacheValue = trocksCacheProvider.get(cacheKey);
        if (StringUtils.isNotBlank(cacheValue) && !"NULL".equals(cacheValue)) {
            oldWorkTimes = JacksonSerializer.INSTANCE().deserialize(cacheValue, new TypeReference<>() {
            });
        }

        boolean changed = true;
        if (CollectionUtils.isEmpty(newWorkTimes) && CollectionUtils.isEmpty(oldWorkTimes)) {
            changed = false;
        } else if (CollectionUtils.isEmpty(newWorkTimes) && CollectionUtils.isNotEmpty(oldWorkTimes)) {
            changed = true;
        } else if (CollectionUtils.isNotEmpty(newWorkTimes) && CollectionUtils.isEmpty(oldWorkTimes)) {
            changed = true;
        } else if (CollectionUtils.isNotEmpty(newWorkTimes) && CollectionUtils.isNotEmpty(oldWorkTimes)) {
            changed = !newWorkTimes.equals(oldWorkTimes);
        }

        trocksCacheProvider.setex(cacheKey, 31 * 24 * 3600, CollectionUtils.isEmpty(newWorkTimes) ? "NULL" : JacksonSerializer.INSTANCE().serialize(newWorkTimes));

        return changed;
    }

    private String getDriverWorkTimeCacheKey(Long driverId) {
        return String.format(RedisConstants.DRIVER_WORKTIME_KEY, driverId);
    }

}
