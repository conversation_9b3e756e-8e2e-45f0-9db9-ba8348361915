package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd;
import com.ctrip.dcs.application.provider.converter.CheckOrderDelayedDspConverter;
import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO;
import com.ctrip.dcs.application.service.dto.DelayedDspCheckResultDTO;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.OrderPreConfirmDTO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.YesOrNo;
import com.ctrip.dcs.domain.dsporder.repository.OrderPreConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import com.ctrip.dcs.domain.schedule.check.CheckChain;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.item.CheckItemId;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.factory.CheckChainFactory;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单是否延后派单检查处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/2
 */
@Component
public class OrderDelayedDspCheckService {
    private static final Logger logger = LoggerFactory.getLogger(OrderDelayedDspCheckService.class);

    @Resource
    private CheckChainFactory checkChainFactory;

    @Autowired
    private DspContextService dspContextService;

    @Autowired
    @Qualifier("checkConfig")
    private ConfigService checkConfig;

    @Autowired
    private OrderPreConfirmRecordRepository orderPreConfirmRecordRepository;

    @Autowired
    @Qualifier("commonConfConfig")
    private BaseConfigService commonConfig;

    @Autowired
    private ShortDistanceStrategyExeCmd shortDistanceStrategyExeCmd;


    /**
     * 检查订单是否人工调度确认
     *
     * @param checkReq 检查请求参数
     * @return 检查结果 {@link DelayedDspCheckResultDTO}
     */
    public DelayedDspCheckResultDTO checkManualSchedulling(CheckOrderDelayedDspDTO checkReq) {
        DelayedDspCheckResultDTO checkResult = new DelayedDspCheckResultDTO();

        // 构建订单信息
        DspOrderVO dspOrder = CheckOrderDelayedDspConverter.toDspOrderVO(checkReq);
        // 急单不进入延后派
        if (dspOrder.isUrgentOrder()) {
            logger.info("checkVBKOrderDelayedDispatch", "the order is urgent, return false");
            return checkResult;
        }

        boolean shortDisOrder = isShortDisOrder(dspOrder);

        // 根据条件查询可派运力组列表
        List<TransportGroupVO> transportGroups = dspContextService.queryTransports(dspOrder);
        if (shortDisOrder) {
            // 短程订单
            transportGroups = transportGroups.stream().filter(t -> Objects.equals(t.getShortDisSwitch(), YesOrNo.YES.getCode())).collect(Collectors.toList());
        }else {
            transportGroups = transportGroups.stream().filter(t -> Objects.equals(t.getShortDisSwitch(), YesOrNo.NO.getCode())).collect(Collectors.toList());
        }
        // 运力组为空 or 含非人工调度类型运力组
        if (CollectionUtils.isEmpty(transportGroups) || !transportGroups.stream().allMatch(TransportGroupVO::isManualScheduling)) {
            logger.info("checkVBKOrderDelayedDispatch", CollectionUtils.isEmpty(transportGroups) ? "The transport groups is empty, return false" : "The transport groups is not all manual, return false");
            return checkResult;
        }

        // 创建检查链 - 订单是否可进入人工调度（检查项：进单时间限制 及 进单数量限制）
        // Check.TRANS_GROUP_DRIVER_TIME_CHECK, Check.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT_WITH_BUFF, Check.SHUNT_SUPPLIER_CHECK, Check.DOWNGRADE_CAR_TYPE_ORDER_TRANSPORT_GROUP_CHECK
        CheckChain checkChain = checkChainFactory.create(Lists.newArrayList(CheckItemId.I107.name(), CheckItemId.I104.name(), CheckItemId.I144.name(), CheckItemId.I143.name(), CheckItemId.I145.name()));
        // 构建检查上下文信息
        CheckContext context = CheckContext.builder()
                .context(new DspContext(dspContextService))
                .configService(checkConfig)
                .subSku(new SubSkuVO())// 默认值留空
                .dspOrder(dspOrder)
                .dspStage(DspStage.DSP)
                .build();
        // 检查项所需信息
        List<CheckModel> checkModels = transportGroups.stream().map(item -> new CheckModel(new DspModelVO(context.getDspOrder(), item))).collect(Collectors.toList());

        // 检查订单是否可派单给对应的运力组
        List<CheckModel> checkResults = checkChain.check(context, checkModels);
        // 检查结果为空 or 所有运力组检查均不通过
        if (CollectionUtils.isEmpty(checkResults) || checkResults.stream().noneMatch(CheckModel::isPass)) {
            logger.info("checkVBKOrderDelayedDispatch", "order dispatch precheck results is empty or all failed.");
            return checkResult;
        }

        // 订单可进入人工调度，返回检查结果
        checkResult.setDelayedDispatch(true);
        checkResult.setOrderConfirmType(ConfirmType.DISPATCH_CONFIRMED);

        // 持久化 通过检查的运力组ID列表
        saveCheckResult(dspOrder, checkResults);

        return checkResult;
    }

    public boolean isShortDisOrder(DspOrderVO dspOrder) {
        try {
            // 灰度城市
            String grayCityIds = commonConfig.getString("short_dis_gray_city_ids", Strings.EMPTY);
            boolean gray = "all".equals(grayCityIds) || Arrays.stream(grayCityIds.split(",")).filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(Integer::parseInt).toList().contains(dspOrder.getCityId());
            if (gray) {
                // 查询短公里订单策略配置
                boolean checkShortDisOrder = shortDistanceStrategyExeCmd.checkShortDisOrder(dspOrder.getCityId(), dspOrder.getCategoryCode().getType(), dspOrder.getCarTypeId().longValue()
                        , dspOrder.getDistributionChannelId().longValue(), dspOrder.getEstimatedKm(), new Timestamp(dspOrder.getEstimatedUseTimeBj().getTime()));
                return checkShortDisOrder;
            }
        } catch (Exception e) {
            logger.warn("checkVBKOrderDelayedDispatch", e);
        }
        return false;
    }

    /**
     * 持久化预确认的运力组检查结果
     *
     * @param dspOrder     订单信息
     * @param checkResults 检查结果
     */
    private void saveCheckResult(DspOrderVO dspOrder, List<CheckModel> checkResults) {
        try {
            List<Long> matchedTransportGroups = checkResults.stream().filter(CheckModel::isPass).map(item -> item.getModel().getTransportGroup().getTransportGroupId()).collect(Collectors.toList());

            OrderPreConfirmDTO preConfirmDTO = new OrderPreConfirmDTO();
            preConfirmDTO.setUserOrderId(dspOrder.getUserOrderId());
            preConfirmDTO.setSkuId(dspOrder.getSkuId().longValue());
            preConfirmDTO.setConfirmType(ConfirmType.DISPATCH_CONFIRMED);
            preConfirmDTO.setMatchedTransportGroupIds(matchedTransportGroups);
            orderPreConfirmRecordRepository.save(preConfirmDTO);
        } catch (Exception e) {
            logger.error("saveCheckResult_error", e);
        }
    }

    /**
     * 检查订单是否进入系统延后派单
     *
     * @param checkReq 检查请求参数
     * @return 检查结果 {@link DelayedDspCheckResultDTO}
     */
    public DelayedDspCheckResultDTO checkSysDelayedDispatch(CheckOrderDelayedDspDTO checkReq) {
        DelayedDspCheckResultDTO checkResult = new DelayedDspCheckResultDTO();
        // todo 系统延后派判断逻辑 待实现

        return checkResult;
    }


}
