package com.ctrip.dcs.application.command.dto;

import java.math.BigDecimal;

/**
 * 修改地址信息DTO
 */
public class ModifyAddressDTO {

    // {"actualFromLongitude":121.320898,"actualFromLatitude":31.195165,"actualFromCoordsys":"GCJ02","fromAddress":"上海虹桥站","fromName":"上海虹桥站"}

    private BigDecimal actualFromLongitude;

    private BigDecimal actualFromLatitude;

    private String actualFromCoordsys;

    private String fromAddress;

    private String fromName;

    public BigDecimal getActualFromLongitude() {
        return actualFromLongitude;
    }

    public void setActualFromLongitude(BigDecimal actualFromLongitude) {
        this.actualFromLongitude = actualFromLongitude;
    }

    public BigDecimal getActualFromLatitude() {
        return actualFromLatitude;
    }

    public void setActualFromLatitude(BigDecimal actualFromLatitude) {
        this.actualFromLatitude = actualFromLatitude;
    }

    public String getActualFromCoordsys() {
        return actualFromCoordsys;
    }

    public void setActualFromCoordsys(String actualFromCoordsys) {
        this.actualFromCoordsys = actualFromCoordsys;
    }

    public String getFromAddress() {
        return fromAddress;
    }

    public void setFromAddress(String fromAddress) {
        this.fromAddress = fromAddress;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }
}
