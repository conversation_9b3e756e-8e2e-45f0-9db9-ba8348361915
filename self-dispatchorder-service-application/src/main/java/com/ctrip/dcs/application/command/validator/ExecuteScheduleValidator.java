package com.ctrip.dcs.application.command.validator;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;

;

/**
 * <AUTHOR>
 */
@Component
public class ExecuteScheduleValidator {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteScheduleValidator.class);

    @Autowired
    private MessageProviderService messageProducer;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConfig;

    public void validate(ExecuteScheduleCommand command, DspOrderVO dspOrderVO, ScheduleDO schedule, List<ScheduleTaskDO> tasks) throws ValidateException {
        Assert.notNull(dspOrderVO);
        Assert.notNull(schedule);
        Assert.notEmpty(tasks);
        if (schedule.isShutdown()) {
            logger.info("schedule execute validator", "schedule has shutdown.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_SHUTDOWN_ERROR);
        }
        if (command.getRound() != 0 && command.getRound() < schedule.getRound()) {
            logger.info("schedule execute validator", "schedule round less.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_EXECUTING_ERROR);
        }
        if (!dspOrderVO.isDispatching()) {
            logger.info("schedule execute validator", "dsp order not dispatching.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR);
        }
        if (schedule.isTimeout(dspOrderVO)) {
            logger.info("schedule execute validator", "schedule has timeout.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            MetricsUtil.recordValue(MetricsConstants.EXECUTE_SCHEDULE_TIMEOUT_COUNT);
            throw new ValidateException(ErrorCode.SCHEDULE_TIMEOUT_ERROR);
        }
        if (schedule.getRound() != null && schedule.getRound() > getMaxScheduleRound()) {
            // 调度轮次超过最大值
            logger.info("schedule execute validator", "schedule round more than max.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_EXECUTING_ERROR);
        }
    }

    private Integer getMaxScheduleRound() {
        String max = commonConfig.get("default_max_schedule_round");
        if (StringUtils.isBlank(max)) {
            return 99999;
        }
        return Integer.valueOf(max);
    }
}
