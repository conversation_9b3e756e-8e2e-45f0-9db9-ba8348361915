package com.ctrip.dcs.application.service.impl;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.application.service.dto.*;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway;
import com.ctrip.dcs.domain.schedule.event.ChangeGrabOrderPushRuleEvent;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabOrderPushRuleDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabOrderPushRuleRecordDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabOrderPushRulePO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabOrderPushRuleRecordPO;
import com.ctrip.dcs.infrastructure.adapter.soa.RhbSettlementRuleProxy;
import com.ctrip.dcs.infrastructure.adapter.soa.ScmMerchantServiceProxy;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryCurrencyPaymentForDriverRequestType;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryCurrencyPaymentForDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRewardsRuleSoaDTO;
import com.ctrip.dcs.settlement.interfaces.dto.QuerySettleRuleVBKRequestType;
import com.ctrip.dcs.settlement.interfaces.dto.QuerySettleRuleVBKResponseType;
import com.ctrip.dcs.settlement.interfaces.dto.SettleRuleInfo;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class GrabOrderPushRuleService implements IGrabOrderPushRuleService {

    private static final Logger logger = LoggerFactory.getLogger(GrabOrderPushRuleService.class);


    @Autowired
    private GrabOrderPushRuleDao grabOrderPushRuleDao;
    @Autowired
    private GrabOrderPushRuleRecordDao recordDao;
    @Autowired
    private MessageProviderService messageProviderService;
    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private ScmMerchantServiceProxy scmMerchantServiceProxy;
    @Autowired
    private RhbSettlementRuleProxy rhbSettlementRuleProxy;
    @Autowired
    private ScmMerchantServiceGateway scmMerchantServiceGateway;
    @Override
    public boolean add(GrabOrderPushRuleDTO grabOrderPushRuleDTO) {
        //获取锁
        DistributedLockService.DistributedLock lock = queryLock(grabOrderPushRuleDTO.getSupplierId());
        try {
            //加锁失败
            if(!lock.tryLock()){
                throw new BizException("0918001", "Do not operate frequently");
            }
            //去重复过滤和转换为po
            List<GrabOrderPushRulePO> poList = queryRulePoList(grabOrderPushRuleDTO);
            if(LocalCollectionUtils.isEmpty(poList)){
                throw new BizException("0918004", "Duplication of rules");
            }
            // 校验结算信息
            checkSettlementInfo(poList);
            //加事务新增
            boolean result = addForTransaction(poList,grabOrderPushRuleDTO.getOperateUser());
            //新增成功发送变更消息
            if(result){
                for (GrabOrderPushRulePO rule : poList) {
                    messageProviderService.send(new ChangeGrabOrderPushRuleEvent(rule.getSupplierId(), rule.getCityId(), rule.getCategoryCode()));
                }
            }
            return result;
        } catch (BizException ex) {
            logger.warn(ex);
            throw ex;
        } catch (Exception e) {
            throw new BizException(e);
        }finally {
            if(lock != null){
                lock.unlock();
            }
        }
    }

    /**
     * 校验结算信息
     * 1、是否有司机币种
     * 2、是否有司机分成规则
     * @param rules
     * @return
     */
    public void checkSettlementInfo(List<GrabOrderPushRulePO> rules) {
        for (GrabOrderPushRulePO rule : rules) {
            checkDriverCurrency(rule);
            // 查询司机结算方式
            Boolean isPayForDriver = isPayForDriver(rule);
            if (!isPayForDriver) {
                // 不跟司机结算的供应商，需要校验是否配置结算规则，余进已确认
                checkDriverSettleRule(rule);
            }
        }
    }

    public Boolean isPayForDriver(GrabOrderPushRulePO rule) {
        return scmMerchantServiceGateway.isPayForDriver(rule.getSupplierId(), CategoryCodeEnum.getByParentType(rule.getCategoryCode()));
    }

    /**
     * 校验司机币种
     * @param rule
     */
    public void checkDriverCurrency(GrabOrderPushRulePO rule) {
        QueryCurrencyPaymentForDriverRequestType requestType = new QueryCurrencyPaymentForDriverRequestType();
        requestType.setCityId(rule.getCityId());
        QueryCurrencyPaymentForDriverResponseType responseType = scmMerchantServiceProxy.queryCurrencyPaymentForDriver(requestType);
        String currency = Optional.ofNullable(responseType).map(QueryCurrencyPaymentForDriverResponseType::getCurrency)
                .filter(StringUtils::isNotBlank)
                .orElseThrow(() -> new BizException("0918002", "driver currency is empty"));
        if (StringUtils.isNotBlank(rule.getRewards())) {
            GrabOrderPushRewardsRuleSoaDTO reward = JacksonSerializer.INSTANCE().deserialize(rule.getRewards(), GrabOrderPushRewardsRuleSoaDTO.class);
            reward.setCurrency(currency);
            rule.setRewards(JacksonSerializer.INSTANCE().serialize(reward));
        }
    }

    /**
     * 校验司机结算规则
     * @param rule
     */
    public void checkDriverSettleRule(GrabOrderPushRulePO rule) {
        Set<String> categoryCodes = CategoryCodeEnum.getByParentType(rule.getCategoryCode()).stream().map(CategoryCodeEnum::getType).collect(Collectors.toSet());
        QuerySettleRuleVBKRequestType requestType = new QuerySettleRuleVBKRequestType();
        // 城市
        requestType.setCityId(Lists.newArrayList(rule.getCityId().intValue()));
        // 自营
        requestType.setVendorType(0);
        // 有效
        requestType.setStatus(1);
        requestType.setSupplierId(rule.getSupplierId().intValue());
        requestType.setPaginator(new PaginatorDTO(1, 1000));
        QuerySettleRuleVBKResponseType responseType = rhbSettlementRuleProxy.querySettleRules(requestType);
        List<SettleRuleInfo> settleRules = Optional.ofNullable(responseType).map(QuerySettleRuleVBKResponseType::getRuleList).orElse(Collections.emptyList());
        List<Long> carTypeIds = LocalStringUtils.stringToLongList(rule.getVehicleGroupIdList());
        for (Long carTypeId : carTypeIds) {
            settleRules.stream()
                    .filter(settle -> Objects.equals(0, settle.getCarType()) || Objects.equals(carTypeId.intValue(), settle.getCarType()))
                    .filter(settle -> Objects.equals("0", settle.getCategoryCode()) || categoryCodes.contains(settle.getCategoryCode()))
                    .filter(settle -> Objects.equals(0, settle.getSupplierId()) || Objects.equals(rule.getSupplierId().intValue(), settle.getSupplierId()))
                    .findAny()
                    .orElseThrow(() -> new BizException("0918003", "driver settle rule is null"));
        }
    }

    /**
     *  加事务新增抢单规则
     * @param poList
     * @param operateUser
     * @return
     * @throws Exception
     */
    @DalTransactional(logicDbName = "dcsselfdispatchdb_dalcluster")
    public boolean addForTransaction(List<GrabOrderPushRulePO> poList ,String operateUser)throws Exception{
        boolean result = grabOrderPushRuleDao.add(poList);
        //保存规则变更记录
        recordDao.add(convertToRecordPoForAdd(poList,operateUser));
        return result;
    }

    @Override
    public boolean deleteById(Long ruleId,String operateUser,Long supplierId) {
        //获取锁
        DistributedLockService.DistributedLock lock = queryLock(supplierId);
        try {
            //加锁失败
            if(!lock.tryLock()){
                return false;
            }
            //查询规则
            GrabOrderPushRulePO po = grabOrderPushRuleDao.queryById(ruleId);
            //加事务删除规则
            boolean result = deleteByIdForTransaction(ruleId,operateUser,po);
            //事务删除成功发送变更消息
            if(result){
                messageProviderService.send(new ChangeGrabOrderPushRuleEvent(po.getSupplierId(), po.getCityId(), po.getCategoryCode()));
            }
            return result;
        } catch (Exception e) {
            throw new BizException(e);
        }finally {
            if(lock != null){
                lock.unlock();
            }
        }
    }

    /**
     * 加事务删除抢单规则
     * @param ruleId
     * @param operateUser
     * @param po
     * @return
     * @throws Exception
     */
    @DalTransactional(logicDbName = "dcsselfdispatchdb_dalcluster")
    public boolean deleteByIdForTransaction(Long ruleId,String operateUser,GrabOrderPushRulePO po) throws Exception {
        //逻辑删除规则
        boolean result = grabOrderPushRuleDao.deleteById(ruleId);
        //保存规则变更记录
        recordDao.add(convertToRecordPoForDelete(po,operateUser));
        return result;
    }

    @Override
    public boolean update(GrabOrderPushRuleDTO grabOrderPushRuleDTO) {
        //获取锁
        DistributedLockService.DistributedLock lock = queryLock(grabOrderPushRuleDTO.getSupplierId());
        try{
            //加锁失败
            if(!lock.tryLock()){
                return false;
            }
            //查询旧的规则
            GrabOrderPushRulePO oldRulePo = grabOrderPushRuleDao.queryById(grabOrderPushRuleDTO.getId());
            //重复规则检查
            if(!checkRuleForUpdate(grabOrderPushRuleDTO)){
                throw new BizException("0918004", "Duplication of rules");
            }
            //变更后新规则
            GrabOrderPushRulePO newRulePo = convertPo(grabOrderPushRuleDTO);
            //这些信息不修改使用 原值
            newRulePo.setCityId(oldRulePo.getCityId());
            newRulePo.setSupplierName(oldRulePo.getSupplierName());
            newRulePo.setSupplierId(oldRulePo.getSupplierId());
            newRulePo.setCategoryCode(oldRulePo.getCategoryCode());
            // 校验结算信息
            checkSettlementInfo(Lists.newArrayList(newRulePo));
            //加事务更新抢单规则
            boolean result = updateForTransaction(oldRulePo,newRulePo,grabOrderPushRuleDTO.getOperateUser());
            //发送变更消息
            if(result){
                messageProviderService.send(new ChangeGrabOrderPushRuleEvent(grabOrderPushRuleDTO.getSupplierId(), grabOrderPushRuleDTO.getCityId(), grabOrderPushRuleDTO.getCategoryCode()));
            }
            return result;
        } catch (BizException ex) {
            logger.warn(ex);
            throw ex;
        }catch (Exception e){
            throw new BizException(e);
        }finally {
            if(lock != null){
                lock.unlock();
            }
        }
    }

    /**
     * 加事务更新抢单规则
     * @param oldRulePo
     * @param newRulePo
     * @param operateUser
     * @return
     * @throws Exception
     */
    @DalTransactional(logicDbName = "dcsselfdispatchdb_dalcluster")
    public boolean updateForTransaction(GrabOrderPushRulePO oldRulePo,GrabOrderPushRulePO newRulePo,String operateUser)throws Exception{
        boolean result = grabOrderPushRuleDao.update(newRulePo);
        //保存规则变更记录
        recordDao.add(convertToRecordPoForUpdate(oldRulePo,newRulePo,operateUser));
        return result;
    }

    @Override
    public QueryGrabOrderPushRuleResultDTO query(QueryGrabOrderPushRuleParamDTO paramDTO) {
        try{
            Integer totalCount = grabOrderPushRuleDao.count(paramDTO.getSupplierId(),paramDTO.getCityId(),
                    paramDTO.getVehicleGroupId());
            QueryGrabOrderPushRuleResultDTO resultDTO = new QueryGrabOrderPushRuleResultDTO();
            if(totalCount > 0){
                List<GrabOrderPushRulePO> poList = grabOrderPushRuleDao.queryByPage(paramDTO.getSupplierId(),paramDTO.getCityId(),
                        paramDTO.getVehicleGroupId(),paramDTO.getPageNo(),paramDTO.getPageSize());
                resultDTO.setRuleDTOList(convertDTOList(poList));
            }
            resultDTO.setPageNo(paramDTO.getPageNo());
            resultDTO.setPageSize(paramDTO.getPageSize());
            resultDTO.setTotalSize(totalCount);
            return resultDTO;
        }catch (Exception e){
            logger.error("QueryGrabOrderPushRuleExp", e, new HashMap<>());
            throw new BizException(e);
        }
    }

    @Override
    public QueryGrabOrderPushRuleRecordResultDTO queryRuleRecord(QueryGrabOrderPushRuleRecordParamDTO paramDTO) {
        try{
            //数据总条数
            Integer totalCount = recordDao.count(paramDTO.getRuleId());
            List<GrabOrderPushRuleRecordDTO> recordDTOList = new ArrayList<>();
            if(totalCount > 0){
                List<GrabOrderPushRuleRecordPO> recordPOList = recordDao.queryByPage(paramDTO.getRuleId(),paramDTO.getPageNo(),paramDTO.getPageSize());
                if(!LocalCollectionUtils.isEmpty(recordPOList)){
                    for (GrabOrderPushRuleRecordPO grabOrderPushRuleRecordPO : recordPOList) {
                        recordDTOList.add(convertToRecordDTO(grabOrderPushRuleRecordPO));
                    }
                }
            }
            QueryGrabOrderPushRuleRecordResultDTO resultDTO = new QueryGrabOrderPushRuleRecordResultDTO();
            resultDTO.setRecordDTOList(recordDTOList);
            resultDTO.setPageNo(paramDTO.getPageNo());
            resultDTO.setPageSize(paramDTO.getPageSize());
            resultDTO.setTotalSize(totalCount);
            return resultDTO;
        }catch (Exception e){
            logger.error("QueryGrabOrderPushRuleRecordExp", e, new HashMap<>());
            throw new BizException(e);
        }
    }

    @Override
    public GrabOrderPushRuleDTO queryById(Long ruleId) {
        //查询规则
        try {
            GrabOrderPushRulePO po = grabOrderPushRuleDao.queryById(ruleId);
            return po == null ? null : convertToDTO(po);
        } catch (SQLException e) {
            logger.error("QueryGrabOrderPushRuleByIdExp", e, new HashMap<>());
            throw new BizException(e);
        }
    }

    /**
     * DTO 转换为 po
     * @param grabOrderPushRuleDTO
     * @return
     */
    public GrabOrderPushRulePO convertPo(GrabOrderPushRuleDTO grabOrderPushRuleDTO){
        GrabOrderPushRulePO po = new GrabOrderPushRulePO();
        if(grabOrderPushRuleDTO.getId()!=null){
            po.setId(grabOrderPushRuleDTO.getId());
        }
        po.setCityId(grabOrderPushRuleDTO.getCityId());
        po.setRuleType(grabOrderPushRuleDTO.getRuleType());
        po.setCategoryCode(grabOrderPushRuleDTO.getCategoryCode());
        //通用规则
        if(grabOrderPushRuleDTO.getRuleType() == 1){
            po.setFixedPushTime(grabOrderPushRuleDTO.getFixedPushTime());
            po.setImmediatePushTime(grabOrderPushRuleDTO.getImmediatePushTime());
            //清空特殊规则
            po.setEndBookTime("");
            po.setStartBookTime("");
            po.setBookTime("");
        }
        //特殊规则
        if(grabOrderPushRuleDTO.getRuleType() == 2){
            po.setEndBookTime(grabOrderPushRuleDTO.getEndBookTime());
            po.setStartBookTime(grabOrderPushRuleDTO.getStartBookTime());
            po.setBookTime(grabOrderPushRuleDTO.getBookTime());
            //清空发单规则
            po.setFixedPushTime("");
            po.setImmediatePushTime(0);
        }
        po.setSupplierId(grabOrderPushRuleDTO.getSupplierId());
        po.setSupplierName(grabOrderPushRuleDTO.getSupplierName());
        String vehicleGroupIds = LocalStringUtils.LongListToStr(grabOrderPushRuleDTO.getVehicleGroupIdList());
        String newVehicleGroupIds = "," + vehicleGroupIds +",";
        po.setVehicleGroupIdList(newVehicleGroupIds);
        po.setPriority(grabOrderPushRuleDTO.getPriority());
        po.setRewards(grabOrderPushRuleDTO.getRewards());
        return po;
    }

    /**
     * poList to  dtoList
     * @param poList
     * @return
     */
    public List<GrabOrderPushRuleDTO> convertDTOList(List<GrabOrderPushRulePO> poList){
        List<GrabOrderPushRuleDTO> ruleDTOList = new ArrayList<>();
        if(!LocalCollectionUtils.isEmpty(poList)){
            for (GrabOrderPushRulePO grabOrderPushRulePO : poList) {
                ruleDTOList.add(convertToDTO(grabOrderPushRulePO));
            }
        }
        return ruleDTOList;
    }

    /**
     * po to  dto
     * @param po
     * @return
     */
    public GrabOrderPushRuleDTO convertToDTO(GrabOrderPushRulePO po){
        GrabOrderPushRuleDTO ruleDTO = new GrabOrderPushRuleDTO();
        ruleDTO.setId(po.getId());
        ruleDTO.setCityId(po.getCityId());
        ruleDTO.setCityIds(Lists.newArrayList(po.getCityId()));
        ruleDTO.setRuleType(po.getRuleType());
        ruleDTO.setCategoryCode(po.getCategoryCode());
        ruleDTO.setEndBookTime(po.getEndBookTime());
        ruleDTO.setStartBookTime(po.getStartBookTime());
        ruleDTO.setBookTime(po.getBookTime());
        ruleDTO.setFixedPushTime(po.getFixedPushTime());
        ruleDTO.setImmediatePushTime(po.getImmediatePushTime());
        ruleDTO.setSupplierId(po.getSupplierId());
        ruleDTO.setSupplierName(po.getSupplierName());
        ruleDTO.setVehicleGroupIdList(LocalStringUtils.stringToLongList(po.getVehicleGroupIdList()));
        ruleDTO.setPriority(po.getPriority());
        ruleDTO.setRewards(po.getRewards());
        return ruleDTO;
    }

    /**
     * 规则记录 po-》 dto
     * @param po
     * @return
     */
    public GrabOrderPushRuleRecordDTO convertToRecordDTO(GrabOrderPushRuleRecordPO po){
        GrabOrderPushRuleRecordDTO recordDTO = new GrabOrderPushRuleRecordDTO();
        recordDTO.setId(po.getId().longValue());
        recordDTO.setRuleId(po.getRuleId());
        recordDTO.setOperatorType(po.getOperatorType());
        recordDTO.setOperatorName(po.getOperatorName());
        recordDTO.setOperatorTime(po.getOperatorTime());
        recordDTO.setBeforeChange(po.getBeforeChange());
        recordDTO.setAfterChange(po.getAfterChange());
        recordDTO.setCreateTime(po.getDatachangeCreatetime());
        recordDTO.setUpdateTime(po.getDatachangeLasttime());
        return recordDTO;
    }

    /**
     * 去重过滤  批量组装
     * @param grabOrderPushRuleDTO
     * @return
     */
    public List<GrabOrderPushRulePO> queryRulePoList(GrabOrderPushRuleDTO grabOrderPushRuleDTO) throws SQLException{
        List<GrabOrderPushRulePO> grabOrderPushRulePOList = grabOrderPushRuleDao.queryRules(grabOrderPushRuleDTO.getSupplierId(),
                grabOrderPushRuleDTO.getCityIds(),grabOrderPushRuleDTO.getCategoryCode());
        for (Long cityId : grabOrderPushRuleDTO.getCityIds()) {
            if(!checkRule(grabOrderPushRulePOList,grabOrderPushRuleDTO,cityId)){
                //有重复的则此次新增全部失败 不处理
                return new ArrayList<>();
            }
        }
        //多个城市公用一个配置 批量新增
        GrabOrderPushRulePO po = convertPo(grabOrderPushRuleDTO);
        List<GrabOrderPushRulePO> poList = new ArrayList<>();
        for (Long cityId : grabOrderPushRuleDTO.getCityIds()) {
            GrabOrderPushRulePO newPo = new GrabOrderPushRulePO();
            BeanUtils.copyProperties(po,newPo);
            newPo.setCityId(cityId);
            poList.add(newPo);
        }
        return poList;
    }

    /**
     * 检查规则重复-更新场景
     * @param grabOrderPushRuleDTO
     * @return
     */
    public boolean checkRuleForUpdate(GrabOrderPushRuleDTO grabOrderPushRuleDTO)throws SQLException{
        //查询供应商+城市+产线下的所有规则
        List<GrabOrderPushRulePO> grabOrderPushRulePOList = grabOrderPushRuleDao.queryRules(grabOrderPushRuleDTO.getSupplierId(),
                Arrays.asList(grabOrderPushRuleDTO.getCityId()),grabOrderPushRuleDTO.getCategoryCode());
        for (GrabOrderPushRulePO rulePO : grabOrderPushRulePOList) {
            //过滤掉当前更新的这个规则  自己和自己不冲突
            if(rulePO.getId().equals(grabOrderPushRuleDTO.getId())){
                continue;
            }
            //有重复的则此次新增全部失败 不处理
            if(!checkDuplicateRule(rulePO,grabOrderPushRuleDTO,grabOrderPushRuleDTO.getCityId())){
                return false;
            }
        }
        return true;
    }
    /**
     * 检查规则重复
     * @param grabOrderPushRulePOList
     * @param grabOrderPushRuleDTO
     * @param cityId
     * @return
     */
    public boolean checkRule(List<GrabOrderPushRulePO> grabOrderPushRulePOList,GrabOrderPushRuleDTO grabOrderPushRuleDTO,Long cityId){
        for (GrabOrderPushRulePO rulePO : grabOrderPushRulePOList) {
            if(!checkDuplicateRule(rulePO,grabOrderPushRuleDTO,cityId)){
                return false;
            }
        }
        return true;
    }
    /**
     * 检查去重复
     * @param po
     * @param grabOrderPushRuleDTO
     * @return
     */
    public boolean checkDuplicateRule(GrabOrderPushRulePO po,GrabOrderPushRuleDTO grabOrderPushRuleDTO,Long cityId){
        boolean checkCityId = po.getCityId().equals(cityId);
        boolean checkCategoryCode = po.getCategoryCode().equals(grabOrderPushRuleDTO.getCategoryCode());
        boolean checkSupplierId = po.getSupplierId().equals(grabOrderPushRuleDTO.getSupplierId());
        boolean checkVehicleGroupId = equalVehicleGroupId(po.getVehicleGroupIdList(),grabOrderPushRuleDTO.getVehicleGroupIdList());
        return !checkCityId || !checkCategoryCode || !checkSupplierId || !checkVehicleGroupId;
    }
    /**
     * 检查车型是否重复
     * @param dbVehicleGroupIdList
     * @param vehicleGroupIdList
     * @return
     */
    public boolean equalVehicleGroupId(String dbVehicleGroupIdList,List<Long> vehicleGroupIdList){
        List<Long> dbVehicleGroupIds = LocalStringUtils.stringToLongList(dbVehicleGroupIdList);
        for (Long vehicleGroupId : vehicleGroupIdList) {
            if(dbVehicleGroupIds.contains(vehicleGroupId)){
                return true;
            }
        }
        return false;
    }

    /**
     * 新增规则记录变更信息
     * @param rulePOList
     * @return
     */
    public List<GrabOrderPushRuleRecordPO> convertToRecordPoForAdd(List<GrabOrderPushRulePO> rulePOList,String operateUser){
        List<GrabOrderPushRuleRecordPO> result = new ArrayList<>();
        for (GrabOrderPushRulePO rulePO : rulePOList) {
            GrabOrderPushRuleRecordPO recordPO = new GrabOrderPushRuleRecordPO();
            recordPO.setRuleId(rulePO.getId());
            recordPO.setOperatorType(0);//新增
            recordPO.setOperatorName(operateUser);//操作人姓名
            recordPO.setOperatorTime(new Timestamp(System.currentTimeMillis()));
            recordPO.setBeforeChange("");
            recordPO.setAfterChange(LocalJsonUtils.toJson(rulePO));
            result.add(recordPO);
        }
        return result;
    }

    /**
     * 封装规则变更记录
     * @param oldRulePO
     * @param newRulePO
     * @param operateUser
     * @return
     */
    public List<GrabOrderPushRuleRecordPO> convertToRecordPoForUpdate(GrabOrderPushRulePO oldRulePO,GrabOrderPushRulePO newRulePO,String operateUser){
        GrabOrderPushRuleRecordPO recordPO = new GrabOrderPushRuleRecordPO();
        recordPO.setRuleId(oldRulePO.getId());
        recordPO.setOperatorType(1);//更新
        recordPO.setOperatorName(operateUser);//操作人姓名
        recordPO.setOperatorTime(new Timestamp(System.currentTimeMillis()));
        recordPO.setBeforeChange(LocalJsonUtils.toJson(oldRulePO));
        recordPO.setAfterChange(LocalJsonUtils.toJson(newRulePO));
        return Arrays.asList(recordPO);
    }

    /**
     * 封装规则变更记录
     * @param oldRulePO
     * @param operateUser
     * @return
     */
    public List<GrabOrderPushRuleRecordPO> convertToRecordPoForDelete(GrabOrderPushRulePO oldRulePO,String operateUser){
        GrabOrderPushRuleRecordPO recordPO = new GrabOrderPushRuleRecordPO();
        recordPO.setRuleId(oldRulePO.getId());
        recordPO.setOperatorType(2);//删除
        recordPO.setOperatorName(operateUser);//操作人姓名
        recordPO.setOperatorTime(new Timestamp(System.currentTimeMillis()));
        recordPO.setBeforeChange(LocalJsonUtils.toJson(oldRulePO));
        recordPO.setAfterChange("");
        return Arrays.asList(recordPO);
    }

    /**
     * 获取分布式锁
     * @param supplierId
     * @return
     */
    public DistributedLockService.DistributedLock queryLock(Long supplierId){
        //获取锁
        String key = "grabRule"+supplierId.toString();
        return distributedLockService.getLock(key);
    }
}
