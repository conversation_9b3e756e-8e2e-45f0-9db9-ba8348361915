package com.ctrip.dcs.application.command;

import cn.hutool.core.math.Money;
import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ctrip.dcs.domain.common.constants.SysConstants.OP_ASSIGN_DRIVER_IDEMPOTENT;

/**
 * Created by xingxing.yu on 2023/2/16.
 */

public abstract class AbstractAssignDriverExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(AbstractAssignDriverExeCmd.class);


    @Autowired
    protected QueryDspOrderService orderQueryService;

    @Autowired
    protected QueryDriverService queryDriverService;

    @Autowired
    protected DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    protected CheckService checkService;

    @Autowired
    protected DriverOrderFactory driverOrderFactory;

    @Autowired
    protected ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    protected QueryVehicleService queryVehicleService;

    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    protected QueryDspOrderService queryDspOrderService;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    protected QueryTransportGroupService queryTransportGroupService;

    @Autowired
    protected DspOrderRepository dspOrderRepository;

    public boolean execute(OperateAssignDriverCommand cmd) {
        String idempotentKey = String.format(OP_ASSIGN_DRIVER_IDEMPOTENT, cmd.getLockPrefixKey(), cmd.getDspOrderId());

        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            // 查询派发单聚合
            DspOrderVO order = queryDspOrderService.query(cmd.getDspOrderId());
            if (order == null) {
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            // 原单修改待确认时不允许任何操作
            OrderModifyHelper.assertNotToBeConfirmed(order.getDspOrderId());
            Long supplierId = order.getSupplierId() == null ? 0L : order.getSupplierId().longValue();
            ParentCategoryEnum parentCategoryEnum = CategoryUtils.selfGetParentType(order);
            DriverVO driverVO = queryDriverService.queryDriver(cmd.getDriverId(),parentCategoryEnum, supplierId);
            if (driverVO == null) {
                throw ErrorCode.FAIL_BY_DRIVER_INFO.getBizException();
            }
            //前置业务检查
            preCheck(order, driverVO, cmd);
            TransportGroupVO groupVO = getFitTransportGroupInfo(order, driverVO, cmd);

            //匹配指派策略
            SubSkuVO subSku = subSkuRepository.find(getSubskuId(order, groupVO, cmd));
            ScheduleTaskDO task = ScheduleTaskDO.builder()
                    .subSku(subSku)
                    .dspOrderId(cmd.getDspOrderId())
                    .status(ScheduleTaskStatus.INIT)
                    .reward(new Money(cmd.getExtraFee()))
                    .scheduleId(System.currentTimeMillis())
                    .taskId(NumberUtils.LONG_ONE)
                    .round(NumberUtils.INTEGER_ONE)
                    .dspRewardStrategyId(NumberUtils.LONG_ZERO)
                    .build();
            DspModelVO model = new DspModelVO(order, driverVO, groupVO);

            VehicleVO vehicle = queryVehicle(driverVO,cmd.getCarId(),parentCategoryEnum);

            //执行指派
            return assign(task, model, vehicle, cmd);

        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } finally {
            lock.unlock();
        }
    }

    private TransportGroupVO getFitTransportGroupInfo(DspOrderVO dspOrder, DriverVO driverVO, OperateAssignDriverCommand cmd) {
        Set<Long> transportIds = driverVO.getTransportGroups().stream().map(TransportGroupVO::getTransportGroupId).collect(Collectors.toSet());
        Integer supplierId = dspOrder.getSupplierId();
        if (SysConstants.AssignRole.SYS_ROLE_BOSS.equals(cmd.getAssignRole()) && supplierId == 0) {
            supplierId = null;
        }
        if (SysConstants.AssignRole.SYS_ROLE_BOSS.equals(cmd.getAssignRole()) && !Objects.equals(supplierId,driverVO.getSupplier().getSupplierId().intValue())) {
            supplierId = driverVO.getSupplier().getSupplierId().intValue();
        }
        List<TransportGroupVO> transportGroups = queryTransportGroupService.queryTransportGroups(dspOrder.getSkuId(), supplierId, cmd.getTransportGroupId().intValue());
        TransportGroupVO transportGroup = transportGroups.stream().filter(Objects::nonNull).filter(vo -> transportIds.contains(vo.getTransportGroupId())).findFirst().orElse(null);
        if (transportGroup == null) {
            throw ErrorCode.DRIVER_TRANSPORT_GROUP_ILLEGAL.getBizException();
        }
        return transportGroup;

    }

    protected boolean assign(ScheduleTaskDO task, DspModelVO model, VehicleVO vehicle, OperateAssignDriverCommand cmd) {
        DspOrderVO order = model.getOrder();
        DriverVO driver = model.getDriver();
        CarVO car = driver.getCar();
        TransportGroupVO transportGroup = model.getTransportGroup();
        DriverOrderVO driverOrder = null;
        //临时司机接单检查不走了
        if (!Integer.valueOf(YesOrNo.YES.getCode()).equals(model.getDriver().getDrvTemporaryMark())) {
            // 接单检查
            CheckModel check = checkService.check(new TakenCheckCommand(model.getOrder(), task.getSubSku(), model.getDriver(), model.getTransportGroup(), DuidVO.of(task)));
            if (!check.isPass()) {
                logger.info("manual operation process", "driver taken check fail!  dsp order id:{}, driver id:{}, check:{}", task.getDspOrderId(), model.getDriver().getDriverId(), check.getCheckCode().getDesc());
                throw ErrorCode.TAKCHK_CHECK_FAIL.getBizException();
            }
        }
        Long supplierId = order.getSupplierId().longValue();
        if(OrderStatusEvent.OFFLINE_ASSIGN.equals(cmd.getEvent()) || OrderStatusEvent.OFFLINE_UPDATE_DRIVER.equals(cmd.getEvent())){
            supplierId = driver.getSupplier().getSupplierId();
        }
        Integer forceAssignCode = cmd.getCheckCode();
        if (Objects.equals(BizAreaTypeEnum.IGT.getCtripCode(), order.getBizAreaType()) || Objects.equals(BizAreaTypeEnum.IGT.getQunarCode(), order.getBizAreaType())) {
            logger.info("manual operation process", "igt order force assign! dsp order id:{}, driver id:{}", task.getDspOrderId(), model.getDriver().getDriverId());
            forceAssignCode = ForceAssignEnum.FORCED_ASSIGN.getCode();
        }
        // 创建司机单
        driverOrder = driverOrderFactory.create(model, new SupplierVO(supplierId),vehicle,task, 1, forceAssignCode);
        if (Objects.isNull(driverOrder)) {
            logger.info("manual operation process", "driver order create fail! dsp order id:{}, driver id:{}", task.getDspOrderId(), model.getDriver().getDriverId());
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_ERR.getBizException();
        }

        boolean confirm = false;
        try {
            if ((Objects.isNull(car) || StringUtils.isEmpty(car.getCarLicense())) && StringUtils.isEmpty(cmd.getCarLicense())) {
                DriverConfirmVO confirmVO = DriverConfirmVO.builder()
                        .dspOrder(order)
                        .serviceProvider(new ServiceProviderVO(order.getSpId()))
                        .supplier(new SupplierVO(supplierId))
                        .transportGroup(transportGroup)
                        .driver(driver)
                        .duid(DuidVO.of(task))
                        .driverOrderId(driverOrder.getDriverOrderId())
                        .rewardAmount(task.getReward().toString())
                        .event(cmd.getEvent())
                        .operator(new OperatorVO(cmd.getSysUserAccount(), cmd.getOperUserName(), cmd.getOperUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                        .build();
                confirmDspOrderService.confirm(confirmVO);
            } else {
                // 应单
                DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                        .dspOrder(order)
                        .serviceProvider(new ServiceProviderVO(order.getSpId()))
                        .supplier(new SupplierVO(supplierId))
                        .transportGroup(transportGroup)
                        .driver(driver)
                        .vehicle(vehicle)
                        .duid(DuidVO.of(task))
                        .driverOrderId(driverOrder.getDriverOrderId())
                        .rewardAmount(task.getReward().toString())
                        .event(cmd.getEvent())
                        .operator(new OperatorVO(cmd.getSysUserAccount(), cmd.getOperUserName(), cmd.getOperUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                        .build();
                confirmDspOrderService.confirm(confirmVO);
            }
            // 应单成功
            confirm = true;

        } catch (Exception e) {
            logger.error("manual_operation_assign_driver_error", e);
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_ERR.getBizException();
        } finally {
            if (!confirm) {
                // 未接单成功，取消司机单
                messageProducer.send(new DriverOrderConfirmFailEvent(driverOrder.getDriverOrderId(), driverOrder.getDspOrderId(), driverOrder.getDriverId()));

            }
        }
        return confirm;
    }

    private VehicleVO queryVehicle(DriverVO driver, Long carIdReq, ParentCategoryEnum parentCategoryEnum) {
        if (carIdReq != null && carIdReq > 0) {
            return queryVehicleService.query(carIdReq, parentCategoryEnum);
        }
        Optional<Long> optional = Optional.ofNullable(driver).map(DriverVO::getCar).map(CarVO::getCarId);
        return optional.map(carId -> queryVehicleService.query(carId, parentCategoryEnum)).orElse(null);
    }

    /**
     * 传司机信息、订单信息、运力组信息
     */
    protected void preCheck(DspOrderVO order, DriverVO driverVO, OperateAssignDriverCommand cmd) {
        //不能重复指派司机
        DspOrderDO dspOrderDO = dspOrderRepository.find(order.getDspOrderId());
        if (dspOrderDO.getConfirmRecordId() != null && dspOrderDO.getConfirmRecordId() > 0L && !Integer.valueOf(YesOrNo.YES.getCode()).equals(driverVO.getDrvTemporaryMark())) {
            DspOrderConfirmRecordVO recordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
            if (ConfirmType.isDriverOrCarConfirm(recordVO.getConfirmType()) && cmd.getDriverId().equals(recordVO.getDriverInfo().getDriverId())) {
                logger.info("manual_operation_process_info", "Cannot assign drivers repeatedly! dsp order id:{}, driver id:{}, driverVO:{}", cmd.getDspOrderId(), cmd.getDriverId(), JsonUtil.toJson(driverVO));
                throw ErrorCode.DUPLICATE_ASSIGN_DRIVER_ERR.getBizException();
            }
        }
        //特惠订单不允许指派
        if (order.isSpecialSaleOrder()) {
            throw ErrorCode.SPECIAL_ORDER_NOT_ASSIGN.getBizException();
        }
        //包车需要车
        if (!Integer.valueOf(YesOrNo.YES.getCode()).equals(driverVO.getDrvTemporaryMark())) {
            if (CategoryCodeEnum.isCharterOrder(dspOrderDO.getCategoryCode()) && StringUtils.isEmpty(cmd.getCarLicense())) {
                throw ErrorCode.ERROR_PARAMS.getBizException();
            }
        }

        if(dspOrderDO.getOrderSourceCode() != null && !OrderSourceCodeEnum.TRIP.getCode().equals(dspOrderDO.getOrderSourceCode())){
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }

    }

    /**
     * 指派解析前段传的duid，更改司机自己match配置
     *
     * @param order
     * @param groupVO
     * @return
     */
    protected abstract Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, OperateAssignDriverCommand cmd);

    public Boolean isOnlyUpdateCar(String dspOrderId, Long driverId, String carLicense) {
        if (!StringUtils.isEmpty(dspOrderId) && driverId != null && driverId > 0L && !StringUtils.isEmpty(carLicense)) {
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            if (dspOrderDO.getConfirmRecordId() != null && dspOrderDO.getConfirmRecordId() > 0L) {
                DspOrderConfirmRecordVO recordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
                Long oldDriverId = Optional.ofNullable(recordVO).map(DspOrderConfirmRecordVO::getDriverInfo).map(DspOrderConfirmRecordVO.DriverRecord::getDriverId).orElse(null);
                String oldCarLicense = Optional.ofNullable(recordVO).map(DspOrderConfirmRecordVO::getCarInfo).map(DspOrderConfirmRecordVO.CarRecord::getCarLicense).orElse(null);
                // 司机相同，车辆不同
                return Objects.equals(driverId, oldDriverId) && !Objects.equals(carLicense, oldCarLicense);
            }
        }

        return false;
    }

}
