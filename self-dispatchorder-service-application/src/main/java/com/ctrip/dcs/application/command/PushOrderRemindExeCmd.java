package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.PushOrderRemindCommand;
import com.ctrip.dcs.application.command.validator.PushOrderRemindValidator;
import com.ctrip.dcs.domain.common.enums.PushOrderRemindType;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.dsporder.gateway.PushOrderRemindGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 进单提醒
 * <AUTHOR>
 */
@Component
public class PushOrderRemindExeCmd implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteScheduleExeCmd.class);

    private static final Map<PushOrderRemindType, PushOrderRemindGateway> context = Maps.newHashMap();

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderDetailRepository dspOrderDetailRepository;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private PushOrderRemindValidator validator;

    @Override
    public void afterPropertiesSet() throws Exception {
        for (PushOrderRemindType type : PushOrderRemindType.values()) {
            PushOrderRemindGateway instance = InstanceLocator.getInstance(type.getCode(), PushOrderRemindGateway.class);
            if (Objects.isNull(instance)) {
                logger.warn("no instance of " + type.getCode());
                continue;
            }
            context.put(type, instance);
        }
    }

    public void execute(PushOrderRemindCommand command) {
        validator.validate(command);
        for (PushOrderRemindType type : command.getTypes()) {
            try {
                PushOrderRemindGateway pushOrderRemindGateway = context.get(type);
                Assert.notNull(pushOrderRemindGateway);
                if (Objects.nonNull(command.getModifyJntOrderVO())) {
                    // 原单修改场景待确认
                    pushOrderRemindGateway.push(command.getDspOrder(), command.getTransportGroup(), command.getModifyJntOrderVO());
                } else {
                    pushOrderRemindGateway.push(command.getDspOrder(), command.getTransportGroup());
                }
            } catch (Exception e) {
                logger.error(e);
            }
        }
    }

}
