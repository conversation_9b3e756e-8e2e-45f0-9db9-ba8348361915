package com.ctrip.dcs.application.command.validator;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.ExecuteTaskExeCmd;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Component
public class ExecuteTaskValidator {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteTaskExeCmd.class);

    private static final Set<DspType> VBK_DSP_TYPE = Sets.newHashSet(DspType.VBK_BROADCAST, DspType.VBK_GRAB_ORDER);

    public void validate(DspOrderVO order, ScheduleTaskDO task) throws ValidateException {
        Assert.notNull(task);
        if (!task.isWaitExecute()) {
            logger.info("execute task validator", "task is not wait execute! task id:{}", task.getTaskId());
            throw new ValidateException(ErrorCode.TASK_NOT_WAIT_EXECUTE);
        }
        Assert.notNull(order);
        if (Objects.equals(task.getSubSku().getDspType(), DspType.GRAB_BROADCAST)) {
            if (!Objects.equals(order.getOrderStatus(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) && !order.isDispatching()) {
                logger.info("execute task validator", "order is not dispatching! task id:{}, order id:{}", task.getTaskId(), task.getDspOrderId());
                throw new ValidateException(ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR);
            }
        } else {
            if (!VBK_DSP_TYPE.contains(task.getSubSku().getDspType()) && !order.isDispatching()) {
                logger.info("execute task validator", "order is not dispatching! task id:{}, order id:{}", task.getTaskId(), task.getDspOrderId());
                throw new ValidateException(ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR);
            }
            if (VBK_DSP_TYPE.contains(task.getSubSku().getDspType()) && !Objects.equals(order.getOrderStatus(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode())) {
                logger.info("execute task validator", "order is not dispatch confirmed! task id:{}, order id:{}", task.getTaskId(), task.getDspOrderId());
                throw new ValidateException(ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR);
            }
        }
    }
}
