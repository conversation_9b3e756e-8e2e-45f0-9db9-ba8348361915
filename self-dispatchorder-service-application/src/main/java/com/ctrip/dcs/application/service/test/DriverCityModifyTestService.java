package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverCityModifyEvent;
import com.ctrip.dcs.application.event.impl.DriverCityModifyEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverCityModifyTestService")
public class DriverCityModifyTestService implements ITestDspOrderService{
    @Autowired
    private DriverCityModifyEventHandler handler;

    @Override
    public String test(Map<String, String> params) {//已测试
        DriverCityModifyEvent event = new DriverCityModifyEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setCityId(Long.valueOf(params.get("cityId")));
        event.setAccountType(params.get("accountType"));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
