package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd;
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand;
import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.CalculateDriverMileageProfitEventType;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.IvrBizTypeEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.event.EmptyAndDurEvent;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.event.ImNoticeEvent;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.adapter.dto.EmptyAndDurDTO;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.dcs.infrastructure.common.constants.NoticeEnum;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class DriverCarConfirmedService {

    private static final Logger logger = LoggerFactory.getLogger(DriverCarConfirmedService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private IvrCallService ivrCallService;

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    @Autowired
    private CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd;

    @Autowired
    private TRocksProviderAdapter tRocksProviderAdapter;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    protected MessageProviderService messageProducer;

    public void confirmed(String dspOrderId, Long confirmRecordId) {
        DspOrderVO dspOrderVO = queryDspOrderService.query(dspOrderId);
        DspOrderConfirmRecordVO dspOrderConfirmRecordDO = dspOrderConfirmRecordRepository.find(confirmRecordId);
        // 客服工作台日志
        sendWorkBenchLog(dspOrderVO, dspOrderConfirmRecordDO);
        // VBK操作记录
        sendVBKOperationRecord(dspOrderVO, dspOrderConfirmRecordDO);
        // 终止调度
        shutdownSchedule(dspOrderVO);
        // 取消调度抢单
        cancelDispatcherGrabOrder(dspOrderVO);
        //取消境外ivr
        ivrCallService.cancelIvrRecord(dspOrderId,IvrBizTypeEnum.CREATE_IVR_RECORD);
        ivrCallService.cancelIvrRecord(dspOrderId,IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD);
        // 取消抢单大厅
        cancelGrabCentre(dspOrderId);
        calculateDriverMileageProfit(dspOrderVO, dspOrderConfirmRecordDO);
        sendEmptyAndDurMessage(dspOrderVO, dspOrderConfirmRecordDO);

        String newDriverId = "";
        String newSupplierId = "";

        if (dspOrderConfirmRecordDO != null) {
            newDriverId = dspOrderConfirmRecordDO.queryDriverIdStr();
            newSupplierId = dspOrderConfirmRecordDO.querySupplierIdStr();
        }

        messageProducer.send(new ImNoticeEvent(dspOrderVO.getUserOrderId(), "", NoticeEnum.TAKEN.getCode(), "", "", newSupplierId, newDriverId));

    }

    private void cancelGrabCentre(String dspOrderId) {
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);
    }

    private void sendWorkBenchLog(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        try {
            WorkBenchLogMessage message = workBenchLogMessageFactory.createDriverCarConfirmWorkBenchLog(dspOrder, confirmRecord);
            workBenchLogGateway.sendWorkBenchLogMessage(message);
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    private void sendVBKOperationRecord(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        try {
            VBKOperationRecordVO record = vbkOperationRecordFactory.createOperationRecord(dspOrder, confirmRecord);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    private void shutdownSchedule(DspOrderVO dspOrder) {
        List<ScheduleDO> schedules = scheduleRepository.query(dspOrder.getDspOrderId());
        if (CollectionUtils.isEmpty(schedules)) {
            return;
        }
        for (ScheduleDO schedule : schedules) {
            try {
                shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(schedule, ScheduleEventType.DSP_ORDER_NOT_DISPATCHING));
            } catch (Exception e) {
                logger.warn(e);
            }
        }
    }

    private void cancelDispatcherGrabOrder(DspOrderVO dspOrderDO) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(dspOrderDO.getUserOrderId());
            cancelDispatcherGrabOrderExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("cancel_dispatcher_grab_order_error", String.format("cancel_dispatcher_grab_order_error,userOrderId=%s", dspOrderDO.getUserOrderId()), e);
        }
    }

    private void calculateDriverMileageProfit(DspOrderVO dspOrderVO, DspOrderConfirmRecordVO dspOrderConfirmRecordDO) {
        try {
            if (dspOrderVO != null && dspOrderConfirmRecordDO != null && dspOrderConfirmRecordDO.getDriverInfo() != null) {
                Long supplierId = dspOrderVO.getSupplierId() != null ? dspOrderVO.getSupplierId().longValue() : 0L;
                DriverVO driverVO = queryDriverService.queryDriver(dspOrderConfirmRecordDO.getDriverInfo().getDriverId(), CategoryUtils.selfGetParentType(dspOrderVO), supplierId);
                DriverWorkTimeVO driverWorkTimeVO = DriverWorkTimeUtil.getDriverWorkTime(driverVO, dspOrderVO.getEstimatedUseTime());
                if (driverWorkTimeVO == null) {
                    return;
                }
                List<DriverMileageProfitTypeVO> driverMileageProfitType = Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY);
                CalculateDriverMileageProfitCommand command = new CalculateDriverMileageProfitCommand(driverVO, driverWorkTimeVO, driverMileageProfitType, dspOrderVO.getDspOrderId(), CalculateDriverMileageProfitEventType.DSP_ORDER_DRIVER_CAR_CONFIRM);
                calculateDriverMileageProfitExeCmd.execute(command);
            }
        } catch (Exception e) {
            logger.warn("calculate_driver_mileage_profit_error", e);
        }
    }

    private void sendEmptyAndDurMessage(DspOrderVO dspOrderVO, DspOrderConfirmRecordVO dspOrderConfirmRecordDO) {
        try {
            if (dspOrderVO == null || dspOrderConfirmRecordDO == null || dspOrderConfirmRecordDO.getDriverInfo() == null) {
                return;
            }
            String key = String.format(EmptyAndDurDTO.EMPTY_AND_DUR_PREFIX, dspOrderVO.getDspOrderId(), dspOrderConfirmRecordDO.getDriverInfo().getDriverId());
            String value = tRocksProviderAdapter.get(key);
            if (StringUtils.isBlank(value)) {
                return;
            }
            EmptyAndDurDTO dto = JsonUtil.fromJson(value, EmptyAndDurDTO.class);
            dto.setDriverTakenTime(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));
            dto.setTakenType(dspOrderConfirmRecordDO.getTakenType());
            messageProviderService.send(new EmptyAndDurEvent(JsonUtil.toJson(dto)));
        } catch (Exception e) {
            logger.warn("send_empty_and_dur_error", e);
        }
    }
}
