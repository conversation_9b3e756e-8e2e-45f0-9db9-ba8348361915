package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabDriverStatus;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabDriverRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskOperateEvent;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


@Component
public class UpdateVBKGrabTaskExeCmd extends AbstractVBKGrabTaskExeCmd{

    private static final Logger logger = LoggerFactory.getLogger(UpdateVBKGrabTaskExeCmd.class);

    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    private VBKDriverGrabDriverRepository vbkDriverGrabDriverRepository;


    public Pair<Integer, Integer> execute(VBKGrabTaskDTO requestType) throws Exception{
        logger.info("UpdateVBKGrabTaskExeCmd_enter", LocalJsonUtils.toJson(requestType));
        VBKDriverGrabTaskDO vbkDriverGrabTask = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(requestType.getTaskId());
        logger.info("UpdateVBKGrabTaskExeCmd_vbkDriverGrabTask", LocalJsonUtils.toJson(vbkDriverGrabTask));
        if(Objects.isNull(vbkDriverGrabTask) || GrabTaskStatus.canNotUpdate(vbkDriverGrabTask.getTaskStatus())){
            throw ErrorCode.GRAB_TASK_STATUS.getBizException();
        }
        if (!Objects.equals(vbkDriverGrabTask.getSupplierId(), requestType.getSupplierId())) {
            throw ErrorCode.UPDATE_GRAB_TASK_ERROR.getBizException();
        }
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_GRAB_TASK_WITH_TASK_ID, requestType.getSupplierId(), requestType.getTaskId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            //操作记录使用
            VBKDriverGrabTaskDO vbkDriverGrabTaskDOTemp = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(requestType.getTaskId());

            //构建VBK抢单任务实体
            VBKDriverGrabTaskDO vbkDriverGrabTaskDO = super.buildVBKDriverGrabTaskDO(requestType);
            //构建VBK抢单司机实体
            List<VBKDriverGrabDriverDO> vbkDriverGrabDriverDOS = vbkDriverGrabDriverRepository.queryDriverListByTaskId(requestType.getTaskId());
            List<String> driverIdList = vbkDriverGrabDriverDOS.stream().map(VBKDriverGrabDriverDO::getDriverId).collect(toList());
            //操作记录使用
            long originDriverNum = vbkDriverGrabDriverDOS.stream().filter(x -> x.getGrabStatus().equals(GrabDriverStatus.IN_PROGRESS.getCode())).count();

            List<String> newDriverIdList = requestType.getDriverIdList();
            List<String> oldUnavailableDriver = driverIdList.stream().filter(e -> !newDriverIdList.contains(e)).collect(toList());
            List<String> oldUsableDriver = driverIdList.stream().filter(newDriverIdList::contains).collect(toList());
            List<String> initDrvIds = newDriverIdList.stream().filter(drvId -> !driverIdList.contains(drvId)).collect(toList());
            List<VBKDriverGrabDriverDO> vbkDriverGrabDriverDOList = initDrvIds.stream().map(driverId -> super.buildVbkDriverGrabDriverDO(requestType, driverId)).collect(Collectors.toList());
            //构建VBK待抢订单实体
            List<VBKDriverGrabOrderDO> grabOrderList = vbkDriverGrabOrderRepository.queryByTaskIdAndStatus(requestType.getTaskId(), GrabTaskStatus.IN_PROGRESS.getCode());
            List<String> dspOrderIdList = grabOrderList.stream().map(VBKDriverGrabOrderDO::getDspOrderId).collect(Collectors.toList());
            List<DspOrderVO> dspOrderVOS = queryDspOrderService.selectDspOrderVOs(dspOrderIdList);
            if(dspOrderVOS.size() != dspOrderIdList.size()){
                throw ErrorCode.PLEASE_TRY_AGAIN.getBizException();
            }
            Map<String, DspOrderVO> dspOrderVOMap = dspOrderVOS.stream().filter(x -> x.getOrderStatus().equals(OrderStatusEnum.DISPATCH_CONFIRMED.getCode())).collect(Collectors.toMap(DspOrderVO::getDspOrderId, o -> o, (o1, o2) -> o2));
            logger.info("UpdateVBKGrabTaskExeCmd_QueryDspOrderServiceImpl_dspOrderVOMap", LocalJsonUtils.toJson(dspOrderVOMap));
            if(dspOrderVOMap.size() == 0){
                throw ErrorCode.ORDER_STATUS_CHANGED.getBizException();
            }
            //构建VBK待抢订单实体
            List<VBKDriverGrabOrderDO> vbkDriverGrabOrderDOList = dspOrderIdList.stream().map(dspOrderId -> super.buildVbkDriverGrabOrderDO(requestType, dspOrderVOMap, dspOrderId)).filter(Objects::nonNull).collect(Collectors.toList());
            //数据修改
            int successNum = vbkDriverGrabTaskRepository.updateVBKGrabTask(vbkDriverGrabTaskDO, vbkDriverGrabOrderDOList, oldUnavailableDriver, oldUsableDriver, vbkDriverGrabDriverDOList, this.getDbBatchSize());
            int total = grabOrderList.size();
            //发送派发消息
            sendVBKGrabDspTask(vbkDriverGrabOrderDOList);
            //操作记录
            sendVBKGrabTaskOperateEvent(requestType, vbkDriverGrabTaskDOTemp, originDriverNum);
            return new Pair<>(successNum, total - successNum);
        } catch (DistributedLockRejectedException e) {
            logger.error("UpdateVBKGrabTaskExeCmd_error", e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        }  finally {
            lock.unlock();
        }
    }


    public void sendVBKGrabTaskOperateEvent(VBKGrabTaskDTO requestType, VBKDriverGrabTaskDO vbkDriverGrabTaskDOTemp, long originDriverNum) {
        try{
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = new VBKGrabTaskOperateDTO();
            vbkGrabTaskOperateDTO.setTaskId(requestType.getTaskId());
            vbkGrabTaskOperateDTO.setCityId(requestType.getCityId());
            vbkGrabTaskOperateDTO.setSupplierId(requestType.getSupplierId());
            vbkGrabTaskOperateDTO.setOperatorName(requestType.getOperatorName());
            vbkGrabTaskOperateDTO.setOperatorType(VBKGrabOperationTypeEnum.OperationTypeEnum.UPDATE.getCode());
            vbkGrabTaskOperateDTO.setOperatorDesc(VBKGrabOperationTypeEnum.OperationTypeEnum.UPDATE.getDes());
            vbkGrabTaskOperateDTO.setCurrentDateStr(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));

            vbkGrabTaskOperateDTO.setInitialType(requestType.getInitial().getType());
            vbkGrabTaskOperateDTO.setInitialValue(requestType.getInitial().getValue());
            vbkGrabTaskOperateDTO.setInitialRate(requestType.getInitial().getRate());
            vbkGrabTaskOperateDTO.setInitialCurrency(requestType.getInitial().getCurrency());
            vbkGrabTaskOperateDTO.setDriverNum((long) requestType.getDriverIdList().size());
            vbkGrabTaskOperateDTO.setRewardsType(requestType.getRewards().getType());
            vbkGrabTaskOperateDTO.setRewardsValue(requestType.getRewards().getValue());
            vbkGrabTaskOperateDTO.setRewardsRate(requestType.getRewards().getRate());
            vbkGrabTaskOperateDTO.setRewardsCurrency(requestType.getRewards().getCurrency());
            vbkGrabTaskOperateDTO.setGrabEndTime(requestType.getGrabEndTime());
            vbkGrabTaskOperateDTO.setMakeUpEffectTime(requestType.getMakeUpEffectTime());
            vbkGrabTaskOperateDTO.setPayForDriver(requestType.getPayForDriver());

            vbkGrabTaskOperateDTO.setInitialTypeOrigin(vbkDriverGrabTaskDOTemp.getInitialType());
            vbkGrabTaskOperateDTO.setInitialValueOrigin(vbkDriverGrabTaskDOTemp.getInitialValue());
            vbkGrabTaskOperateDTO.setInitialRateOrigin(vbkDriverGrabTaskDOTemp.getInitialRate());
            vbkGrabTaskOperateDTO.setInitialCurrencyOrigin(vbkDriverGrabTaskDOTemp.getInitialCurrency());
            vbkGrabTaskOperateDTO.setRewardsTypeOrigin(vbkDriverGrabTaskDOTemp.getRewardsType());
            vbkGrabTaskOperateDTO.setRewardsValueOrigin(vbkDriverGrabTaskDOTemp.getRewardsValue());
            vbkGrabTaskOperateDTO.setRewardsRateOrigin(vbkDriverGrabTaskDOTemp.getRewardsRate());
            vbkGrabTaskOperateDTO.setRewardsCurrencyOrigin(vbkDriverGrabTaskDOTemp.getRewardsCurrency());
            vbkGrabTaskOperateDTO.setGrabEndTimeOrigin(vbkDriverGrabTaskDOTemp.getGrabLimitHours().toString());
            BigDecimal grabRewardHours = vbkDriverGrabTaskDOTemp.getGrabRewardHours();
            vbkGrabTaskOperateDTO.setMakeUpEffectTimeOrigin(grabRewardHours == null ? null : grabRewardHours.toString());
            vbkGrabTaskOperateDTO.setOriginDriverNum(originDriverNum);
            String jsonStr = JsonUtils.toJson(vbkGrabTaskOperateDTO);
            logger.info("UpdateVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", jsonStr);
            VBKGrabTaskOperateEvent shutdownScheduleEvent = new VBKGrabTaskOperateEvent(jsonStr);
            messageProducer.send(shutdownScheduleEvent);
        }catch (Exception ex){
            logger.error("UpdateVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", ex);
        }
    }
}
