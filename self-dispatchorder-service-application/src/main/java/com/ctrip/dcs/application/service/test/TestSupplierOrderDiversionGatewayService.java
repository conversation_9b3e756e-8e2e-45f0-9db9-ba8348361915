package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.schedule.gateway.SupplierOrderDiversionGateway;
import com.ctrip.dcs.domain.schedule.value.SupplierDiversionVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
@Component
public class TestSupplierOrderDiversionGatewayService implements ITestDspOrderService{
    @Autowired
    private SupplierOrderDiversionGateway gateway;
    @Override
    public String test(Map<String, String> params) {
        String cityId = params.get("cityId");
        String categoryCode = params.get("categoryCode");
        Date bookTime = DateUtil.parseDateStr2Date(params.get("bookTime"));
        List<Long> supplierList = LocalStringUtils.stringToLongList(params.get("supplierIds"));
        List<SupplierDiversionVO> result = gateway.query(Integer.valueOf(cityId),categoryCode,bookTime,bookTime,supplierList,0);
        return LocalJsonUtils.toJson(result);
    }
}
