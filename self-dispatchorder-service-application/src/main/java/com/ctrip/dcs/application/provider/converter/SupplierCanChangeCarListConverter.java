package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.ctrip.dcs.application.command.dto.CarInfo4VbkDTO;
import com.ctrip.dcs.order.interfaces.dto.VehicleInfo;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

public class SupplierCanChangeCarListConverter {
    public static List<VehicleInfo> convert(List<CarInfo4VbkDTO> carInfo4VbkDTOS){
        if(CollectionUtil.isEmpty(carInfo4VbkDTOS)){
            return Collections.emptyList();
        }
        List<VehicleInfo> res = Lists.newArrayList();
        for (CarInfo4VbkDTO carInfo4VbkDTO : carInfo4VbkDTOS){
            VehicleInfo vehicleInfo = new VehicleInfo();
            vehicleInfo.setVehicleId(carInfo4VbkDTO.getVehicleId());
            vehicleInfo.setVehicleBrandId(carInfo4VbkDTO.getVehicleBrandId());
            vehicleInfo.setVehicleBrandName(carInfo4VbkDTO.getVehicleBrandName());
            vehicleInfo.setVehicleLicense(carInfo4VbkDTO.getVehicleLicense());
            vehicleInfo.setVehicleSeriesId(carInfo4VbkDTO.getVehicleSeriesId());
            vehicleInfo.setVehicleSeriesName(carInfo4VbkDTO.getVehicleSeriesName());
            vehicleInfo.setVehicleTypeId(carInfo4VbkDTO.getVehicleTypeId());
            vehicleInfo.setVehicleTypeName(carInfo4VbkDTO.getVehicleTypeName());
            vehicleInfo.setVehicleColorId(carInfo4VbkDTO.getVehicleColorId());
            vehicleInfo.setVehicleColorName(carInfo4VbkDTO.getVehicleColorName());
            vehicleInfo.setTemporaryDispatchMark(carInfo4VbkDTO.getTemporaryDispatchMark());
            res.add(vehicleInfo);
        }
        return res;
    }

}
