package com.ctrip.dcs.application.service.risk;

import com.ctrip.dcs.domain.common.enums.LateRiskTypeEnum;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.gateway.LateRiskRemindGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/7 11:51
 */
@Service
public class LateRiskTypeBeforeToMeetStrategy extends AbstractLateRiskTypeStrategy {
    private Logger logger = LoggerFactory.getLogger(LateRiskTypeBeforeToMeetStrategy.class);
    @Resource
    LateRiskRemindGateway lateRiskRemindGateway;
    
    @Override
    public Integer getType() {
        return LateRiskTypeEnum.BEFORE_TO_MEET.getType();
    }
    
    @Override
    public void sendEmail(DspOrderVO dspOrder, TransportGroupVO transportGroupVO) {
        if (StringUtils.isBlank(transportGroupVO.getInformEmail())) {
            logger.info("late_risk_before_toMeet_send_email_" + dspOrder.getDspOrderId(), "receiver email is empty, no need send");
            return;
        }
        lateRiskRemindGateway.pushLateRiskBeforeToMeetForEmail(dspOrder, transportGroupVO);
    }
    
    @Override
    public void sendPcStationLetter(DspOrderVO dspOrder, TransportGroupVO transportGroup) {
        lateRiskRemindGateway.pushLateRiskBeforeToMeetForPcSite(dspOrder, transportGroup);
    }
    
    @Override
    public void sendMobileSupplierRemind(DspOrderVO dspOrder, TransportGroupVO transportGroup) {
        lateRiskRemindGateway.pushLateRiskBeforeToMeetForApp(dspOrder, transportGroup);
    }
    
    @Override
    public void sendLateRiskIVR(DspOrderVO dspOrderVO, TransportGroupVO transportGroupDetail) {
        lateRiskRemindGateway.pushLateRiskBeforeToMeetForIVR(dspOrderVO, transportGroupDetail);
    }
}
