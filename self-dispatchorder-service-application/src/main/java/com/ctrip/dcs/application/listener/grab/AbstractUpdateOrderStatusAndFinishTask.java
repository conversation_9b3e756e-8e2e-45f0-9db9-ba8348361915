package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.CancelVBKGrabTaskExeCmd;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.FinishTaskEvent;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


public abstract class AbstractUpdateOrderStatusAndFinishTask extends MqListener{

    private static final Logger logger = LoggerFactory.getLogger(AbstractUpdateOrderStatusAndFinishTask.class);

    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private CancelVBKGrabTaskExeCmd cancelVBKGrabTaskExeCmd;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    protected MessageProviderService messageProducer;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;


    public void dealMessageLogic(String supplyOrderId, Integer orderStatus, Integer grabStatus, String driverOrderId) throws DistributedLockRejectedException {
        List<VBKDriverGrabOrderDO> list = vbkDriverGrabOrderRepository.queryByDspOrderId(supplyOrderId, null);
        logger.info("VBKGrabOrderCancelListener_dealMessageLogic_queryByDspOrderId", JsonUtils.toJson(list));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Optional<Long> first = list.stream().map(VBKDriverGrabOrderDO::getSupplierId).findFirst();
        if(first.isEmpty()){
            return;
        }
        Long supplierId = first.get();
        logger.info("VBKGrabOrderCancelListener_dealMessageLogic_supplierId", JsonUtils.toJson(supplierId));
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_GRAB_TASK_WITH_DSP_ORDER_ID, supplierId, supplyOrderId);
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try{
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            finishGrabOrder(list, orderStatus, grabStatus, driverOrderId);
        } finally {
            lock.unlock();
        }
    }


    private void finishGrabOrder(List<VBKDriverGrabOrderDO> list, Integer orderStatus, Integer grabStatus, String driverOrderId) {
        Set<String> dspOrderIdList = list.stream().map(VBKDriverGrabOrderDO::getDspOrderId).collect(Collectors.toSet());
        logger.info("VBKGrabOrderTakenListener_finishGrabOrder_finishBatchByDspOrderIds", JsonUtils.toJson(dspOrderIdList));
        //修改待抢订单状态
        vbkDriverGrabOrderRepository.finishBatchByDspOrderIds(orderStatus, grabStatus, new ArrayList<>(dspOrderIdList), driverOrderId);
        //派发任务中止
        cancelVBKGrabTaskExeCmd.sendShutdownScheduleEvent(new ArrayList<>(dspOrderIdList));
        //检查抢单任务，把没有待抢订单的任务设置为结束
        List<String> taskIdList = list.stream().map(VBKDriverGrabOrderDO::getVbkGrabTaskId).collect(Collectors.toList());
        logger.info("VBKGrabOrderTakenListener_finishGrabOrder_taskIdList", JsonUtils.toJson(taskIdList));
        List<String> inProcessTaskIdList = vbkDriverGrabTaskRepository.queryTaskIdByVBKGrabTaskIds(taskIdList,  GrabTaskStatus.IN_PROGRESS.getCode());
        logger.info("VBKGrabOrderTakenListener_finishGrabOrder_inProcessTaskIdList", JsonUtils.toJson(inProcessTaskIdList));
        if(CollectionUtils.isEmpty(inProcessTaskIdList)){
            return;
        }
        Set<String> collect = inProcessTaskIdList.stream().filter(x -> {
            int num = vbkDriverGrabOrderRepository.countByTaskIdAndStatus(x, GrabTaskStatus.IN_PROGRESS.getCode());
            return num == 0;
        }).collect(Collectors.toSet());
        logger.info("VBKGrabOrderTakenListener_finishGrabOrder_needFinishTaskIds", JsonUtils.toJson(collect));
        //完成任务
        this.sendFinishTaskEvent(collect, GrabTaskStatus.FINISH.getCode());
    }


    private void sendFinishTaskEvent(Set<String> needFinishTaskIds, Integer grabStatus) {
        try{
            logger.info("VBKGrabOrderCancelListener_sendFinishTaskEvent", JsonUtils.toJson(needFinishTaskIds));
            if(CollectionUtils.isEmpty(needFinishTaskIds)){
                return;
            }
            String taskIdStr = org.apache.commons.lang.StringUtils.join(needFinishTaskIds, ",");
            FinishTaskEvent finishTaskEvent = new FinishTaskEvent(taskIdStr, grabStatus);
            logger.info("VBKGrabOrderCancelListener_sendFinishTaskEvent_event", LocalJsonUtils.toJson(finishTaskEvent));
            messageProducer.send(finishTaskEvent);
        }catch (Exception ex){
            logger.error("VBKGrabOrderCancelListener_sendFinishTaskEvent", ex);
        }
    }
}
