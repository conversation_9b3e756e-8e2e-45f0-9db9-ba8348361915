package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.dsporder.entity.tool.SupplierInfoDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DspOrderBasic;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BatchQueryDspOrderExecutor extends AbstractRpcExecutor<BatchQueryDspOrderRequestType, BatchQueryDspOrderResponseType>
        implements Validator<BatchQueryDspOrderRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(BatchQueryDspOrderExecutor.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Override
    public BatchQueryDspOrderResponseType execute(BatchQueryDspOrderRequestType requestType) {
        BatchQueryDspOrderResponseType response = new BatchQueryDspOrderResponseType();
        try {
            List<SupplierInfoDO> supplierInfoList = dspOrderRepository.batchQueryDspOrdersByUserOrderIds(requestType.getUserOrderIdList());
            List<DspOrderBasic> orderBasics = Lists.newArrayList();
            response.setDspOrderBasicList(orderBasics);
            for (SupplierInfoDO supplier : supplierInfoList) {
                orderBasics.add(transform(supplier));
            }
            return ServiceResponseUtils.success(response);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(response, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("BatchQueryDspOrder_Error", ex);
            return ServiceResponseUtils.fail(response);
        }
    }

    public DspOrderBasic transform(SupplierInfoDO supplier) {
        if (supplier == null) {
            return null;
        }
        DspOrderBasic basic = new DspOrderBasic();
        basic.setUserOrderId(supplier.getUserOrderId());
        basic.setSpId(supplier.getSpId());
        basic.setSupplierId(supplier.getSupplierId());
        basic.setOrderStatus(supplier.getOrderStatus());
        basic.setDspOrderId(supplier.getDspOrderId());
        return basic;
    }

    @Override
    public void validate(AbstractValidator<BatchQueryDspOrderRequestType> validator) {
        validator.ruleFor("userOrderIdList").notNull().notEmpty();
    }
}
