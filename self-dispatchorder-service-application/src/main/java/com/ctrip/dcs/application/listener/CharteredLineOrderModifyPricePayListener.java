package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ModifyCharteredLineOrderExeCmd;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;

/**
 * 线路包车订单修改后，支付或者退款通知
 */
@Component
public class CharteredLineOrderModifyPricePayListener {

    private static final Logger logger = LoggerFactory.getLogger(CharteredLineOrderModifyPricePayListener.class);

    @Autowired
    ModifyCharteredLineOrderExeCmd modifyCharteredLineOrderExeCmd;

    /**
     * 修改支付状态（支付成功、退款成功）
     *
     * @param message
     */
    @QmqLogTag(tagKeys = {"orderId"})
    @QmqConsumer(prefix = EventConstants.USER_ORDER_PAYMENT_EVENT, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String userOrderId = message.getStringProperty("orderId");
        // eventType=refund.confirmed，表示退款
        String eventType = message.getStringProperty("eventtype");
        String content = message.getStringProperty("content");
        if (StringUtils.isBlank(userOrderId) || StringUtils.isBlank(content) || StringUtils.isBlank(eventType)) {
            logger.warn("CharteredLineOrderModifyPricePayListener_error", "qmq param error");
            return;
        }

        Map contentMap = JacksonSerializer.INSTANCE().deserialize(content, Map.class);
        if (CollectionUtils.isEmpty(contentMap) || !contentMap.containsKey("scenesType") || contentMap.get("scenesType") == null) {
            logger.warn("CharteredLineOrderModifyPricePayListener_" + userOrderId, "qmq param error or scenesType error");
            return;
        }

        // 502是用户订单约定的，包车修改补退款标识，非502，不做处理
        Integer scenesType = (Integer) contentMap.get("scenesType");
        if (!Integer.valueOf("502").equals(scenesType)) {
            logger.info("CharteredLineOrderModifyPricePayListener_" + userOrderId, "scenesType is not 502, return");
            return;
        }

        // 1=退款，0=补款
        int payStatus = "refund.confirmed".equals(eventType) ? 1 : 0;

        try {
            logger.info("CharteredLineOrderModifyPricePayListener_" + userOrderId, "modifyCharteredLineOrderPriceStatus inner START, payStatus={}", payStatus);
            boolean result = modifyCharteredLineOrderExeCmd.modifyCharteredLineOrderPriceStatus(userOrderId, payStatus);
            logger.info("CharteredLineOrderModifyPricePayListener_" + userOrderId, "modifyCharteredLineOrderPriceStatus END, res={}", JacksonSerializer.INSTANCE().serialize(result));
            if (!result) {
                // 一单已报
                MetricsUtil.recordValue(MetricsConstants.MODIFY_CHARTERED_LINE_ORDER_PRICE_FAIL);
            }
        } catch (Exception e) {
            logger.warn("CharteredLineOrderModifyPricePayListenerError", e);
            // 一单已报
            MetricsUtil.recordValue(MetricsConstants.MODIFY_CHARTERED_LINE_ORDER_PRICE_EXCEPTION);
            throw e;
        }
    }

}
