package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("GrabOrderDetailRepositoryTestService")
public class GrabOrderDetailRepositoryTestService implements ITestDspOrderService{
    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;
    @Override
    public String test(Map<String, String> params) {
        String dspOrderId = params.get("dspOrderId");
        String driverIdStr = params.get("driverId");
        String duid = params.get("duid");
        if(StringUtils.isNotBlank(duid) && StringUtils.isNotBlank(driverIdStr)){
            grabOrderDetailRepository.deleteByDuidAndDriverId(duid, Long.valueOf(driverIdStr));
        }else if(StringUtils.isNotBlank(dspOrderId) && StringUtils.isNotBlank(driverIdStr)){
            grabOrderDetailRepository.delete(dspOrderId, Long.valueOf(driverIdStr));
        }else if(StringUtils.isNotBlank(dspOrderId)){
            grabOrderDetailRepository.deleteAll(dspOrderId);
        }
        return LocalJsonUtils.toJson("success");
    }
}
