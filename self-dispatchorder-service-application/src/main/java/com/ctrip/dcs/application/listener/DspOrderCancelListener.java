package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd;
import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand;
import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.command.dto.AbroadOrderCancelDTO;
import com.ctrip.dcs.application.service.ShutdownScheduleService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum;
import com.ctrip.dcs.domain.common.enums.CalculateDriverMileageProfitEventType;
import com.ctrip.dcs.domain.common.enums.CancelReasonIdEnum;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.IvrBizTypeEnum;
import com.ctrip.dcs.domain.common.enums.NewLossTypeEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.DecryptPhoneService;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.service.SendEmailService;
import com.ctrip.dcs.domain.common.service.SendSmsService;
import com.ctrip.dcs.domain.common.service.VbkAppPushService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.DriverWorkTimeUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.TripSendEmailVO;
import com.ctrip.dcs.domain.common.value.TripSendMessageVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.domain.dsporder.entity.QueryGeoDetailReqDTO;
import com.ctrip.dcs.domain.dsporder.entity.QueryGeoDetailResDTO;
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail;
import com.ctrip.dcs.domain.dsporder.event.OriModifyOrderCancelDelayCheckEvent;
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;
import static com.ctrip.dcs.domain.common.constants.SysConstants.Email.ORDER_FINE_CANCEL_CH;
import static com.ctrip.dcs.domain.common.constants.SysConstants.Email.ORDER_FINE_CANCEL_EN;
import static com.ctrip.dcs.domain.common.constants.SysConstants.Email.ORDER_NO_FINE_CANCEL_CH;
import static com.ctrip.dcs.domain.common.constants.SysConstants.Email.ORDER_NO_FINE_CANCEL_EN;

/**
 * 派发单取消消息
 *
 * <AUTHOR>
 */
@Component
public class DspOrderCancelListener {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderCancelListener.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private SendSmsService sendSmsService;

    @Autowired
    private SendEmailService sendEmailService;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    private PlatformGeoServiceGateway platformGeoServiceGateway;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private DecryptPhoneService decryptPhoneService;

    @Autowired
    private IvrCallService ivrCallService;

    @Autowired
    private VbkAppPushService vbkAppPushService;

    @Autowired
    private DspOrderFeeRepository dspOrderFeeRepository;

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;


    @Autowired
    private CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private ShutdownScheduleService shutdownScheduleService;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;
    @Autowired
    private DspDrvOrderLimitTakenRecordGateway dspDrvOrderLimitTakenRecordGateway;
    @Autowired
    private MessageProviderService messageProviderService;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_send_message", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) throws SQLException {
        logger.info("DspOrderCancelListener.onMessage begin", JacksonUtil.serialize(message));
        //"drvOrderId":"司机单号","dspOrderId":"派发单号",
        String driverOrderId = message.getStringProperty("driverOrderId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        String cancelReasonId = message.getStringProperty("cancelReasonId");
        if (Strings.isBlank(dspOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("DspOrderCancelListener_dsporder_cancel qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            DspOrderConfirmRecordVO confirmRecordDO = dspOrderConfirmRecordRepository.findByDspOrderId(dspOrderId, driverOrderId);
            if (dspOrderDO == null) {
                logger.info("DspOrderCancelListener_dsporder_cancel dspOrder is null,dspOrderId=" + dspOrderId);
                return;
            }
            // 取消调度抢单
            cancelDispatcherGrabOrder(dspOrderDO);
            // 取消抢单大厅
            cancelGrabCentre(dspOrderId);
            // 终止调度
            shutdownSchedule(dspOrderId);
            // 取消调度ivr任务
            cancelIvrCall(dspOrderId);
            // 取消订单通知
            sendNotice(cancelReasonId, dspOrderDO, confirmRecordDO);
            // 计算里程价值
            calculateDriverMileProfit(confirmRecordDO, dspOrderDO);
            // 取消公里数限制订单接单记录
            cancelTakenRecord(dspOrderId);
        } catch (Exception e) {
            logger.error("DspOrderCancelListener.error", e);
            throw e;
        }
    }

    private void cancelTakenRecord(String dspOrderId) {
        dspDrvOrderLimitTakenRecordGateway.orderCancel(dspOrderId);
    }

    private void cancelIvrCall(String dspOrderId) {
        ivrCallService.cancelIvrRecord(dspOrderId, IvrBizTypeEnum.CREATE_IVR_RECORD);
        ivrCallService.cancelIvrRecord(dspOrderId, IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD);
        ivrCallService.cancelIvrRecord(dspOrderId, IvrBizTypeEnum.ORI_ORDER_MODIFY_CREATE_IVR_RECORD);
    }

    private void calculateDriverMileProfit(DspOrderConfirmRecordVO confirmRecordDO, DspOrderDO dspOrderDO) {
        if (confirmRecordDO == null || dspOrderDO == null) {
            return;
        }
        // 计算里程价值
        try {
            if (confirmRecordDO.getDriverInfo() != null && confirmRecordDO.getDriverInfo().getDriverId() != null && confirmRecordDO.getDriverInfo().getDriverId() != 0) {
                Long supplierId = confirmRecordDO.getSupplierInfo().getSupplierId() != null ? confirmRecordDO.getSupplierInfo().getSupplierId() : 0L;
                DriverVO driverVO = queryDriverService.queryDriver(confirmRecordDO.getDriverInfo().getDriverId(), CategoryUtils.selfGetParentType(dspOrderDO.getCategoryCode()), supplierId);
                DriverWorkTimeVO driverWorkTimeVO = DriverWorkTimeUtil.getDriverWorkTime(driverVO, dspOrderDO.getEstimatedUseTime());
                if (driverWorkTimeVO == null) {
                    return;
                }
                List<DriverMileageProfitTypeVO> driverMileageProfitType = Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY);
                CalculateDriverMileageProfitCommand command = new CalculateDriverMileageProfitCommand(driverVO, driverWorkTimeVO, driverMileageProfitType, dspOrderDO.getDspOrderId(), CalculateDriverMileageProfitEventType.DSP_ORDER_CANCEL);
                calculateDriverMileageProfitExeCmd.execute(command);
                logger.info("calculateDriverMileageProfitExeCmd.execute");
            }
        } catch (Exception e) {
            logger.warn("calculate_driver_mileage_profit_error", e);
        }
    }

    private void sendNotice(String cancelReasonId, DspOrderDO dspOrderDO, DspOrderConfirmRecordVO confirmRecordDO) {
        if (confirmRecordDO == null) {
            return;
        }
        AbroadOrderCancelDTO cancelDTO = buildCancelDTO(confirmRecordDO);
        cancelDTO.setCancelReasonId(cancelReasonId);
        if (cancelDTO.getTransportGroupId() == null || cancelDTO.getTransportGroupId() == 0) {
            return;
        }
        // 包车订单单独处理
        if (CategoryCodeEnum.isCharterOrder(dspOrderDO.getCategoryCode())) {
            this.charteredOrderCancel(cancelDTO, dspOrderDO);
            // 包车订单取消 新增邮件提醒
            this.orderCancel(cancelDTO, dspOrderDO);
            return;
        }
        // 非包车，如果是原单修改，先不发送取消通知（延迟3分钟检查，如果不是原供应商接单，再发送取消通知）
        boolean needHoldCancel = Objects.equals(String.valueOf(CancelReasonIdEnum.ORI_MODIFY_CANCEl.getCode()), cancelReasonId);
        if (needHoldCancel) {
            logger.info("DspOrderCancelListener_" + dspOrderDO.getDspOrderId(), "is oriModifyCancel,send delay check qmq");
            long delay = Optional.ofNullable(commonConfConfig.getLong("oriModifyOrderCancelDelaySecond")).orElse(180L) * 1000;
            messageProviderService.send(new OriModifyOrderCancelDelayCheckEvent(cancelDTO.getDspOrderId(), cancelDTO.getDriverOrderId(), dspOrderDO.getUserOrderId(), cancelDTO.getCancelReasonId(), delay));
            return;
        }
        cancelNoticeForJnt(dspOrderDO, cancelDTO);
    }

    private void cancelNoticeForJnt(DspOrderDO dspOrderDO, AbroadOrderCancelDTO cancelDTO) {
        // 发送vbk APP推送、站内信
        vbkAppPushService.dspOrderCancelAppVoicePush(dspOrderDO);
        // 发送邮件
        orderCancel(cancelDTO, dspOrderDO);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.ORI_MODIFY_ORDER_CANCEL_DELAY_CHECK_TOPIC, consumerGroup = "100041593", idempotentChecker = "redisIdempotentChecker")
    public void onOriModifyCancelDelay(Message message) {
        String userOrderId = message.getStringProperty("userOrderId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        String driverOrderId = message.getStringProperty("driverOrderId");
        String cancelReasonId = message.getStringProperty("cancelReasonId");
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(userOrderId, dspOrderId)) {
            return;
        }
        logger.info("DspOrderCancelListener.onOriModifyCancelDelay_" + dspOrderId, "userOrderId=" + userOrderId + ", cancelReasonId=" + cancelReasonId);
        // 查询修改后的新派发单
        List<DspOrderDO> validDspOrders = dspOrderRepository.queryValidDspOrders(userOrderId);
        if (CollectionUtils.isNotEmpty(validDspOrders)) {
            // 原供应商接单，不发送取消通知
            Optional<DspOrderDO> newDspOrder = validDspOrders.stream().filter(dsp -> dspOrderId.equals(dsp.getOriginalDspOrderId())).findFirst();
            if (newDspOrder.isPresent()) {
                if (Objects.equals(1, newDspOrder.get().getOriginalSupplierConfirm())) {
                    logger.info("newDspOrder is originalSupplierConfirm, no need send cancel notice");
                    Cat.logEvent("Ori.Modify.Order.Cancel.Delay.Check", "OriginalSupplierConfirm");
                    return;
                }
                logger.info("newDspOrder is oriOrderModify, but not yet confirm");
                Cat.logEvent("Ori.Modify.Order.Cancel.Delay.Check", "OriOrderModifyNotYetConfirm");
            }
        }
        // 原供应商未在5min内应单或其他供应商接单或用户单取消，继续发送取消通知
        DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
        if (dspOrderDO == null) {
            logger.error("dspOrder is null,dspOrderId=" + dspOrderId);
            Cat.logEvent("Ori.Modify.Order.Cancel.Delay.Check", "NoOriDspOrder");
            return;
        }
        DspOrderConfirmRecordVO confirmRecordDO = dspOrderConfirmRecordRepository.findByDspOrderId(dspOrderId, driverOrderId);
        if (confirmRecordDO == null) {
            logger.error("queryConfirmRecord is null");
            Cat.logEvent("Ori.Modify.Order.Cancel.Delay.Check", "NoOriDspOrderConfirmRecord");
            return;
        }
        AbroadOrderCancelDTO cancelDTO = buildCancelDTO(confirmRecordDO);
        cancelDTO.setCancelReasonId(cancelReasonId);
        if (cancelDTO.getTransportGroupId() == null || cancelDTO.getTransportGroupId() == 0) {
            return;
        }
        // 取消订单通知商家
        cancelNoticeForJnt(dspOrderDO, cancelDTO);
        logger.info("newDspOrder is not originalSupplierConfirm, send cancel notice end.");
        Cat.logEvent("Ori.Modify.Order.Cancel.Delay.Check", "NoOriginalSupplierConfirm");
    }

    public void shutdownSchedule(String dspOrderId) {
        try {
            shutdownScheduleService.shutdown(dspOrderId, ScheduleEventType.DSP_ORDER_NOT_DISPATCHING);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public void calculateDriverMileageProfit(DspOrderDO dspOrderDO, DspOrderConfirmRecordVO confirmRecordDO) {
        try {
            if (dspOrderDO != null && confirmRecordDO != null && confirmRecordDO.getDriverInfo() != null && confirmRecordDO.getDriverInfo().getDriverId() != null && confirmRecordDO.getDriverInfo().getDriverId() != 0) {
                Long supplierId = confirmRecordDO.getSupplierInfo().getSupplierId() != null ? confirmRecordDO.getSupplierInfo().getSupplierId() : 0L;
                DriverVO driverVO = queryDriverService.queryDriver(confirmRecordDO.getDriverInfo().getDriverId(), CategoryUtils.selfGetParentType(dspOrderDO.getCategoryCode()), supplierId);
                DriverWorkTimeVO driverWorkTimeVO = DriverWorkTimeUtil.getDriverWorkTime(driverVO, dspOrderDO.getEstimatedUseTime());
                if (driverWorkTimeVO == null) {
                    return;
                }
                List<DriverMileageProfitTypeVO> driverMileageProfitType = Lists.newArrayList(DriverMileageProfitTypeVO.EXPECT, DriverMileageProfitTypeVO.TODAY);
                CalculateDriverMileageProfitCommand command = new CalculateDriverMileageProfitCommand(driverVO, driverWorkTimeVO, driverMileageProfitType, dspOrderDO.getDspOrderId(), CalculateDriverMileageProfitEventType.DSP_ORDER_CANCEL);
                calculateDriverMileageProfitExeCmd.execute(command);
                logger.info("calculateDriverMileageProfitExeCmd.execute");
            }
        } catch (Exception e) {
            logger.warn("calculate_driver_mileage_profit_error", e);
        }
    }

    private void cancelGrabCentre(String dspOrderId) {
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);

    }

    private void orderCancel(AbroadOrderCancelDTO cancelDTO, DspOrderDO dspOrderDO) {
        // 非包车订单，需要判断境内外
        if ( !CategoryCodeEnum.isCharterOrder(dspOrderDO.getCategoryCode()) && (dspOrderDO.getBizAreaType() == null ||
                (BizAreaTypeEnum.IGT.getCtripCode().intValue() != dspOrderDO.getBizAreaType().intValue())) ) {
            logger.info("DspOrderCancelListener_info", String.format("Domestic orders do not send information,dspOrderId=%s,drvOrderId=%s", cancelDTO.getDspOrderId(), cancelDTO.getDriverOrderId()));
            return;
        }

        // 构建邮件、发送邮件
        TransportGroupVO transportGroupDetail = queryTransportGroupService.queryTransportGroup(cancelDTO.getTransportGroupId().longValue());
        if (transportGroupDetail == null) {
            return;
        }
        // 运力组通知开关：0=关，1=开
        Integer informSwitch = transportGroupDetail.getInformSwitch();
        String email = transportGroupDetail.getInformEmail();
        if (!Objects.equals(informSwitch, YesOrNo.YES.getCode()) || StringUtils.isEmpty(email)) {
            return;
        }
        String transportGroupName = transportGroupDetail.getTransportGroupName();
        QueryGeoDetailResDTO resDTO = platformGeoServiceGateway.queryGeoDetail(new QueryGeoDetailReqDTO(3, dspOrderDO.getCityId().longValue()));
        if (resDTO == null) {
            logger.error("jntOrderCancel-sendEmail_"+ dspOrderDO.getDspOrderId(),"queryCityName is null");
            return;
//            throw ErrorCode.THIRD_CALL_EXCEPTION.getBizException();
        }
        boolean isFineCancel = false;
        DspOrderFeeDO dspOrderFeeDO = dspOrderFeeRepository.find(dspOrderDO.getDspOrderId());
        if(dspOrderFeeDO != null && BigDecimal.ZERO.compareTo(dspOrderFeeDO.getCancelFineRate()) != 0){
            isFineCancel = true;
        }

        String content = this.buildEmailContent4Cancel(dspOrderDO, transportGroupName, cancelDTO, resDTO,isFineCancel);
        sendEmailService.send(buildEmailVO(dspOrderDO, email, transportGroupName, businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ORDER_CANCEL_SUBJECT) , content));
    }

    private void charteredOrderCancel(AbroadOrderCancelDTO cancelDTO, DspOrderDO dspOrderDO) {
        // 包车订单，需要判断境内外,境外不发送短信
        if (dspOrderDO.getBizAreaType() == null || (BizAreaTypeEnum.IGT.getCtripCode().intValue() == dspOrderDO.getBizAreaType().intValue())) {
            logger.info("DspOrderCancelListener_info", String.format("foreign orders do not send information,dspOrderId=%s,drvOrderId=%s", cancelDTO.getDspOrderId(), cancelDTO.getDriverOrderId()));
            return;
        }

        // 构建邮件、发送邮件
        TransportGroupVO transportGroupDetail = queryTransportGroupService.queryTransportGroup(cancelDTO.getTransportGroupId().longValue());
        if (transportGroupDetail == null) {
            return;
        }
        // 运力组通知开关：0=关，1=开
        Integer informSwitch = transportGroupDetail.getInformSwitch();
        String phone = transportGroupDetail.getInformPhone();
        if (!Objects.equals(informSwitch, YesOrNo.YES.getCode()) || StringUtils.isEmpty(phone)) {
            return;
        }
        String transportGroupName = transportGroupDetail.getTransportGroupName();
        TripSendMessageVO message = this.buildCharteredCancelMessage(dspOrderDO, phone, transportGroupName);
        sendSmsService.send(message);
    }

    /**
     * 包车订单：构建调度确认取消短信发送DTO
     *
     * @param orderMain
     * @param phone
     * @param transportGroupName
     * @return
     */
    private TripSendMessageVO buildCharteredCancelMessage(DspOrderDO orderMain, String phone, String transportGroupName) {
        try {
            return TripSendMessageVO.builder()
                    .mobilePhone(phone)
                    .orderId(orderMain.getUserOrderId())
                    .messageCode(310578)
                    .content(this.buildMessageContent4CharteredOrderCancelMap(orderMain, transportGroupName))
                    .build();
        } catch (Exception e) {
            logger.error("build_send_message_error", String.format("charteredOrder,send sms error,userID=%s", orderMain.getUserOrderId()), e);
            return null;
        }
    }

    /**
     * 包车订单：构建取消，给调度发送短信的内容Map
     * 您好，您旗下的运力组{{TransportGroupName}}，{{FromAdress}}出发的{{Package}}{{CategoryName}}订单{{OrderID}}，用车时间：{{BookTime}}，已被取消。
     *
     * @param orderMain
     * @param transportGroupName
     * @return
     */
    private Map<String, String> buildMessageContent4CharteredOrderCancelMap(DspOrderDO orderMain, String transportGroupName) {
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put(SysConstants.Message.REAL_GROUP_NAME, transportGroupName);
        contentMap.put(SysConstants.Message.BOOK_TIME, DateUtil.formatDate(orderMain.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT));
        contentMap.put(SysConstants.Message.CATEGORY_NAME, businessTemplateInfoConfig.getServiceTypeDesMap().get(orderMain.getCategoryCode()));

        contentMap.put(SysConstants.Message.PACKAGE, orderMain.getProductName());
        contentMap.put(SysConstants.Message.FROM_ADDRESS, orderMain.getFromPoiDTO().getFromAddress());
        contentMap.put(SysConstants.Message.ORDER_ID, orderMain.getUserOrderId());

        return contentMap;
    }


    /**
     * 取消订单：构建发送邮件的内容
     *
     * @param orderMain
     * @param transportGroupName
     * @param cancelDTO
     * @param resDTO
     * @return
     */
    public String buildEmailContent4Cancel(DspOrderDO orderMain, String transportGroupName, AbroadOrderCancelDTO cancelDTO, QueryGeoDetailResDTO resDTO,boolean isFineCancel) {
        //承担方中文，英文
        String bearZh = "-";
        String bearEn = "-";
        UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(orderMain.getUserOrderId());
        if(userOrderDetail != null && userOrderDetail.getUserCancelInfo() != null && userOrderDetail.getUserCancelInfo().getNewLossType() != null){
            NewLossTypeEnum newLossTypeEnum = NewLossTypeEnum.getNewLossTypeEnum(userOrderDetail.getUserCancelInfo().getNewLossType());
            if(newLossTypeEnum != null){
                bearZh = businessTemplateInfoConfig.getValueByKey(newLossTypeEnum.descZh);
                bearEn = businessTemplateInfoConfig.getValueByKey(newLossTypeEnum.descEn);
            }
        }
        logger.info("order_cancel"+orderMain.getUserOrderId(),"bearZh="+bearZh+"bearEn="+bearEn);
        //修改buffer导致的取消
        if(CancelReasonIdEnum.BUFFER_CHANGE_CANCEL.getCode().equals(cancelDTO.getCancelReasonId())){
            String format = String.format( businessTemplateInfoConfig.getValueByKey(SysConstants.Email.BUFFER_CHANGE_CONTENT),
                    resDTO.getChineseName(),
                    businessTemplateInfoConfig.getServiceTypeDesMap().get(orderMain.getCategoryCode()),
                    orderMain.getUserOrderId(),
                    DateUtil.getStringDate(orderMain.getEstimatedUseTime()),
                    DateUtil.getStringDate(orderMain.getLastConfirmCarTime()), //最晚派遣时间
                    bearZh,
                    orderMain.getCategoryCode(),
                    orderMain.getUserOrderId(),
                    resDTO.getEnglishName(),
                    DateUtil.getStringDate(orderMain.getEstimatedUseTime()),
                    DateUtil.getStringDate(orderMain.getLastConfirmCarTime()),
                    bearEn
            );
            logger.info("order_cancel_buffer"+orderMain.getUserOrderId(),"format="+format);
            return format;
        }
        String cancelFineChinese = isFineCancel ? businessTemplateInfoConfig.getValueByKey(ORDER_FINE_CANCEL_CH) : businessTemplateInfoConfig.getValueByKey(ORDER_NO_FINE_CANCEL_CH);
        String cancelFineEnglish = isFineCancel ? businessTemplateInfoConfig.getValueByKey(ORDER_FINE_CANCEL_EN) : businessTemplateInfoConfig.getValueByKey(ORDER_NO_FINE_CANCEL_EN);
        if (StringUtils.isEmpty(cancelDTO.getDriverName()) && StringUtils.isEmpty(cancelDTO.getDriverPhone())) {
            String format = String.format(businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ORDER_CANCEL_CONTENT_NO_DRIVER) ,
                    transportGroupName,
                    orderMain.getUserOrderId(),
                    cancelFineChinese,
                    resDTO.getChineseName(),
                    businessTemplateInfoConfig.getServiceTypeDesMap().get(orderMain.getCategoryCode()),
                    DateUtil.formatDate(orderMain.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT),
                    bearZh,
                    orderMain.getUserOrderId(),
                    cancelFineEnglish,
                    resDTO.getEnglishName(),
                    orderMain.getCategoryCode(),
                    DateUtil.formatDate(orderMain.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT),
                    bearEn
            );
            logger.info("order_cancel_no_driver"+orderMain.getUserOrderId(),"format="+format);
            return format;
        } else {
            String format = String.format(businessTemplateInfoConfig.getValueByKey(SysConstants.Email.ORDER_CANCEL_CONTENT),
                    transportGroupName,
                    orderMain.getUserOrderId(),
                    cancelFineChinese,
                    resDTO.getChineseName(),
                    businessTemplateInfoConfig.getServiceTypeDesMap().get(orderMain.getCategoryCode()),
                    DateUtil.formatDate(orderMain.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT),
                    cancelDTO.getDriverName(),
                    decryptPhoneService.decrypt(cancelDTO.getDriverPhone()),
                    bearZh,
                    orderMain.getUserOrderId(),
                    cancelFineEnglish,
                    resDTO.getEnglishName(),
                    orderMain.getCategoryCode(),
                    DateUtil.formatDate(orderMain.getEstimatedUseTime(), DateUtil.DATETIME_FORMAT),
                    cancelDTO.getDriverName(),
                    decryptPhoneService.decrypt(cancelDTO.getDriverPhone()),
                    bearEn);
            logger.info("order_cancel_has_driver"+orderMain.getUserOrderId(),"format="+format);
            return format;
        }
    }

    /**
     * 构建发送邮件DTO
     *
     * @return
     */
    private TripSendEmailVO buildEmailVO(DspOrderDO orderMain, String email, String transportGroupName, String subject, String content) {
        return TripSendEmailVO.builder()
                .orderId(orderMain.getUserOrderId())
                .producer(TripSendEmailVO.Producer.builder()
                        .email(SysConstants.Email.COMMON_EMAIL_SENDER)
                        .name(businessTemplateInfoConfig.getValueByKey(SysConstants.Email.DISPATCHER_CONFIRM_SENDER_NAME))
                        .build())
                .receiver(Lists.newArrayList(TripSendEmailVO.Receiver.builder().email(email).name(transportGroupName).build()))
                .subject(subject)
                .content(content)
                .channel(SysConstants.Email.channel)
                .contentType(SysConstants.Email.contentType)
                .hasAttach(false)
                .build();
    }


    /**
     * 构建取消订单的DTO
     *
     * @param confirmRecordDO
     * @return
     */
    private AbroadOrderCancelDTO buildCancelDTO(DspOrderConfirmRecordVO confirmRecordDO) {
        AbroadOrderCancelDTO target = new AbroadOrderCancelDTO();
        target.setDspOrderId(confirmRecordDO.getDspOrderId());
        // 子单号
        target.setDriverOrderId(confirmRecordDO.getDriverOrderId());
        // 司机相关
        if (confirmRecordDO.getDriverInfo() != null) {
            target.setDriverId(confirmRecordDO.getDriverInfo().getDriverId().intValue());
            target.setDriverName(confirmRecordDO.getDriverInfo().getDriverName());
            target.setDriverPhone(confirmRecordDO.getDriverInfo().getDriverPhone());
            // 运力组ID
            target.setTransportGroupId(confirmRecordDO.getDriverInfo().getTransportGroupId().intValue());
        }

        return target;
    }

    private void cancelDispatcherGrabOrder(DspOrderDO dspOrderDO) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(dspOrderDO.getUserOrderId(), Lists.newArrayList(dspOrderDO.getDspOrderId()));
            cancelDispatcherGrabOrderExeCmd.execute(command);
        } catch (Exception e) {
            logger.error("cancel_dispatcher_grab_order_error", String.format("cancel_dispatcher_grab_order_error,userOrderId=%s", dspOrderDO.getUserOrderId()), e);
        }
    }
}
