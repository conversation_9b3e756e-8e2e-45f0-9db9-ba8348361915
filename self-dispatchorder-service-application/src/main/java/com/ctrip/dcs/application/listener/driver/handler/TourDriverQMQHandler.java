package com.ctrip.dcs.application.listener.driver.handler;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 司导平台供应链消息预处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1 16:25:15
 */
public class TourDriverQMQHandler {
    private static final Logger logger = LoggerFactory.getLogger(TourDriverQMQHandler.class);

    /**
     * 司导供应链定义的司机所属产线 - 包车
     */
    private static final String DRIVER_PRODUCTLINE_CHARTERED = "V";

    /**
     * 判断司导供应链消息中的产线列表中是否还有包车
     *
     * @param strDriverProductLineList 司机所属产线列表，多个产线以逗号分割
     * @return true-含包车，false-无包车
     */
    public static boolean isCharteredDriverCarChanged(String strDriverProductLineList) {
        if (StringUtils.isBlank(strDriverProductLineList)) {
            logger.info("TourDriverQMQHandler.isCharteredDriverChanged", "The input parameter is empty. jsonDriverProductLineList:" + strDriverProductLineList);
            return false;
        }

        // 解析为list
        List<String> driverProductLineList = Splitter.on(",").splitToList(strDriverProductLineList);

        // 检查司机产线是否含有包车
        if (CollectionUtils.isNotEmpty(driverProductLineList) && driverProductLineList.contains(DRIVER_PRODUCTLINE_CHARTERED)) {
            logger.info("TourDriverQMQHandler.isCharteredDriverChanged", "DriverProductLineList contain chartered. DriverProductLineList:" + strDriverProductLineList);
            return true;
        }

        logger.info("TourDriverQMQHandler.isCharteredDriverChanged", "DriverProductLineList don't contain chartered. DriverProductLineList:" + strDriverProductLineList);
        return false;
    }


}
