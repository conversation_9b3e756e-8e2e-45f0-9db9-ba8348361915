package com.ctrip.dcs.application.listener;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd;
import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 司机提交抢单消息
 * <AUTHOR>
 */
@Component
public class SubmitGrabOrderListener {

    @Autowired
    private SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd;

    @QmqLogTag(tagKeys = {"taskId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.SUBMIT_GRAB_ORDER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessageJNT(Message message) {
        SubmitGrabOrderCommand command = MessageConverter.INSTANCE.toJntSubmitGrabOrderCommand(message);
        Assert.notNull(command);
        submitBroadcastGrabExeCmd.execute(command);
    }

    @QmqLogTag(tagKeys = {"taskId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.SUBMIT_GUIDE_GRAB_ORDER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessageDAY(Message message) {
        SubmitGrabOrderCommand command = MessageConverter.INSTANCE.toDaySubmitGrabOrderCommand(message);
        Assert.notNull(command);
        submitBroadcastGrabExeCmd.execute(command);
    }
}
