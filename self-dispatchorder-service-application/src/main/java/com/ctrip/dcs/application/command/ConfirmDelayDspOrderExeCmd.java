package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ConfirmDelayDspOrderCommand;
import com.ctrip.dcs.application.command.validator.ConfirmDelayDspOrderValidator;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.application.provider.executor.ConfirmDelayDspOrderExecutor;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.process.impl.SystemAssignProcess;
import com.ctrip.dcs.domain.schedule.process.impl.SystemDelayAssignProcess;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class ConfirmDelayDspOrderExeCmd {

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private ConfirmDelayDspOrderValidator validator;

    @Autowired
    private SystemDelayAssignProcess systemDelayAssignProcess;

    public void execute(ConfirmDelayDspOrderCommand command) throws ValidateException, OrderStatusException {
        DspOrderVO dspOrder = queryDspOrderService.query(command.getDspOrderId());

        Long supplierId = (dspOrder!= null && dspOrder.getSupplierId() != null) ? dspOrder.getSupplierId().longValue() : 0L;
        DriverVO driver = queryDriverService.queryDriver(command.getDriverId(), CategoryUtils.selfGetParentType(dspOrder), supplierId);
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(command.getTransportGroupId());
        ScheduleTaskDO scheduleTask = scheduleTaskRepository.query(command.getDuid());
        validator.validate(dspOrder, driver, transportGroup, scheduleTask);
        DspModelVO dspModel = new DspModelVO(dspOrder, driver, transportGroup);
        systemDelayAssignProcess.dspTakenExecute(scheduleTask, dspModel);
    }
}
