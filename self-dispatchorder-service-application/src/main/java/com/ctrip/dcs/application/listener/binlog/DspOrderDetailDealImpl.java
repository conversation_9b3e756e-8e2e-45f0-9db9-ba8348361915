package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dsp_order_detail")
public class DspOrderDetailDealImpl implements BinlogDeal{

    @Autowired
    private DspOrderDetailRepository dspOrderDetailRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        dspOrderDetailRepository.clearCache(dspOrderId);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> dspOrderDetailRepository.clearCache(dspOrderId));
    }
}
