package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.SelectGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.process.impl.SelectGrabBroadcastGrabProcess;
import com.ctrip.dcs.domain.schedule.process.impl.SelectGrabOrderProcess;
import com.ctrip.dcs.domain.schedule.process.impl.SelectVBKGrabOrderProcess;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class SelectGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SelectGrabOrderExeCmd.class);

    @Autowired
    private QueryDspOrderService dspOrderService;

    @Autowired
    private SubSkuRepository subSkuRepository;

    @Autowired
    private SelectGrabOrderProcess selectGrabOrderProcess;

    @Autowired
    private SelectVBKGrabOrderProcess selectVBKGrabOrderProcess;

    @Autowired
    private SelectGrabBroadcastGrabProcess selectBroadcastGrabProcess;

    private static final Set<DspType> VBK_DSP_TYPE = Sets.newHashSet(DspType.VBK_BROADCAST, DspType.VBK_GRAB_ORDER);

    public void execute(SelectGrabOrderCommand command) {
        SubSkuVO subSku = subSkuRepository.find(command.getSubSkuId());
        DspOrderVO dspOrder = dspOrderService.queryOrderDetail(command.getDspOrderId());
        Assert.notNull(subSku);
        Assert.notNull(dspOrder);
        logger.info("SelectGrabOrderInfo", "dspType:{}", subSku.getDspType().name());
        if (Objects.equals(subSku.getDspType(), DspType.GRAB_BROADCAST)) {
            selectBroadcastGrabProcess.execute(dspOrder, subSku);
            return;
        }
        if (VBK_DSP_TYPE.contains(subSku.getDspType())) {
            // VBK抢单轮选请求
            selectVBKGrabOrderProcess.execute(dspOrder, subSku);
            return;
        }
        selectGrabOrderProcess.execute(dspOrder, subSku);
    }
}
