package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CustomerAssignDriverExeCmd;
import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 客服指派
 */
@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
public class CustomerAssignDriverExecutor extends AbstractRpcExecutor<CustomerAssignDriverRequestType, CustomerAssignDriverResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CustomerAssignDriverExecutor.class);

    @Autowired
    private CustomerAssignDriverExeCmd customerAssignDriverExeCmd;


    @Override
    public CustomerAssignDriverResponseType execute(CustomerAssignDriverRequestType requestType) {
        CustomerAssignDriverResponseType responseType = new CustomerAssignDriverResponseType();
        if (!validatePrm(requestType)) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

        }
        try {
            customerAssignDriverExeCmd.execute(CustomerAssignDriverConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            logger.error("customerAssignDriverException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }

    private boolean validatePrm(CustomerAssignDriverRequestType requestType) {
        return org.apache.commons.lang.StringUtils.isNotBlank(requestType.getDspOrderId())
                && Objects.nonNull(requestType.getDriverId())
                && requestType.getDriverId() > 0L
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserName())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getSysUserAccount())
                && org.apache.commons.lang.StringUtils.isNotBlank(requestType.getOperUserType());
    }
}
