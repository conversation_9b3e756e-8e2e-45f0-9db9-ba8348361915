package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CreateGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.GrabOrderType;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.OldDuidVO;
import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.event.ExpireGrabOrderEvent;
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class CreateGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CreateGrabOrderExeCmd.class);


    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private GrabOrderFactory grabOrderFactory;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private BroadcastRepository broadcastRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    @Autowired
    private SubSkuRepository subSkuRepository;

    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    @Qualifier("grabOrderDealCacheThreadPool")
    private ExecutorService grabOrderDealCacheThreadPool;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConf;


    public void execute(CreateGrabOrderCommand command) {
        BaseDetailVO baseDetail = selfOrderQueryGateway.queryOrderBaseDetail(command.getOrderId());
        Assert.notNull(baseDetail);
        Integer orderStatus = baseDetail.getOrderStatus();
        //如果订单状态不为200 删除映射
        if(Objects.nonNull(orderStatus) && !OrderStatusEnum.TO_BE_CONFIRMED.getCode().equals(orderStatus)){
            grabCentreRepository.deleteAll(baseDetail.getDspOrderId());
            return;
        }
        OldDuidVO duid = OldDuidVO.of(command.getDuid());
        Assert.notNull(duid);
        SubSkuVO subSku = subSkuRepository.find(duid.getSubSku());
        Assert.notNull(subSku);

        //抢单大厅缓存相关的指定司机的可抢订单列表
        List<GrabOrderDO> grabOrders = queryDriverAndBuildGrabOrderDO(command, subSku, baseDetail);
        logger.info("CreateGrabOrderExeCmd_queryDriverAndBuildGrabOrderDO", LocalJsonUtils.toJson(grabOrders));
        if(CollectionUtils.isEmpty(grabOrders)){
            return;
        }
        if(command.getType().equals(GrabOrderType.GRAB_CENTRE)){
            //清空之前的抢单信息
            logger.info("CreateGrabOrderExeCmd_grabCentreRepository_execute", LocalJsonUtils.toJson(command));

            String orDefault = commonConf.getOrDefault(CommonConstants.CREATE_GRAB_ORDER_FILTER_DRIVER, "1");
            logger.info("CreateGrabOrderExeCmd_grabCentreRepository_orDefault", orDefault);
            if("1".equals(orDefault)){
                Set<Long> driverIds = grabCentreRepository.getOrderDriverMapping(command.getOrderId());
                if(CollectionUtils.isNotEmpty(driverIds)){
                    grabOrders = grabOrders.stream().filter(order -> !driverIds.contains(order.getDriverId())).collect(Collectors.toList());
                }
            }
            if(CollectionUtils.isEmpty(grabOrders)){
                return;
            }
            dealGrabCache(command, baseDetail, subSku, grabOrders);
        }else {
            dealBroadCastCache(command, baseDetail, subSku, grabOrders);
        }
    }

    public void dealBroadCastCache(CreateGrabOrderCommand command, BaseDetailVO baseDetail, SubSkuVO subSku, List<GrabOrderDO> grabOrders) {
        try{
            logger.info("CreateGrabOrderExeCmd_dealBroadCastCache_grabOrders", LocalJsonUtils.toJson(grabOrders));
            final CountDownLatch countDownLatch = new CountDownLatch(4);
            LoggerContext current = LoggerContext.getCurrent();
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                broadcastRepository.save(grabOrders);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                List<GrabOrderDTO> grabOrderDTOS = queryGrabDriverList(command, subSku, baseDetail);
                logger.info("CreateGrabOrderExeCmd_dealBroadCastCache_grabOrderDTOS", LocalJsonUtils.toJson(grabOrderDTOS));
                grabOrderDetailRepository.saveFullData(grabOrderDTOS);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabOrderDetailRepository.saveDuid_DriverIdSetMapping(grabOrders);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabOrderDetailRepository.saveOrderId_DuidSetMapping(grabOrders);
                countDownLatch.countDown();
            });
            if (countDownLatch.await(getCreateGrabOrderTimeOut(), TimeUnit.SECONDS)) {
                // 发送失效消息
                sendGrabOrderExpireMessage(grabOrders);
            }
        }catch (Exception ex){
            logger.error("CreateGrabOrderExeCmd_dealBroadCastCache_error", ex);
        }
    }


    private int getCreateGrabOrderTimeOut(){
        try{
            String orDefault = commonConf.getOrDefault(CommonConstants.CREATE_GRAB_ORDER_TIME_OUT, "10");
            return Integer.parseInt(orDefault);
        }catch (Exception ex){
            logger.error("CreateGrabOrderExeCmd_getCreateGrabOrderTimeOut", ex);
            return 10;
        }
    }


    public void dealGrabCache(CreateGrabOrderCommand command, BaseDetailVO baseDetail, SubSkuVO subSku, List<GrabOrderDO> grabOrders) {
        try{
            logger.info("CreateGrabOrderExeCmd_dealGrabCache_grabOrders", LocalJsonUtils.toJson(grabOrders));
            final CountDownLatch countDownLatch = new CountDownLatch(5);
            LoggerContext current = LoggerContext.getCurrent();
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabCentreRepository.saveOrderDriverMapping(grabOrders);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabCentreRepository.saveDriverOrderList(grabOrders);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                List<GrabOrderDTO> grabOrderDTOS = queryGrabDriverList(command, subSku, baseDetail);
                logger.info("CreateGrabOrderExeCmd_dealGrabCache_grabOrderDTOS", LocalJsonUtils.toJson(grabOrderDTOS));
                grabOrderDetailRepository.saveFullData(grabOrderDTOS);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabOrderDetailRepository.saveDuid_DriverIdSetMapping(grabOrders);
                countDownLatch.countDown();
            });
            grabOrderDealCacheThreadPool.execute(() -> {
                LoggerContext.newContext().withGlobalTraceId(current.getGlobalTraceId()).init();
                grabOrderDetailRepository.saveOrderId_DuidSetMapping(grabOrders);
                countDownLatch.countDown();
            });
            if (countDownLatch.await(getCreateGrabOrderTimeOut(), TimeUnit.SECONDS)) {
                // 发送失效消息
                sendGrabOrderExpireMessage(grabOrders);
            }
        }catch (Exception ex){
            logger.error("CreateGrabOrderExeCmd_dealGrabCache_error", ex);
        }

    }


    private List<GrabOrderDTO> queryGrabDriverList(CreateGrabOrderCommand cmd, SubSkuVO subSku, BaseDetailVO baseDetail) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("baseDetail", JsonUtil.toJson(baseDetail));
            map.put("subSku", JsonUtil.toJson(subSku));
            map.put("cmd", JsonUtil.toJson(cmd));
            logger.info("queryGrabDriverList_enter", LocalJsonUtils.toJson(map));
            Set<Long> driverIds = cmd.getDriverIds();
            if (CollectionUtils.isEmpty(driverIds)) {
                return Collections.emptyList();
            }
            List<GrabOrderDTO> grabOrderDTOS = selfOrderQueryGateway.queryGrabDriverList(cmd.getOrderId(), cmd.getDuid(), new HashSet<>(driverIds));
            logger.info("queryGrabDriverList_exit", LocalJsonUtils.toJson(grabOrderDTOS));
            return grabOrderDTOS;
        } catch (Exception e) {
            logger.error("CreateGrabOrderExeCmd_queryGrabDriverList", e);
        }
        return Lists.newArrayList();
    }


    private List<GrabOrderDO> queryDriverAndBuildGrabOrderDO(CreateGrabOrderCommand cmd, SubSkuVO subSku, BaseDetailVO baseDetail) {
        Map<String, String> map = new HashMap<>();
        map.put("baseDetail", JsonUtil.toJson(baseDetail));
        map.put("subSku", JsonUtil.toJson(subSku));
        map.put("cmd", JsonUtil.toJson(cmd));
        logger.info("CreateGrabOrderExeCmd_enter", LocalJsonUtils.toJson(map));
        Set<Long> driverIds = cmd.getDriverIds();
        if (CollectionUtils.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        try {
            List<GrabOrderDO> grabOrders = grabOrderFactory.create(cmd.getOrderId(), baseDetail.getUserOrderId(),
                    DateUtil.parseDateStr2Date(baseDetail.getEstimatedUseTime()), baseDetail.getPremiumOrderFlag(), cmd.getDuid(), subSku, cmd.getDriverIds());
            logger.info("CreateGrabOrderExeCmd_exit", LocalJsonUtils.toJson(grabOrders));
            return grabOrders;
        } catch (Exception e) {
            logger.error("CreateGrabOrderExeCmd_queryDriver",e);
        }
        return Collections.emptyList();
    }


    public void sendGrabOrderExpireMessage(List<GrabOrderDO> orders) {
        try{
            logger.info("CreateGrabOrderExeCmd_sendGrabOrderExpireMessage_enter", LocalJsonUtils.toJson(orders));
            if(CollectionUtils.isEmpty(orders)){
                return;
            }
            List<Long> driverIds = orders.stream().map(GrabOrderDO::getDriverId).collect(Collectors.toList());
            GrabOrderDO grabOrderDO = orders.get(0);
            logger.info("CreateGrabOrderExeCmd_sendGrabOrderExpireMessage_driverIds", LocalJsonUtils.toJson(driverIds));
            String driverIdsStr = StringUtils.join(driverIds, ",");
            long delay = grabOrderDO.getExpire() - System.currentTimeMillis();
            logger.info("CreateGrabOrderExeCmd_sendGrabOrderExpireMessage_delay", LocalJsonUtils.toJson(delay));
            messageProducer.send(new ExpireGrabOrderEvent(grabOrderDO.getDuid(), grabOrderDO.getDspOrderId(), grabOrderDO.getDriverId(), grabOrderDO.getExpire(), delay, driverIdsStr));
        }catch (Exception ex){
            logger.error("CreateGrabOrderExeCmd_sendGrabOrderExpireMessage", ex);
        }
    }


}
