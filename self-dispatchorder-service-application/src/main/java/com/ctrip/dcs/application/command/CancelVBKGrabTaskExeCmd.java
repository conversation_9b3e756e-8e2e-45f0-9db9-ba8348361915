package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.ShutdownScheduleEvent;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskOperateEvent;
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class CancelVBKGrabTaskExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CancelVBKGrabTaskExeCmd.class);

    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    protected DateZoneConvertUtil dateZoneConvertUtil;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    protected MessageProviderService messageProducer;


    public Pair<Integer, Integer> execute(CancelVBKGrabTaskRequestType requestType) throws Exception{
        logger.info("CancelVBKGrabTaskExeCmd_enter", LocalJsonUtils.toJson(requestType));
        VBKDriverGrabTaskDO vbkDriverGrabTaskDO = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(requestType.getTaskId());
        logger.info("CancelVBKGrabTaskExeCmd_vbkDriverGrabTaskDO", LocalJsonUtils.toJson(vbkDriverGrabTaskDO));
        if(Objects.isNull(vbkDriverGrabTaskDO) || GrabTaskStatus.canNotCancel(vbkDriverGrabTaskDO.getTaskStatus())){
            throw ErrorCode.GRAB_TASK_STATUS.getBizException();
        }
        if (!Objects.equals(vbkDriverGrabTaskDO.getSupplierId(), requestType.getSupplierId())) {
            throw ErrorCode.CANCEL_GRAB_TASK_ERROR.getBizException();
        }
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_GRAB_TASK_WITH_TASK_ID, requestType.getSupplierId(), requestType.getTaskId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR.getBizException();
            }
            List<VBKDriverGrabOrderDO> grabOrderList = vbkDriverGrabOrderRepository.queryByTaskIdAndStatus(requestType.getTaskId(), GrabTaskStatus.IN_PROGRESS.getCode());
            List<String> dspOrderIdList = grabOrderList.stream().map(VBKDriverGrabOrderDO::getDspOrderId).collect(Collectors.toList());
            //任务取消
            int successNum = vbkDriverGrabTaskRepository.cancelVBKGrabTask(requestType.getTaskId(), requestType.getOperatorName(), requestType.getSupplierId());
            //操作记录
            sendVBKGrabTaskOperateEvent(requestType, successNum);
            //取消派发任务
            sendShutdownScheduleEvent(dspOrderIdList);
            return new Pair<>(successNum, dspOrderIdList.size() - successNum);
        } catch (DistributedLockRejectedException e) {
            logger.error("CancelVBKGrabTaskExeCmd_error", e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        }  finally {
            lock.unlock();
        }
    }

    public void sendVBKGrabTaskOperateEvent(CancelVBKGrabTaskRequestType requestType, int successNum) {
        try{
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = new VBKGrabTaskOperateDTO();
            vbkGrabTaskOperateDTO.setSuccessNum(successNum);
            vbkGrabTaskOperateDTO.setTaskId(requestType.getTaskId());
            vbkGrabTaskOperateDTO.setSupplierId(requestType.getSupplierId());
            vbkGrabTaskOperateDTO.setOperatorName(requestType.getOperatorName());
            vbkGrabTaskOperateDTO.setOperatorType(VBKGrabOperationTypeEnum.OperationTypeEnum.CANCEL.getCode());
            vbkGrabTaskOperateDTO.setOperatorDesc(VBKGrabOperationTypeEnum.OperationTypeEnum.CANCEL.getDes());
            vbkGrabTaskOperateDTO.setCurrentDateStr(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));
            String jsonStr = JsonUtils.toJson(vbkGrabTaskOperateDTO);
            logger.info("CancelVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", jsonStr);
            VBKGrabTaskOperateEvent shutdownScheduleEvent = new VBKGrabTaskOperateEvent(jsonStr);
            messageProducer.send(shutdownScheduleEvent);
        }catch (Exception ex){
            logger.error("CancelVBKGrabTaskExeCmd_sendVBKGrabTaskOperateEvent", ex);
        }
    }

    public void sendShutdownScheduleEvent(List<String> orderIdList) {
        try{
            logger.info("CancelVBKGrabTaskExeCmd_sendShutdownScheduleEvent", JsonUtils.toJson(orderIdList));
            if(CollectionUtils.isEmpty(orderIdList)){
                return;
            }
            String orderIdStr = org.apache.commons.lang.StringUtils.join(orderIdList, ",");
            ShutdownScheduleEvent shutdownScheduleEvent = new ShutdownScheduleEvent(orderIdStr);
            logger.info("CancelVBKGrabTaskExeCmd_sendShutdownScheduleEvent_event", LocalJsonUtils.toJson(shutdownScheduleEvent));
            messageProducer.send(shutdownScheduleEvent);
        }catch (Exception ex){
            logger.error("CancelVBKGrabTaskExeCmd_sendShutdownScheduleEvent", ex);
        }
    }
}
