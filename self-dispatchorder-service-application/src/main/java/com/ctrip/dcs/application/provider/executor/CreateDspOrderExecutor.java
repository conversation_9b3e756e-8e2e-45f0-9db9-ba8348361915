package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CreateDspOrderExeCmd;
import com.ctrip.dcs.application.provider.converter.CreateDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 创建派发单
 */
@Component
@ServiceLogTagPair(key = "createDspOrderInfo.baseInfo.userOrderId", alias = "userOrderId")
public class CreateDspOrderExecutor extends AbstractRpcExecutor<CreateDspOrderRequestType, CreateDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CreateDspOrderExecutor.class);

    @Autowired
    private CreateDspOrderExeCmd createDspOrderExeCmd;

    @Override
    public CreateDspOrderResponseType execute(CreateDspOrderRequestType requestType) {
        CreateDspOrderResponseType responseType = new CreateDspOrderResponseType();
        if (requestType.getCreateDspOrderInfo() == null
                || requestType.getCreateDspOrderInfo().getBaseInfo() == null
                || requestType.getCreateDspOrderInfo().getFeeInfo() == null
                || requestType.getCreateDspOrderInfo().getProductInfo() == null) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }

        try {
            String dspOrderId = createDspOrderExeCmd.execute(CreateDspOrderConverter.converter(requestType));
            responseType.setDspOrderId(dspOrderId);
        } catch (BizException e) {
            logger.warn("createDspOrderException", e);
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("createDspOrderException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }
}
