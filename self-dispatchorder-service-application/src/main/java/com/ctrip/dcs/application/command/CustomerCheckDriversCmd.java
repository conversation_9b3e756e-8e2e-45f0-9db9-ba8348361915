package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CustomerCheckDriversCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.TransportGroupMode;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.PageUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.entity.CustomerCheckDriversResDTO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.gateway.QueryOrderTakenCostGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.domain.schedule.value.TakenCostVO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig;
import com.ctrip.dcs.infrastructure.common.util.StreamUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CheckDriverItem;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CustomerCheckDriversCmd {

    private static final Logger logger = LoggerFactory.getLogger(VbkDriverCheckListExeCmd.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    private CheckService checkService;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd;

    @Autowired
    private QueryOrderTakenCostGateway queryOrderTakenCostGateway;

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @Resource
    private SysSwitchConfig sysSwitchConfig;

    public CustomerCheckDriversResDTO query(CustomerCheckDriversCommand command) {
        logger.info("checkDrivers_info_" + command.getUserOrderId(), "Start new process,userOrderId=" + command.getUserOrderId());

        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(command.getUserOrderId());
        if (CollectionUtils.isEmpty(dspOrderDOS)) {
            logger.info("VbkDriverCheckListExeCmd", "queryValidDspOrders is null");
            return new CustomerCheckDriversResDTO(null, null, null);
        }
        command.setDspOrderId(dspOrderDOS.get(0).getDspOrderId());
        // 查询订单信息
        DspOrderVO dspOrder = queryDspOrderService.query(command.getDspOrderId());
        if (null == dspOrder) {
            logger.info("VbkDriverCheckListExeCmd", "dspOrder is null");
            return new CustomerCheckDriversResDTO(null, null, null);
        }
        // 是否是特惠订单
        if (dspOrder.isSpecialSaleOrder()) {
            logger.info("VbkDriverCheckListExeCmd", "this is specialSaleOrder");
            return new CustomerCheckDriversResDTO(null, null, null);
        }
        // 司机检查
        List<CheckModel> checkModels = asyncCheckDrivers(dspOrder);
        if (CollectionUtils.isEmpty(checkModels)) {
            logger.info("VbkDriverCheckListExeCmd", "check result is null");
            return new CustomerCheckDriversResDTO(null, null, null);
        }
        // 按司机id去重
        checkModels = checkModels.stream()
                .filter(StreamUtil.distinctByKey(model -> model.getModel().getDriver().getDriverId()))
                .limit(businessTemplateInfoConfig.getCustomerCheckDriverMaxSize())
                .collect(Collectors.toList());
        Set<Long> driverIds = checkModels.stream().map(checkModel -> checkModel.getModel().getDriver().getDriverId()).collect(Collectors.toSet());
        Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : 0L;
        List<DriverVO> driverInfos = queryDriverService.queryDriver(driverIds, CategoryUtils.selfGetParentType(dspOrder), supplierId);
        Map<Long, DriverVO> driverMap = driverInfos.stream().collect(Collectors.toMap(DriverVO::getDriverId, driver -> driver));
        // 过滤排序
        checkModels = filterAndSortCheckResult(dspOrder, checkModels, driverMap);
        List<CheckDriverItem> items = Lists.newArrayList();
        for (CheckModel checkResult : checkModels) {
            DriverVO driverVO = driverMap.get(checkResult.getModel().getDriver().getDriverId());
            if (driverVO != null) {
                items.add(buildAssignDriverItem(checkResult, driverVO));
            }
        }
        // 分页
        PaginationDTO paginationDTO = convertPageInfo(command.getPageNo(), items.size(), command.getPageSize());
        items = PageUtil.page(items, paginationDTO);
        return buildRes(items, paginationDTO);
    }

    public CustomerCheckDriversResDTO buildRes(List<CheckDriverItem> items, PaginationDTO paginationDTO) {
        CustomerCheckDriversResDTO resDTO = new CustomerCheckDriversResDTO();
        resDTO.setDrvs(items);
        resDTO.setDriverFee(0.0D);
        resDTO.setPaginationDTO(paginationDTO);
        return resDTO;
    }

    /**
     * @param page
     * @param count
     * @param pageSize
     * @return
     */
    public static PaginationDTO convertPageInfo(Integer page, int count, Integer pageSize) {
        PaginationDTO pageInfo = new PaginationDTO();
        pageInfo.setPageNo(page);
        pageInfo.setTotalPages((int) Math.ceil(count * 1.0 / pageSize));
        pageInfo.setTotalSize(count);
        pageInfo.setPageSize(pageSize);
        return pageInfo;
    }

    public CheckDriverItem buildAssignDriverItem(CheckModel checkResult, DriverVO driverVO) {
        TransportGroupVO transportGroupInfo = null;
        Map<Integer, String> transportGroupModeMap = businessTemplateInfoConfig.getTransportGroupModeMap();
        for (TransportGroupVO tg : driverVO.getTransportGroups()) {
            if (checkResult.getModel().getTransportGroup().getTransportGroupId().equals(tg.getTransportGroupId())) {
                transportGroupInfo = tg;
                break;
            }
        }
        if (transportGroupInfo == null || transportGroupInfo.getTransportGroupId() == null) {
            return null;
        }
        CheckDriverItem item = new CheckDriverItem();
        BeanUtils.copyProperties(driverVO, item);
        item.setDrvId(driverVO.getDriverId().intValue());
        String udl = Optional.ofNullable(checkResult.getModel().getDriver()).map(DriverVO::getUdl).orElse(null);
        item.setDrvUdl(udl);
        item.setDrvName(driverVO.getDriverName());
        CarVO car = driverVO.getCar();
        if (car != null) {
            item.setCarTypeId(car.getCarTypeId() == null ? null : car.getCarTypeId().intValue());
            item.setCarTypeName(car.getCarTypeName());
        }
        item.setDrvPhone(driverVO.getDriverPhone());
        item.setCityId(driverVO.getCityId().intValue());
        item.setCoopMode(transportGroupInfo.getTransportGroupMode().getCode());
        TransportGroupMode transportGroupMode = transportGroupInfo.getTransportGroupMode();
        if (transportGroupMode != null) {
            item.setCoopModeName(transportGroupModeMap.get(transportGroupMode.getCode()));
        }
        item.setTransportGroupId(transportGroupInfo.getTransportGroupId().intValue());
        if (sysSwitchConfig.getNotReturnDrvSensitiveSwitch()) {
            item.setDrvName(null);
            item.setDrvPhone(null);
        }
        return item;
    }

    public List<CheckModel> filterAndSortCheckResult(DspOrderVO dspOrder, List<CheckModel> checkModels, Map<Long, DriverVO> driverMap) {
        Map<Integer /*driverId*/, Set<Integer> /*transportIdList*/> driverTransportMap = Maps.newHashMap();
        for (CheckModel checkResult : checkModels) {
            Set<Integer> set = driverTransportMap.computeIfAbsent(checkResult.getModel().getDriver().getDriverId().intValue(), k -> Sets.newHashSet());
            set.add(checkResult.getModel().getTransportGroup().getTransportGroupId().intValue());
        }

        Set<TransportGroupVO> transportGroupInfos = Sets.newHashSet();
        for (DriverVO drvInfo : driverMap.values()) {
            transportGroupInfos.addAll(drvInfo.getTransportGroups());
        }
        Map<Long /*transportId*/, TransportGroupMode /*transportMode*/> transportModeMap = transportGroupInfos.stream()
                .collect(Collectors.toMap(TransportGroupVO::getTransportGroupId, TransportGroupVO::getTransportGroupMode, (o1, o2) -> o2));
        // 过滤
        checkModels = filter(dspOrder, checkModels, driverTransportMap, transportModeMap);
        logger.info("filterAndSortCheckResult", "Drivers who passed the inspection：" + JsonUtil.toJson(checkModels));
        // 排序
        return sort(dspOrder, checkModels, driverMap, transportModeMap);
    }

    public List<CheckModel> sort(DspOrderVO dspOrder, List<CheckModel> checkModels, Map<Long, DriverVO> driverMap, Map<Long, TransportGroupMode> transportModeMap) {
        if (CollectionUtils.isEmpty(checkModels)) {
            return checkModels;
        }
        Map<Integer /*carTypeId*/, Integer /*level*/> carRelationMap = vbkDriverCheckListExeCmd.queryCarLevelRelation(dspOrder.getCarTypeId());
        Map<Integer /*driverId*/, Double /*cost*/> driverCost = queryDriverCost(dspOrder.getDspOrderId(), driverMap.keySet());
        checkModels.sort((r1, r2) -> {
            int mode1 = transportModeMap.get(r1.getModel().getTransportGroup().getTransportGroupId()).getCode();
            int mode2 = transportModeMap.get(r2.getModel().getTransportGroup().getTransportGroupId()).getCode();
            if (mode1 != mode2) {
                return Integer.compare(mode1, mode2);   // 全职>兼职
            }
            // 平派>上派
            DriverVO d1 = driverMap.get(r1.getModel().getDriver().getDriverId());
            DriverVO d2 = driverMap.get(r2.getModel().getDriver().getDriverId());
            int c1 = carRelationMap.computeIfAbsent(d1.getCar().getCarTypeId().intValue(), v -> 0);
            int c2 = carRelationMap.computeIfAbsent(d2.getCar().getCarTypeId().intValue(), v -> 0);
            if (!Objects.equals(c1, c2)) {
                return Integer.compare(c2, c1);   // 车型平派>车型上派
            }
            // 司机分成本排序
            Double cost1 = driverCost.computeIfAbsent(r1.getModel().getDriver().getDriverId().intValue(), v -> 0D);
            Double cost2 = driverCost.computeIfAbsent(r2.getModel().getDriver().getDriverId().intValue(), v -> 0D);
            return Double.compare(cost1, cost2);
        });
        return checkModels;
    }

    public Map<Integer, Double> queryDriverCost(String dspOrderId, Set<Long> keySet) {
        List<Integer> driverIds = new ArrayList<>(keySet).stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
        return queryOrderTakenCostGateway.queryOrderTakenCost(dspOrderId, driverIds).stream()
                .collect(Collectors.toMap(TakenCostVO::getDrvId, cost -> cost.getkCost().add(cost.gettCost()).doubleValue()));
    }

    public List<CheckModel> filter(DspOrderVO dspOrder, List<CheckModel> checkModels, Map<Integer, Set<Integer>> driverTransportMap, Map<Long, TransportGroupMode> transportModeMap) {
        Long driverId = vbkDriverCheckListExeCmd.getDriverId(dspOrder);
        return checkModels.stream()
                .filter(r -> {
                    DriverVO driver = r.getModel().getDriver();
                    if (driver.getDriverId().equals(driverId)) {
                        return false;
                    }
                    Set<Integer> set = driverTransportMap.get(driver.getDriverId().intValue());
                    if (set.size() == 1) { // 司机只有一个运力组结果，通过过滤
                        return true;
                    }
                    List<Integer> fullTimeId = Lists.newArrayList();
                    List<Integer> partTimeId = Lists.newArrayList();
                    Set<Integer> fullTimeGroupModeSet = Sets.newHashSet(TransportGroupMode.FULL_TIME_ASSIGN.getCode(), TransportGroupMode.REGISTER_DISPATCH.getCode());
                    for (Integer id : set) {
                        TransportGroupMode transportGroupMode = transportModeMap.get(id.longValue());
                        if (transportGroupMode == null) {
                            continue;
                        }
                        Integer mode = transportGroupMode.getCode();
                        if (fullTimeGroupModeSet.contains(mode)) {
                            fullTimeId.add(id);
                        } else {
                            partTimeId.add(id);
                        }
                    }
                    if (fullTimeId.isEmpty() && partTimeId.isEmpty()) {
                        return false;
                    }
                    TransportGroupVO transportGroup = r.getModel().getTransportGroup();
                    if (!fullTimeId.isEmpty() && !partTimeId.isEmpty()) {   // 司机检查结果既有全职又有兼职，取全职的第一个，其余的过滤
                        return transportGroup.getTransportGroupId().equals(fullTimeId.get(0).longValue());
                    }
                    if (!fullTimeId.isEmpty()) {    // 只有全职
                        return transportGroup.getTransportGroupId().equals(fullTimeId.get(0).longValue());
                    }
                    return transportGroup.getTransportGroupId().equals(partTimeId.get(0).longValue());   // 只有兼职
                })
                .collect(Collectors.toList());
    }

    public List<CheckModel> asyncCheckDrivers(DspOrderVO dspOrderVO) {
        List<CheckModel> res = Lists.newArrayList();
        try {
            DuidVO duid = initDuid(dspOrderVO);
            List<CheckModel> checkModels = checkDrivers(dspOrderVO, Collections.emptyList(), duid);
            res.addAll(checkModels);
        } catch (Exception e) {
            logger.error("checkDriversError", e);
        }
        return res.stream()
                .filter(r -> Objects.nonNull(r) && Objects.nonNull(r.getCheckCode()))
                .filter(r -> r.getCheckCode().isPass())
                .collect(Collectors.toList());
    }

    private String getGlobalTraceId() {
        LoggerContext current = LoggerContext.getCurrent();
        if (current == null || StringUtils.isEmpty(current.getGlobalTraceId())) {
            current = LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        }
        return current.getGlobalTraceId();
    }

    private DuidVO initDuid(DspOrderVO order) {
        Integer subSkuId = manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, 0, SysConstants.AssignRole.SYS_ROLE_BOSS);
        return DuidVO.of(order.getDspOrderId(), subSkuId, DspType.OFFLINE_ASSIGN.getCode(), TakenType.BOSS_ASSIGN.getCode());
    }

    /**
     * 手动检查
     *
     * @param dspOrder
     * @param drivers
     */
    public List<CheckModel> checkDrivers(DspOrderVO dspOrder, List<DriverVO> drivers, DuidVO duid) {
        SubSkuVO subSku = subSkuRepository.find(duid.getSubSkuId());
        return checkService.check(new DspCheckCommand(dspOrder, subSku, drivers, duid));
    }

}
