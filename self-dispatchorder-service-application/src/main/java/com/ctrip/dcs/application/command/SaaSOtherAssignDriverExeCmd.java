package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.command.validator.SaaSOtherAssignDriverValidator;
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;
import java.util.Set;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class SaaSOtherAssignDriverExeCmd extends AbstractSaaSOperateExeCmd {

    @Autowired
    private SaaSOtherAssignDriverValidator saaSOtherAssignDriverValidator;


    private static final Logger logger = LoggerFactory.getLogger(SaaSOtherAssignDriverExeCmd.class);


    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        saaSOtherAssignDriverValidator.validate(cmd, saaSBusinessVO);
    }


    protected SaaSBusinessVO buildSaaSBusinessVO(SaaSOperateDriverCarCommand cmd) {
        DspOrderVO dspOrderVO = orderQueryService.query(cmd.getDspOrderId());
        if(dspOrderVO != null){
            dspOrderVO.setDriverVisibleRemark(cmd.getDriverVisibleRemark());
        }
        VehicleVO vehicleVO = getVehicleVO(cmd);
        DriverVO driverVO = getDriverVO(cmd);
        if(Boolean.TRUE.equals(cmd.isNewProcess()) ){
            if(Integer.valueOf(1).equals(cmd.getIsSelfDriver())){
                driverVO =queryDriverService.queryDriver(Long.valueOf(cmd.getDriverId()), CategoryUtils.selfGetParentType(dspOrderVO),cmd.getSupplierId());
            }
            if(cmd.getCarId() != null && cmd.getCarId() != 0){
                vehicleVO = queryVehicleService.query(Long.valueOf(cmd.getCarId()),CategoryUtils.selfGetParentType(dspOrderVO));
            }
        }
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO(dspOrderVO, driverVO, vehicleVO);
        return saaSBusinessVO;
    }

    private DriverVO getDriverVO(SaaSOperateDriverCarCommand cmd) {
        DriverVO driverVO = SaaSOperateDriverCarConverter.converterDriverVO(cmd);
        return driverVO;
    }

    private VehicleVO getVehicleVO(SaaSOperateDriverCarCommand cmd) {
        VehicleVO vehicleVO = SaaSOperateDriverCarConverter.converterVehicleVO(cmd);
        return vehicleVO;
    }


    @Override
    protected String assign(ScheduleTaskDO task, SaaSBusinessVO saaSBusinessVO, SaaSOperateDriverCarCommand cmd,
                             Long supplierId, DriverOrderVO driverOrder) {
        DspOrderVO order = saaSBusinessVO.getDspOrderVO();
        DriverVO driver = saaSBusinessVO.getDriverVO();
        TransportGroupVO transportGroup = saaSBusinessVO.getTransportGroupVO();
        VehicleVO vehicleVO = saaSBusinessVO.getVehicleVO();
        boolean confirm = false;
        try {
            // 应单
            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(order)
                    .serviceProvider(new ServiceProviderVO(order.getSpId()))
                    .supplier(new SupplierVO(supplierId))
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicleVO)
                    .duid(new DuidVO())
                    .driverOrderId(driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .event(cmd.getEvent())
                    .operator(new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(),
                            cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode()))
                    .otherInfoFromSupplier(new OtherInfoFromSupplierVO(cmd.getDriverVisibleRemark(), cmd.getDriverSettlePrice(),cmd.getDriverSettleCurrency(),cmd.getSupplierRemark(), isNeedUpdateSupplierRemark(cmd.isNotUpdateSupplierRemark(),cmd.isBatchAssign()), cmd.getNotUpdateSettlePriceAndCurrency(), cmd.isNotUpdateDriverVisibleRemark()))
                    .build();
            confirmSaasDspOrderService.confirm(confirmVO);
            // 应单成功
            confirm = true;
        } catch (Exception e) {
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("task", LocalJsonUtils.toJson(task));
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            objectObjectHashMap.put("driverOrder", LocalJsonUtils.toJson(driverOrder));
            logger.warn("SaaSOtherAssignDriverExeCmd_confirm", LocalJsonUtils.toJson(objectObjectHashMap));
            logger.error("SaaSOtherAssignDriverExeCmd_confirm", e);
            throw ErrorCode.VBK_OPERATE_DRIVER_NEWTAKEN_AFTER_ERR.getBizException();
        } finally {
            if (!confirm) {
                // 未接单成功，取消司机单
                messageProducer.send(new DriverOrderConfirmFailEvent(driverOrder.getDriverOrderId(), driverOrder.getDspOrderId(), driverOrder.getDriverId()));
            }
        }
        return order.getDspOrderId();
    }

}
