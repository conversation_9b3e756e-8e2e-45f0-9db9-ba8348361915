package com.ctrip.dcs.application.command;

import com.ctrip.dcs.domain.common.enums.DriverOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/5/30 14:12
 */
@Component
public class AllowAuthCmd {

    public static final Logger logger = LoggerFactory.getLogger(AllowAuthCmd.class);

    @Resource
    private SelfOrderQueryGateway selfOrderQueryGateway;

    public Result<Boolean> queryAllowAuth(Integer roleId, String userOrderId) {
        String message = ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getDesc();
        String code = "";
        ReassignTaskEnum.RoleEnum role = ReassignTaskEnum.RoleEnum.getInstance(roleId);
        if (role == null) {
            throw ErrorCode.ROLE_MISS_ERROR.getBizException();
        }
        if (role.isFromDriverApp()) {
            code = ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getCode();
        }
        BaseDetailVO orderDetail = null;
        try {
            orderDetail = selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null);
        } catch (Exception e) {
            logger.error("queryAllowAuthError", "userOrderId not found userOrderId:{}", userOrderId);
        }
        if (orderDetail == null) {
            if (role.isFromCustomer()) {
                message = ErrorCode.DISPATCH_NOT_ALLOW_ERROR.getDesc();
            } else if (role.isFromDriverApp()) {
                code = ErrorCode.DISPATCH_SUBMIT_NOT_FIND_ORDER_ERROR.getCode();
            } else {
                message = ErrorCode.DISPATCH_SUBMIT_NOT_FIND_ORDER_ERROR.getDesc();
            }
            return Result.Builder.<Boolean>newResult().fail().withCode(code).withMsg(message).build();
        }
        if (orderDetail.isDay()) {
            return Result.Builder.<Boolean>newResult().fail().withCode(code).withMsg(ErrorCode.DISPATCH_SUBMIT_NOT_SUPPORT_ORDER_ERROR.getDesc()).build();
        }
        // 客服侧要特殊的返回值，所以得改
        if (role.isFromCustomer()) {
            // 已下单或者 开始服务状态不允许改派
            if (orderDetail.isInServiced() || orderDetail.getOrderStatus() == DriverOrderStatusEnum.NEW.getCode().intValue()) {
                return Result.Builder.<Boolean>newResult().fail().withCode(code).withMsg(ErrorCode.DISPATCH_NOT_ALLOW_STATUS_ERROR.getDesc()).build();
            }
        }
        if (allowAuthMethod(role, orderDetail)) {
            return Result.Builder.<Boolean>newResult().fail().withCode(code).withMsg(message).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    public boolean allowAuthMethod(ReassignTaskEnum.RoleEnum role, BaseDetailVO orderDetail) {
        // cbp 是否展示改派按钮，需要限制车头以及时间
        if (role.isFromVBK()) {
            return Boolean.FALSE;
        } else if (role.isFromCustomer() || role.getCode().intValue() == ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode().intValue()) {
            return Boolean.FALSE;
        } else {
            Date nowBj = new Date();
            // 这个逻辑是给客服工作台以外的渠道使用的
            if (orderDetail.isInServiced() || (!orderDetail.isAirportPickup() && DateUtil.parseDateStr2Date(orderDetail.getEstimatedUseTimeBj()).before(nowBj))) {
                logger.info("ChangeDriverError", "not allow userOrderId:{}", orderDetail.attrs(), orderDetail.getUserOrderId());
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

}