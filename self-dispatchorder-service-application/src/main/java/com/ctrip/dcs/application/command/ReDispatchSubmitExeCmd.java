package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.converter.ReDispatchConverter;
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter;
import com.ctrip.dcs.application.query.ReasonsQuery;
import com.ctrip.dcs.application.service.RedispatchRightAndPointService;
import com.ctrip.dcs.application.service.reDispath.ReassignTaskManger;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SharkKey;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.ArithUtil;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.entity.*;
import com.ctrip.dcs.domain.dsporder.event.CarFaultFreezeDispatchEvent;
import com.ctrip.dcs.domain.dsporder.event.PunishNoticeEvent;
import com.ctrip.dcs.domain.dsporder.event.ReDispatchSubmitEvent;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.ReDispatchRecordRepository;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.config.ReasonDetailConfig;
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig;
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Minutes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import static com.ctrip.dcs.domain.common.enums.ReassignTaskEnum.DispatchReasonIdEnum.judgeFlightReason;


/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class ReDispatchSubmitExeCmd {

    private static Logger logger = LoggerFactory.getLogger(ReDispatchSubmitExeCmd.class);

    @Autowired
    protected QueryDriverService queryDriverService;

    @Autowired
    protected DriverOrderFactory driverOrderFactory;

    @Autowired
    protected ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    protected MessageProviderService messageProducer;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    protected QueryDspOrderService queryDspOrderService;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    protected QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private MessageProviderService messageProvider;
    @Autowired
    private ReasonsQuery reasonsQuery;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private RedispatchRightConfig rightConfig;
    @Autowired
    private RedispatchRightAndPointService redispatchRightAndPointService;

    @Autowired
    private ReasonDetailConfig reasonDetailConfig;
    @Autowired
    private ReDispatchRecordRepository reDispatchRecordRepository;
    @Autowired
    private DriverDomainServiceGateway driverDomainServiceGateway;

    @Autowired
    private QueryFlightService queryFlightService;
    @Autowired
    private QueryDriverLocationService queryDriverLocationService;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private CancelDspOrderExeCmd cancelDspOrderExeCmd;

    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @Resource
    SysSwitchConfig sysSwitchConfig;


    public Result<ReasonDetailVO> execute(ReDispatchSubmitCommand command) {
        String idempotentKey = String.format(SysConstants.REDISPATCH_SUBMIT_IDEMPOTENT, command.getUserOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);

        try {
            if (!lock.tryLock()) {
                WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(command.getUserOrderId());
                workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
                logger.info("ReDispatchSubmitExeCmd_info", "Failed to obtain the reassignment lock.request=" + JacksonUtil.serialize(command));
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getDesc()).build();
            }

            ReassignTaskEnum.RoleEnum role = ReassignTaskEnum.RoleEnum.getInstance(command.getRoleId());
            if (role == null) {
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.ROLE_MISS_ERROR.getCode()).withMsg(ErrorCode.ROLE_MISS_ERROR.getDesc()).build();
            }
            RightAndPointService service = ReassignTaskManger.getAgent(role.getDesc());
            if (service == null) {
                logger.error("ReDispatchSubmitExeCmd_error", "miss Service:{}", role.getDesc());
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.UN_KNOWN.getCode()).withMsg(ErrorCode.UN_KNOWN.getDesc()).build();
            }
            logger.info("ReDispatchSubmitExeCmd_info", "Entry Request Parameters.request=" + JacksonUtil.serialize(command));
            logger.info("ReDispatchSubmitExeCmd_info", "Matching Implementation Class.handle=" + service.getClass().getName());

            //查询出最新的派发单
            BaseDetailVO detailVO = selfOrderQueryGateway.queryOrderBaseDetail(command.getUserOrderId(), null);
            if (Objects.isNull(detailVO)) {
                logger.error("ReDispatchSubmitExeCmd_error", "invalid userOrderId:{}", command.getUserOrderId());
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.NULL_ORDER_ERROR.getCode()).withMsg(ErrorCode.NULL_ORDER_ERROR.getDesc()).build();
            }

            Result<ReasonDetailVO> verifyRes = bizVerify(detailVO, command);
            if (verifyRes != null) {
                logger.info("ReDispatchSubmitExeCmd_info", "Business verification failed, stop reassigning.verifyRes=" + JacksonUtil.serialize(verifyRes) + ",userOrderId=" + command.getUserOrderId());
                return verifyRes;
            }

            //0、订单信息detailVo再组装处理
            if (StringUtils.isEmpty(command.getBookTime())) {
                command.setBookTime(detailVO.getEstimatedUseTime());
            }

            Result checkRes = service.convertAndCheckPrm(command, detailVO);
            if (!checkRes.isSuccess()) {
                //设置原因返回
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(checkRes.getCode()).withMsg(checkRes.getMsg()).build();
            }
            Result<RightAndPointVO> res = service.queryRightAndPointDTO(ReDispatchConverter.buildCommand(command), detailVO);
            if (!res.isSuccess() || res.getData() == null) {
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getDesc()).build();
            }
            logger.info("ReDispatchSubmitExeCmd_info", "queryRightAndPointDTO.Response=" + JacksonUtil.serialize(res.getData()));

            //有改派原因中断流程，需要check。biz是部分场景设置这个值。新流程是都有这个值，有问题
            if (Objects.nonNull(res.getData().getReasonDetail())) {
                WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(command.getUserOrderId());
                workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
                return Result.Builder.<ReasonDetailVO>newResult().fail()
                        .withCode(ErrorCode.DISPATCH_REASON_CHOOSE_ERROR.getCode())
                        .withMsg(ErrorCode.DISPATCH_REASON_CHOOSE_ERROR.getDesc())
                        .withData(res.getData().getReasonDetail()).build();
            }
            //0.组装改派context
            buildReDispatchSubmitContext(command, detailVO);
            //1.rollBackOrder
            rollBackOrder(command, detailVO, res.getData());
            logger.info("ReDispatchSubmitExeCmd_info", "Cancel order process completed...");

            //2.发送改派后接单检查延迟消息,异步提交派发任务

            //3.构建所需参数,接入判罚中台
            joinNewPunish(command, detailVO, res.getData());

            //5. 构建操作日志,写入ck
            this.sendVBKOperationRecord(detailVO, command);

        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } catch (Exception e) {
            logger.error(e);
            throw ErrorCode.SERVER_ERROR.getBizException();
        } finally {
            ReDispatchSubmitContext.destroy();
            lock.unlock();
        }

        return Result.Builder.<ReasonDetailVO>newResult().success().build();
    }

    private Result<ReasonDetailVO> bizVerify(BaseDetailVO detailVO, ReDispatchSubmitCommand command) {
        //自营供应商发起的改派，如果最新派发单上没有供应商id,则不允许改派
        if(ReassignTaskEnum.RoleEnum.ROLE_SELF_SUPPLIER.getCode().equals(command.getRoleId())){
            if(Objects.isNull(detailVO.getSupplierId())){
                return Result.Builder.<ReasonDetailVO>newResult().fail().
                        withCode(ErrorCode.ERROR_AUTHORITY.getCode()).withMsg(ErrorCode.ERROR_AUTHORITY.getDesc()).build();
            }
        }
        // 订单已完成不允许改派
        if (Objects.nonNull(detailVO.getSupplierId()) && Objects.nonNull(command.getSupplierId())
                && !detailVO.getSupplierId().equals(command.getSupplierId())) {
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.ERROR_AUTHORITY.getCode()).withMsg(ErrorCode.ERROR_AUTHORITY.getDesc()).build();
        }
        if (ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getCode().equals(command.getRoleId())
                && Objects.nonNull(detailVO.getDrvId()) && Objects.nonNull(command.getDrvId())
                && !detailVO.getDrvId().equals(command.getDrvId())) {
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.DISPATCH_DRIVER_NO_ORDER_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_DRIVER_NO_ORDER_ERROR.getDesc()).build();
        }
        //订单状态大于等于500，不允许改派
        if (detailVO.getOrderStatus() > DriverOrderStatusEnum.ARRIVE.getCode()) {
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.ERROR_STATE_CHG.getCode()).withMsg(ErrorCode.ERROR_STATE_CHG.getDesc()).build();
        }

        //1、业务校验。// 境外订单不允许改派
        if (BizAreaTypeEnum.IGT.getCtripCode().equals(detailVO.getBizAreaType())) {
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.IGT_ORDER_NOT_ALLOW_DISPATCH.getCode()).withMsg(ErrorCode.IGT_ORDER_NOT_ALLOW_DISPATCH.getDesc()).build();
        }
        //包车订单不支持改派
        if (CategoryCodeEnum.isCharterOrder(detailVO.getCategoryCode())) {
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.DISPATCH_SUBMIT_NOT_SUPPORT_ORDER_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_SUBMIT_NOT_SUPPORT_ORDER_ERROR.getDesc()).build();
        }
        if (allowAuthMethod(command, detailVO)) {
            WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(command.getUserOrderId());
            workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
            return Result.Builder.<ReasonDetailVO>newResult().fail().
                    withCode(ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getDesc()).build();
        }
        return null;
    }


    public void joinNewPunish(ReDispatchSubmitCommand command, BaseDetailVO detailVO, RightAndPointVO rightAndPointVO) {
        try {
            if (Boolean.TRUE.equals(rightAndPointVO.getResponsable()) || command.getRoleId().equals(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode())) {
                PunishQmqDTO punishQmqDTO = new PunishQmqDTO();
                punishQmqDTO.setUserOrderId(detailVO.getUserOrderId());
                punishQmqDTO.setSupplyOrderId(detailVO.getSupplyOrderId());
                punishQmqDTO.setReasonId(Integer.valueOf(command.getReasonId()));
                punishQmqDTO.setDriverId(command.getDrvId() == null ? null : command.getDrvId().intValue());
                punishQmqDTO.setType(ReassignTaskEnum.PunishType.DISPATCH_TYPE.getCode());
                punishQmqDTO.setSupplierId(detailVO.getSupplierId());
                punishQmqDTO.setReDispatchType(rightAndPointVO.getDispatchType());
                punishQmqDTO.setSourceType(ReassignTaskEnum.PunishSourceType.SYSTEM.getCode());
                if (command.getRoleId().equals(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode())) {
                    punishQmqDTO.setSourceType(ReassignTaskEnum.PunishSourceType.PEOPLE.getCode());
                }
                punishQmqDTO.setSupplierOrderId(detailVO.getDspOrderId());
                punishQmqDTO.setDriverOrderId(detailVO.getDriverOrderId());
                String qmqParam = JacksonUtil.serialize(punishQmqDTO);
                logger.info("ReDispatchSubmitExeCmd_info", "punish notice qmq params=" + qmqParam);
                messageProducer.send(new PunishNoticeEvent(qmqParam, 0L));

            }
            if (ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode().equals(Integer.valueOf(command.getReasonId()))) {
                if (Objects.nonNull(detailVO.getSupplierId()) && Objects.nonNull(detailVO.getDrvId()) && detailVO.getDrvId() > 0) {
                    int freezeTotalHours = reasonsQuery.getFreezeTotalHours(new Date(), DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()));
                    queryDriverService.addTmsDrvFreeze(detailVO.getSupplierId().longValue(), detailVO.getDrvId(), freezeTotalHours);

                    LogContentDTO logContentDTO = initLogContentDTO(command, detailVO, rightAndPointVO);

                    messageProducer.send(new CarFaultFreezeDispatchEvent(JacksonUtil.serialize(logContentDTO), detailVO.getDrvId(), detailVO.getUserOrderId(), detailVO.getDspOrderId(), detailVO.getEstimatedUseTimeBj(), freezeTotalHours, 0L));

                }
            } else {
                //发送客服工作台日志
                sendNewWorkBenchLogMessage(command, detailVO, rightAndPointVO);
            }
        } catch (Exception e) {
            Map<String, String> map = Maps.newHashMap();
            map.put("userOrderId", command.getUserOrderId());
            logger.error("joinNewPunishError", e, map);
        }
    }

    public boolean allowAuthMethod(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        if (ReassignTaskEnum.RoleEnum.isCustomer(command.getRoleId()) || Objects.equals(command.getRoleId(), ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode())) {
            return false;
        }
        if (Objects.equals(command.getRoleId(), ReassignTaskEnum.RoleEnum.ROLE_SELF_SUPPLIER.getCode()) || Objects.equals(command.getRoleId(), ReassignTaskEnum.RoleEnum.ROLE_BD_CLIENT.getCode())) {
            return false;
        }

        if (detailVO.getOrderStatus() >= DriverOrderStatusEnum.BEGIN.getCode() ||
                (!detailVO.getCategoryCode().equals(CategoryCodeEnum.FROM_AIRPORT.getType()) && DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()).before(new Date()))) {
            return true;
        }
        return false;
    }

    public LogContentDTO initLogContentDTO(ReDispatchSubmitCommand command, BaseDetailVO detailVO, RightAndPointVO rightAndPointVO) {
        ReDispatchSubmitContext context = ReDispatchSubmitContext.getCurrent();
        LogContentDTO logContentDTO = new LogContentDTO();
        logContentDTO.setReassignmentId(detailVO.getDrvId() == null ? null : detailVO.getDrvId().toString());
        //开关开启，不写入司机敏感信息
        if (!sysSwitchConfig.getQmqRemoveDriverSensitiveInfoSwitch()) {
            logContentDTO.setReassignmentName(detailVO.getDrvName());
            logContentDTO.setReassignmentTel(detailVO.getDrvPhone());
        }
        logContentDTO.setReassignmentReason(context.getDispatchReason().getReasonDetail());
        if (Objects.nonNull(rightAndPointVO.getDispatchRightInfo())) {
            logContentDTO.setReassignmentRights("1");
            if (command.getNewRights() != null && command.getNewRights() == 1) {
                DriverRightsVO rightsVO = driverDomainServiceGateway.queryDriverRightsInfo(detailVO.getDrvId());
                if (rightsVO != null && rightsVO.getReassignUsedByWeek() != null) {
                    logContentDTO.setReassignmentSurplusRights(String.valueOf(rightsVO.getReassignUsedByWeek()));
                }
            } else {
                logContentDTO.setReassignmentSurplusRights(String.valueOf(rightAndPointVO.getDispatchRightInfo().getTimes()));
            }
        }
        PunishVO punishVO = rightAndPointVO.getPunishInfo();
        if (Objects.nonNull(punishVO)) {
            logContentDTO.setReassignmentGrade(String.valueOf(punishVO.getPunishPoint()));
            if (ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode().equals(punishVO.getPunishRoleId())) {
                logContentDTO.setReassignmentCost(String.valueOf(punishVO.getPunishAmount()));
            } else if (ReassignTaskEnum.PunishRoleEnum.PUNISH_SUPPLIER.getCode().equals(punishVO.getPunishRoleId())) {
                logContentDTO.setReassignmentSupplierCost(String.valueOf(punishVO.getPunishAmount()));

            }
        }
        return logContentDTO;

    }

    public void sendNewWorkBenchLogMessage(ReDispatchSubmitCommand command, BaseDetailVO detailVO, RightAndPointVO rightAndPointVO) {
        LogContentDTO logContentDTO = initLogContentDTO(command, detailVO, rightAndPointVO);
        Map<String, String> keyMaps = new HashMap<>();
        keyMaps.put(SysConstants.LogKeyMaps.TITLE_KEY,SharkKey.REASSIGNMENT_TITLE);
        keyMaps.put(SysConstants.LogKeyMaps.CONTENT_KEY,SharkKey.REASSIGNMENT_CONTENT);
        specialLogBuild(command, detailVO, logContentDTO, keyMaps);

        if (ReassignTaskEnum.RoleEnum.isCustomer(command.getRoleId())) {
            WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLogByBoss(command.getUserOrderId(), JacksonUtil.serialize(logContentDTO), keyMaps);
            workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
        } else {
            WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(command.getUserOrderId(), JacksonUtil.serialize(logContentDTO), keyMaps);
            workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);

        }

    }

    public ReDispatchSubmitContext buildReDispatchSubmitContext(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        ReDispatchSubmitContext.Builder builder = ReDispatchSubmitContext.newContext();
        builder.withUserOrderId(detailVO.getUserOrderId());
        try {
            ReasonDetailVO dispatchReason = reasonDetailConfig.getReasonDetail(command.getReasonDetailId());
            if (StringUtils.isBlank(command.getReasonId()) || command.getReasonDetailId().toString().equals(command.getReasonId())) {
                command.setReasonId(dispatchReason.getReasonId().toString());
            }
            command.setDspOrderId(detailVO.getDspOrderId());
            builder.withReasonDetailVO(dispatchReason);
            if (judgeFlightReason(Integer.valueOf(command.getReasonId())) || Objects.equals(Integer.valueOf(command.getReasonId()), ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())) {
                FlightVO flightVO = queryFlightService.queryFlight(command.getUserOrderId());
                builder.withFlightVO(flightVO);
            }

            //有司机接单才查预估路径
            if (detailVO.getDrvId() != null && detailVO.getDrvId() > 0L && detailVO.getFromPoiDTO() != null) {
                DriverToPointEstimateDataQueryDTO queryDTO = new DriverToPointEstimateDataQueryDTO();
                queryDTO.setLocationTimestamp(new Date().getTime());
                queryDTO.setDriverId(detailVO.getDrvId().toString());
                DriverToPointEstimateDataQueryDTO.PointDTO gpsdto = new DriverToPointEstimateDataQueryDTO.PointDTO();
                gpsdto.setLongitude(detailVO.getFromPoiDTO().getActualFromLongitude());
                gpsdto.setLatitude(detailVO.getFromPoiDTO().getActualFromLatitude());
                gpsdto.setCoordSys(detailVO.getFromPoiDTO().getActualFromCoordsys());
                queryDTO.setDestination(gpsdto);
                queryDTO.setLongTime(180L);
                DriverToPointEstimateDataVO driverToPointEstimateDataVO = queryDriverLocationService.queryDriverToPointEstimateData(queryDTO);
                builder.withDriverToPointEstimateDataVO(driverToPointEstimateDataVO);
            }

        } catch (Exception e) {
            Map<String, String> map = Maps.newHashMap();
            map.put("userOrderId", command.getUserOrderId());
            logger.error("ReDispatchSubmitExeCmd_warn", e, map);
        }
        return builder.init();
    }

    public void specialLogBuild(ReDispatchSubmitCommand command, BaseDetailVO detailVO, LogContentDTO logContentDTO, Map<String, String> keyMaps) {
        ReDispatchSubmitContext context = ReDispatchSubmitContext.getCurrent();
        FlightVO flightVO = context.getFlightVO();
        DriverToPointEstimateDataVO dataVO = context.getDriverToPointEstimateDataVO();
        String estimateDepartureDate = "";
        String estimateArriveDate = "";
        String reassignmentBufferTime = "";
        if (flightVO != null) {
            estimateDepartureDate = flightVO.getEstimateDepartureDate();
            estimateArriveDate = flightVO.getEstimateArriveDate();
            if (Objects.equals(detailVO.getCategoryCode(), CategoryCodeEnum.FROM_AIRPORT.getType())) {
                String flightArriveDateStr;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(flightVO.getActualArrivalDate())) {
                    flightArriveDateStr = flightVO.getActualArrivalDate();
                } else if (org.apache.commons.lang3.StringUtils.isNotBlank(flightVO.getEstimateArriveDate())) {
                    flightArriveDateStr = flightVO.getEstimateArriveDate();
                } else {
                    flightArriveDateStr = flightVO.getPlannedArrivalDate();
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(flightArriveDateStr)) {
                    Date flightArriveDate = DateUtil.parseDateStr2Date(flightArriveDateStr);
                    reassignmentBufferTime = String.valueOf(Minutes.minutesBetween(new DateTime(flightArriveDate), new DateTime(DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTime()))).getMinutes());
                }
            } else {
                String extendInfo = detailVO.getOrderDetailExtendInfo();
                String useTime = detailVO.getEstimatedUseTime();
                if(StringUtils.isNotBlank(extendInfo)){
                    Map<String,Object> extendInfoMap = JacksonUtil.deserialize(extendInfo, HashMap.class);
                    Object o = extendInfoMap.get(SysConstants.Order.USE_TIME);
                    if (o != null) {
                        useTime = o.toString();
                    }
                }
                reassignmentBufferTime = String.valueOf(Minutes.minutesBetween(new DateTime(DateUtil.parseDateStr2Date(useTime)), new DateTime(DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTime()))).getMinutes());
            }
        }
        if (RoleReflectEnum.isCustomer(command.getRoleId().toString()) || Objects.equals(command.getRoleId().toString(), String.valueOf(RoleReflectEnum.ROLE_DRIVER_APP.getCode()))) {
            //航变相关原因
            if (Objects.equals(detailVO.getCategoryCode(), CategoryCodeEnum.FROM_AIRPORT.getType()) && judgeFlightReason(Integer.valueOf(command.getReasonId()))
                    && dataVO != null) {
                logContentDTO.setReassignmentFlightTime(estimateDepartureDate);
                logContentDTO.setFlightReassignmentArriveTime(estimateArriveDate);
                logContentDTO.setReassignmentDriverMileage(String.valueOf(ArithUtil.div(dataVO.getDuration(), 60, 2)));
                logContentDTO.setReassignmentDriverKm(String.valueOf(ArithUtil.div(dataVO.getDistance(), 1000, 2)));
                logContentDTO.setReassignmentBufferTime(reassignmentBufferTime);
                keyMaps.put(SysConstants.LogKeyMaps.TITLE_KEY,SharkKey.REASSIGNMENT_TITLE_1);
                keyMaps.put(SysConstants.LogKeyMaps.CONTENT_KEY,SharkKey.REASSIGNMENT_CONTENT_1);
            }
            //来不及赶到
            if (Objects.equals(Integer.valueOf(command.getReasonId()), ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode()) && dataVO != null) {
                if (!sysSwitchConfig.getQmqRemoveDriverSensitiveInfoSwitch()) {
                    logContentDTO.setReassignmentLoc(dataVO.getDriverPoint().getLongitude() + "," + dataVO.getDriverPoint().getLatitude());
                }
                logContentDTO.setReassignmentArriveTime(DateUtil.getStringDate(DateUtil.addSeconds(new Date(), dataVO.getDuration())));
                logContentDTO.setReassignmentDriverKm(String.valueOf(ArithUtil.div(dataVO.getDistance(), 1000, 2)));
                keyMaps.put(SysConstants.LogKeyMaps.TITLE_KEY,SharkKey.REASSIGNMENT_TITLE_2);
                keyMaps.put(SysConstants.LogKeyMaps.CONTENT_KEY,SharkKey.REASSIGNMENT_CONTENT_2);
            }
            //订单冲突
            if (Objects.equals(Integer.valueOf(command.getReasonId()), ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())) {
                logContentDTO.setReassignmentFlightTime(estimateDepartureDate);
                logContentDTO.setFlightReassignmentArriveTime(estimateArriveDate);
                logContentDTO.setReassignmentBufferTime(reassignmentBufferTime);
                keyMaps.put(SysConstants.LogKeyMaps.TITLE_KEY,SharkKey.REASSIGNMENT_TITLE_4);
                keyMaps.put(SysConstants.LogKeyMaps.CONTENT_KEY,SharkKey.REASSIGNMENT_CONTENT_4);
            }
        }
    }


    public void rollBackOrder(ReDispatchSubmitCommand command, BaseDetailVO detailVO, RightAndPointVO rightAndPointVO) throws SQLException {
        //判断需改派单是否是当前接单最新订单
        if (!checkNeedRedispatchOrder(command, detailVO)) {
            DspOrderDO dspOrderDO = dspOrderRepository.queryDspOrderByDspOrderIdOrDriverOrderId(command.getDspOrderId(),command.getDriverOrderId());
            cancelDspOrder(command, Collections.singletonList(dspOrderDO));
        }else {
            //1.取消所有派发单，发送取消消息
            batchCancelValidDspOrder(command);
        }


        //2. 落订单权益使用记录，新权益是否全量
        try {
            if (rightAndPointVO.getDispatchRightInfo() != null) {
                if (command.getNewRights() != null && command.getNewRights() == 1
                        && String.valueOf(ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()).equals(rightAndPointVO.getDispatchRightInfo().getRightId())) {
                    boolean res = driverDomainServiceGateway.useDriverDispatchRight(command.getDrvId(), detailVO);
                    logger.info("ReDispatchSubmitExeCmd_info", "New equity usage results,res=" + res + ",userOrderId=" + command.getUserOrderId());

                }
            } else if (!rightAndPointVO.getResponsable() && command.getRightRecordId() != null && command.getRightRecordId() > 0) {
                if (command.getNewRights() != null && command.getNewRights() == 1
                        && rightAndPointVO.getDispatchRightInfo() != null
                        && String.valueOf(ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()).equals(rightAndPointVO.getDispatchRightInfo().getRightId())) {
                    boolean res = driverDomainServiceGateway.useDriverDispatchRight(command.getDrvId(), detailVO);
                    logger.info("ReDispatchSubmitExeCmd_info", "New equity usage results,res=" + res + ",userOrderId=" + command.getUserOrderId());
                }
            }
        } catch (Exception e) {
            Map<String, String> map = Maps.newHashMap();
            map.put("userOrderId", command.getUserOrderId());
            logger.error("ReDispatchSubmitExeCmd_warn", e, map);
        }

        //1.插入改派记录
        loadReDispatchRecord(command, detailVO, rightAndPointVO);

        //1.发送改派消息
        //发送取消消息,发送取消记录消息
        messageProvider.send(new ReDispatchSubmitEvent(detailVO.getDspOrderId(), detailVO.getUserOrderId(), detailVO.getDriverOrderId(),
                rightAndPointVO.getDispatchType(), command.getRoleId(), command.getReasonDetailId(), detailVO.getSupplierId(), 0L));

    }

    private boolean checkNeedRedispatchOrder(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        //如果参数中的派发单号不为空，则判断参数中的派发单号是否是最新单的单号
        if (StringUtils.isNotBlank(command.getDspOrderId())) {
            return command.getDspOrderId().equals(detailVO.getDspOrderId());
        }
        //如果参数中的司机单号不为空，则判断参数中的司机单号是否是最新单的接单司机单号
        if (StringUtils.isNotBlank(command.getDriverOrderId())) {
            return command.getDriverOrderId().equals(detailVO.getDriverOrderId());
        }
        //其他没传单号的，在权限检验的时候已经拦截
        return true;
    }

    private void loadReDispatchRecord(ReDispatchSubmitCommand command, BaseDetailVO detailVO, RightAndPointVO rightAndPointVO) throws SQLException {
        ReDispatchRecordDO recordDO = new ReDispatchRecordDO();
        recordDO.setDrivId(detailVO.getDrvId());
        recordDO.setDspOrderId(detailVO.getDspOrderId());
        recordDO.setUserOrderId(detailVO.getUserOrderId());
        recordDO.setDrivOrderId(detailVO.getDriverOrderId());
        recordDO.setAppealStatus(ReassignTaskEnum.AppealStatusEnum.APPEAL_NO_RECORD.getCode());
        recordDO.setSysExpectBookTimeBj(new Timestamp(DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()).getTime()));
        recordDO.setFrozenDuration(Objects.isNull(command.getFrozenDuration()) ? new BigDecimal(0) : command.getFrozenDuration());
        recordDO.setIsNight(DateUtil.locateTimeRange(DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTime()), rightConfig.getNightRangeTimeStart(), rightConfig.getNightRangeTimeEnd()) ? 1 : 0);
        recordDO.setIsPay(rightAndPointVO.getResponsable() ? 1 : 0);
        recordDO.setIsSubjective(recordDO.getIsPay());
        if (rightAndPointVO.getReasonDetail() != null) {
            recordDO.setIsSubjective(rightAndPointVO.getReasonDetail().getResponsible());
        }
        recordDO.setModUser(StringUtils.isEmpty(command.getUserName()) ? ReassignTaskEnum.RoleEnum.getOpName(command.getRoleId()) : command.getUserName());
        recordDO.setNightRanges(rightConfig.getNightRangeTimeStart() + "-" + rightConfig.getNightRangeTimeEnd());
        // 有惩时的属性赋值
        if (Objects.nonNull(rightAndPointVO.getPunishInfo())) {
            recordDO.setPayAccount(rightAndPointVO.getPunishInfo().getPunishRoleId());
            recordDO.setReDispatchCost(rightAndPointVO.getPunishInfo().getPunishAmount());
            recordDO.setReDispatchPoint(rightAndPointVO.getPunishInfo().getPunishPoint());
            recordDO.setReDispatchType(rightAndPointVO.getDispatchType());
        } else {
            ReassignTaskEnum.ChangeDriveTypeEnum changeDriverTypeEnum = redispatchRightAndPointService.confirmDispatchType(detailVO);
            recordDO.setReDispatchType(changeDriverTypeEnum.getReasonId());
        }
        // 如果记录是无责，且使用权益记录不为空时 实际就是循环嵌套使用的复用
        if (!rightAndPointVO.getResponsable() && Objects.nonNull(command.getRightRecordId()) && command.getRightRecordId() > 0) {
            recordDO.setFullTimeInterests(ReassignTaskEnum.InterestsRightEnum.USE_RIGHT.getCode());
            recordDO.setRightRecordId(command.getRightRecordId());
        } else {
            recordDO.setFullTimeInterests(Objects.nonNull(rightAndPointVO.getDispatchRightInfo()) ? ReassignTaskEnum.InterestsRightEnum.USE_RIGHT.getCode() : ReassignTaskEnum.InterestsRightEnum.NONE_RIGHT.getCode());
            recordDO.setRightRecordId(0L);
        }
        recordDO.setReDispatchReasonId(command.getReasonDetailId());
        recordDO.setReDispatchFrom(command.getRoleId());
        // 定位 - 改派原因
        ReDispatchSubmitContext context = ReDispatchSubmitContext.getCurrent();
        ReasonDetailVO dispatchReason = context.getDispatchReason();
        recordDO.setReasonDetail(Objects.isNull(dispatchReason) ? command.getReasonDetailId().toString() : dispatchReason.getReasonDetail());
        recordDO.setFrozenDuration(command.getFrozenDuration());
        recordDO.setCityId(detailVO.getCityId());
        recordDO.setSnapshotInfo(buildSnapshotInfo(command, detailVO));
        recordDO.setDrvStrId(detailVO.getDrvId() == null ? "0" : detailVO.getDrvId().toString());
        recordDO.setDriverOrderUseTimeRecentChangeEventId(selfOrderQueryGateway.queryDriverOrderUseTimeRecentChangeEventId(detailVO.getDriverOrderId()));
        reDispatchRecordRepository.save(recordDO);
    }

    public String buildSnapshotInfo(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        Map<String, Object> map = Maps.newHashMap();
        try {
            ReDispatchSubmitContext context = ReDispatchSubmitContext.getCurrent();
            map.put(ReassignTaskEnum.PUNISH_DISPATCH_SYS_EXPECT_BOOK_TIME, detailVO.getEstimatedUseTime());
            FlightVO flightVO = context.getFlightVO();
            if (Objects.nonNull(detailVO.getDrvId()) && detailVO.getDrvId() > 0) {
                //改派时到上车点预估里程
                //改派时到上车点预估时间
                DriverToPointEstimateDataVO dataVO = context.getDriverToPointEstimateDataVO();
                if (dataVO != null && dataVO.getDriverPoint() != null) {
                    PositionDTO positionDTO = new PositionDTO();
                    positionDTO.setLatitude(dataVO.getDriverPoint().getLatitude());
                    positionDTO.setLongitude(dataVO.getDriverPoint().getLongitude());
                    positionDTO.setType(dataVO.getDriverPoint().getCoordSys());
                    map.put(ReassignTaskEnum.LOCATION_DISPATCH, JacksonUtil.serialize(positionDTO));
                }
                if (dataVO != null && dataVO.getDistance() != null) {
                    map.put(ReassignTaskEnum.PUNISH_DISPATCH_ESTIMATE_DISTANCE, ArithUtil.div(dataVO.getDistance(), 1000, 2));

                }
                if (dataVO != null && dataVO.getDuration() != null) {

                    map.put(ReassignTaskEnum.PUNISH_DISPATCH_ESTIMATE_DURATION, ArithUtil.div(dataVO.getDuration(), 60, 2));
                }
            }
            if (ReassignTaskEnum.RoleEnum.ROLE_SYSTEM.getCode().equals(command.getRoleId())) {
                map.put(ReassignTaskEnum.PUNISH_DISPATCH_USER_ID, "System");
            }
            if (ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getCode().equals(command.getRoleId())) {
                map.put(ReassignTaskEnum.PUNISH_DISPATCH_USER_ID, detailVO.getDrvId());
            }
            if (flightVO != null && StringUtils.isNotBlank(flightVO.getEstimateArriveDate())) {
                map.put(ReassignTaskEnum.PUNISH_DISPATCH_FLIGHT_EXPECT_ARRIVE_TIME, flightVO.getEstimateArriveDate());
            }
            if(StringUtils.isNotBlank(command.getSutSource())){
                map.put(ReassignTaskEnum.SUT_SOURCE,command.getSutSource());
            }

        } catch (Exception e) {
            Map<String, String> logmap = Maps.newHashMap();
            map.put("userOrderId", command.getUserOrderId());
            logger.error("buildSnapshotInfoError", e, logmap);
        }
        return JacksonUtil.serialize(map);
    }

    private void batchCancelValidDspOrder(ReDispatchSubmitCommand command) throws SQLException {
        //更新状态为取消、更新费用、插入取消记录、更新接单记录为无效、插入订单操作记录(vbk)
        List<DspOrderDO> dspOrderDOList = dspOrderRepository.queryValidDspOrders(command.getUserOrderId());
        if (CollectionUtils.isEmpty(dspOrderDOList)) {
            return;
        }
        cancelDspOrder(command, dspOrderDOList);
    }

    private void cancelDspOrder(ReDispatchSubmitCommand command, List<DspOrderDO> dspOrderDOList) {
        String cancelReason = businessTemplateInfoConfig.getCancelReasonMap().get(CancelReasonIdEnum.REDISPATCH.getCode().toString());

        ReasonDetailVO reDispatchReason = reasonDetailConfig.getReasonDetail(command.getReasonDetailId());

        Integer cancelReasonCode = CancelReasonIdEnum.REDISPATCH.getCode();

        if (reDispatchReason != null) {
            // 取消原因描述 = 改派原因描述
            cancelReason = reDispatchReason.getReasonDetail();
            // 取消原因id = 改派原因id
            cancelReasonCode = reDispatchReason.getId();
        }

        //------------------new
        for (DspOrderDO dspOrderDO : dspOrderDOList) {
            try {
                cancelDspOrderExeCmd.execute(CancelDspOrderConverter.converter(dspOrderDO, cancelReasonCode, cancelReason));
            } catch (Exception e) {
                MetricsUtil.recordValue(MetricsConstants.REDISPATCH_CANCEL_DSP_ORDER_ERROR, 1);
                logger.warn("ReDispatcherSubmit_Cancel_Error", e);
            }

        }
    }

    public void sendVBKOperationRecord(BaseDetailVO detailVO, ReDispatchSubmitCommand prm) {
        try {
            // 定位 - 改派原因
            ReDispatchSubmitContext context = ReDispatchSubmitContext.getCurrent();
            ReasonDetailVO dispatchReason = context.getDispatchReason();
            String comment = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(prm.getReasonId())) {
                comment = String.format(SysConstants.OperationRecordLog.REDISPATCH_REASON, Objects.isNull(dispatchReason) ? prm.getReasonId() : dispatchReason.getReasonDetail());
            }
            Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), detailVO.getCityId());
            VBKOperationRecordVO record = vbkOperationRecordFactory.createReDispatchSubmitOperationRecord(detailVO
                    ,OperateUserInfoDTO.buildInstance(prm.getUserName(), mappingRoles(prm), SystemOperateUserType.SYSTEMUSER)
                    ,comment,localTimeNow);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    public String mappingRoles(ReDispatchSubmitCommand prm) {
        Integer roleId = Integer.valueOf(prm.getRoleId());
        RoleReflectEnum role = RoleReflectEnum.getEnumByCode(roleId);
        if (null == role) {
            return OperaterEnum.SYSTEM.getDesc();
        }
        String res;
        String suffix;
        switch (role) {
            case ROLE_DRIVER_APP:
                suffix = null != prm.getDrvId() ? "{" + prm.getDrvId() + "}" : "";
                res = OperaterEnum.DRIVER.getDesc() + suffix;
                break;
            case ROLE_SELF_SUPPLIER:
            case ROLE_OTA_API:
                // 如果登录用户名不为空则使用登录用户名
                if (StringUtils.isNotBlank(prm.getUserName())) {
                    suffix = "{" + prm.getUserName() + "}";
                } else {// 否则兜底使用供应商ID
                    suffix = null != prm.getSupplierId() ? "{" + prm.getSupplierId() + "}" : "";
                }
                res = OperaterEnum.SUPPLIER.getDesc() + suffix;
                break;
            case ROLE_CUSTOMER_CLIENT:
            case ROLE_CUSTOMER_USER:
                res = OperaterEnum.CUSTOMER.getDesc();
                break;
            case ROLE_USER_CLIENT:
                res = OperaterEnum.USER.getDesc();
                break;
            case ROLE_BD_CLIENT:
                res = OperaterEnum.OPERATION.getDesc();
                break;
            case ROLE_SYSTEM:
            default:
                res = OperaterEnum.SYSTEM.getDesc();
        }
        return res;
    }
}
