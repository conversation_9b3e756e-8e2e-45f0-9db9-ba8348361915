package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateCancelRateRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateCancelRateResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 更新结算取消比例
 */
@Component
@ServiceLogTagPair(key = "supplyOrderId", alias = "dspOrderId")
public class UpdateCancelRateExecutor extends AbstractRpcExecutor<UpdateCancelRateRequestType, UpdateCancelRateResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateCancelRateExecutor.class);

    @Autowired
    private DspOrderFeeRepository dspOrderFeeRepository;

    @Override
    public UpdateCancelRateResponseType execute(UpdateCancelRateRequestType requestType) {
        UpdateCancelRateResponseType responseType = new UpdateCancelRateResponseType();
        if (StringUtils.isEmpty(requestType.getSupplyOrderId()) || requestType.getCancelRate() == null) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            DspOrderFeeDO dspOrderFeeDO = dspOrderFeeRepository.find(requestType.getSupplyOrderId());
            if (dspOrderFeeDO == null) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.NULL_ORDER_ERROR.getCode(), ErrorCode.NULL_ORDER_ERROR.getDesc());
            }
            dspOrderFeeDO.setDspOrderId(requestType.getSupplyOrderId());
            dspOrderFeeDO.setCancelFineRate(requestType.getCancelRate());
            dspOrderFeeRepository.update(dspOrderFeeDO);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("updateCancelRateException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


}
