package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRewardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dsp_order_reward_info")
public class DspOrderRewardDealImpl implements BinlogDeal{

    @Autowired
    private DspOrderRewardRepository dspOrderRewardRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        dspOrderRewardRepository.cleanCacheForDspOrderReward(dspOrderId);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> dspOrderRewardRepository.cleanCacheForDspOrderReward(dspOrderId));
    }
}
