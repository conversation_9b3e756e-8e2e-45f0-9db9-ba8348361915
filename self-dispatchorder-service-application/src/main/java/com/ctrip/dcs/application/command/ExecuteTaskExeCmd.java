package com.ctrip.dcs.application.command;


import com.ctrip.dcs.application.command.api.ExecuteTaskCommand;
import com.ctrip.dcs.application.command.validator.ExecuteTaskValidator;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.process.ProcessContext;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

;


/**
 * <AUTHOR>
 */
@Component
public class ExecuteTaskExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteTaskExeCmd.class);

    private static final String DISTRIBUTED_LOCK_KEY = "DISTRIBUTED_LOCK_EXECUTE_SCHEDULE_TASK_";

    @Autowired
    private DistributedLockService distributedLockService;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private ExecuteTaskValidator validator;

    public void execute(ExecuteTaskCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(DISTRIBUTED_LOCK_KEY + command.getScheduleTaskId());
        try {
            if (!lock.tryLock()) {
                // 任务正在执行，则返回。
                logger.info("task execute", "task is executing.task id:{}", command.getTaskId());
                return;
            }
            // 查询调度任务
            ScheduleTaskDO task = taskRepository.find(command.getScheduleTaskId(), command.getDspOrderId());
            // 查询派发单聚合
            DspOrderVO order = queryDspOrderService.queryOrderDetailForTask(command.getDspOrderId(), task);
            // 业务校验
            validator.validate(order, task);
            // 执行任务
            task.execute();
            taskRepository.execute(task, order);
            ProcessContext.execute(task, order);
        } catch (ValidateException | DistributedLockRejectedException e) {
            logger.warn(e);
        } catch (Exception e) {
            logger.error(e);
        } finally {
            lock.unlock();
        }
    }
}
