package com.ctrip.dcs.application.listener.driver;

import com.ctrip.dcs.application.event.IDriverVehicleChangeEventHandler;
import com.ctrip.dcs.application.event.IDriverVehicleInfoModifyEventHandler;
import com.ctrip.dcs.application.event.IDriverVehicleTypeModifyEventHandler;
import com.ctrip.dcs.application.event.dto.DriverVehicleChangeEvent;
import com.ctrip.dcs.application.event.dto.DriverVehicleInfoModifyEvent;
import com.ctrip.dcs.application.event.dto.DriverVehicleTypeModifyEvent;
import com.ctrip.dcs.application.listener.driver.handler.TourDriverQMQHandler;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 一、用车供应链：
 * 司机的车辆变更消息 dcs.tms.transport.driver.vehicle.changed
 * 司机换车 tag_drivervehicle_change
 * 司机车辆车型变更 tag_drivervehicletype_modify
 * 司机车辆信息修改 tag_drivervehicle_modify
 *
 * 二、司导平台供应链
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=2473657898
 *
 */
@Component
public class DriverVehicleModifyListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverVehicleModifyListener.class);
    @Autowired
    private IDriverVehicleChangeEventHandler driverVehicleChangeEventHandler;
    @Autowired
    private IDriverVehicleInfoModifyEventHandler driverVehicleInfoModifyEventHandler;
    @Autowired
    private IDriverVehicleTypeModifyEventHandler driverVehicleTypeModifyEventHandler;

    @QmqLogTag(tagKeys = {"driverId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_VEHICLE_MODIFY_TOPIC,
            tagType = TagType.OR,
            tags = {"tag_drivervehicle_change","tag_drivervehicletype_modify","tag_drivervehicle_modify"},
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handle(Message message){
        logger.info("DriverVehicleModifyListener_msg", LocalJsonUtils.toJson(message));
        //司机更换车辆 vehicleId 有值
        //司机解绑车辆 vehicleId =0
        //车辆网约车运输证校验 vehicleId 有值
        if(message.getTags().contains("tag_drivervehicle_change")){
            //司机的车辆信息变更
            int vehicleId = message.getIntProperty("vehicleId");
            if(vehicleId > 0){
                logger.info("driverVehicleInfoModify",LocalJsonUtils.toJson(message));
                driverVehicleInfoModify(message);
                return;
            }
            //司机更换车辆
            logger.info("driverVehicleChange",LocalJsonUtils.toJson(message));
            driverVehicleChange(message);
            return;
        }
        //司机的车辆车型变更
        if(message.getTags().contains("tag_drivervehicletype_modify")){
            logger.info("driverVehicleTypeModify",LocalJsonUtils.toJson(message));
            driverVehicleTypeModify(message);
            return;
        }
        //司机的车辆信息变更
        if(message.getTags().contains("tag_drivervehicle_modify")){
            logger.info("driverVehicleInfoModify",LocalJsonUtils.toJson(message));
            driverVehicleInfoModify(message);
            return;
        }
    }

    /**
     * 处理司导平台供应链变更消息 - 车辆信息变更
     *
     * @param message
     */
    @QmqLogTag
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_VEHICLE_INFO_CHANGED,
            consumerGroup = EventConstants.CONSUMER_GROUP,
            idempotentChecker = "redisIdempotentChecker")
    public void handleDrvGuideTransportChgMessage(Message message) {
        long vehicleId = message.getLongProperty("vehicleId");
        long driverId = message.getLongProperty("driverId");
        int operateFrom = message.getIntProperty("operateFrom");
        String strDriverProductLineList = message.getStringProperty("driverProductLineList"); // 司机所属产线列表

        if (driverId <= 0) {
            logger.info("handleDrvGuideTransportChgMessage", String.format("driverId[%s] is invalid", driverId+"" ));
            return;
        }

        // 检查司机产线是否含有包车，没有则不处理直接返回
        if (!TourDriverQMQHandler.isCharteredDriverCarChanged(strDriverProductLineList)) {
            logger.info(message.getSubject() + "_" + message.getMessageId(), "driverProductLineList don't contain charteredCar, return. driverProductLineList: " + strDriverProductLineList);
            return;
        }

        DriverVehicleInfoModifyEvent event = new DriverVehicleInfoModifyEvent();
        event.setVehicleId(vehicleId);
        event.setDriverId(driverId);
        event.setAccountType(String.valueOf(operateFrom));
        event.setSupplierId(0L);
        event.setFromDrvGuide(true);
        driverVehicleInfoModifyEventHandler.handle(event);

    }


    /**
     * 司机车辆车型变更
     * @param message
     */
    private void driverVehicleTypeModify(Message message){
        int drvId = message.getIntProperty("drvId");
        int vehicleId = message.getIntProperty("vehicleId");
        String accountType = message.getStringProperty("accountType");
        long supplierId = message.getLongProperty("supplierId");
        if(drvId == 0 || vehicleId == 0){
            logger.info("driverId_vehicleId_null", LocalJsonUtils.toJson(message));
            return;
        }
        DriverVehicleTypeModifyEvent event = new DriverVehicleTypeModifyEvent();
        event.setVehicleId(Long.valueOf(vehicleId));
        event.setDriverId(Long.valueOf(drvId));
        event.setAccountType(accountType);
        event.setSupplierId(supplierId);
        event.setFromDrvGuide(false);
        driverVehicleTypeModifyEventHandler.handle(event);
    }
    /**
     * 司机更换车辆
     * @param message
     */
    private void driverVehicleChange(Message message){
        long supplierId = message.getLongProperty("supplierId");
        long drvId = message.getLongProperty("drvId");
        if(drvId == 0){
            logger.info("driverId_null", LocalJsonUtils.toJson(message));
            return;
        }
        String accountType = message.getStringProperty("accountType");
        //查询司机已接单 已派单 进行改派
        DriverVehicleChangeEvent event = new DriverVehicleChangeEvent();
        event.setDriverId(drvId);
        event.setAccountType(accountType);
        event.setSupplierId(supplierId);
        event.setFromDrvGuide(false);
        driverVehicleChangeEventHandler.handle(event);
    }
    /**
     * 司机信息变更
     * @param message
     */
    private void driverVehicleInfoModify(Message message){
        long drvId = message.getLongProperty("drvId");
        long vehicleId = message.getLongProperty("vehicleId");
        long supplierId = message.getLongProperty("supplierId");
        String accountType = message.getStringProperty("accountType");
        if(drvId == 0 || vehicleId == 0){
            logger.info("driverId_vehicleId_null", LocalJsonUtils.toJson(message));
            return;
        }
        DriverVehicleInfoModifyEvent event = new DriverVehicleInfoModifyEvent();
        event.setVehicleId(vehicleId);
        event.setDriverId(drvId);
        event.setAccountType(accountType);
        event.setSupplierId(supplierId);
        event.setFromDrvGuide(false);
        driverVehicleInfoModifyEventHandler.handle(event);
    }
}
