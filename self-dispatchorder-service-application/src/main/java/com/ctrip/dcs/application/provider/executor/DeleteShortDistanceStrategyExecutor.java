package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteShortDistanceStrategyResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DeleteShortDistanceStrategyExecutor extends AbstractRpcExecutor<DeleteShortDistanceStrategyRequestType, DeleteShortDistanceStrategyResponseType> implements Validator<DeleteShortDistanceStrategyRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(DeleteShortDistanceStrategyExecutor.class);

    @Autowired
    private ShortDistanceStrategyExeCmd strategyExeCmd;

    @Override
    public void validate(AbstractValidator<DeleteShortDistanceStrategyRequestType> validator) {
        validator.ruleFor("id").notNull().greaterThan(0L);
        validator.ruleFor("operator").notNull();
    }

    @Override
    public DeleteShortDistanceStrategyResponseType execute(DeleteShortDistanceStrategyRequestType requestType) {
        try {
            boolean success = strategyExeCmd.delete(requestType.getId());
            DeleteShortDistanceStrategyResponseType responseType = new DeleteShortDistanceStrategyResponseType();
            return success ? ServiceResponseUtils.success(responseType) : ServiceResponseUtils.fail(responseType);
        } catch (Exception e) {
            logger.error("DeleteShortDistanceStrategyExecutor_Exp", e);
            return ServiceResponseUtils.fail(new DeleteShortDistanceStrategyResponseType());
        }
    }
}
