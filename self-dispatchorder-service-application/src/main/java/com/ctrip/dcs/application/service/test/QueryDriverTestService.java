package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component("QueryDriverTestService")
public class QueryDriverTestService implements ITestDspOrderService{
    @Autowired
    private QueryDriverService queryDriverService;
    @Override
    public String test(Map<String, String> params) {
        String coopMode = params.get("coopMode");
        String transportGroupIds = params.get("transportGroupIds");
        String supplierIdStr = params.get("supplierId");
        Long supplierId = StringUtils.isNumeric(supplierIdStr) ? Long.valueOf(supplierIdStr) : 0L;

        Set<Long> transportGroupIdSet = new HashSet<>();
        for (String id : Arrays.asList(transportGroupIds.split(","))) {
            transportGroupIdSet.add(Long.valueOf(id));
        }
        if(!LocalStringUtils.isEmpty(coopMode)){
            //测试接口仅接送机用
            List<DriverVO> result = queryDriverService.queryDriver(transportGroupIdSet,Integer.valueOf(coopMode), ParentCategoryEnum.JNT, supplierId);
            return LocalJsonUtils.toJson(result);
        }

        Long transportGroupId =  new ArrayList<>(transportGroupIdSet).get(0);
        String driverName = params.get("driverName");
        String driverPhone = params.get("driverPhone");

        List<DriverVO> result = queryDriverService.queryDriversByCondition(Integer.valueOf(transportGroupId.toString()),driverName,driverPhone,ParentCategoryEnum.JNT, supplierId);
        return LocalJsonUtils.toJson(result);
    }
}
