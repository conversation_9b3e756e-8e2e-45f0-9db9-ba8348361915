package com.ctrip.dcs.application.command;

import cn.hutool.core.collection.CollectionUtil;
import com.ctrip.dcs.application.command.api.QuerySupplierCanChangeCarListCommand;
import com.ctrip.dcs.application.command.dto.CarInfo4VbkDTO;
import com.ctrip.dcs.application.command.dto.DriverCheckListResDTO;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryVehicleService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.VehicleVO;
import com.ctrip.dcs.domain.dsporder.value.SeriesVO;
import com.ctrip.dcs.domain.schedule.gateway.CarSeriesGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class QueryCanChangeCarListCmd {
    private static final Logger logger = LoggerFactory.getLogger(QueryCanChangeCarListCmd.class);

    @Autowired
    SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    CarSeriesGateway carSeriesGateway;

    @Autowired
    QueryVehicleService queryVehicleService;

    public List<CarInfo4VbkDTO> querySupplierCanChangeCarList(QuerySupplierCanChangeCarListCommand command){
        // 查询订单信息
        DspOrderVO dspOrder = selfOrderQueryGateway.queryDspOrder(command.getDspOrderId());
        if (null == dspOrder) {
            logger.info("VbkDriverCheckListExeCmd", "dspOrder is null");
            return Collections.emptyList();
        }
        if(!Integer.valueOf(command.getSupplierId().intValue()).equals(dspOrder.getSupplierId())){
            logger.info("VbkDriverCheckListExeCmd", "command supplierId is not order's supplierId,dspOrder supplierId is" +dspOrder.getSupplierId());
            return Collections.emptyList();
        }
        //查出供应商下没有绑定司机的车辆
        List<VehicleVO> vehicleVOList = queryVehicleService.queryUnBindDrvVehicleList(command.getSupplierId(), dspOrder.getCategoryCode(), command.getCityId(), dspOrder.getCarTypeId().longValue());
        if(CollectionUtil.isEmpty(vehicleVOList)){
            logger.info("VbkDriverCheckListExeCmd", "vehicleVOList is empty");
            return Collections.emptyList();
        }
        //如果是指定车系订单，则需要匹配指定车系的车辆
        if(CategoryCodeEnum.isCharterOrder(dspOrder.getCategoryCode().getType()) && dspOrder.isDesignatedVehicleOrder()){
            //查出订单用车城市和相应车型对应的车系
            SeriesVO series = carSeriesGateway.query(command.getCityId().intValue(), dspOrder.getCarTypeId());
            vehicleVOList = vehicleVOList.stream().filter(vehicleVO -> series.contains(vehicleVO.getCarSeriesId())).collect(Collectors.toList());
            logger.info("VbkDriverCheckListExeCmd", "DesignatedVehicleOrder filter vehicle list is" + JacksonSerializer.INSTANCE().serialize(vehicleVOList));
        }
        return convertDto(command, vehicleVOList);
    }

    private List<CarInfo4VbkDTO> convertDto(QuerySupplierCanChangeCarListCommand command, List<VehicleVO> vehicleVOList) {
        List<CarInfo4VbkDTO> carInfo4VbkDTOS = Lists.newArrayList();
        for (VehicleVO vehicleVO : vehicleVOList){
            CarInfo4VbkDTO carInfo4VbkDTO = new CarInfo4VbkDTO();
            carInfo4VbkDTO.setCityId(command.getCityId());
            carInfo4VbkDTO.setVehicleId(vehicleVO.getCarId());
            carInfo4VbkDTO.setVehicleTypeId(vehicleVO.getCarTypeId());
            carInfo4VbkDTO.setVehicleTypeName(vehicleVO.getCarTypeName());
            carInfo4VbkDTO.setVehicleLicense(vehicleVO.getCarLicense());
            carInfo4VbkDTO.setVehicleBrandId(vehicleVO.getCarBrandId());
            carInfo4VbkDTO.setVehicleBrandName(vehicleVO.getCarBrandName());
            carInfo4VbkDTO.setVehicleSeriesId(vehicleVO.getCarSeriesId());
            carInfo4VbkDTO.setVehicleSeriesName(vehicleVO.getCarSeriesName());
            carInfo4VbkDTO.setVehicleColorId(vehicleVO.getCarColorId());
            carInfo4VbkDTO.setVehicleColorName(vehicleVO.getCarColor());
            carInfo4VbkDTO.setTemporaryDispatchMark(vehicleVO.getTemporaryDispatchMark());
            carInfo4VbkDTOS.add(carInfo4VbkDTO);
        }
        return carInfo4VbkDTOS;
    }
}
