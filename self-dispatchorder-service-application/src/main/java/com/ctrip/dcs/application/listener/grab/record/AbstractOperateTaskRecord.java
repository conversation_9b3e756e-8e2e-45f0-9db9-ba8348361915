package com.ctrip.dcs.application.listener.grab.record;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKGrabTaskRecordDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository;
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.Timestamp;
import java.util.Objects;

public abstract class AbstractOperateTaskRecord implements OperateTaskRecordService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractOperateTaskRecord.class);

    @Autowired
    protected DateZoneConvertUtil dateZoneConvertUtil;
    @Autowired
    protected VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    protected VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository;
    @Autowired
    protected VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    @Qualifier("commonConfConfig")
    protected ConfigService map;

    protected static final String DISPLAY = "display";
    protected static final String UPDATE_DRIVER_NUM = "update_driver_num";
    protected static final String UPDATE_INIT_NUM = "update_init_num";
    protected static final String UPDATE_GRAB_LIMIT = "update_grab_limit";
    protected static final String UPDATE_REWARD_NUM = "update_reward_num";
    protected static final String UPDATE_REWARD_LIMIT = "update_reward_limit";
//    protected static final String DISPLAY_STR = "不展示";
//    protected static final String UPDATE_DRIVER_NUM_STR = "筛选司机数量：%s -> %s 个;";
//    protected static final String UPDATE_INIT_NUM_STR = "抢单初始金额：%s -> %s ;";
//    protected static final String UPDATE_GRAB_LIMIT_STR = "抢单截止时间：最晚派遣时间：%s -> %s 小时;";
//    protected static final String UPDATE_REWARD_NUM_STR = "抢单加价金额：%s -> %s ;";
//    protected static final String UPDATE_REWARD_LIMIT_STR = "加价生效时间：最晚派遣时间：%s -> %s 小时;";



    protected static final String DRIVER_NUM = "driver_num";
    protected static final String INIT_NUM = "init_num";
    protected static final String GRAB_LIMIT = "grab_limit";
    protected static final String REWARD_NUM = "reward_num";
    protected static final String REWARD_LIMIT = "reward_limit";

//    protected static final String DRIVER_NUM_STR = "筛选司机数量：%s 个;";
//    protected static final String INIT_NUM_STR = "抢单初始金额：%s %s ;";
//    protected static final String GRAB_LIMIT_STR = "抢单截止时间：最晚派遣时间：%s 小时;";
//    protected static final String REWARD_NUM_STR = "抢单加价金额：%s %s ;";
//    protected static final String REWARD_LIMIT_STR = "加价生效时间：最晚派遣时间：%s 小时;";
//    protected static final String FINISH_CONTENT_STR = "%s 笔订单抢单成功";
//    protected static final String CANCEL_CONTENT_STR = "订单数量：%s  笔订单取消抢单";

    protected static final String FINISH_CONTENT = "finish_content";
    protected static final String CANCEL_CONTENT = "cancel_content";

    protected static final String DISPLAY_STR = "";
    protected static final String DRIVER_NUM_STR = "";
    protected static final String INIT_NUM_STR = "";
    protected static final String GRAB_LIMIT_STR = "";
    protected static final String REWARD_NUM_STR = "";
    protected static final String REWARD_LIMIT_STR = "";
    protected static final String FINISH_CONTENT_STR = "";
    protected static final String CANCEL_CONTENT_STR = "";
    protected static final String UPDATE_DRIVER_NUM_STR = "";
    protected static final String UPDATE_INIT_NUM_STR = "";
    protected static final String UPDATE_GRAB_LIMIT_STR = "";
    protected static final String UPDATE_REWARD_NUM_STR = "";
    protected static final String UPDATE_REWARD_LIMIT_STR = "";

    @Override
    public void buildAndInsertRecord(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        try{
            assembleParam(vbkGrabTaskOperateDTO);
            VBKGrabTaskRecordDO vbkGrabTaskRecordDO = new VBKGrabTaskRecordDO();
            vbkGrabTaskRecordDO.setVbkGrabTaskId(vbkGrabTaskOperateDTO.getTaskId());
            vbkGrabTaskRecordDO.setSupplierId(vbkGrabTaskOperateDTO.getSupplierId());
            vbkGrabTaskRecordDO.setOperationType(vbkGrabTaskOperateDTO.getOperatorType().toString());
            vbkGrabTaskRecordDO.setOperatorName(vbkGrabTaskOperateDTO.getOperatorName());
            String localDateStr = getLocalTimeByCityId(vbkGrabTaskOperateDTO.getCityId(), vbkGrabTaskOperateDTO.getCurrentDateStr());
            vbkGrabTaskRecordDO.setDatachangeLasttime(Timestamp.valueOf(localDateStr));
            vbkGrabTaskRecordDO.setOperationContent(buildRecordContent(vbkGrabTaskOperateDTO));
            vbkGrabTaskRecordRepository.insert(vbkGrabTaskRecordDO);
        }catch (Exception ex){
            logger.error("AbstractOperateTaskRecord_buildAndInsertUpdateRecord", ex);
            throw ex;
        }
    }


    public String getLocalTimeByCityId(Integer cityId, String currentDateStr) {
        try{
            logger.info("AbstractOperateTaskRecord_getLocalTimeByCityId_enter", currentDateStr);
            if(Objects.isNull(cityId) || StringUtils.isBlank(currentDateStr)){
                return null;
            }
            String localDateStr = dateZoneConvertUtil.getLocalTimeByCityId(currentDateStr, DateZoneConvertUtil.BEIJING_CITY_ID, cityId);
            logger.info("AbstractOperateTaskRecord_getLocalTimeByCityId_exit", localDateStr);
            return localDateStr;
        }catch (Exception ex){
            logger.error("AbstractOperateTaskRecord_getLocalTimeByCityId", ex);
            return currentDateStr;
        }
    }

    protected abstract String buildRecordContent(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO);

    protected void assembleParam(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO){

    }

}
