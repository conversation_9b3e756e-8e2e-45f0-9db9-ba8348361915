package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ShutdownScheduleService {

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    public ScheduleEventType shutdown(String dspOrderId, ScheduleEventType eventType) {
        List<ScheduleDO> schedules = scheduleRepository.query(dspOrderId);
        if (CollectionUtils.isNotEmpty(schedules)) {
            for (ScheduleDO schedule : schedules) {
                shutdown(schedule, eventType);
            }
        }
        return eventType;
    }

    public ScheduleEventType shutdown(List<Long> scheduleIds, ScheduleEventType eventType) {
        for (Long scheduleId : scheduleIds) {
            ScheduleDO schedule = scheduleRepository.find(scheduleId);
            shutdown(schedule, eventType);
        }
        return eventType;
    }

    public ScheduleEventType shutdown(ScheduleDO schedule, ScheduleEventType eventType) {
        if (Objects.nonNull(schedule)) {
            ShutdownScheduleCommand command = new ShutdownScheduleCommand(schedule, eventType);
            shutdownScheduleExeCmd.execute(command);
        }
        return eventType;
    }
}
