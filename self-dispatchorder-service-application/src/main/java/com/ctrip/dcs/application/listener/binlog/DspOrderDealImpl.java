package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dsp_order")
public class DspOrderDealImpl implements BinlogDeal{

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        dspOrderRepository.clearCache(dspOrderId);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> dspOrderRepository.clearCache(dspOrderId));
    }
}
