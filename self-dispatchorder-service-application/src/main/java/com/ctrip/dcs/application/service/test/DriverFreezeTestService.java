package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverFreezeEvent;
import com.ctrip.dcs.application.event.impl.DriverFreezeEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverFreezeTestService")
public class DriverFreezeTestService implements ITestDspOrderService{
    @Autowired
    private DriverFreezeEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverFreezeEvent event = new DriverFreezeEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setAccountType(params.get("accountType"));
        event.setIsToSendType(params.get("isToSendType"));
        event.setStartTime(params.get("startTime"));
        event.setEndTime(params.get("endTime"));
        event.setChgAllOrder(params.get("chgAllOrder"));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
