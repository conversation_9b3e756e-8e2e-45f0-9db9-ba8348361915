package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO;
import com.ctrip.dcs.domain.schedule.value.QueryShortDistanceStrategyCondition;

import java.util.List;

public interface ShortDistanceStrategyService {
    void saveShortDistanceStrategy(ShortDistanceStrategyDO shortDistanceStrategyDO);

    List<ShortDistanceStrategyDO> query(QueryShortDistanceStrategyCondition condition);
}
