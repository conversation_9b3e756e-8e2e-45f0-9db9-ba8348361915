package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.AllowAuthCmd;
import com.ctrip.dcs.application.provider.converter.ReDispatchedConverter;
import com.ctrip.dcs.application.query.ReasonsQuery;
import com.ctrip.dcs.domain.common.value.ReasonDetailVO;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsAndAllowAuthRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsAndAllowAuthResponseType;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询改派原因及是否可以改派
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/5/16 19:39
 */
@Component
@ServiceLogTagPair(key = "userOrderId")
public class QueryReasonsAndAllowAuthExecutor extends AbstractRpcExecutor<QueryReasonsAndAllowAuthRequestType, QueryReasonsAndAllowAuthResponseType> implements Validator<QueryReasonsAndAllowAuthRequestType> {
    @Resource
    private AllowAuthCmd allowAuthCmd;

    @Resource
    private ReasonsQuery reasonsQuery;

    @Override
    public QueryReasonsAndAllowAuthResponseType execute(QueryReasonsAndAllowAuthRequestType req) {
        QueryReasonsAndAllowAuthResponseType responseType = new QueryReasonsAndAllowAuthResponseType();
        Result<Boolean> allowRes = allowAuthCmd.queryAllowAuth(req.getRoleId(), req.getUserOrderId());
        if (!allowRes.isSuccess() || allowRes.getData() == null) {
            return ServiceResponseUtils.fail(responseType, allowRes.getCode(), allowRes.getMsg());
        }
        Result<List<ReasonDetailVO>> queryReasonRes = reasonsQuery.queryReason(req.getRoleId(), req.getUserOrderId());
        if (!queryReasonRes.isSuccess() || queryReasonRes.getData() == null) {
            return ServiceResponseUtils.fail(responseType, queryReasonRes.getCode(), queryReasonRes.getMsg());
        }
        responseType.setIsAllowAuth(allowRes.getData());
        responseType.setReDispatchReasonList(ReDispatchedConverter.build(queryReasonRes.getData()));
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryReasonsAndAllowAuthRequestType> validator) {
        validator.ruleFor("drvId").notNull().greaterThan(0L);
        validator.ruleFor("roleId").notNull().greaterThan(0);
        validator.ruleFor("userOrderId").notNull().notEmpty();
    }

}