package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CustomerCheckSingleDriverCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf;
import com.ctrip.dcs.domain.dsporder.entity.CustomerCheckSingleDriverResDTO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ctrip.dcs.infrastructure.common.constants.CommonConstants.*;

@Component
public class CustomerCheckSingleDriverCmd {
    @Autowired
    private VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    protected ManualSubSkuConf manualSubSkuConf;

    @Autowired
    private CheckService checkService;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConf;

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    protected QueryDspOrderService orderQueryService;

    public CustomerCheckSingleDriverResDTO check(CustomerCheckSingleDriverCommand command) {
        CustomerCheckSingleDriverResDTO resDTO = new CustomerCheckSingleDriverResDTO();
        resDTO.setCheckCode(YesOrNo.NO.getCode());
        resDTO.setDriverId(Long.valueOf(command.getDriverId()));
        resDTO.setCheckResult(commonConf.get(NOT_ALLOWED_TO_BE_ASSIGNED));
        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(command.getUserOrderId());
        if (CollectionUtils.isEmpty(dspOrderDOS)) {
            resDTO.setCheckResult(ORDER_INFO_IS_EMPTY);
            return resDTO;
        }
        command.setDspOrderId(dspOrderDOS.get(0).getDspOrderId());
        // 查询订单信息
        DspOrderVO dspOrder = queryDspOrderService.query(command.getDspOrderId());
        // 查询司机信息
        Long supplierId = (dspOrder != null && dspOrder.getSupplierId() != null) ? dspOrder.getSupplierId().longValue() : 0L;
        DriverVO drvInfo = queryDriverService.queryDriver(Long.valueOf(command.getDriverId()), CategoryUtils.selfGetParentType(dspOrder), supplierId);
        if (drvInfo == null) {
            resDTO.setCheckResult(DRIVER_INFO_IS_EMPTY);
            return resDTO;
        }
        if (Long.valueOf(command.getDriverId()).equals(vbkDriverCheckListExeCmd.getDriverId(dspOrder))) {
            resDTO.setCheckResult(commonConf.get(CAN_NOT_ASSIGN_DRIVER_REPEATEDLY));
            return resDTO;
        }
        if (dspOrder.isSpecialSaleOrder()) {
            resDTO.setCheckResult(commonConf.get(SPECIAL_ORDERS_DO_NOT_ALLOW_ASSIGNMENTS));
            return resDTO;
        }
        List<TransportGroupVO> transportGroupInfos = drvInfo.getTransportGroups().stream()
                .filter(tg -> {
                    if (StringUtils.isBlank(command.getTransportGroupId())) {
                        return true;
                    }
                    return Integer.valueOf(command.getTransportGroupId()).equals(tg.getTransportGroupId().intValue());
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transportGroupInfos)) {
            resDTO.setCheckResult(commonConf.get(TRANSPORT_GROUP_INFO_IS_EMPTY));
            return resDTO;
        }
        // 创建duid
        DuidVO duid = initDuid(dspOrder);
        List<CheckModel> checkModels = checkDrivers(dspOrder, Lists.newArrayList(drvInfo), duid);
        List<CheckModel> pass = checkModels.stream()
                .filter(r -> Objects.nonNull(r) && Objects.nonNull(r.getCheckCode()))
                .filter(r -> r.getCheckCode().isPass())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pass)) {
            resDTO = convertDriverCheck(pass.get(0), duid);
        } else if (CollectionUtils.isNotEmpty(checkModels)) {
            resDTO = convertDriverCheck(checkModels.get(0), duid);
        }
        return resDTO;
    }

    private CustomerCheckSingleDriverResDTO convertDriverCheck(CheckModel checkModel, DuidVO duid) {
        CustomerCheckSingleDriverResDTO checkDTO = new CustomerCheckSingleDriverResDTO();
        CheckCode checkCode = checkModel.getCheckCode();
        checkDTO.setDriverId(checkModel.getModel().getDriver().getDriverId());
        checkDTO.setDrvUdl(checkModel.getModel().getDriver().getUdl());
        checkDTO.setTransportGroupId(checkModel.getModel().getTransportGroup().getTransportGroupId().intValue());
        checkDTO.setCheckCode(vbkDriverCheckListExeCmd.convrtToTaken(checkModel, duid));
        checkDTO.setCheckResult(checkCode.getDesc());
        checkDTO.setCheckResultCode(checkCode.getCode());
        return checkDTO;
    }

    private DuidVO initDuid(DspOrderVO order) {
        Integer subSkuId = manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, 0, SysConstants.AssignRole.SYS_ROLE_BOSS);
        return DuidVO.of(order.getDspOrderId(), subSkuId, DspType.OFFLINE_ASSIGN.getCode(), TakenType.BOSS_ASSIGN.getCode());
    }

    /**
     * 手动检查
     *
     * @param dspOrder
     * @param drivers
     */
    private List<CheckModel> checkDrivers(DspOrderVO dspOrder, List<DriverVO> drivers, DuidVO duid) {
        SubSkuVO subSku = subSkuRepository.find(duid.getSubSkuId());
        return checkService.check(new DspCheckCommand(dspOrder, subSku, drivers, duid));
    }

}
