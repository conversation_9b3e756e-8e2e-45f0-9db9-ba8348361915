package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSOtherAssignDriverExeCmd;
import com.ctrip.dcs.application.command.SaaSTripAssignDriverExeCmd;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.dsporder.repository.CkLogRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSCarInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSDriverInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOperatorInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOrderInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSSupplierInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * saas订单指派，包含了携程渠道订单和非携程渠道订单
 */
@Component
@ServiceLogTagPair(key = "order.dspOrderId", alias = "dspOrderId")
public class SaaSAssignDriverExecutor extends AbstractRpcExecutor<SaasAssignDriverRequestType, SaasAssignDriverResponseType> implements Validator<SaasAssignDriverRequestType> {



    private static final Logger logger = LoggerFactory.getLogger(SaaSAssignDriverExecutor.class);

    @Autowired
    private SaaSOtherAssignDriverExeCmd saaSOtherAssignDriverExeCmd;
    @Autowired
    private SaaSTripAssignDriverExeCmd saaSTripAssignDriverExeCmd;

    @Autowired
    private CkLogRepository ckLogRepository;


    @Override
    public SaasAssignDriverResponseType execute(SaasAssignDriverRequestType requestType) {
        SaasAssignDriverResponseType responseType = new SaasAssignDriverResponseType();
        long startTime = System.currentTimeMillis();
        long timeConsume =  0L;
        Date assignTimeBj = new Date();
        String resCode="200";
        String resMessage="success";
        SaaSOperateDriverCarCommand converter = null;
        try {
            if (!validRequestByOrderSourceCode(requestType)) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

            }
            converter = SaaSOperateDriverCarConverter.converter(requestType);
            if (Objects.equals(converter.getOrderSourceCode(), OrderSourceCodeEnum.TRIP)) {
                saaSTripAssignDriverExeCmd.execute(converter);
            } else {
                saaSOtherAssignDriverExeCmd.execute(converter);
            }
        } catch (BizException e) {
            resCode =e.getCode();
            resMessage=e.getMessage();
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("SaaSAssignDriverExecutor", ex);
            resCode ="500";
            resMessage="";
            return ServiceResponseUtils.fail(responseType);
        }finally {
            //记录ck日志
            timeConsume = System.currentTimeMillis() - startTime;
            if(converter != null && converter.getAssignResLogVO() != null){
                converter.getAssignResLogVO().setAssignResCode(resCode);
                converter.getAssignResLogVO().setAssignResDesc(resMessage);
                converter.getAssignResLogVO().setTimeConsumption(timeConsume);
                converter.getAssignResLogVO().setAssignTimeBj(assignTimeBj);
                ckLogRepository.saveAssignResLog(converter.getAssignResLogVO());
            }
        }

        return ServiceResponseUtils.success(responseType);
    }

    private boolean validRequestByOrderSourceCode(SaasAssignDriverRequestType requestType) {
        SaaSOrderInfo order = requestType.getOrder();


        Integer orderSourceCode = order.getOrderSourceCode();
        String dspOrderId = order.getDspOrderId();
        if(Objects.isNull(orderSourceCode) || LocalStringUtils.isEmpty(dspOrderId)){
            logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","orderSourceCode | dspOrderId is null");
            return false;
        }

        SaaSSupplierInfo supplier = requestType.getSupplier();
        Long supplierId = supplier.getSupplierId();
        if(Objects.isNull(supplierId)){
            logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","supplierId is null");
            return false;
        }


        SaaSOperatorInfo operator = requestType.getOperator();
        String operatorUserType = operator.getOperatorUserType();
        String operatorUserAccount = operator.getOperatorUserAccount();
        String operatorUserName = operator.getOperatorUserName();
        if(LocalStringUtils.isEmpty(operatorUserType) || LocalStringUtils.isEmpty(operatorUserAccount) || LocalStringUtils.isEmpty(operatorUserName)){
            logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","operatorUserType|operatorUserAccount|operatorUserName is null");
            return false;
        }

        SaaSDriverInfo driver = requestType.getDriver();

        Integer isSelfDriver = driver.getIsSelfDriver();
        if(Objects.isNull(isSelfDriver)){
            logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","isSelfDriver is null");
            return false;
        }

        SaaSCarInfo car = requestType.getCar();
        String driverId = driver.getDriverId();
        Integer carId = car.getCarId();
        if(OrderSourceCodeEnum.TRIP.getCode().equals(order.getOrderSourceCode())){
            if(Boolean.TRUE.equals(requestType.isNewProcess())){
                if(LocalStringUtils.isEmpty(driverId)){
                    logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","driverId is null");
                    return false;
                }
            }else {
                if(Objects.isNull(carId) || LocalStringUtils.isEmpty(driverId)){
                    logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","carId|driverId is null");
                    return false;
                }
            }

        }else {
            //非携程渠道，如果车、司机是自营的则传入id
            if(Objects.isNull(carId)){
                String carLicense = car.getCarLicense();
                if(LocalStringUtils.isEmpty(carLicense)){
                    logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","carLicense is null");
                    return false;
                }
            }
        }

        if(LocalStringUtils.isEmpty(driverId)){
            String driverName = driver.getDriverName();
            String driverMobile = driver.getDriverMobile();
            if(LocalStringUtils.isEmpty(driverMobile)
                    || (!Boolean.TRUE.equals(requestType.isNewProcess()) && LocalStringUtils.isEmpty(driverName))){
                logger.warn("SaaSAssignDriverExecutor_validRequestByOrderSourceCode","driverName|driverMobile is null");
                return false;
            }
        }

        if(isSelfDriver == 1 && !OrderSourceCodeEnum.TRIP.getCode().equals(order.getOrderSourceCode())){
            if(!Boolean.TRUE.equals(requestType.isNewProcess())){
                if(Objects.isNull(driver.getCityId())){
                    logger.warn("SaasChangeDriverExecutor_validRequestByOrderSourceCode","cityId is null");
                    return false;
                }
            }
        }
        return true;
    }


    @Override
    public void validate(AbstractValidator<SaasAssignDriverRequestType> validator) {
        validator.ruleFor("order").notNull();
        validator.ruleFor("driver").notNull();
        validator.ruleFor("car").notNull();
        validator.ruleFor("supplier").notNull();
        validator.ruleFor("operator").notNull();

    }
}