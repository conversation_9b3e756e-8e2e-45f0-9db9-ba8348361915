package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskTaskFinishEvent;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.UUID;

@Component
public class VBKGrabTaskTaskFinishJob {
    private static final Logger logger = LoggerFactory.getLogger(VBKGrabTaskTaskFinishJob.class);


    @Autowired
    private MessageProviderService messageProducer;

    private static final Integer PAGE_SIZE = 20;

    @QSchedule("vbk.grab.task.finish.job")
    public void execute(Parameter param) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        logger.info("VBKGrabTaskTaskFinishJob_start", JsonUtils.toJson(param));
        Integer pageSize = getProperty(param);
        logger.info("VBKGrabTaskTaskFinishJob_pageSize", JsonUtils.toJson(pageSize));
        messageProducer.send(new VBKGrabTaskTaskFinishEvent(pageSize));
    }




    private Integer getProperty(Parameter parameter) {
        Integer interval = parameter.getProperty("pageSize", Integer.class);
        return interval == null ? VBKGrabTaskTaskFinishJob.PAGE_SIZE : interval;
    }
}
