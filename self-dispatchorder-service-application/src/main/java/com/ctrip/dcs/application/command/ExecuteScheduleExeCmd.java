package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.application.command.validator.ExecuteScheduleValidator;
import com.ctrip.dcs.application.command.validator.ExecuteVbkScheduleValidator;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.ScheduleStatus;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.SopRecord;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.ExecuteScheduleEvent;
import com.ctrip.dcs.domain.schedule.event.WaitExecutedDispatchTaskEvent;
import com.ctrip.dcs.domain.schedule.event.WaitExecutedTaskEvent;
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.value.RewardVO;
import com.ctrip.dcs.domain.schedule.value.ScheduleRecordVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.adapter.limiter.DspRateLimiter;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ScheduleInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 */
@Component
public class ExecuteScheduleExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteScheduleExeCmd.class);

    private static final String DISTRIBUTED_LOCK_KEY = "DISTRIBUTED_LOCK_EXECUTE_SCHEDULE_";

    @Autowired
    private DistributedLockService distributedLockService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private ExecuteScheduleValidator validator;

    @Autowired
    private ExecuteVbkScheduleValidator vbkValidator;

    @Autowired
    private ScheduleRecordRepository scheduleRecordRepository;

    @Autowired
    private ScheduleRecordFactory scheduleRecordFactory;

    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private DspRateLimiter dspRateLimiter;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConf;

    public static final List<DspType> DISPATCHER_ASSIGN_LIST = Lists.newArrayList(DspType.DISPATCHER_ASSIGN, DspType.REFUSE_DISPATCHER_ASSIGN);

    public void execute(ExecuteScheduleCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(DISTRIBUTED_LOCK_KEY + command.getScheduleId());
        try {
            if (!lock.tryLock()) {
                // 调度正在执行，则返回。
                logger.info("schedule execute", "schedule is executing.schedule id:{}", command.getScheduleId());
                return;
            }
            // 查询调度
            ScheduleDO schedule = scheduleRepository.find(command.getScheduleId());
            // 查询要调度的派发单
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetailForSchedule(command.getDspOrderId(), true, false);
            // 查询调度任务
            List<ScheduleTaskDO> tasks = taskRepository.query(command.getScheduleId(), command.getDspOrderId());
            // 业务校验
            validate(command, dspOrder, schedule, tasks);
            // 执行
            doExecute(command, schedule, dspOrder, tasks);
        } catch (DistributedLockRejectedException e) {
            logger.warn("ExecuteScheduleExeCmdError", e);
            throw ErrorCode.SCHEDULE_EXECUTING_ERROR.getBizException();
        } catch (ValidateException e) {
            logger.warn(e);
        } finally {
            lock.unlock();
        }
    }

    private void validate(ExecuteScheduleCommand command, DspOrderVO dspOrder, ScheduleDO schedule, List<ScheduleTaskDO> tasks) throws ValidateException {
        if (Objects.equals(schedule.getType(), ScheduleType.VBK) || Objects.equals(schedule.getType(), ScheduleType.VBK_SYSTEM)) {
            VBKDriverGrabOrderDO vbkGrabOrder = vbkDriverGrabOrderRepository.queryBySupplierId(dspOrder.getDspOrderId(), dspOrder.getSupplierId());
            vbkValidator.validate(command, dspOrder, schedule, tasks, vbkGrabOrder);
        } else {
            validator.validate(command, dspOrder, schedule, tasks);
        }
    }

    private void doExecute(ExecuteScheduleCommand command, ScheduleDO schedule, DspOrderVO dspOrder, List<ScheduleTaskDO> tasks)  {
        try {
            if (ScheduleDO.isExecuting(tasks)) {
                // 有正在执行的未超时任务
                logger.info("ScheduleExecuting", "schedule is executing.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
                return;
            }
            if (!Objects.equals(command.getEvent(), ScheduleEventType.DSP_ORDER_BOOK) && !dspRateLimiter.tryAcquire(DspRateLimiter.SCHEDULE_TASK_RATE_LIMITER)) {
                logger.warn("DspRateLimit_Schedule", "schedule rate limiter is limited.schedule id:{}, order id:{}", command.getScheduleId(), command.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.EXECUTE_SCHEDULE_LIMIT_COUNT);
                return;
            }
            // 查询奖励
            RewardVO reward = queryDspReward(schedule, dspOrder);
            // 调度执行
            schedule.execute(dspOrder, tasks, reward);
            // 更新调度信息
            scheduleRepository.execute(schedule);
            // 调度记录
            ScheduleRecordVO record = scheduleRecordFactory.create(schedule, command.getEvent());
            scheduleRecordRepository.save(record);
            boolean res = tasks.stream().map(ScheduleTaskDO::getSubSku).filter(Objects::nonNull).map(SubSkuVO::getDspType).filter(Objects::nonNull).allMatch(this::matchDispatchDspType);
            logger.info("ExecuteScheduleExeCmd_doExecute_matchDispatchAssignDspType_res", JsonUtil.toJson(res));
            if(res){
                // 发送待执行任务消息
                sendWaitExecuteDispatchTaskMessage(schedule.getNext());
            }else {
                // 发送待执行任务消息
                sendWaitExecuteTaskMessage(schedule.getNext());
            }
        } catch (Exception e) {
            logger.error(e);
            throw ErrorCode.SCHEDULE_EXECUTING_ERROR.getBizException();
        } finally {
            sendExecuteScheduleDelayMessage(schedule, tasks, ScheduleEventType.SCHEDULE_DELAY_QMQ);
        }
    }

    private boolean matchDispatchDspType(DspType dspType){
        return DISPATCHER_ASSIGN_LIST.contains(dspType);
    }

    /**
     * 查询派发奖励
     * @param schedule
     * @param dspOrder
     * @return
     */
    public RewardVO queryDspReward(ScheduleDO schedule, DspOrderVO dspOrder) {
        if (Objects.equals(schedule.getType(), ScheduleType.VBK)) {
            VBKDriverGrabOrderDO grabOrder = vbkDriverGrabOrderRepository.queryBySupplierId(dspOrder.getDspOrderId(), dspOrder.getSupplierId());
            if (Objects.nonNull(grabOrder)) {
                logger.info("ScheduleExecuteInfo", "vbk driver grab order : {}", JacksonUtil.serialize(grabOrder));
                return new RewardVO(dspOrder.getDspAddPrice(), grabOrder.getRewardsAmount(), grabOrder.getGrabRewardTimeBJ(), commonConf.get(RewardVO.ADD_FEE_OFFLINE_CONFIG_KEY), schedule.getRewardStrategyList());
            }
        }
        return new RewardVO(dspOrder.getDspAddPrice(), commonConf.get(RewardVO.ADD_FEE_OFFLINE_CONFIG_KEY), schedule.getRewardStrategyList());
    }

    public void sendExecuteScheduleDelayMessage(ScheduleDO schedule, List<ScheduleTaskDO> tasks, ScheduleEventType event) {
        Long retry = schedule.retry(tasks);
        if (retry == null || retry == 0L) {
            logger.info("schedule no retry", "schedule id: {}", schedule.getScheduleId());
            return;
        }
        messageProducer.send(new ExecuteScheduleEvent(schedule.getDspOrderId(), schedule.getScheduleId(), schedule.getRound() + 1, event, retry * 1000));
    }

    public void sendWaitExecuteDispatchTaskMessage(List<ScheduleTaskDO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        for (ScheduleTaskDO task : tasks) {
            // 发送任务执行消息
            messageProducer.send(new WaitExecutedDispatchTaskEvent(task.getDspOrderId(), task.getScheduleId(), task.getTaskId(), task.getScheduleTaskId()));
        }
    }

    public void sendWaitExecuteTaskMessage(List<ScheduleTaskDO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        for (ScheduleTaskDO task : tasks) {
            // 发送任务执行消息
            messageProducer.send(new WaitExecutedTaskEvent(task.getDspOrderId(), task.getScheduleId(), task.getTaskId(), task.getScheduleTaskId()));
        }
    }
    
    
    public List<ScheduleInfo> queryScheduleList(String dspOrderId) {
        List<ScheduleDO> scheduleDOList = scheduleRepository.query(dspOrderId);
        return Optional.ofNullable(scheduleDOList).orElse(Lists.newArrayList()).stream().map(this::convert).collect(Collectors.toList());
    }
    
    public  ScheduleInfo convert(ScheduleDO scheduleDO) {
        ScheduleInfo scheduleInfo = new ScheduleInfo();
        scheduleInfo.setScheduleId(scheduleDO.getScheduleId());
        scheduleInfo.setDspOrderId(scheduleDO.getDspOrderId());
        scheduleInfo.setRound(scheduleDO.getRound());
        scheduleInfo.setScheduleType(Optional.ofNullable(scheduleDO.getType()).map(ScheduleType::getCode).orElse(null));
        scheduleInfo.setScheduleStatus(Optional.ofNullable(scheduleDO.getStatus()).map(ScheduleStatus::getCode).orElse(null));
        return scheduleInfo;
    }
    
}
