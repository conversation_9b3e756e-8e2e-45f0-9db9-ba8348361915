package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ExecuteTaskExeCmd;
import com.ctrip.dcs.application.command.api.ExecuteTaskCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.UUID;

/**
 * 派发任务已待执行
 * <AUTHOR>
 */
@Component
public class WaitExecutedTaskListener {

    private static final Logger logger = LoggerFactory.getLogger(WaitExecutedTaskListener.class);

    @Autowired
    private ExecuteTaskExeCmd executeTaskExeCmd;

    @QmqLogTag(tagKeys = {"taskId", "dspOrderId", "scheduleId", "scheduleTaskId"})
    @QmqConsumer(prefix = EventConstants.WAIT_EXECUTED_TASK_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            ExecuteTaskCommand command = MessageConverter.INSTANCE.toExecuteTaskCommand(message);
            executeTaskExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("WaitExecutedTaskListenerError", e);
            MetricsUtil.recordValue(MetricsConstants.EXECUTE_SCHEDULE_TASK_ERROR_COUNT);
        }
    }

}
