package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushPriorityRuleSoaDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRewardsRuleSoaDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class QueryGrabOrderPushRuleDetailExecutor extends AbstractRpcExecutor<QueryGrabOrderPushRuleDetailRequestType, QueryGrabOrderPushRuleDetailResponseType> implements Validator<QueryGrabOrderPushRuleDetailRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryGrabOrderPushRuleDetailExecutor.class);
    @Autowired
    private IGrabOrderPushRuleService grabOrderPushRuleService;
    @Override
    public QueryGrabOrderPushRuleDetailResponseType execute(QueryGrabOrderPushRuleDetailRequestType requestType) {
        try{
            GrabOrderPushRuleDTO result = grabOrderPushRuleService.queryById(requestType.getRuleId());
            GrabOrderPushRuleSoaDTO soaDTO = result==null?null:convertToSoaDto(result);
            QueryGrabOrderPushRuleDetailResponseType responseType = new QueryGrabOrderPushRuleDetailResponseType();
            responseType.setRule(soaDTO);
            return ServiceResponseUtils.success(responseType);
        }catch (Exception e){
            logger.error("QueryGrabOrderPushRuleDetailExecutorExp","QueryGrabOrderPushRuleDetailExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new QueryGrabOrderPushRuleDetailResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryGrabOrderPushRuleDetailRequestType> validator) {
        validator.ruleFor("ruleId").notNull();
    }

    /**
     * dto -> soaDto
     * @param resultDTO
     * @return
     */
    public GrabOrderPushRuleSoaDTO convertToSoaDto(GrabOrderPushRuleDTO resultDTO){
        GrabOrderPushRuleSoaDTO soaDTO = new GrabOrderPushRuleSoaDTO();
        soaDTO.setId(resultDTO.getId());
        soaDTO.setCityId(resultDTO.getCityId());
        soaDTO.setRuleType(resultDTO.getRuleType());
        soaDTO.setCategoryCode(resultDTO.getCategoryCode());
        soaDTO.setEndBookTime(resultDTO.getEndBookTime());
        soaDTO.setStartBookTime(resultDTO.getStartBookTime());
        soaDTO.setBookTime(resultDTO.getBookTime());
        soaDTO.setFixedPushTime(resultDTO.getFixedPushTime());
        soaDTO.setImmediatePushTime(resultDTO.getImmediatePushTime());
        soaDTO.setSupplierId(resultDTO.getSupplierId());
        soaDTO.setSupplierName(resultDTO.getSupplierName());
        soaDTO.setVehicleGroupIdList(resultDTO.getVehicleGroupIdList());
        if (StringUtils.isNotBlank(resultDTO.getPriority())) {
            soaDTO.setPriority(JacksonSerializer.INSTANCE().deserialize(resultDTO.getPriority(), GrabOrderPushPriorityRuleSoaDTO.class));
        }
        if (StringUtils.isNotBlank(resultDTO.getRewards())) {
            soaDTO.setRewards(JacksonSerializer.INSTANCE().deserialize(resultDTO.getRewards(), GrabOrderPushRewardsRuleSoaDTO.class));
        }
        return soaDTO;
    }
}
