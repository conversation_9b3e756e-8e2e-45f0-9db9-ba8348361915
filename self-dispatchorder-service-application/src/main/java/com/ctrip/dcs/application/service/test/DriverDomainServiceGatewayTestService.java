package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.google.common.base.Splitter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2024/9/4 15:08
 */
@Component("DriverDomainServiceGatewayTestService")
public class DriverDomainServiceGatewayTestService implements ITestDspOrderService {

    @Autowired
    private DriverDomainServiceGateway driverDomainServiceGateway;

    @Override
    public String test(Map<String, String> params) {

        String drvStrArr = params.get("drvIdArr");

        List<Long> driverIdList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(drvStrArr).stream().map(r -> Long.valueOf(r)).collect(Collectors.toList());

        driverDomainServiceGateway.query(driverIdList);

        return LocalJsonUtils.toJson("success");
    }
}
