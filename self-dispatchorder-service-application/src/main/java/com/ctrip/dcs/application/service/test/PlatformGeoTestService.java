package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.dsporder.entity.QueryDistanceReqDTO;
import com.ctrip.dcs.domain.dsporder.entity.QueryDistanceResDTO;
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway;
import com.ctrip.dcs.infrastructure.common.util.CacheUtil;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component("PlatformGeoTestService")
public class PlatformGeoTestService implements ITestDspOrderService {
    @Autowired
    private PlatformGeoServiceGateway service;
    @Autowired
    private CacheUtil cacheUtil;

    @Override
    public String test(Map<String, String> params) {//已测试

        String json = params.get("geoReq");
        if (StringUtils.isBlank(json)) {
            List<QueryDistanceReqDTO> query = Lists.newArrayList();
            QueryDistanceReqDTO dto = new QueryDistanceReqDTO();
            dto.setCityId(3L);
            dto.setCoordType("GCJ02");
            dto.setOriginLongitude(BigDecimal.valueOf(117.208873));
            dto.setOriginLatitude(BigDecimal.valueOf(39.135101));
            dto.setDestinationLongitude(BigDecimal.valueOf(117.178776));
            dto.setDestinationLatitude(BigDecimal.valueOf(39.103398));

            QueryDistanceReqDTO dto1 = new QueryDistanceReqDTO();
            dto1.setCityId(3L);
            dto1.setCoordType("GCJ02");
            dto1.setOriginLongitude(BigDecimal.valueOf(117.361697));
            dto1.setOriginLatitude(BigDecimal.valueOf(39.131592));
            dto1.setDestinationLongitude(BigDecimal.valueOf(117.20655));
            dto1.setDestinationLatitude(BigDecimal.valueOf(39.130333));

            QueryDistanceReqDTO dto3 = new QueryDistanceReqDTO();
            dto3.setCityId(3L);
            dto3.setCoordType("GCJ02");
            dto3.setOriginLongitude(BigDecimal.valueOf(117.361697));
            dto3.setOriginLatitude(BigDecimal.valueOf(39.131592));
            dto3.setDestinationLongitude(BigDecimal.valueOf(117.106561));
            dto3.setDestinationLatitude(BigDecimal.valueOf(39.127737));

            QueryDistanceReqDTO dto2 = new QueryDistanceReqDTO();
            dto2.setCityId(3L);
            dto2.setCoordType("GCJ02");
            dto2.setOriginLongitude(BigDecimal.valueOf(117.666362));
            dto2.setOriginLatitude(BigDecimal.valueOf(39.002146));
            dto2.setDestinationLongitude(BigDecimal.valueOf(117.361697));
            dto2.setDestinationLatitude(BigDecimal.valueOf(39.131592));

            query.add(dto);
            query.add(dto2);
            query.add(dto1);
            query.add(dto3);

            json = JacksonSerializer.INSTANCE().serialize(query);
        }
        List<String> key = Lists.newArrayList();
        List<String> value = Lists.newArrayList();
        try {
             cacheUtil.mSet(10, key, value);
        } catch (Exception e) {
            key.add("key1");
            value.add("value1");
        }
        key.add("key");
        value.add("value");
        cacheUtil.mSet(10, key, value);
        List<QueryDistanceReqDTO> queryReqDTOList = JacksonSerializer.INSTANCE().deserialize(json, new TypeReference<List<QueryDistanceReqDTO>>() {
        });
        List<QueryDistanceResDTO> res = service.batchQueryPreBackDistance(queryReqDTOList);
        if (!CollectionUtils.isEmpty(res)) {
            return JacksonSerializer.INSTANCE().serialize(res);
        }
        return "ok";
    }

//    public static void main(String[] args) {
//        List<QueryDistanceReqDTO> query = Lists.newArrayList();
//        QueryDistanceReqDTO dto = new QueryDistanceReqDTO();
//        dto.setCityId(3L);
//        dto.setCoordType("GCJ02");
//        dto.setOriginLongitude(BigDecimal.valueOf(117.208873));
//        dto.setOriginLatitude(BigDecimal.valueOf(39.135101));
//        dto.setDestinationLongitude(BigDecimal.valueOf(117.178776));
//        dto.setDestinationLatitude(BigDecimal.valueOf(39.103398));
//
//        QueryDistanceReqDTO dto1 = new QueryDistanceReqDTO();
//        dto1.setCityId(3L);
//        dto1.setCoordType("GCJ02");
//        dto1.setOriginLongitude(BigDecimal.valueOf(117.361697));
//        dto1.setOriginLatitude(BigDecimal.valueOf(39.131592));
//        dto1.setDestinationLongitude(BigDecimal.valueOf(117.20655));
//        dto1.setDestinationLatitude(BigDecimal.valueOf(39.130333));
//
//        QueryDistanceReqDTO dto3 = new QueryDistanceReqDTO();
//        dto3.setCityId(3L);
//        dto3.setCoordType("GCJ02");
//        dto3.setOriginLongitude(BigDecimal.valueOf(117.361697));
//        dto3.setOriginLatitude(BigDecimal.valueOf(39.131592));
//        dto3.setDestinationLongitude(BigDecimal.valueOf(117.106561));
//        dto3.setDestinationLatitude(BigDecimal.valueOf(39.127737));
//
//        query.add(dto);
//        query.add(dto1);
//        query.add(dto3);
//
//        String json = JacksonSerializer.INSTANCE().serialize(query);
//        System.out.println("json=" + json);
//
//    }

//String s = "[{\"originLatitude\":39.135101,\"originLongitude\":117.208873,\"destinationLatitude\":39.103398,\"destinationLongitude\":117.178776,\"cityId\":3,\"coordType\":\"GCJ02\"},{\"originLatitude\":39.131592,\"originLongitude\":117.361697,\"destinationLatitude\":39.130333,\"destinationLongitude\":117.20655,\"cityId\":3,\"coordType\":\"GCJ02\"},{\"originLatitude\":39.131592,\"originLongitude\":117.361697,\"destinationLatitude\":39.127737,\"destinationLongitude\":117.106561,\"cityId\":3,\"coordType\":\"GCJ02\"}]";
}
