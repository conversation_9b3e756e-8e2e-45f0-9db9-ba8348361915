package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.api.RefuseDispatcherGrabOrderCommand;
import com.ctrip.dcs.domain.common.constants.VbkOperateRecordConstant;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DispatcherGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class RefuseDispatcherGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(RefuseDispatcherGrabOrderExeCmd.class);

    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private DispatcherGrabOrderDao dispatcherGrabOrderDao;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;
    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;
    @Autowired
    VBKOperationRecordGateway vbkOperationRecordGateway;
    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;

    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private PurchaseSupplyOrderGateway purchaseSupplyOrderGateway;
    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private DispatcherGrabOrderRepository dispatcherGrabOrderRepository;

    public void execute(RefuseDispatcherGrabOrderCommand command) {
        if (SupplierConfirmSceneEnum.ORI_MODIFY.getScene().equals(command.getScene())) {
            refuseForOriModify(command);
        } else {
            refuse(command);
        }
    }

    private void refuse(RefuseDispatcherGrabOrderCommand command) {
        try {
            DispatcherGrabOrderDO order = dispatcherGrabOrderGateway.query(command.getDspOrderId(), command.getSupplierId());
            checkGrabOrder(order);
            order.refuse();
            int i = dispatcherGrabOrderDao.refuse(order.getId(), command.getSource());
            if (i != 1) {
                throw ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getBizException();
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("RefuseDispatcherGrabOrderExeCmdError", e);
            throw ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
    }

    private void refuseForOriModify(RefuseDispatcherGrabOrderCommand command) {
        try {
            if (Objects.isNull(command.getPkId())) {
                throw new IllegalArgumentException("pkId can not be null");
            }
            DispatcherGrabOrderDO order = dispatcherGrabOrderGateway.query(command.getPkId(), command.getSupplierId());
            checkGrabOrder(order);
            order.setRevokeReason("2");
            order.setRevokeReasonDesc("SUPPLIER_REFUSE");
            dispatcherGrabOrderRepository.refuseForOriModify(order.getId(), command.getSource(), order.getDspOrderId());
            boolean result = purchaseSupplyOrderGateway.confirmModifiedJntOrder(order.getUserOrderId(), order.getModifyVersion(), DispatcherGrabOrderStatusEnum.REFUSE.getCode(), null, null);
            if (!result) {
                throw ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getBizException();
            }
            //原单修改修改订单且需要商家同意-客服工作台通知
            WorkBenchLogMessage oriOrderModifyMessage = workBenchLogMessageFactory.createOriModifySupplierRefuseConfirmWorkBenchLog(order);
            workBenchLogGateway.sendWorkBenchLogMessage(oriOrderModifyMessage);
            insertVbkWorkLogOriOrderModifyRefuse(order);
            Cat.logEvent("dcs.self.dsp.refuseForOriModify", "success");
        } catch (SQLException e) {
            logger.error("RefuseDispatcherGrabOrderExeCmdError", e);
            Cat.logEvent("dcs.self.dsp.refuseForOriModify", "fail");
            throw new RuntimeException(e);
        }
    }

    private static void checkGrabOrder(DispatcherGrabOrderDO order) {
        if (Objects.isNull(order)) {
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        if (DispatcherGrabOrderStatusEnum.isFinalState(order.getGrabStatus().getCode())) {
            throw ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
    }

    private void insertVbkWorkLogOriOrderModifyRefuse(DispatcherGrabOrderDO order) {
        // 查询派发单
        DspOrderVO dspOrderVO = queryDspOrderService.query(order.getDspOrderId());
        Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderVO.getCityId());
        // 构建VO
        VBKOperationRecordVO recordVO = VBKOperationRecordVO.builder()
                .supplyOrderId(order.getDspOrderId())
                .userOrderId(order.getUserOrderId())
                .vendorOrderId("")
                .sysUserAccount("system")
                .operUserName("user")
                .operUserType("user")
                .operateType(VbkOperateRecordConstant.ORI_MODIFY_SUPPLIER_REFUSE)
                .operateName(businessTemplateInfoConfig.getVbkOperationName(VbkOperateRecordConstant.ORI_MODIFY_SUPPLIER_REFUSE))
                .operateLocalTime(localTimeNow.getTime())
                .recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW)
                .comment(commonConfConfig.getString("ori_modify_supplier_refuse_vbk"))
                .supplierId(order.getSupplierId().intValue())
                .orderSourceCode(String.valueOf(dspOrderVO.getOrderSourceCode()))
                .timeZone(dspOrderVO.getTimeZone() == null ? 0D : dspOrderVO.getTimeZone().doubleValue())
                .beforeOperateData(String.valueOf(dspOrderVO.getOrderStatus()))
                .afterOperateData(String.valueOf(dspOrderVO.getOrderStatus()))
                .build();
        vbkOperationRecordGateway.record(recordVO);
    }
}
