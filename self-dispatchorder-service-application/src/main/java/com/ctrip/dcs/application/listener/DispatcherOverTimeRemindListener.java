package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.TriggerTypeEnum;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.VbkAppPushService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DispatcherOverTimeAppVoicePushVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Date;

@Component
public class DispatcherOverTimeRemindListener {

    private static final Logger logger = LoggerFactory.getLogger(DispatcherOverTimeRemindListener.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private IvrCallService ivrCallService;

    @Autowired
    private VbkAppPushService timeOutNoDispatchService;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DISPATCHER_OVERTIME_REMIND_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message msg) {
//        UnifyLogger.info(LogIdConst.INFO, true, "监听并处理调度超时未指派订单延时检查任务消息[DispatcherRemindQmqListener.onDispatcherRemindMessage]处理开始。消息内容：", JsonUtil.toJson(msg));

//         检查重试次数 times是这个消息总重试次数
        if (msg.times() > 4) {
            logger.warn("dispatcherRemind_qmq_consume", "dispatcherRemind_qmq_consume retry " + msg.times() + ", messageId：" + msg.getMessageId());
            return;
        }
        // localRetries是连续本地重试了多少次的意思(防止死循环)
//        if (msg.localRetries() > 2) {
//            //60秒后再来看看能不能更新成功
//            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "Multiple local retries still failed");
//        }

        try {
            // 取得消息内容
            String dspOrderId = msg.getStringProperty("dspOrderId"); //派发单号
            String lastConfirmCarTime = msg.getStringProperty("lastConfirmCarTime"); //最晚派遣时间
            Date msgLastConfirmCarTime = DateUtil.parse(lastConfirmCarTime, DateUtil.DATETIME_FORMAT);
            int triggerType = msg.getIntProperty("triggerType");
            TriggerTypeEnum triggerTypeEnum = TriggerTypeEnum.getTriggerType(triggerType);
            int checkTimes = msg.getIntProperty("checkTimes"); // 所属轮次的第几次检查

            int msgOrderStatus = msg.getIntProperty("orderStatus");

            // 取得订单信息
            DspOrderVO dspOrder = queryDspOrderService.query(dspOrderId);
            if (dspOrder == null) {
                logger.warn("dispatcherRemind_qmq_consume", "dspOrder is null。 orderId：" + dspOrderId);
            } else {
                Date dbLastConfirmCarTime = dspOrder.getLastConfirmCarTime();
                //境外超时未派遣新增确认确认司机车辆时间校验
                if(TriggerTypeEnum.IGT_DISPATCHER_CONFIRM_USE_CAR_IVR.getCode() == triggerType){
                    if(dbLastConfirmCarTime == null || dbLastConfirmCarTime.compareTo(msgLastConfirmCarTime) != 0){
                        logger.info("dispatcher_over_time"+dspOrder.getUserOrderId(), "msgLastConfirmCarTime is change lastConfirmCarTime" + lastConfirmCarTime+" dbLastConfirmCarTime="+DateUtil.getStringDate(dbLastConfirmCarTime));
                    }else {
                        logger.info("dispatcher_over_time"+dspOrder.getUserOrderId(), "msgLastConfirmCarTime is equal lastConfirmCarTime" + lastConfirmCarTime+" dbLastConfirmCarTime="+DateUtil.getStringDate(dbLastConfirmCarTime));
                        //时间相等
                        ivrCallService.sendFirstIvrIgtOrder(dspOrder,msgOrderStatus,triggerTypeEnum);
                    }
                    return;
                }

                // 判断最晚确认司机车辆时间是否发生变化
                if (CategoryCodeEnum.isCharterOrder(dspOrder.getCategoryCode().getType()) ||  dbLastConfirmCarTime.compareTo(msgLastConfirmCarTime) == 0) {// 未变化
                    ivrCallService.handleDispatcherRemindQMQ(dspOrder, checkTimes, triggerTypeEnum);
                } else { // 已变化，处理终止，等待新的消息通知
                    logger.info("dispatcherRemind_qmq_consume", "order's dbLastConfirmCarTime changed ,do nothing,orderId：" + dspOrderId + ", msgLastConfirmCarTime：" + msgLastConfirmCarTime + ", dbLastConfirmCarTime：" + DateUtil.formatDate(dbLastConfirmCarTime, DateUtil.DATETIME_FORMAT));
                }
            }
        } catch (Exception e) {
            logger.warn("dispatcherRemind_qmq_consume", e);
            throw new NeedRetryException("dispatcherRemind_qmq_consume exception");
        }
    }


    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.QMQ_TIMEOUT_NO_DISPATCH_PUSH, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void timeoutNoDispatchPush(Message msg) {
//        UnifyLogger.info(LogIdConst.INFO, true, "监听并处理调度超时未指派订单延时检查任务消息[DispatcherRemindQmqListener.onDispatcherRemindMessage]处理开始。消息内容：", JsonUtil.toJson(msg));

        // 检查重试次数 times是这个消息总重试次数
        if (msg.times() > 1) {
            logger.warn("dispatcherRemind_qmq_consume", "dispatcherRemind_qmq_consume retry " + msg.times() + ", messageId：" + msg.getMessageId());
            return;
        }
        // localRetries是连续本地重试了多少次的意思(防止死循环)
//        if (msg.localRetries() > 1) {
//            //60秒后再来看看能不能更新成功
//            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "Multiple local retries still failed");
//        }

        try {
            //赋值推送参数
            DispatcherOverTimeAppVoicePushVO newOrderPushDTO=new DispatcherOverTimeAppVoicePushVO();
            newOrderPushDTO.setPushRounds(msg.getIntProperty("pushRounds"));
            newOrderPushDTO.setQmqSupplierId(msg.getIntProperty("supplierId"));
            newOrderPushDTO.setQmqLastConfirmCarTime(msg.getStringProperty("lastConfirmCarTime"));
            newOrderPushDTO.setPushPhase(msg.getIntProperty("pushPhase"));
            newOrderPushDTO.setDspOrderId(msg.getStringProperty("dspOrderId"));
            newOrderPushDTO.setUserOrderId(msg.getStringProperty("userOrderId"));
           timeOutNoDispatchService.timeOutNoDispatchAppVoicePush(newOrderPushDTO);
        } catch (Exception e) {
            logger.warn("dispatcherRemind_qmq_consume", e);
            throw new NeedRetryException("dispatcherRemind_qmq_consume exception");
        }
    }




}

