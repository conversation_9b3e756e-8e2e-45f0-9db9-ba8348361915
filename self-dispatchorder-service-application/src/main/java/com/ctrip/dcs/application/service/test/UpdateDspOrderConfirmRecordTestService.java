package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryVehicleService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.VehicleVO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component("updateDspOrderConfirmRecordTestService")
public class UpdateDspOrderConfirmRecordTestService implements ITestDspOrderService {

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private QueryVehicleService queryVehicleService;

    @Override
    public String test(Map<String, String> params) {
        String dspOrderConfirmRecordId = params.get("dspOrderConfirmRecordId");
        DspOrderConfirmRecordVO dspOrderConfirmRecord = dspOrderConfirmRecordRepository.find(Long.valueOf(dspOrderConfirmRecordId));
        CategoryCodeEnum categoryCodeEnum = CategoryCodeEnum.getByType(dspOrderConfirmRecord.getCategoryCode()).orElse(CategoryCodeEnum.ALL);
        VehicleVO vehicle = queryVehicleService.query(dspOrderConfirmRecord.getCarInfo().getCarId(), CategoryUtils.selfGetParentType(categoryCodeEnum));
        dspOrderConfirmRecord.getCarInfo().setEnergyType(vehicle.getEnergyType());
        dspOrderConfirmRecordRepository.update(dspOrderConfirmRecord);
        return LocalJsonUtils.toJson("success");
    }
}
