package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.google.common.base.Splitter;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

import static com.ctrip.dcs.domain.common.constants.EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_CANCEL;


@Component
public class VBKGrabOrderCancelListener extends AbstractUpdateOrderStatusAndFinishTask{

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabOrderCancelListener.class);

    @QmqConsumer(prefix = CAR_QBEST_ORDER_ORDERSTATE_ORDER_CANCEL, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "orderId", alias = "userOrderId")
    @QmqLogTagPair(key = "supplyOrderIds", alias = "supplyOrderId")
    public void userOrderIdCancel(Message msg) {
        try {
            if(super.validConsumerTimes(msg, MetricsConstants.VBK_GRAB_TASK_CANCEL_ERROR_COUNT) == 0){
                return;
            }
            String supplyOrderIds = msg.getStringProperty("supplyOrderIds");
            logger.info("VBKGrabOrderCancelListener_userOrderIdCancel_supplyOrderId", supplyOrderIds);
            if (Strings.isBlank(supplyOrderIds)) {
                return;
            }
            List<String> supplyOrderIdList = Splitter.on(",").splitToList(supplyOrderIds);
            for (String supplyOrderId : supplyOrderIdList) {
                super.dealMessageLogic(supplyOrderId, OrderStatusEnum.ORDER_CANCEL.getCode(), GrabTaskStatus.CANCEL.getCode(), null);
            }
        } catch (DistributedLockRejectedException e) {
            logger.warn("VBKGrabOrderCancelListener_userOrderIdCancel_tryLock");
            throw ErrorCode.LOCK_FAILED.getBizException();
        }catch (Exception ex){
            logger.error("VBKGrabOrderCancelListener_userOrderIdCancel", ex);
            throw new NeedRetryException("VBKGrabOrderCancelListener_userOrderIdCancel");
        }
    }


    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_CANCEL_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    public void dspOrderIdCancel(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_CANCEL_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderCancelListener_dspOrderIdCancel_dspOrderId", dspOrderId);
            if (Strings.isBlank(dspOrderId)) {
                return;
            }
            super.dealMessageLogic(dspOrderId, OrderStatusEnum.ORDER_CANCEL.getCode(), GrabTaskStatus.CANCEL.getCode(), null);
        }catch (Exception ex){
            logger.error("VBKGrabOrderCancelListener_userOrderIdCancel", ex);
            throw new NeedRetryException("VBKGrabOrderCancelListener_userOrderIdCancel");
        }
    }


    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    @QmqConsumer(prefix = EventConstants.OLD_DSP_ORDER_CANCEL_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    public void oldDspOrderIdCancel(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_CANCEL_ERROR_COUNT) == 0){
                return;
            }
            String oldSupplyOrderIds = message.getStringProperty("supplyOrderIds");
            logger.info("VBKGrabOrderCancelListener_oldDspOrderIdCancel_supplyOrderId", oldSupplyOrderIds);
            if (Strings.isBlank(oldSupplyOrderIds)) {
                return;
            }
            List<String> oldSupplyOrderIdList = Splitter.on(",").splitToList(oldSupplyOrderIds);
            for (String supplyOrderId : oldSupplyOrderIdList) {
                super.dealMessageLogic(supplyOrderId, OrderStatusEnum.ORDER_CANCEL.getCode(), GrabTaskStatus.CANCEL.getCode(), null);
            }
        }catch (Exception ex){
            logger.error("VBKGrabOrderCancelListener_oldDspOrderIdCancel", ex);
            throw new NeedRetryException("VBKGrabOrderCancelListener_oldDspOrderIdCancel");
        }
    }


    @QmqLogTagPair(key = "dspOrderId", alias = "dspOrderId")
    @QmqConsumer(prefix = EventConstants.VBK_GRAB_ORDER_CANCEL_TASK_BY_SCHEDULER_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    public void schedulerDspOrderIdCancel(Message message) {
        try {
            if(super.validConsumerTimes(message, MetricsConstants.VBK_GRAB_TASK_SCHEDULE_CANCEL_ERROR_COUNT) == 0){
                return;
            }
            String dspOrderId = message.getStringProperty("dspOrderId");
            logger.info("VBKGrabOrderCancelListener_schedulerDspOrderIdCancel_supplyOrderId", dspOrderId);
            if (Strings.isBlank(dspOrderId)) {
                return;
            }
            super.dealMessageLogic(dspOrderId, null, GrabTaskStatus.FINISH.getCode(), null);
        }catch (Exception ex){
            logger.error("VBKGrabOrderCancelListener_schedulerDspOrderIdCancel", ex);
            throw new NeedRetryException("VBKGrabOrderCancelListener_schedulerDspOrderIdCancel");
        }
    }

}
