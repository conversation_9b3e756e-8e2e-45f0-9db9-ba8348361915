package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.UpdateOrderSettleToDriverBeforeTakenCommand;
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.OrderVersionEnum;
import com.ctrip.dcs.infrastructure.adapter.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 更新调度确认订单的结算方式-刷数据用
 * <AUTHOR>
 */
@Component
public class UpdateOrderDetailSettlementExecutor extends AbstractRpcExecutor<UpdateOrderDetailSettlementRequestType, UpdateOrderDetailSettlementResponseType> implements Validator<UpdateOrderDetailSettlementRequestType> {

    @Autowired
    private UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd;

    @Autowired
    private SelfOrderQueryServiceProxy selfOrderQueryServiceProxy;

    @Override
    public UpdateOrderDetailSettlementResponseType execute(UpdateOrderDetailSettlementRequestType requestType) {
        List<OrderDetail> orders = queryDspOrderList(requestType.getDspOrderIds());
        if (CollectionUtils.isEmpty(orders)) {
            return ServiceResponseUtils.success(new UpdateOrderDetailSettlementResponseType());
        }
        for (OrderDetail dspOrder : orders) {
            updateOrderSettleToDriverBeforeTakenCmd.execute(new UpdateOrderSettleToDriverBeforeTakenCommand(
                            dspOrder.getBaseDetail().getUserOrderId(),
                            dspOrder.getBaseDetail().getDspOrderId(),
                            dspOrder.getBaseDetail().getOrderCarTypeId().intValue(),
                            dspOrder.getBaseDetail().getCategoryCode(),
                            dspOrder.getBaseDetail().getSupplierId().longValue(),
                            dspOrder.getBaseDetail().getServiceProviderId(),
                            dspOrder.getBaseDetail().getOrderSource(),
                            OrderVersionEnum.of(dspOrder.getBaseDetail().getOrderVersion()).getVersion(),
                            dspOrder.getBaseDetail().getSalesMode(),
                            dspOrder.getBaseDetail().getCityId()
                    )
            );
        }
        return ServiceResponseUtils.success(new UpdateOrderDetailSettlementResponseType());
    }

    public List<OrderDetail> queryDspOrderList(List<String> dspOrderIds) {
        QueryOrderListRequestType request = new QueryOrderListRequestType();
        request.setDspOrderIds(dspOrderIds);
        request.setStatusList(Lists.newArrayList(OrderStatusEnum.DISPATCH_CONFIRMED.getCode()));
        DataSwitch dataSwitch = new DataSwitch();
        dataSwitch.setBaseDetailSwitch(true);
        dataSwitch.setConfirmRecordSwitch(true);
        request.setDataSwitch(dataSwitch);
        QueryOrderListResponseType responseType = selfOrderQueryServiceProxy.queryOrderList(request);
        return Optional.ofNullable(responseType).map(QueryOrderListResponseType::getOrderList).orElse(Collections.emptyList());
    }

    @Override
    public void validate(AbstractValidator<UpdateOrderDetailSettlementRequestType> validator) {
        validator.ruleFor("dspOrderIds").notNull().notEmpty();
    }
}
