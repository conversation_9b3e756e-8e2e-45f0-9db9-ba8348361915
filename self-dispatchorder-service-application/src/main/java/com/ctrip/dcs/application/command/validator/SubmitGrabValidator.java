package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.SubmitBroadcastExeCmd;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class SubmitGrabValidator {

    private static final Logger logger = LoggerFactory.getLogger(SubmitBroadcastExeCmd.class);

    public void validate(GrabOrderDO grabOrder) throws ValidateException {
        if (Objects.isNull(grabOrder)) {
            // 老流程抢单，不做处理
            logger.info("submit grab order validate", "grab order is null");
            throw new ValidateException(ErrorCode.GRAB_ORDER_NULL_ERROR);
        }
        if (grabOrder.getExpire() < System.currentTimeMillis()) {
            logger.info("submit grab order validate", "grab order is expire.duid id:{}, driver id is {}", grabOrder.getDuid(), grabOrder.getDriverId());
            throw new ValidateException(ErrorCode.GRAB_ORDER_EXPIRE_ERROR);
        }
    }
}
