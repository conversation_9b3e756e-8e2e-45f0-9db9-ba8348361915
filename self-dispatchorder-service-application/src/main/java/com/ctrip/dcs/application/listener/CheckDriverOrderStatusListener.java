package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DriverOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderDriverIndexDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderSnapshotDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class CheckDriverOrderStatusListener {

    private static final Logger logger = LoggerFactory.getLogger(CheckDriverOrderStatusListener.class);

    @Autowired
    private DspOrderDao dspOrderDao;

    @Autowired
    private DriverOrderDao driverOrderDao;

    @Autowired
    private GrabDspOrderSnapshotDao grabDspOrderSnapshotDao;

    @Autowired
    private GrabDspOrderDriverIndexDao grabDspOrderDriverIndexDao;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_STATUS_DELAY_CHECK_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            if (StringUtils.isBlank(dspOrderId)) {
                logger.info("CheckDriverOrderStatusListenerInfo", "dspOrderId or driverOrderId is empty. dspOrderId:{},", dspOrderId);
                return;
            }
            DspOrderPO dspOrder = dspOrderDao.findByDspOrderId(dspOrderId);
            List<DrvOrderPO> driverOrderList = driverOrderDao.queryByDspOrderId(dspOrderId);
            if (Objects.isNull(dspOrder) || CollectionUtils.isEmpty(driverOrderList)) {
                logger.info("CheckDriverOrderStatusListenerInfo", "dspOrder or driverOrderList is empty. dspOrderId:{},", dspOrderId);
                return;
            }
            // 检查是否有未取消的司机单
            if (isDriverOrderNoCancel(dspOrder, driverOrderList)) {
                logger.warn("CheckDriverOrderStatusListener_DriverOrderNoCancel", "dspOrder is cancel, but driverOrder is not cancel. dspOrderId:{},", dspOrder.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.DRIVER_ORDER_NO_CANCEL);
            }
            // 检查是否有多个有效的司机单
            if (isDriverOrderMultiConfirmed(dspOrder, driverOrderList)) {
                logger.warn("CheckDriverOrderStatusListener_DriverOrderMultiConfirmed", "dspOrder is confirmed, but driverOrder is more than one confirmed. dspOrderId:{},", dspOrder.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.DRIVER_ORDER_MULTI_CONFIRMED);
            }
            // 检查是否有司机单状态不一致
            if (isNotEqualDriverOrderStatus(dspOrder, driverOrderList)) {
                logger.warn("CheckDriverOrderStatusListener_NotEqualDriverOrderStatus", "dspOrder is confirmed, but driverOrder is not confirmed. dspOrderId:{},", dspOrder.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.DRIVER_ORDER_STATUS_NOT_EQUAL);
            }
            GrabDspOrderSnapshotPO snapshot = grabDspOrderSnapshotDao.query(dspOrderId);
            List<GrabDspOrderDriverIndexPO> indexs = grabDspOrderDriverIndexDao.queryByDspOrderId(dspOrderId);
            // 检查是否有抢单映射状态不一致
            if (isNotEqualGrabOrderSnapshotStatus(dspOrder, snapshot, indexs)) {
                logger.warn("CheckDriverOrderStatusListener_NotEqualGrabOrderSnapshotStatus", "grab snapshot or index is illegal. dspOrderId:{},", dspOrder.getDspOrderId());
                MetricsUtil.recordValue(MetricsConstants.GRAB_ORDER_SNAPSHOT_STATUS_NOT_EQUAL);
            }
        } catch (Exception e) {
            logger.warn("CheckDriverOrderStatusListenerError", e);
            throw new NeedRetryException("CheckDriverOrderStatusListenerError");
        }
    }

    public boolean isNotEqualGrabOrderSnapshotStatus(DspOrderPO dspOrder, GrabDspOrderSnapshotPO snapshot, List<GrabDspOrderDriverIndexPO> indexs) {
        if (Objects.isNull(snapshot)|| CollectionUtils.isEmpty(indexs)) {
            return false;
        }
        Set<Integer> snapshotStatus = Sets.newHashSet(GrabDspOrderSnapshotStatusEnum.INIT.getCode(), GrabDspOrderSnapshotStatusEnum.GRAB.getCode());
        if (Objects.equals(GrabDspOrderSnapshotTypeEnum.SYSTEM.getCode(), snapshot.getGrabType())) {
            if (!OrderStatusEnum.isDispatching(dspOrder.getOrderStatus())) {
                // 派发单不是200状态，则快照和映射必须是无效状态
                return snapshotStatus.contains(snapshot.getGrabStatus()) || indexs.stream().anyMatch(index -> index.getIsValid() != 2);
            }
        } else {
            if (!OrderStatusEnum.isDispatcherConfirm(dspOrder.getOrderStatus())) {
                // 派发单不是220状态，则快照和映射必须是无效状态
                return snapshotStatus.contains(snapshot.getGrabStatus()) || indexs.stream().anyMatch(index -> index.getIsValid() != 2);
            }
        }
        return false;
    }

    public boolean isDriverOrderNoCancel(DspOrderPO dspOrder, List<DrvOrderPO> driverOrderList) {
        // dspOrder已取消，但是driverOrder未取消
        return OrderStatusEnum.isCancel(dspOrder.getOrderStatus())
                &&
                driverOrderList.stream().anyMatch(driverOrder -> !OrderStatusEnum.isCancel(driverOrder.getOrderStatus()));
    }

    public boolean isDriverOrderMultiConfirmed(DspOrderPO dspOrder, List<DrvOrderPO> driverOrderList) {
        // dspOrder已确认，但是driverOrder多个已确认
        return OrderStatusEnum.isAfterDispatcherConfirm(dspOrder.getOrderStatus())
                &&
                driverOrderList.stream().filter(driverOrder -> OrderStatusEnum.isAfterDispatcherConfirm(driverOrder.getOrderStatus())).count() != 1;
    }

    public boolean isNotEqualDriverOrderStatus(DspOrderPO dspOrder, List<DrvOrderPO> driverOrderList) {
        // dspOrder已确认，但是driverOrder未确认
        return OrderStatusEnum.isAfterDispatcherConfirm(dspOrder.getOrderStatus())
                &&
                driverOrderList.stream()
                        .filter(driverOrder -> Objects.equals(dspOrder.getDriverOrderId(), driverOrder.getDrvOrderId()))
                        .anyMatch(driverOrder -> !OrderStatusEnum.isAfterDispatcherConfirm(driverOrder.getOrderStatus()));
    }
}
