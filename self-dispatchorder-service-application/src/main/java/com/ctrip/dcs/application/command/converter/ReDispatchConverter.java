package com.ctrip.dcs.application.command.converter;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;

/**
 * 改派Converter
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/10/26 17:07
 */
public class ReDispatchConverter {

    public static RightAndPointCommand buildCommand(ReDispatchSubmitCommand command) {
        RightAndPointCommand resultCommand = new RightAndPointCommand();
        // 改派确认
        resultCommand.setPunishOperateType(ReassignTaskEnum.PunishOperateType.SUBMIT);
        // 司机ID
        resultCommand.setDrvId(command.getDrvId());
        // 是否使用新改派权益
        resultCommand.setNewRights(command.getNewRights());
        // 改派类型
        resultCommand.setReassignmentType(command.getReassignmentType());
        // 新请假权益
        resultCommand.setDriverLeaveRights(command.getDriverLeaveRights());
        resultCommand.setIsUseReDispatchRight(command.getIsUseReDispatchRight()==1?true:false);
        // 通用三件套
        resultCommand.setRoleId(command.getRoleId());
        resultCommand.setUserOrderId(command.getUserOrderId());
        resultCommand.setReasonDetailId(command.getReasonDetailId());
        return resultCommand;
    }

}