package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd;
import com.ctrip.dcs.application.provider.converter.ReDispatchSubmitConverter;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.ReasonDetailVO;
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 提交改派
 */
@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
public class ReDispatchSubmitExecutor extends AbstractRpcExecutor<ReDispatchSubmitRequestType, ReDispatchSubmitResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(ReDispatchSubmitExecutor.class);

    @Autowired
    private ReDispatchSubmitExeCmd reDispatchSubmitExeCmd;


    @Override
    public ReDispatchSubmitResponseType execute(ReDispatchSubmitRequestType requestType) {
        ReDispatchSubmitResponseType responseType = new ReDispatchSubmitResponseType();
        try {
            Result<ReasonDetailVO> result = reDispatchSubmitExeCmd.execute(ReDispatchSubmitConverter.converter(requestType));
            responseType.setReDispatchReason(ReDispatchSubmitConverter.converter(result.getData()));
            if (!result.isSuccess() || (result.getData()!=null && result.getData().getOrderVersion() == 4 && Integer.valueOf(result.getCode()) != 0)) {
                MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags(result.getCode()));
                return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
            }

        } catch (BizException e) {
            MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags(e.getCode()));
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("reDispatchSubmitException", ex);
            MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags("500"));
            return ServiceResponseUtils.fail(responseType);
        }
        MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags("200"));
        return ServiceResponseUtils.success(responseType);
    }

}
