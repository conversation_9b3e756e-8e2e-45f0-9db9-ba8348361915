package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.google.common.base.Splitter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhangZhen
 * @create 2024/9/4 15:07
 */
@Component("ConflictGatewayTestService")
public class ConflictGatewayTestService implements ITestDspOrderService{

    @Autowired
    private ConflictGateway conflictGateway;

    @Override
    public String test(Map<String, String> params) {
        String driverOrderId = params.get("driverOrderId");
        String dspOrderId = params.get("dspOrderId");
        String estimatedUseTimeBj = params.get("estimatedUseTimeBj");
        String predicServiceStopTimeBj = params.get("predicServiceStopTimeBj");

        String startTime = params.get("startTime");
        String endTime = params.get("endTime");

        String drvStrArr = params.get("drvIdArr");
        String categoryCode = params.get("categoryCode");
        boolean ignoreDelayOrder = "true".equals(params.get("ignoreDelayOrder"));

        DspOrderVO dspOrderVO = new DspOrderVO();

        dspOrderVO.setEstimatedUseTimeBj(DateUtil.parseDateStr2Date(estimatedUseTimeBj));
        dspOrderVO.setPredicServiceStopTimeBj(DateUtil.parseDateStr2Date(predicServiceStopTimeBj));
        dspOrderVO.setDspOrderId(dspOrderId);

        List<Long> driverIdList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(drvStrArr).stream().map(r -> Long.valueOf(r)).collect(Collectors.toList());

        conflictGateway.checkDriverOrderConflict(dspOrderVO, driverIdList, ignoreDelayOrder);

        conflictGateway.checkDriverInventory(driverIdList, dspOrderId, startTime, endTime, ignoreDelayOrder,categoryCode,driverOrderId, "");

        return LocalJsonUtils.toJson("success");
    }
}
