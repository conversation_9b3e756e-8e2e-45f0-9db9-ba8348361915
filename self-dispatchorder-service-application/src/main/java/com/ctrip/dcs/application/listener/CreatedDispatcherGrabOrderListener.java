package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.api.RemindDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dispatchergrab.RemindDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class CreatedDispatcherGrabOrderListener {

    private static final Logger logger = LoggerFactory.getLogger(CreatedDispatcherGrabOrderListener.class);

    @Autowired
    private RemindDispatcherGrabOrderExeCmd cmd;


    @QmqConsumer(prefix = EventConstants.CREATED_DISPATCHER_GRAB_ORDER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            RemindDispatcherGrabOrderCommand command = MessageConverter.INSTANCE.toRemindDispatcherGrabOrderCommand(message);
            cmd.execute(command);
        } catch (Exception e) {
            logger.error("CreateDispatcherGrabOrderListenerError", e);
        }
    }
}
