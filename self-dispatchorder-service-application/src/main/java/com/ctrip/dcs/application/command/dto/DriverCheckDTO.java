package com.ctrip.dcs.application.command.dto;

public class DriverCheckDTO {

    /**
     * 供应子订单id
     */
    private String subOrderId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 运力组id
     */
    private Integer transportGroupId;

    /**
     * 运力组名称
     */
    private String transportGroupName;

    /**
     * 司机电话区号
     */
    private String driverPhoneAreaCode;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车辆id
     */
    private String carId;

    /**
     * 车型id
     */
    private Integer carTypeId;

    /**
     * 车型名称
     */
    private String carTypeName;

    /**
     * 车牌号
     */
    private String carLicense;

    /**
     * 车辆描述
     */
    private String carDesc;

    /**
     * 车辆颜色
     */
    private String carColor;

    /**
     * 司机语言
     */
    private String driverLanguage;

    /**
     * 是否绑定车辆 NO-未绑定，YES-绑定
     */
    private String bindCar;

    /**
     * 工作时段
     */
    private String workTime;

    /**
     * 待执行订单数量
     */
    private Integer orderCounts;

    /**
     * -1-未通过，0-部分通过，1-通过
     */
    private Integer checkCode;

    /**
     * 检查结果code
     */
    private Integer checkResultCode;

    /**
     * 检查结果
     */
    private String checkResult;

    /**
     * duid
     */
    private String duid;

    /**
     * 车品牌车系拼接
     */
    private String carBrandSeriesName;

    private Boolean raisingPickUp = false;

    private Boolean childSeat = false;

    private Integer isAboard;

    private Integer driveLevel;

    public Boolean getRaisingPickUp() {
        return raisingPickUp;
    }

    public void setRaisingPickUp(Boolean raisingPickUp) {
        this.raisingPickUp = raisingPickUp;
    }

    public Boolean getChildSeat() {
        return childSeat;
    }

    public void setChildSeat(Boolean childSeat) {
        this.childSeat = childSeat;
    }

    public String getCarBrandSeriesName() {
        return carBrandSeriesName;
    }

    public void setCarBrandSeriesName(String carBrandSeriesName) {
        this.carBrandSeriesName = carBrandSeriesName;
    }

    public String getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Integer getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Integer transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public String getTransportGroupName() {
        return transportGroupName;
    }

    public void setTransportGroupName(String transportGroupName) {
        this.transportGroupName = transportGroupName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public Integer getCarTypeId() {
        return carTypeId;
    }

    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }

    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    public String getCarLicense() {
        return carLicense;
    }

    public void setCarLicense(String carLicense) {
        this.carLicense = carLicense;
    }

    public String getCarDesc() {
        return carDesc;
    }

    public void setCarDesc(String carDesc) {
        this.carDesc = carDesc;
    }

    public String getCarColor() {
        return carColor;
    }

    public void setCarColor(String carColor) {
        this.carColor = carColor;
    }

    public String getDriverLanguage() {
        return driverLanguage;
    }

    public void setDriverLanguage(String driverLanguage) {
        this.driverLanguage = driverLanguage;
    }

    public String getBindCar() {
        return bindCar;
    }

    public void setBindCar(String bindCar) {
        this.bindCar = bindCar;
    }

    public String getWorkTime() {
        return workTime;
    }

    public void setWorkTime(String workTime) {
        this.workTime = workTime;
    }

    public Integer getOrderCounts() {
        return orderCounts;
    }

    public void setOrderCounts(Integer orderCounts) {
        this.orderCounts = orderCounts;
    }

    public Integer getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(Integer checkCode) {
        this.checkCode = checkCode;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getDuid() {
        return duid;
    }

    public void setDuid(String duid) {
        this.duid = duid;
    }

    public String getDriverPhoneAreaCode() {
        return driverPhoneAreaCode;
    }

    public void setDriverPhoneAreaCode(String driverPhoneAreaCode) {
        this.driverPhoneAreaCode = driverPhoneAreaCode;
    }

    public String getCarId() {
        return carId;
    }

    public void setCarId(String carId) {
        this.carId = carId;
    }

    public Integer getCheckResultCode() {
        return checkResultCode;
    }

    public void setCheckResultCode(Integer checkResultCode) {
        this.checkResultCode = checkResultCode;
    }

    public Integer getIsAboard() {
        return isAboard;
    }

    public void setIsAboard(Integer isAboard) {
        this.isAboard = isAboard;
    }

    public Integer getDriveLevel() {
        return driveLevel;
    }

    public void setDriveLevel(Integer driveLevel) {
        this.driveLevel = driveLevel;
    }
}
