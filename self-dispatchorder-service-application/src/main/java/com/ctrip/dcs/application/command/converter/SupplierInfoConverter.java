package com.ctrip.dcs.application.command.converter;

import com.ctrip.dcs.domain.dsporder.entity.tool.BatchUpdateSupplierExecContext;
import com.ctrip.dcs.domain.dsporder.entity.tool.SupplierInfoDO;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO.DispatcherRecord;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupDetailSOAType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 供应商信息转换器
 *
 * <AUTHOR>
 * @date 2025-02-25 21:50:54
 */
public class SupplierInfoConverter {
    public static List<SupplierInfoDO> convert(List<SupplierInfo> supplierInfoDTOList, BatchUpdateSupplierExecContext context) {
        if (CollectionUtils.isEmpty(supplierInfoDTOList)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(Lists.transform(supplierInfoDTOList, it -> {
            SupplierInfoDO supplierInfoDO = new SupplierInfoDO();
            supplierInfoDO.setSpId(it.getSpId());
            supplierInfoDO.setSkuId(it.getSkuId());
            supplierInfoDO.setSupplierId(it.getSupplierId());
            supplierInfoDO.setUserOrderId(it.getUserOrderId());
            supplierInfoDO.setTransportGroupId(it.getTransportGroupId());
            supplierInfoDO.setDispatcherRecord(context.getTransportGroupMap().get(it.getTransportGroupId()));
            SupplierInfoDO persisted = context.getPersistSupplierInfoDOMap().get(it.getUserOrderId());
            supplierInfoDO.setId(Objects.requireNonNull(persisted).getId());
            supplierInfoDO.setCityId(persisted.getCityId());
            return supplierInfoDO;
        }));
    }

    public static DispatcherRecord convert(TransportGroupDetailSOAType tmsGroup) {
        DispatcherRecord dispatcherRecord = new DispatcherRecord();
        dispatcherRecord.setDispatcherEmail(tmsGroup.getDispatcherEmail());
        dispatcherRecord.setDispatcherName(tmsGroup.getTransportGroupName());
        dispatcherRecord.setDispatcherLanguage(tmsGroup.getDispatcherLanguage());
        dispatcherRecord.setDispatcherPhone(tmsGroup.getDispatcherPhone());
        dispatcherRecord.setDispatcherPhoneCode(tmsGroup.getIgtCode());
        dispatcherRecord.setDispatcherContactAccount("wechat");
        return dispatcherRecord;
    }

    public static List<DspOrderPO> convert(List<DspOrderPO> dspOrderPOList, List<SupplierInfo> supplierInfoList) {
        if (CollectionUtils.isEmpty(supplierInfoList)) {
            return Collections.emptyList();
        }
        Map<String, SupplierInfo> map = Maps.newHashMap();
        for (SupplierInfo supplierInfo : supplierInfoList) {
            map.put(supplierInfo.getUserOrderId(), supplierInfo);
        }
        for (DspOrderPO orderPO : dspOrderPOList) {
            var supplierInfoDO = map.get(orderPO.getUserOrderId());
            orderPO.setSupplierId(Math.toIntExact(supplierInfoDO.getSupplierId()));
            orderPO.setSpId(Math.toIntExact(supplierInfoDO.getSpId()));
        }
        return dspOrderPOList;
    }

}
