package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.CreateDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dto.DispatcherGrabOrderDTO;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDispatcherGrabOrdersRequestType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CreateDispatcherGrabOrderConverter {

    public static CreateDispatcherGrabOrderCommand toCreateDispatcherGrabOrderCommand(CreateDispatcherGrabOrdersRequestType request) {
        List<DispatcherGrabOrderDTO> orders = request.getOrders().stream().map(dto -> {
            DispatcherGrabOrderDTO order = new DispatcherGrabOrderDTO();
            order.setDspOrderId(dto.getDspOrderId());
            order.setUserOrderId(dto.getUserOrderId());
            order.setSupplierId(dto.getSupplierId());
            order.setTransportGroupId(dto.getTransportGroupId());
            order.setSubSkuId(dto.getSubSkuId());
            order.setDuid(dto.getDuid());
            order.setConfirmScene(dto.getUrgent());
            order.setLastConfirmTime(DateUtil.parse(dto.getLastConfirmTime(), DateUtil.DATETIME_FORMAT));
            order.setOrderSource(dto.getOrderSource());
            order.setModifyVersion(dto.getModifyVersion());
            order.setModifyRole(dto.getModifyRole());
            return order;
        }).collect(Collectors.toList());
        return new CreateDispatcherGrabOrderCommand(orders,request.getSupplierConfirmScene());
    }
}
