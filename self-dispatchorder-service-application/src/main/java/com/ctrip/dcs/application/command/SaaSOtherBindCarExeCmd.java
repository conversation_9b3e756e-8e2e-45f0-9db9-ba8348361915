package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.command.validator.SaaSOtherBindCarValidator;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.UpdateDspOrderConfirmRecordService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.framework.clogging.agent.util.StringUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class SaaSOtherBindCarExeCmd extends AbstractSaaSOperateExeCmd {

    @Autowired
    private SaaSOtherBindCarValidator saaSOtherBindCarValidator;
    @Autowired
    protected QueryDspOrderService queryDspOrderService;
    @Autowired
    private DriverOrderGateway driverOrderGateway;
    @Autowired
    private UpdateDspOrderConfirmRecordService updateDspOrderConfirmRecordService;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    private static final Logger logger = LoggerFactory.getLogger(SaaSOtherBindCarExeCmd.class);

    public String execute(SaaSOperateDriverCarCommand cmd) {
        logger.info("SaaSOtherBindCarExeCmd_enter", LocalJsonUtils.toJson(cmd));
        SaaSBusinessVO saaSBusinessVO = buildSaaSBusinessVO(cmd);
        logger.info("SaaSOtherBindCarExeCmd_buildSaaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
        //构建日志参数-vbk融合项目
        cmd.setAssignResLogVO(buildResLogVO(saaSBusinessVO,cmd));
        //业务校验
        this.validBusiness(cmd, saaSBusinessVO);
        //分布式锁
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, cmd.getDspOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            OperatorVO operatorVO = new OperatorVO(cmd.getOperatorUserAccount(), cmd.getOperatorUserName(), cmd.getOperatorUserType(), cmd.getTkeSource(), cmd.getCheckCode());
            DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
            // 原单修改待确认时不允许任何操作
            OrderModifyHelper.assertNotToBeConfirmed(dspOrderVO.getDspOrderId());
            Long supplierId = Long.valueOf(dspOrderVO.getSupplierId());
            HashMap<Object, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
            objectObjectHashMap.put("supplierId", LocalJsonUtils.toJson(supplierId));
            logger.info("SaaSOtherBindCarExeCmd_confirmCar_enter", LocalJsonUtils.toJson(objectObjectHashMap));
            //更新司机单
            driverOrderGateway.confirmCar(dspOrderVO.getDspOrderId(), dspOrderVO.getDriverOrderId(), null, saaSBusinessVO.getVehicleVO());
            // 更新应单记录的车辆和操作人信息
            updateDspOrderConfirmRecordService.update(dspOrderVO, saaSBusinessVO.getVehicleVO(), operatorVO);
            // VBK操作记录
            saveVBKOperateRecord(dspOrderVO, operatorVO);
            logger.info("SaaSOtherBindCarExeCmd_confirmCar_exit", LocalJsonUtils.toJson(objectObjectHashMap));
            return StringUtil.EMPTY;
        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } finally {
            lock.unlock();
        }
    }

    private void saveVBKOperateRecord(DspOrderVO dspOrder, OperatorVO operator) {
        try {
            VBKOperationRecordVO record = vbkOperationRecordFactory.createSaasBindCarOperationRecord(dspOrder, operator);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.error("saveVBKOperateRecordError", e);
        }
    }


    protected void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        saaSOtherBindCarValidator.validate(cmd, saaSBusinessVO);
    }


    protected SaaSBusinessVO buildSaaSBusinessVO(SaaSOperateDriverCarCommand cmd) {
        DspOrderVO dspOrderVO = queryDspOrderService.query(cmd.getDspOrderId());
        VehicleVO vehicleVO = getVehicleVO(cmd);
        if(Boolean.TRUE.equals(cmd.isNewProcess())){
            if(cmd.getCarId() != null && cmd.getCarId() != 0){
                vehicleVO = queryVehicleService.query(Long.valueOf(cmd.getCarId()), CategoryUtils.selfGetParentType(dspOrderVO));
            }
        }
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO(dspOrderVO, null, vehicleVO);
        return saaSBusinessVO;
    }


    private VehicleVO getVehicleVO(SaaSOperateDriverCarCommand cmd) {
        VehicleVO vehicleVO = SaaSOperateDriverCarConverter.converterVehicleVO(cmd);
        return vehicleVO;
    }




    @Override
    protected String assign(ScheduleTaskDO task, SaaSBusinessVO saaSBusinessVO, SaaSOperateDriverCarCommand cmd,
                             Long supplierId, DriverOrderVO driverOrder) {
        return StringUtil.EMPTY;
    }

}
