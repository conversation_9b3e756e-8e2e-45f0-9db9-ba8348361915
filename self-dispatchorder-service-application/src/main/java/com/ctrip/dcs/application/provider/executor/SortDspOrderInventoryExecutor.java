package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SortDspOrderInventoryExeCmd;
import com.ctrip.dcs.application.command.api.SortDspOrderInventoryCommand;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.self.dispatchorder.interfaces.SortDspOrderInventoryRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SortDspOrderInventoryResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SortDspOrderInventoryDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SortDspOrderInventoryExecutor extends AbstractRpcExecutor<SortDspOrderInventoryRequestType, SortDspOrderInventoryResponseType> implements Validator<SortDspOrderInventoryRequestType> {

    @Autowired
    private SortDspOrderInventoryExeCmd cmd;

    @Override
    public SortDspOrderInventoryResponseType execute(SortDspOrderInventoryRequestType requestType) {
        List<SortModel> list = cmd.execute(new SortDspOrderInventoryCommand(requestType.getDspOrderId(), requestType.getSubSkuId(), requestType.getDriverIds()));
        return toSortDspOrderInventoryResponseType(list);
    }

    @Override
    public void validate(AbstractValidator<SortDspOrderInventoryRequestType> validator) {
        validator.ruleFor("driverIds").notNull().notEmpty();
        validator.ruleFor("subSkuId").notNull();
        validator.ruleFor("dspOrderId").notNull().notEmpty();
    }

    public SortDspOrderInventoryResponseType toSortDspOrderInventoryResponseType(List<SortModel> list) {
        SortDspOrderInventoryResponseType responseType = new SortDspOrderInventoryResponseType();
        responseType.setSortList(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(list)) {
            List<SortDspOrderInventoryDTO> result = list.stream().map(this::toSortDspOrderInventoryDTO).filter(Objects::nonNull).collect(Collectors.toList());
            responseType.setSortList(result);
        }
        return responseType;
    }

    public SortDspOrderInventoryDTO toSortDspOrderInventoryDTO(SortModel model) {
        if (model == null || model.getModel() == null || model.getModel().getOrder() == null) {
            return null;
        }
        SortDspOrderInventoryDTO dto = new SortDspOrderInventoryDTO();
        dto.setDspOrderId(model.getModel().getOrder().getDspOrderId());
        if (model.getModel().getDriver() != null) {
            dto.setDriverId(model.getModel().getDriver().getDriverId());
        }
        if (model.getModel().getTransportGroup() != null) {
            dto.setTransportGroupId(model.getModel().getTransportGroup().getTransportGroupId());
        }
        dto.setScore(model.getScore());
        return dto;
    }
}
