package com.ctrip.dcs.application.command.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class BroadcastDTO {

    private Map<Long /*driver id*/, List<BroadcastDetailDTO>> driverSubjectOrders;

    public String getOrderId() {
        if (MapUtils.isEmpty(driverSubjectOrders)) {
            return StringUtils.EMPTY;
        }
        return driverSubjectOrders.values()
                .stream()
                .findFirst()
                .orElse(Collections.emptyList())
                .stream()
                .map(BroadcastDetailDTO::getPOrderId)
                .findFirst()
                .orElse(StringUtils.EMPTY);
    }

    public String getDuid() {
        if (MapUtils.isEmpty(driverSubjectOrders)) {
            return StringUtils.EMPTY;
        }
        return driverSubjectOrders.values()
                .stream()
                .findFirst()
                .orElse(Collections.emptyList())
                .stream()
                .map(BroadcastDetailDTO::getPduid)
                .findFirst()
                .orElse(StringUtils.EMPTY);
    }

    public Set<Long> getDriverIds() {
        if (MapUtils.isEmpty(driverSubjectOrders)) {
            return Collections.emptySet();
        }
        return driverSubjectOrders.keySet();
    }

    @Getter
    @Setter
    public static class BroadcastDetailDTO {

        private String pOrderId;

        private String sOrderId = "";

        private String orderMatchType = "SINGLE";

        private String pduid;

        private String sduid = "";

        private Long transportGroupId;

        private String transportGroupName;

        private Integer transportGroupMode;

        private Long supplierId;

        private String extendInfo;
    }
}
