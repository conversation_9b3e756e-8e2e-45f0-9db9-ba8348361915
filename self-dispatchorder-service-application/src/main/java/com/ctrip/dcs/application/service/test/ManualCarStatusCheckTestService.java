package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.item.impl.ManualCarStatusCheck;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.scm.sdk.domain.category.Category;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("ManualCarStatusCheckTestService")
public class ManualCarStatusCheckTestService implements ITestDspOrderService {
//    @Autowired
//    private ManualCarStatusCheck check;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

//    @Autowired
//    private CategoryRepository categoryRepository;


    @Override
    public String test(Map<String, String> params) {//已测试
//        DspModelVO dspModelVO = new DspModelVO();
//        CheckModel checkModel = new CheckModel(dspModelVO);
//        check.check(checkModel,null);
        Map<Object,Object> map = Maps.newHashMap();
//        Category oneByCategoryCode = categoryRepository.findOneByCategoryCode("airport_pickup","");
//        if (oneByCategoryCode != null) {
//            String s = oneByCategoryCode.getName();
//            map.put("categoryCodeName",s);
//        }
        map.put("vehicleModelName",workBenchLogMessageFactory.getVehicleModelName(Long.valueOf(params.get("carTypeId"))));
        return JacksonUtil.serialize(map);
    }
}
