package com.ctrip.dcs.application.service.grab;

import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao;
import com.ctrip.dcs.infrastructure.repository.TransportInventoryCaseServiceGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("defaultSubmitDispatcherGrabOrderService")
public class DefaultSubmitDispatcherGrabOrderService implements SubmitDispatcherGrabOrderService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultSubmitDispatcherGrabOrderService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private DispatcherGrabOrderDao dispatcherGrabOrderDao;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private TransportInventoryCaseServiceGateway transportInventoryCaseServiceGateway;

    @Override
    public void submit(SubmitDispatcherGrabOrderCommand command) {
        // 查询待抢单信息
        DispatcherGrabOrderDO order = dispatcherGrabOrderGateway.query(command.getDspOrderId(), command.getSupplierId());
        if (Objects.isNull(order)) {
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        if (order.isGrab()) {
            // 已抢，直接返回
            return;
        }
        if (!order.isInit()) {
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        // 查询派发单
        DspOrderVO dspOrderVO = queryDspOrderService.query(order.getDspOrderId());
        if (Objects.isNull(dspOrderVO)) {
            throw ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
        if (dspOrderVO.isConfirm()) {
            throw new BizException("503", "order has confirmed");
        }
        if (dspOrderVO.isCancel()) {
            throw new BizException("502", "order has canceled");
        }
        // 查询运力组
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(order.getTransportGroupId());
        if (Objects.isNull(transportGroup) || Objects.isNull(transportGroup.getTransportGroupMode()) || !transportGroup.getTransportGroupMode().isManual()) {
            throw new BizException("401", "transport group is not manual");
        }
        try {
            //新增运力组运力校验
            transportInventoryCaseServiceGateway.checkTransportInventory(dspOrderVO,order);
            order.submit(command.getOperatorUserId(), command.getOperatorUserName());
            dispatcherGrabOrderDao.submit(order.getId(),command.getSource());
            // 应单
            DispatcherConfirmVO confirmVO = DispatcherConfirmVO.builder()
                    .dspOrder(dspOrderVO)
                    .serviceProvider(new ServiceProviderVO(dspOrderVO.getSpId()))
                    .supplier(new SupplierVO(transportGroup.getSupplierId()))
                    .transportGroup(transportGroup)
                    .duid(DuidVO.of(order.getDuid()))
                    .event(OrderStatusEvent.DISPATCHER_GRAB)
                    .operator(new OperatorVO(command.getOperatorUserId(), command.getOperatorUserName(), "VBK", "", YesOrNo.NO.getCode()))
                    .build();
            //todo 新增抢单更新db
            confirmDspOrderService.confirm(confirmVO);
        } catch (OrderStatusException e) {
            logger.error("DefaultSubmitDispatcherGrabOrderStatusError", e);
            throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        } catch (SQLException e) {
            logger.error("DefaultSubmitDispatcherGrabOrderServiceError", e);
            throw ErrorCode.SUBMIT_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
    }
}
