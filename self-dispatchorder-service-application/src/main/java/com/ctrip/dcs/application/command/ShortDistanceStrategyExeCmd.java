package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SaveShortDistanceStrategyCommand;
import com.ctrip.dcs.application.command.dto.QueryShortDistanceStrategyResDTO;
import com.ctrip.dcs.application.command.dto.SaveShortDistanceStrategyResDTO;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO;
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDateDTO;
import com.ctrip.dcs.domain.dsporder.repository.ShortDistanceStrategyRepository;
import com.ctrip.dcs.domain.schedule.value.QueryShortDistanceStrategyCondition;
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService;
import com.ctrip.dcs.shopping.common.channelnumber.NChannelNumber;
import com.ctrip.dcs.shopping.common.channelnumber.NChannelNumberRepository;
import com.ctrip.dcs.vehicle.domain.repository.StandardVehicleModelRepository;
import com.ctrip.dcs.vehicle.domain.value.StandardVehicleModel;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.result.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ShortDistanceStrategyExeCmd {
    private static final Logger logger = LoggerFactory.getLogger(ShortDistanceStrategyExeCmd.class);
    @Autowired
    private ShortDistanceStrategyRepository repository;
    @Autowired
    @Qualifier("commonConfConfig")
    private BaseConfigService commonConfig;
    @Autowired
    private NChannelNumberRepository nChannelNumberRepository;
    @Autowired
    private StandardVehicleModelRepository standardVehicleModelRepository;


    public boolean checkShortDisOrder(Integer cityId,
                                      String categoryCode,
                                      Long vehicleGroupId,
                                      Long channelNumber,
                                      BigDecimal estimatedKm,
                                      Timestamp estimatedUseTimeBj) {
        // 查询城市生效的配置
        List<ShortDistanceStrategyDO> list = repository.find(cityId, 1);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("checkShortDisOrder", "cityId：{} has not valid config", cityId);
            return false;
        }
        // 查询车型组
        StandardVehicleModel standardVehicleModel = standardVehicleModelRepository.findOne(vehicleGroupId);
        if (standardVehicleModel == null) {
            logger.info("checkShortDisOrder", "query standardVehicleModel is null");
            return false;
        }
        // 查询渠道组
        NChannelNumber nChannelNumber = nChannelNumberRepository.findOne(channelNumber);
        if (nChannelNumber == null) {
            logger.info("checkShortDisOrder", "query nChannelNumber is null");
            return false;
        }
        // 过滤条件
        LocalDate estimatedUseDateBj = estimatedUseTimeBj.toLocalDateTime().toLocalDate();
        boolean result = list.stream().anyMatch(e -> CollectionUtils.isNotEmpty(e.getStartEndDateBjList())
                && e.getCategoryCodeList().contains(categoryCode)
                && e.getVehicleGroupIdList().contains(standardVehicleModel.getVehicleModelGroupId())
                && matchChannel(e.getChannelIdList(), nChannelNumber)
                && Objects.nonNull(estimatedKm) && estimatedKm.compareTo(BigDecimal.valueOf(e.getStartDis())) != -1 && estimatedKm.compareTo(BigDecimal.valueOf(e.getEndDis())) != 1
                && e.getStartEndDateBjList().stream().anyMatch(d -> !estimatedUseDateBj.isBefore(d.getStartDate()) && !estimatedUseDateBj.isAfter(d.getEndDate())));
        logger.info("checkShortDisOrder", "result：{}, param： cityId: {}, categoryCode: {}, vehicleModelGroupId: {}, nChannelNumber: {}, estimatedKm: {}, estimatedUseTimeBj: {}, StrategyList：{}", result,
                cityId, categoryCode, standardVehicleModel.getVehicleModelGroupId(), JacksonSerializer.INSTANCE().serialize(nChannelNumber), estimatedKm, estimatedUseTimeBj, JacksonSerializer.INSTANCE().serialize(list));
        return result;
    }

    /**
     * 根据配置的渠道组，和订单的渠道id 进行匹配
     *
     * @param channelStr     配置的渠道组（XX, XX-YY, XX-YY-ZZ）
     * @param nChannelNumber 渠道号详情
     * @return
     */
    private boolean matchChannel(List<String> channelStr, NChannelNumber nChannelNumber) {
        boolean result = channelStr.stream().anyMatch(e -> {
            List<Long> configChannelList = Arrays.stream(e.split("-")).map(Long::parseLong).toList();
            return switch (configChannelList.size()) {
                case 1 -> configChannelList.getFirst().equals(nChannelNumber.getPrimaryChannelId());
                case 2 -> configChannelList.get(1).equals(nChannelNumber.getSecondaryChannelId());
                case 3 -> configChannelList.get(2).equals(nChannelNumber.getTertiaryChannelId());
                default -> false; // 超过范围的索引
            };
        });
        logger.info("checkShortDisOrder_matchChannel", "matchChannel: {}", result);
        return result;
    }

    public Result<SaveShortDistanceStrategyResDTO> save(SaveShortDistanceStrategyCommand cmd) {
        // 校验生效时间是否重叠
        if (hasOverlap(cmd.getShortDistanceStrategy().getStartEndDateBjList())) {
            return Result.Builder.<SaveShortDistanceStrategyResDTO>newResult().fail().withCode(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_START_END_STATE_HAVE_OVERLAP.getCode())
                    .withMsg(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_START_END_STATE_HAVE_OVERLAP.getDesc()).build();
        }
        if (cmd.getShortDistanceStrategy().getId() == null) {
            // 最大配置数量限制
            int count = repository.countByCityId(cmd.getShortDistanceStrategy().getCityId().intValue());
            int maxSize = ObjectUtils.defaultIfNull(commonConfig.getInteger("shortDisStrategyConfigMaxSize"), 20);
            if (count >= maxSize) {
                return Result.Builder.<SaveShortDistanceStrategyResDTO>newResult().fail().withCode(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_OVER_MAX_SIZE_ERROR.getCode())
                        .withMsg(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_OVER_MAX_SIZE_ERROR.getDesc()).build();
            }
        }
        if (cmd.getShortDistanceStrategy().getState() == 1) {
            // 查询城市生效的配置
            List<ShortDistanceStrategyDO> list = repository.find(cmd.getShortDistanceStrategy().getCityId().intValue(), 1);
            if (CollectionUtils.isNotEmpty(list)) {
                // 唯一性校验（相同城市、渠道、服务类型、车型组 只有一条配置）
                Set<Long> conflictingIds = checkIsConflict(list, cmd.getShortDistanceStrategy());
                if (CollectionUtils.isNotEmpty(conflictingIds)) {
                    SaveShortDistanceStrategyResDTO resDTO = new SaveShortDistanceStrategyResDTO();
                    resDTO.setConflictingStrategyId(conflictingIds.iterator().next());
                    return Result.Builder.<SaveShortDistanceStrategyResDTO>newResult().fail().withCode(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.getCode())
                            .withMsg(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.getDesc()).withData(resDTO).build();
                }
            }
        }
        boolean success = repository.save(cmd.getShortDistanceStrategy());
        return success ? Result.Builder.<SaveShortDistanceStrategyResDTO>newResult().success().build() :
                Result.Builder.<SaveShortDistanceStrategyResDTO>newResult().fail().withCode(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_ERROR.getCode()).withMsg(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_ERROR.getDesc()).build();
    }

    /**
     * 生效时间是否重叠
     *
     * @param startEndDateBjList
     * @return
     */
    private boolean hasOverlap(List<ShortDistanceStrategyDateDTO> startEndDateBjList) {
        if (CollectionUtils.isNotEmpty(startEndDateBjList)) {
            // 排序
            startEndDateBjList.sort(Comparator.comparing(ShortDistanceStrategyDateDTO::getStartDate).thenComparing(ShortDistanceStrategyDateDTO::getEndDate));
            // 检查相邻段
            for (int i = 1; i < startEndDateBjList.size(); i++) {
                ShortDistanceStrategyDateDTO prev = startEndDateBjList.get(i - 1);
                ShortDistanceStrategyDateDTO curr = startEndDateBjList.get(i);
                // 如果后一段开始时间 < 前一段结束时间，则重叠了
                if (!curr.getStartDate().isAfter(prev.getEndDate())) {
                    return true;
                }
            }
        }
        return false;
    }

    private Set<Long> checkIsConflict(List<ShortDistanceStrategyDO> existingList, ShortDistanceStrategyDO newObj) {
        Set<Long> conflictingIds = new HashSet<>();
        for (ShortDistanceStrategyDO strategyDO : existingList) {
            if (strategyDO.getId().equals(newObj.getId())) {
                continue; // 排除自己
            }
            if (check(strategyDO, newObj)) {
                conflictingIds.add(strategyDO.getId());
            }
        }
        logger.info("conflictingIds：{}", conflictingIds);
        return conflictingIds;
    }

    private boolean check(ShortDistanceStrategyDO A, ShortDistanceStrategyDO B) {
        // 处理空值情况
        List<String> aCategories = A.getCategoryCodeList() != null ? A.getCategoryCodeList() : Collections.emptyList();
        List<String> bCategories = B.getCategoryCodeList() != null ? B.getCategoryCodeList() : Collections.emptyList();
        List<Long> aVehicles = A.getVehicleGroupIdList() != null ? A.getVehicleGroupIdList() : Collections.emptyList();
        List<Long> bVehicles = B.getVehicleGroupIdList() != null ? B.getVehicleGroupIdList() : Collections.emptyList();
        List<String> aChannels = A.getChannelIdList() != null ? A.getChannelIdList() : Collections.emptyList();
        List<String> bChannels = B.getChannelIdList() != null ? B.getChannelIdList() : Collections.emptyList();

        // 品类校验：至少有一个共同品类
        Set<String> commonCategories = new HashSet<>(aCategories);
        commonCategories.retainAll(bCategories);
        if (commonCategories.isEmpty()) return false;

        // 车组校验：至少有一个共同车组
        Set<Long> commonVehicles = new HashSet<>(aVehicles);
        commonVehicles.retainAll(bVehicles);
        if (commonVehicles.isEmpty()) return false;

        // 渠道校验：存在渠道包含关系
        for (String channelA : aChannels) {
            for (String channelB : bChannels) {
                if (isChannelRelation(channelA, channelB)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断两个渠道是否存在包含关系（相同或父子级）
     * 判断一个列表是否是另一个的前缀：
     * 示例：[101] 是 [101, 2] 的前缀，[101, 2] 是 [101, 2, 12] 的前缀。
     *
     * @param a
     * @param b
     * @return
     */
    private boolean isChannelRelation(String a, String b) {
        String[] arrA = a.split("-");
        String[] arrB = b.split("-");

        int minLen = Math.min(arrA.length, arrB.length);
        for (int i = 0; i < minLen; i++) {
            if (!arrA[i].equals(arrB[i])) {
                return false;
            }
        }
        return true;
    }

    public boolean delete(Long id) {
        return repository.delete(id);
    }

    public QueryShortDistanceStrategyResDTO query(QueryShortDistanceStrategyCondition condition) {
        List<ShortDistanceStrategyDO> result = repository.find(condition);
        // 内存过滤
        if (CollectionUtils.isNotEmpty(condition.getCategoryCodeList())) {
            result = result.stream().filter(e -> condition.getCategoryCodeList().stream().anyMatch(e.getCategoryCodeList()::contains)
            ).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(condition.getVehicleGroupIdList())) {
            result = result.stream().filter(e -> condition.getVehicleGroupIdList().stream().anyMatch(e.getVehicleGroupIdList()::contains)
            ).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(condition.getChannelIdList())) {
            result = result.stream().filter(e -> condition.getChannelIdList().stream().anyMatch(c -> e.getChannelIdList().stream().anyMatch(ch -> isChannelRelation(ch, c)))
            ).collect(Collectors.toList());
        }
        // 状态有效排前面，创建时间倒序
        result = Optional.ofNullable(result).orElse(Collections.emptyList()).stream()
                .sorted(Comparator.comparing(ShortDistanceStrategyDO::getState, Comparator.reverseOrder())
                        .thenComparing(ShortDistanceStrategyDO::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
        return page(condition, result);
    }

    private QueryShortDistanceStrategyResDTO page(QueryShortDistanceStrategyCondition condition, List<ShortDistanceStrategyDO> list) {
        int pageNo = Math.max(condition.getPaginator().getPageNo(), 1);
        int pageSize = condition.getPaginator().getPageSize() > 0 ? condition.getPaginator().getPageSize() : 20; // 默认页大小

        PaginationDTO pagination = new PaginationDTO();
        pagination.setTotalSize(list.size());
        pagination.setPageSize(pageSize);
        pagination.setTotalPages((int) Math.ceil(list.size() * 1.0 / pageSize));
        pagination.setPageNo(Math.min(pageNo, pagination.getTotalPages()));  // 不能超过总页数

        int fromIndex = (pagination.getPageNo() - 1) * pagination.getPageSize();
        fromIndex = Math.min(Math.max(fromIndex, 0), list.size());
        int toIndex = Math.min(fromIndex + pagination.getPageSize(), list.size());

        return new QueryShortDistanceStrategyResDTO(list.subList(fromIndex, toIndex), pagination);
    }
}
