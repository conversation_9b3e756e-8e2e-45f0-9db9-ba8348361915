package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.IdempotentCheckService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.ExecuteScheduleEvent;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 调度重试
 * <AUTHOR>
 */
@Component
public class RetryScheduleJob extends BaseJob {

    private static final Logger logger = LoggerFactory.getLogger(RetryScheduleJob.class);

    /**
     * 默认10分钟
     */
    private static final int DEFAULT_BEGIN_INTERVAL = 60 * 60;

    private static final int DEFAULT_END_INTERVAL = 60 * 10;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private IdempotentCheckService idempotentCheckService;

    @QSchedule("com.ctrip.dcs.dsporder.schedule.retry.job")
    public void execute(Parameter parameter) throws Exception {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer beginInterval = getProperty(parameter, "beginInterval", DEFAULT_BEGIN_INTERVAL);
        Integer endInterval = getProperty(parameter, "endInterval", DEFAULT_END_INTERVAL);
        Date beginTime = DateUtil.addSeconds(new Date(), -beginInterval);
        Date endTime = DateUtil.addSeconds(new Date(), -endInterval);
        List<ScheduleDO> schedules = scheduleRepository.queryBefore(beginTime, endTime);
        sendMessage(schedules);
    }

    public void sendMessage(List<ScheduleDO> schedules) {
        if (CollectionUtils.isEmpty(schedules)) {
            logger.info("RetryScheduleJobInfo_Empty", "schedules is empty");
            return;
        }
        for (ScheduleDO schedule : schedules) {
            Long delay = executeScheduleDelayTime(schedule);
            if (delay == null) {
                logger.info("RetryScheduleJobInfo_DelayTimeNull", "scheduleId:{}, dspOrderId:{}", schedule.getScheduleId(), schedule.getDspOrderId());
                continue;
            }
            if (delay > 0) {
                boolean notProcessed = idempotentCheckService.isNotProcessed("schedule_retry_job_" + schedule.getScheduleId(), delay);
                if (!notProcessed) {
                    logger.info("RetryScheduleJobInfo_Processed", "scheduleId:{}, dspOrderId:{}", schedule.getScheduleId(), schedule.getDspOrderId());
                    continue;
                }
            }
            logger.info("RetryScheduleJobInfo_Retry", "scheduleId:{}, dspOrderId:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            messageProducer.send(new ExecuteScheduleEvent(schedule.getDspOrderId(), schedule.getScheduleId(), schedule.getRound() + 1, ScheduleEventType.SCHEDULE_RETRY_JOB, delay * 1000));
        }
    }

    /**
     * 计算调度重试延迟时间
     * 防止梯度降级之后，调度重复执行
     * @param schedule
     * @return 延迟时间-秒
     */
    public Long executeScheduleDelayTime(ScheduleDO schedule) {

        List<ScheduleTaskDO> tasks = scheduleTaskRepository.query(schedule.getScheduleId(), schedule.getDspOrderId());
        Long retry = schedule.retry(tasks);
        if (retry == null || retry <= 0) {
            // 不重试
            return null;
        }
        double seconds = DateUtil.seconds(schedule.getExecuteTime(), new Date());
        return (long) Math.max(retry - seconds, 0);
    }
}
