package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSCancelDspOrderExeCmd;
import com.ctrip.dcs.application.provider.converter.SaaSCancelDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * Created by xingxing.yu on 2023/2/16.
 * 取消派发单
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class SaasCancelDspOrderExecutor extends AbstractRpcExecutor<SaasCancelDspOrderRequestType, SaasCancelDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(SaasCancelDspOrderExecutor.class);

    @Autowired
    private SaaSCancelDspOrderExeCmd saaSCancelDspOrderExeCmd;


    @Override
    public SaasCancelDspOrderResponseType execute(SaasCancelDspOrderRequestType requestType) {
        SaasCancelDspOrderResponseType responseType = new SaasCancelDspOrderResponseType();
        if (Objects.isNull(requestType.getSupplierId())
                ||StringUtils.isEmpty(requestType.getVbkOrderId())
                || StringUtils.isEmpty(requestType.getDspOrderId())
                || StringUtils.isEmpty(requestType.getOrderCancelReasonDetail())) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            saaSCancelDspOrderExeCmd.execute(SaaSCancelDspOrderConverter.converter(requestType));
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("cancelDspOrderException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


}
