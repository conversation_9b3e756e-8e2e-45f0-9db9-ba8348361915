package com.ctrip.dcs.application.command;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.ScheduleStatus;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.event.CreatedScheduleEvent;
import com.ctrip.dcs.domain.schedule.factory.ScheduleFactory;
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.adapter.carconfig.VbkDriverGrabDspStrategyConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;


/**
 * 创建调度
 *
 * <AUTHOR>
 */
@Component
public class CreateScheduleExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CreateScheduleExeCmd.class);

    @Autowired
    private ScheduleFactory scheduleFactory;

    @Autowired
    private QueryDspOrderService dspOrderService;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleRecordRepository scheduleRecordRepository;

    @Autowired
    private ScheduleRecordFactory scheduleRecordFactory;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private VbkDriverGrabDspStrategyConfig vbkDriverGrabDspStrategyConfig;

    public void execute(CreateScheduleCommand command) {
        if (hasExecutingSchedule(command.getDspOrderId())) {
            // 有正在执行的调度
            logger.info("CreateScheduleExeCmdInfo", "has executing schedule, dspOrderId: " + command.getDspOrderId());
            return;
        }
        // 查询派发单
        DspOrderVO order = dspOrderService.queryOrderDetailForSchedule(command.getDspOrderId(), true, true);
        Assert.notNull(order);
        // 策略id
        Long strategyId = getStrategyId(command, order);
        // 创建调度
        ScheduleDO schedule = scheduleFactory.create(order, command.getScheduleType(), strategyId);
        scheduleRepository.save(schedule);
        // 发送调度已创建事件
        messageProducer.send(new CreatedScheduleEvent(schedule.getDspOrderId(), schedule.getScheduleId(), command.getEvent()));
    }

    public Long getStrategyId(CreateScheduleCommand command, DspOrderVO order) {
        if (Objects.equals(command.getScheduleType(), ScheduleType.SYSTEM)) {
            return order.parseStrategyId();
        }
        return vbkDriverGrabDspStrategyConfig.getStrategyId(order, command.getScheduleType().getCode());
    }

    public boolean hasExecutingSchedule(String dspOrderId) {
        List<ScheduleDO> schedules = scheduleRepository.query(dspOrderId);
        if (CollectionUtils.isNotEmpty(schedules)) {
            for (ScheduleDO schedule : schedules) {
                if (schedule.getStatus() == ScheduleStatus.EXECUTING) {
                    return true;
                }
            }
        }
        return false;
    }

}
