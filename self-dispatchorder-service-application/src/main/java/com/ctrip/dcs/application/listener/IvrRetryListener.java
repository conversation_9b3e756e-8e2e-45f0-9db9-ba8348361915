package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/12/5 16:17
 */
@Component
public class IvrRetryListener {
    
    @Resource
    IvrCallService ivrCallService;
    
    @QmqConsumer(prefix = EventConstants.IVR_RETRY, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void ivrRetry(Message msg) {
        Integer ivrBizType = msg.getIntProperty("ivrBizType");
        Integer retryCount = msg.getIntProperty("retryCount");
        //不同的bizType对应自己的data数据
        String dataJson = msg.getStringProperty("data");
        ivrCallService.ivrRetry(ivrBizType, retryCount, dataJson);
    }

    
}
