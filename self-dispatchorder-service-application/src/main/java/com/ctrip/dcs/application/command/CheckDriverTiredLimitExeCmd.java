package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CheckDriverTiredLimitCommand;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDriverTiredService;
import com.ctrip.dcs.domain.common.value.DriverTiredLimitVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CheckDriverTiredLimitExeCmd {

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryDriverTiredService queryDriverTiredService;

    public List<DriverTiredLimitVO> execute(CheckDriverTiredLimitCommand command) {
        //疲劳驾驶仅接送机会调用
        List<DriverVO> drivers = queryDriverService.queryDriver(command.getDriverIds(), ParentCategoryEnum.JNT, null);
        return queryDriverTiredService.query(command.getEstimatedUseTime(), drivers);
    }
}
