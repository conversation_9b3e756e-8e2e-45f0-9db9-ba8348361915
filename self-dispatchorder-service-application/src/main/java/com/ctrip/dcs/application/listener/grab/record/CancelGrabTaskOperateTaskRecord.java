package com.ctrip.dcs.application.listener.grab.record;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component(value = VBKGrabOperationTypeEnum.CANCEL_SERVICE)
public class CancelGrabTaskOperateTaskRecord extends AbstractOperateTaskRecord{

    @Override
    protected void assembleParam(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        VBKDriverGrabTaskDO vbkDriverGrabTaskDO = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(vbkGrabTaskOperateDTO.getTaskId());
        if(Objects.nonNull(vbkDriverGrabTaskDO)){
            vbkGrabTaskOperateDTO.setCityId(vbkDriverGrabTaskDO.getCityId());
        }
    }

    @Override
    protected String buildRecordContent(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        return String.format(map.getString(CANCEL_CONTENT, CANCEL_CONTENT_STR), vbkGrabTaskOperateDTO.getSuccessNum());
    }


}
