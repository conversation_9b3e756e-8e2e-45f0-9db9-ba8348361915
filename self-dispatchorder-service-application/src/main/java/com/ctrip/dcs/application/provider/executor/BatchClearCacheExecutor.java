package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.listener.binlog.BinlogDeal;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchClearCacheRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchClearCacheResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class BatchClearCacheExecutor extends AbstractRpcExecutor<BatchClearCacheRequestType, BatchClearCacheResponseType>
        implements Validator<BatchClearCacheRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(BatchClearCacheExecutor.class);

    @Autowired
    private Map<String, BinlogDeal> map;

    @Override
    public BatchClearCacheResponseType execute(BatchClearCacheRequestType requestType) {
        BatchClearCacheResponseType response = new BatchClearCacheResponseType();
        try {
            List<String> dspOrderIds = requestType.getDspOrderIds();
            map.forEach((tableName, binlogDeal) -> {
                logger.info("batch_clearCache_" + tableName, JsonUtils.toJson(dspOrderIds));
                binlogDeal.clearCache(dspOrderIds);
            });
            return ServiceResponseUtils.success(response);
        } catch (Exception ex) {
            logger.error("BatchClearCache_Error", ex);
            return ServiceResponseUtils.fail(response);
        }
    }


    @Override
    public void validate(AbstractValidator<BatchClearCacheRequestType> validator) {
        validator.ruleFor("dspOrderIds").notNull().notEmpty();
    }
}
