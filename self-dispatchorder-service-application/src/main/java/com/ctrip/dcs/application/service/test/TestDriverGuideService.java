package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.service.DriverConfirmedService;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.value.QueryDriverInfoCondition;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.infrastructure.service.QueryDriverServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Map;

@Component("TestDriverGuideService")
public class TestDriverGuideService implements ITestDspOrderService {

    @Autowired
    DriverPointsGateway driverPointsGateway;
    @Autowired
    QueryDriverServiceImpl queryDriverService;
    @Autowired
    DriverConfirmedService driverConfirmedService;

    @Override
    public String test(Map<String, String> params) {
        driverConfirmedService.contractUpdateDrvLevel(5018924L,341139L);
        QueryDriverInfoCondition queryParam = new QueryDriverInfoCondition();
        queryParam.setDriverId(5025672L);
        queryParam.setCategoryCode(CategoryCodeEnum.C_DAY_RENTAL);
        queryParam.setSupplierId(6206L);
        queryDriverService.queryDriversForVbk(queryParam);
        String ids = params.get("driverGuideIds");
        Map<Long, Integer> result = driverPointsGateway.batchQueryDriverGuideLevel(LocalStringUtils.stringToLongList(ids));
        return LocalJsonUtils.toJson(result);
    }
}
