package com.ctrip.dcs.application.command.dto;

import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKGrabTaskRecordDO;

import java.util.List;

public class QueryVBKGrabTaskRecordDTO {

    /**
     * 检查结果集合
     */
    private List<VBKGrabTaskRecordDO> list;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 分页条数
     */
    private int pageSize;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 总记录数
     */
    private int totalSize;

    public QueryVBKGrabTaskRecordDTO() {
    }

    public QueryVBKGrabTaskRecordDTO(Integer pageNo, Integer pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public QueryVBKGrabTaskRecordDTO(List<VBKGrabTaskRecordDO> list, int pageNo, int pageSize, int totalPages, int totalSize) {
        this.list = list;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
        this.totalSize = totalSize;
    }

    public List<VBKGrabTaskRecordDO> getList() {
        return list;
    }

    public void setList(List<VBKGrabTaskRecordDO> list) {
        this.list = list;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(int totalSize) {
        this.totalSize = totalSize;
    }
}
