package com.ctrip.dcs.application.processor;

import com.ctrip.dcs.domain.common.enums.ConnectModeEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DriverOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDetailDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderDetailPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderFeePO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderRewardPO;
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants;
import com.ctrip.dcs.infrastructure.common.constants.PushSettlementTypeEnum;
import com.ctrip.dcs.infrastructure.service.QueryDspOrderServiceImpl;
import com.ctrip.dcs.settlement.mq.DriverOrderRewardDto;
import com.ctrip.dcs.settlement.mq.PlatformSettlementInfoDto;
import com.ctrip.dcs.settlement.mq.SettlePriceMessage;
import com.ctrip.dcs.settlement.mq.SupplyChangePriceInfoDto;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.NeedRetryException;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> liu
 */
@Component
public class PushSettlementProcessor {

    private static final Logger logger = LoggerFactory.getLogger(PushSettlementProcessor.class);

    private static final String DCS_PRICE_NOTIFY = "dcs.purchase.supplyorder.price.notify";

    @Resource
    private QueryDspOrderServiceImpl queryDspOrderService;

    @Resource
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Resource
    private DspOrderFeeDao dspOrderFeeDao;

    @Resource
    private DspOrderDao dspOrderDao;

    @Resource
    private DriverOrderDao driverOrderDao;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private DspOrderDetailDao dspOrderDetailDao;

    /**
     * 处理司机接单结算费项
     * @param dspOrderId
     * @param confirmRecordId
     * @param driverOrderId
     * @throws SQLException
     */
    public void process(String dspOrderId, Long confirmRecordId, String driverOrderId,
                        PushSettlementTypeEnum settlementTypeEnum) throws SQLException {
        SettlePriceMessage message = new SettlePriceMessage();
        // 供应商单号 自营即派发单号
        message.setSupplierOrderId(dspOrderId);
        // 自营
        message.setConnectMode(ConnectModeEnum.VBK.mode);
        message.setSourceType("dcs_self_dispatch");
        // 司机单号
        message.setDriverOrderId(driverOrderId);

        // 销售模式
        // 查询派发单表 dsp_order
        DspOrderPO dspOrder = dspOrderDao.findByDspOrderId(dspOrderId);
        if ( Objects.nonNull(dspOrder) ) {
            message.setSalesMode(dspOrder.getSalesMode());
        }

        // sku_id
        DspOrderDetailPO dspOrderDetail = dspOrderDetailDao.find(dspOrderId);
        if ( Objects.nonNull(dspOrderDetail) ) {
            message.setSkuId(Long.valueOf(dspOrderDetail.getSkuId()));
        }

        // 查询派发加价奖励 vbk抢单奖励
        // 查询应单记录表 dsp_order_confirm_record
        DspOrderConfirmRecordVO dspOrderConfirmRecord = dspOrderConfirmRecordRepository.find(confirmRecordId);
        if ( Objects.nonNull(dspOrderConfirmRecord) ) {
            // 用户单号
            message.setUserOrderId(dspOrderConfirmRecord.getUserOrderId());
            // 供应子单号
            message.setSupplyOrderId(dspOrderConfirmRecord.getSupplyOrderId());
            // 额外附加服务
            message.setAdditionalServices(Lists.newArrayList());
            SupplyChangePriceInfoDto supplyChangePriceInfoDto = buildSupplyChangePriceInfoDto(driverOrderId, dspOrderId);
            if (StringUtils.isNotBlank(dspOrderConfirmRecord.getVbkRewardAmount())) {
                // vbk接单奖励
                supplyChangePriceInfoDto.setVbkRewardAmount(new BigDecimal(dspOrderConfirmRecord.getVbkRewardAmount()));
            }
            message.setSupplyChangePriceInfo(supplyChangePriceInfoDto);
        }

        // 推送消息
        pushSettlementMessage(message, settlementTypeEnum);
    }

    /**
     * 推送结算QMQ消息
     * @param message
     * @param type
     */
    void pushSettlementMessage(SettlePriceMessage message, @NotNull PushSettlementTypeEnum type) {
        try {
            logger.info("PushSettlementProcessor_" + message.getUserOrderId(), "push settlement message, message={}", message);
            HashMap<String, Object> param = new HashMap<>();
            param.put(CommonConstants.PROPERTY_DATA, JacksonSerializer.INSTANCE().serialize(message));
            messageProvider.sendMessage(DCS_PRICE_NOTIFY, param, Sets.newHashSet(type.getCode()));
        } catch (Exception e) {
            logger.error("PushSettlementProcessor_", "push settlement message error, message={}", message, e);
        }
    }

    private SupplyChangePriceInfoDto buildSupplyChangePriceInfoDto(String driverOrderId, String dspOrderId) {
        try{
            SupplyChangePriceInfoDto dto = new SupplyChangePriceInfoDto();
            // 查询派发单费用表 dsp_order_fee
            DspOrderFeePO dspOrderFee = dspOrderFeeDao.find(dspOrderId);

            if( Objects.nonNull(dspOrderFee) ) {
                // 币种
                dto.setUserCurrency(dspOrderFee.getUserCurrency());
                dto.setSupplierCurrency(dspOrderFee.getSupplierCurrency());
                dto.setDriverCurrency(dspOrderFee.getDriverCurrency());
                dto.setSc2cnyExchangeRate(dspOrderFee.getSc2cnyExchangeRate());
                dto.setCny2ucExchangeRate(dspOrderFee.getCny2ucExchangeRate());
                dto.setSc2driverExchangeRate(dspOrderFee.getSc2driverExchangeRate());
                dto.setUc2driverExchangeRate(dspOrderFee.getUc2driverExchangeRate());
                // 派发加价
                dto.setDspAddPrice(dspOrderFee.getDspAddPrice());
                dto.setPremiumPriceList(Lists.newArrayList(buildPlatformSettlementInfoDto(dspOrderFee)));
                // 查询司机单表 drv_order
                List<DrvOrderPO> drvOrderList = driverOrderDao.queryByDriverOrderId(driverOrderId, dspOrderId);
                if ( Objects.nonNull(drvOrderList) && !drvOrderList.isEmpty() ) {
                    // 是否结算给司机
                    dto.setSettleToDriver(drvOrderList.getFirst().getSettleToDriver() == 1);
                }

                // 查询舒适升舱补贴
                // 查询派发单奖励表 dsp_order_reward_info
                List<DspOrderRewardPO> rewards = queryDspOrderService.queryDspOrderRewardPOs(dspOrderId);
                if ( Objects.nonNull(rewards) && !rewards.isEmpty() ) {
                    // 舒适升舱补贴
                    dto.setRewardInfos(Lists.newArrayList(buildDriverOrderRewardDto(rewards.getFirst())));
                }
            }
            return dto;
        } catch (Exception e){
            logger.error("PushSettlementProcessor_" + dspOrderId, "build SupplyChangePriceInfoDto error, {}", e);
            throw new NeedRetryException(System.currentTimeMillis() + 5 * 1000,
                    "build SupplyChangePriceInfoDto error");
        }
    }

    /**
     * 解析json字符串 "[\"strategyId\": \"9\", \"feeAmount\": 5]"
     * @param dspOrderFee
     * @return
     */
    private PlatformSettlementInfoDto buildPlatformSettlementInfoDto(DspOrderFeePO dspOrderFee) {
        PlatformSettlementInfoDto dto = new PlatformSettlementInfoDto();
        try{
            List premiumPriceList = JsonUtils.fromJson(dspOrderFee.getPremiumPrice(), List.class);
            if ( CollectionUtils.isNotEmpty(premiumPriceList) ) {
                Map premiumPriceMap = (Map) premiumPriceList.getFirst();
                dto.setStrategyType(String.valueOf(premiumPriceMap.get("strategyId")));
                dto.setSettleAmount(new BigDecimal(String.valueOf(premiumPriceMap.get("feeAmount"))));
            }
        } catch (Exception e){
            logger.error("PushSettlementProcessor_" + dspOrderFee.getDspOrderId(),
                    "build PlatformSettlementInfoDto error, {}", e);
        }
        return dto;
    }

    private DriverOrderRewardDto buildDriverOrderRewardDto(DspOrderRewardPO reward) {
        DriverOrderRewardDto dto = new DriverOrderRewardDto();
        dto.setRuleSceneId(reward.getRuleSceneId());
        dto.setCurrencyCode(reward.getCurrencyCode());
        dto.setReward2driverExchangeRate(reward.getReward2driverExchangeRate());
        dto.setTotalAmount(reward.getTotalAmount());
        return dto;
    }
}
