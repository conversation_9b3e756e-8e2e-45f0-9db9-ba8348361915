package com.ctrip.dcs.application.service.grab;

import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand;
import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SubmitDispatcherGrabOrderServiceContext {

    @Autowired
    protected QueryDspOrderService orderQueryService;

    @Autowired
    @Qualifier("defaultSubmitDispatcherGrabOrderService")
    private SubmitDispatcherGrabOrderService submitDispatcherGrabOrderService;

    @Autowired
    @Qualifier("oriOrderModifySubmitDispatcherGrabOrderService")
    private SubmitDispatcherGrabOrderService oriOrderModifySubmitDispatcherGrabOrderService;

    public SubmitDispatcherGrabOrderService get(SubmitDispatcherGrabOrderCommand command) {
        return Optional.of(command.getScene())
                .filter(urgent -> Objects.equals(urgent, SupplierConfirmSceneEnum.ORI_MODIFY.getScene()))
                .map(data -> oriOrderModifySubmitDispatcherGrabOrderService)
                .orElseGet(() -> submitDispatcherGrabOrderService);
    }
}
