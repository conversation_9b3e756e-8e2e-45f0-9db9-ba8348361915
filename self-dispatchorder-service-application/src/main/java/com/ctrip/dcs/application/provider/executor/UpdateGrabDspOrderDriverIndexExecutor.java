package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderDriverIndexDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderSnapshotDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO;
import com.ctrip.dcs.self.dispatchorder.interfaces.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class UpdateGrabDspOrderDriverIndexExecutor extends AbstractRpcExecutor<UpdateGrabDspOrderDriverIndexRequestType, UpdateGrabDspOrderDriverIndexResponseType> implements Validator<UpdateGrabDspOrderDriverIndexRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabDspOrderDriverIndexExecutor.class);

    @Autowired
    private GrabDspOrderDriverIndexDao grabDspOrderDriverIndexDao;

    @Override
    public UpdateGrabDspOrderDriverIndexResponseType execute(UpdateGrabDspOrderDriverIndexRequestType requestType) {
        for (GrabDspOrderDriverIndexDTO dto : requestType.getIndexes()) {
            try {
                List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.query(dto.getDspOrderId(), Lists.newArrayList(dto.getDriverId()));
                if (CollectionUtils.isNotEmpty(list)) {
                    for (GrabDspOrderDriverIndexPO po : list) {
                        if (dto.getIsValid() != null) {
                            po.setIsValid(dto.getIsValid());
                        }
                        if (dto.getIsBroadcast() != null) {
                            po.setIsBroadcast(dto.getIsBroadcast());
                        }
                    }
                    grabDspOrderDriverIndexDao.update(list);
                }
            } catch (Exception e) {
                logger.warn("UpdateGrabDspOrderDriverIndexExecutorError", e);
            }
        }
        return ServiceResponseUtils.success(new UpdateGrabDspOrderDriverIndexResponseType());
    }

    @Override
    public void validate(AbstractValidator<UpdateGrabDspOrderDriverIndexRequestType> validator) {
        validator.ruleFor("indexes").notNull().notEmpty();
    }
}
