package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.infrastructure.adapter.carconfig.SupplierDiversionConfig;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversion;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 查询分流配置方法
 */
@Component
@ServiceLogTagPair(key = "cityId", alias = "cityId")
public class SupplierDiversionListExecutor extends AbstractRpcExecutor<SupplierDiversionListRequestType, SupplierDiversionListResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(SupplierDiversionListExecutor.class);

    @Autowired
    private SupplierDiversionConfig supplierDiversionConfig;

    @Override
    public SupplierDiversionListResponseType execute(SupplierDiversionListRequestType requestType) {
        SupplierDiversionListResponseType responseType = new SupplierDiversionListResponseType();
        try {
            // 参数校验
            Boolean checkParam = this.checkParam(requestType);
            if (Boolean.TRUE.equals(checkParam)) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            // 构建数据
            List<SupplierDiversion> diversionList = this.buildSupplierDiversionList(requestType);
            responseType.setSupplierDiversionList(diversionList);
            return ServiceResponseUtils.success(responseType);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("SupplierDiversionListError", "SupplierDiversionList ERROR", e);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    /**
     * 构建返回值
     *
     * @param requestType
     * @return
     */
    public List<SupplierDiversion> buildSupplierDiversionList(SupplierDiversionListRequestType requestType) {
        // 查询配置系统
        List<SupplierDiversionConfig.Value> valueList = supplierDiversionConfig.getConfigValue(requestType.getCityId(), requestType.getCategoryCode());
        if (CollectionUtils.isEmpty(valueList)) {
            // 埋点
            MetricsUtil.recordValue(MetricsConstants.SUPPLIER_DIVERSION_NO_CONFIG);
            return Collections.emptyList();
        }

        BigDecimal sum = BigDecimal.ZERO;
        // 转换返回值
        List<SupplierDiversion> targetList = Lists.newArrayList();
        for (SupplierDiversionConfig.Value value : valueList) {
            SupplierDiversion target = new SupplierDiversion();
            target.setDiversionVal(value.getDiversionVal());
            target.setSupplierId(value.getSupplierId());
            targetList.add(target);
            sum = sum.add(value.getDiversionVal());
        }

        // 配置的和如果不等于100，则埋点报警
        if (sum.compareTo(new BigDecimal("100")) != 0) {
            MetricsUtil.recordValue(MetricsConstants.SUPPLIER_DIVERSION_CONFIG_ERROR);
        }
        return targetList;
    }

    /**
     * 参数校验
     *
     * @param requestType
     */
    public Boolean checkParam(SupplierDiversionListRequestType requestType) {
        if (requestType == null || requestType.getCityId() == null || StringUtils.isEmpty(requestType.getCategoryCode())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
