package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CancelDspOrderCommand;
import com.ctrip.dcs.domain.common.enums.CancelReasonIdEnum;
import com.ctrip.dcs.domain.common.enums.CancelRoleEnum;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderCancelDO;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderRequestType;
import org.apache.commons.lang3.StringUtils;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class SaaSCancelDspOrderConverter {

    public static CancelDspOrderCommand converter(SaasCancelDspOrderRequestType requestType) {
        Assert.notNull(requestType);
        CancelDspOrderCommand cmd = new CancelDspOrderCommand();
        cmd.setSupplierId(requestType.getSupplierId());
        cmd.setCancelRole(CancelRoleEnum.SAAS.getCode());
        cmd.setVbkOrderId(requestType.getVbkOrderId());
        cmd.setCancelReason(requestType.getOrderCancelReasonDetail());
        cmd.setDspOrderId(requestType.getDspOrderId());
        cmd.setOperator(requestType.getOperator());
        cmd.setNewProcess(requestType.getNewProcess());
        return cmd;
    }

    public static DspOrderCancelDO buildDspOrderCancelDO(CancelDspOrderCommand cmd) {
        DspOrderCancelDO cancelDO = new DspOrderCancelDO();
        cancelDO.setOrderCancelReasonDetail(cmd.getCancelReason());
        cancelDO.setCancelDesc(CancelReasonIdEnum.UNKNOWN_CANCEL.getDesc());
        cancelDO.setCancelReasonId(cmd.getCancelReasonId());
        cancelDO.setCancelCode(CancelReasonIdEnum.UNKNOWN_CANCEL.getCode().toString());
        cancelDO.setDspOrderId(cmd.getDspOrderId());
        cancelDO.setUserOrderId(StringUtils.EMPTY);
        cancelDO.setOperator(cmd.getOperator());
        return cancelDO;
    }
}
