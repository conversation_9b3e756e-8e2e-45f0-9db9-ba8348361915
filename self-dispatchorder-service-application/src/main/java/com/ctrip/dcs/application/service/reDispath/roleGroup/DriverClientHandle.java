package com.ctrip.dcs.application.service.reDispath.roleGroup;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.service.RedispatchRightAndPointService;
import com.ctrip.dcs.application.service.convertor.ServiceConverter;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.DispatchRightVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig;
import com.ctrip.dcs.infrastructure.common.constants.DescribeConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/5/25 17:29
 */
@Component(value = ReassignTaskEnum.DRIVER_APP)
public class DriverClientHandle implements RightAndPointService {

    private static final Logger logger = LoggerFactory.getLogger(DriverClientHandle.class);

    @Resource
    private RedispatchRightAndPointService redispatchRightAndPointService;

    @Resource
    private DriverDomainServiceGateway driverDomainService;

    @Override
    public Result<RightAndPointVO> queryRightAndPointDTO(RightAndPointCommand command, BaseDetailVO detailVO) {
        if (!Objects.equals(detailVO.getDrvId(), command.getDrvId())) {
            return Result.Builder.<RightAndPointVO>newResult().fail().
                    withCode(ErrorCode.DISPATCH_DRIVER_NO_ORDER_ERROR.getCode()).
                    withMsg(ErrorCode.DISPATCH_DRIVER_NO_ORDER_ERROR.getDesc()).build();
        }
        Result<RightAndPointVO> rightRes = redispatchRightAndPointService.checkPunishReason(command, detailVO);
        if (!rightRes.isSuccess() || rightRes.getData() == null) {
            return rightRes;
        }
        RightAndPointVO responseCheckResult = rightRes.getData();
        Boolean canUseRight = Boolean.FALSE;
        if (command.getDrvId() != null && command.getDrvId() > 0 &&
                responseCheckResult.isResponsible() && responseCheckResult.isPunishDriver()) {
            canUseRight = driverDomainService.queryDriverDispatchRights(command.getDrvId());
        }

        // fixme 后期有时间优化下，太乱...

        if (Boolean.TRUE.equals(canUseRight) && detailVO.isAllowUseRightSysBookTime() && detailVO.isDriverAppDispatcherTakenType() && command.getIsUseReDispatchRight()) {
            responseCheckResult.setResponsable(Boolean.FALSE);
            DriverRightsVO driverRightsVO = driverDomainService.queryDriverRightsInfo(command.getDrvId());
            DispatchRightVO rightVO = ServiceConverter.buildRight(driverRightsVO, ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT);
            rightVO.setRightDetailDesc(String.format(DescribeConstants.RIGHTS_TIPS, rightVO.getTimes(), driverRightsVO.getTimesForMonth()));
            responseCheckResult.setDispatchRightInfo(rightVO);
        }
        // 最终改派流程 且 接口中使用权益 且 最后是有责  则 返回固定code（60024），当前权益不可用，改派失败
        if (ReassignTaskEnum.PunishOperateType.SUBMIT.getCode().equals(command.getPunishOperateType()) && Boolean.TRUE.equals(responseCheckResult.getResponsable()) && command.getIsUseReDispatchRight()) {
            logger.info("DriverClientRightAndPointDispatchFail", "UseRight:True Responsibility:True detailVO={}", detailVO.attrs(), JsonUtil.toJson(responseCheckResult));
            throw ErrorCode.DISPATCH_DRIVER_RIGHT_RIGHT_NOT_USE.getBizException();
        }
        return rightRes;
    }

    @Override
    public Result convertAndCheckPrm(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        if (DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()).before(new Date())) {
            if (command.getReasonDetailId().toString().equals(ReassignTaskEnum.SpecialReflectReasonEnum.DRIVER_FLIGHT_STATUS_DISPATCH.getCode())) {
                return Result.Builder.newResult().fail().
                        withCode(ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getDesc()).build();
            }
        }
        return Result.Builder.newResult().success().build();
    }

}