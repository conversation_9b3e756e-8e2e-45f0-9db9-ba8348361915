package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.redis.IDispatchModifyInfoCacheService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

@Component
public class SupplyOrderRedispatchCancelListener {
    private static final Logger logger = LoggerFactory.getLogger(ReDispatchSubmitListener.class);
    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private IDispatchModifyInfoCacheService cacheService;


    @QmqConsumer(prefix = EventConstants.SUPPLY_ORDER_REDISPATCH_CANCEL, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        logger.info("purchaseSupplyOrderCancelByRedispatch.onMessage begin", JacksonUtil.serialize(message));
        String driverIdStr = message.getStringProperty("driverId");
        String userOrderId = message.getStringProperty("userOrderId");
        if (Strings.isBlank(driverIdStr) || Strings.isBlank(userOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("purchaseSupplyOrderCancelByRedispatch qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            //存储用户手机号-改派过的司机id
            //存储订单id-改派过的供应商id
            cacheService.cacheOrderDspModifyDriver(userOrderId,driverIdStr);
        } catch (Exception e) {
            logger.error("purchaseSupplyOrderCancelByRedispatch.error", e);
            throw e;
        }
    }
}
