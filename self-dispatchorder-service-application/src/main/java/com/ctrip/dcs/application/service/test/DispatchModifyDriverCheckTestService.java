package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.item.impl.AbstractCheck;
import com.ctrip.dcs.domain.schedule.check.item.impl.AbstractDriverCheck;
import com.ctrip.dcs.domain.schedule.check.item.impl.DispatchModifyDriverCheck;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.adapter.redis.IDispatchModifyInfoCacheService;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.infrastructure.service.DspContextServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component("DispatchModifyDriverCheckTestService")
public class DispatchModifyDriverCheckTestService implements ITestDspOrderService{
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private QueryDriverService queryDriverService;
    @Autowired
    private QueryTransportGroupService queryTransportGroupService;
    @Autowired
    private IDispatchModifyInfoCacheService cacheService;
    @Autowired
    private DspOrderConfirmRecordRepository recordRepository;
    @Override
    public String test(Map<String, String> params) {
        DspOrderVO order = queryDspOrderService.query(params.get("dspOrderId"));
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId().longValue() : 0L;
        DriverVO driver = queryDriverService.queryDriver(Long.valueOf(params.get("driverId")), CategoryUtils.selfGetParentType(order), supplierId);
        TransportGroupVO transportGroupVO = queryTransportGroupService.queryTransportGroup(Long.valueOf(params.get("transportGroupId")));
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDO.setDriverOrderId(params.get("driverOrderId"));
        dspOrderDO.setDspOrderId(params.get("dspOrderId"));
        dspOrderDO.setUserOrderId(params.get("userOrderId"));
        dspOrderDO.setSupplierId(transportGroupVO.getSupplierId().intValue());
        cacheDspInfo(dspOrderDO,15);
        AbstractCheck check = new DispatchModifyDriverCheck();
        CheckContext context = new CheckContext(new DspContext(new DspContextServiceImpl()), new TestConfigService(), new SubSkuVO(), order, driver, transportGroupVO, DspStage.DSP, null, null,null);
        List<CheckModel> result = check.check(Arrays.asList(new CheckModel(new DspModelVO(order,driver,transportGroupVO))),context);
        return LocalJsonUtils.toJson(result);
    }

    public void cacheDspInfo(DspOrderDO dspOrderDO,Integer reasonDetailId){
        //改派过的司机
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = recordRepository.findByDspOrderId(dspOrderDO.getDspOrderId(),dspOrderDO.getDriverOrderId());
        if(dspOrderConfirmRecordVO != null && dspOrderConfirmRecordVO.getDriverInfo() != null){
            Long driverId = dspOrderConfirmRecordVO.getDriverInfo().getDriverId();
            if(driverId != null){
                cacheService.cacheOrderDspModifyDriver(dspOrderDO.getUserOrderId(),driverId.toString());
            }
        }
        //自营供应商发起的全程改派
        if(ReassignTaskEnum.SpecialReflectReasonEnum.SELF_ALL_DISPATCH.getCode().equals(reasonDetailId.toString())){
            //改派过的供应商
            cacheService.cacheOrderDspModifySupplier(dspOrderDO.getUserOrderId(),dspOrderDO.getSupplierId().toString());
        }
    }
}
