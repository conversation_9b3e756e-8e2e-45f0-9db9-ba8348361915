package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.DspOrderRewardStrategyActiveEvent;
import com.ctrip.dcs.domain.schedule.factory.DspOrderRewardStrategyFactory;
import com.ctrip.dcs.domain.schedule.process.impl.GrabBroadcastProcess;
import com.ctrip.dcs.domain.schedule.repository.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DspOrderRewardStrategyService {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderRewardStrategyService.class);

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository;

    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private GrabBroadcastProcess grabBroadcastProcessor;

    @Autowired
    protected GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService;

    @Autowired()
    private DspOrderRewardStrategyFactory dspOrderRewardStrategyFactory;

    @Autowired
    private MessageProviderService messageProviderService;

    /**
     * 到达奖励策略生效时间
     * @param strategy
     */
    public void activeDspOrderRewardStrategy(DspOrderRewardStrategyDO strategy) {
        strategy.setDspRewardStatus(YesOrNo.YES.getCode());
        dspOrderRewardStrategyRepository.update(Lists.newArrayList(strategy));
        double seconds = DateUtil.seconds(new Date(), strategy.getDspRewardTimeBj());
        messageProviderService.send(new DspOrderRewardStrategyActiveEvent(strategy.getId(), strategy.getDspOrderId(), strategy.getScheduleId(), Double.valueOf(seconds).longValue() * 1000));
    }

    public void updateRewardStrategy(Long strategyId) {
        DspOrderRewardStrategyDO strategy = dspOrderRewardStrategyRepository.find(strategyId);
        if (Objects.isNull(strategy) || Objects.equals(strategy.getDspRewardStatus(), YesOrNo.NO.getCode())) {
            return;
        }
        ScheduleDO schedule = scheduleRepository.find(strategy.getScheduleId());
        if (schedule == null || schedule.isShutdown()) {
            return;
        }
        DspOrderVO order = queryDspOrderService.queryOrderDetail(strategy.getDspOrderId());
        List<ScheduleTaskDO> tasks = scheduleTaskRepository.query(schedule.getScheduleId(), schedule.getDspOrderId());
        for (ScheduleTaskDO task : tasks) {
            if (Objects.equals(task.getStatus(), ScheduleTaskStatus.CANCEL)) {
                continue;
            }
            if (!Objects.equals(DspType.GRAB_BROADCAST, task.getSubSku().getDspType())) {
                continue;
            }
            // 1、更新任务
            updateScheduleTask(order, task, strategy);
            // 查询快照
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(order.getDspOrderId());
            if (snapshot != null) {
                // 2、更新快照
                updateGrabDspOrderSnapshot(order, schedule, task, snapshot);
                // 3、vbk操作记录
                grabDspOrderSnapshotRecordService.sendVBKOperationRecordWithReward(snapshot, strategy);
            }
        }
    }


    public void updateScheduleTask(DspOrderVO order, ScheduleTaskDO task, DspOrderRewardStrategyDO dspRewardStrategy) {
        // 更新任务的策略id
        task.setDspRewardStrategyId(dspRewardStrategy.getId());
        scheduleTaskRepository.updateDspRewardStrategyId(task, order);
    }

    public void updateGrabDspOrderSnapshot(DspOrderVO order, ScheduleDO schedule, ScheduleTaskDO task, GrabDspOrderSnapshotDO snapshot) {
        List<TransportGroupVO> transportGroups = grabBroadcastProcessor.queryTransportGroups(schedule, order);
        List<OrderSettlePriceVO> orderSettlePrices = grabBroadcastProcessor.queryOrderSettlePrice(order, schedule, task, transportGroups);
        snapshot.updateOrderSettlePrices(orderSettlePrices);
        snapshot.updateScheduleTaskDetail(task);
        // 更新快照
        grabDspOrderSnapshotRepository.updateSettlePriceAndDuid(snapshot);
    }


    /**
     * 重新生成奖励策略
     * @param dspOrderId
     */
    public void rebuildRewardStrategy(String dspOrderId) {
        try {
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(dspOrderId);
            if (snapshot == null || Objects.equals(snapshot.getGrabType(), GrabDspOrderSnapshotTypeEnum.SYSTEM) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.SUCCESS) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.CANCEL)) {
                // 过滤非法状态
                return;
            }
            if (Objects.equals(snapshot.getGrabType(), GrabDspOrderSnapshotTypeEnum.VBK_DISPATCHER) && !DateUtil.isAfter(snapshot.getGrabPushTimeBj(), new Date())) {
                // 已过发单时间的，不再变更
                return;
            }
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(snapshot.getDspOrderId());
            List<ScheduleDO> schedules = scheduleRepository.query(snapshot.getDspOrderId());
            for (ScheduleDO schedule: schedules) {
                if (schedule.isShutdown()) {
                    continue;
                }
                // 删除原有的策略
                List<DspOrderRewardStrategyDO> strategies = schedule.getRewardStrategyList();
                for (DspOrderRewardStrategyDO strategy : strategies) {
                    strategy.setDspRewardStatus(2);
                }
                dspOrderRewardStrategyRepository.update(strategies);
                // 重新生成策略
                strategies = dspOrderRewardStrategyFactory.create(dspOrder, DspOrderRewardStrategyType.valueOf(schedule.getType().getCode()));
                for (DspOrderRewardStrategyDO strategy : strategies) {
                    strategy.setScheduleId(schedule.getScheduleId());
                }
                dspOrderRewardStrategyRepository.save(strategies);
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }
}
