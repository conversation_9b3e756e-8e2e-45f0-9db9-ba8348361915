package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd;
import com.ctrip.dcs.application.command.api.SaveShortDistanceStrategyCommand;
import com.ctrip.dcs.application.command.dto.SaveShortDistanceStrategyResDTO;
import com.ctrip.dcs.application.provider.converter.ShortDistanceStrategyConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SaveShortDistanceStrategyExecutor extends AbstractRpcExecutor<SaveShortDistanceStrategyRequestType, SaveShortDistanceStrategyResponseType> implements Validator<SaveShortDistanceStrategyRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(SaveShortDistanceStrategyExecutor.class);

    @Autowired
    private ShortDistanceStrategyExeCmd strategyExeCmd;

    @Autowired
    private ShortDistanceStrategyConverter shortDistanceStrategyConverter;

    @Override
    public void validate(AbstractValidator<SaveShortDistanceStrategyRequestType> validator) {
        validator.ruleFor("categoryCodeList").notEmpty();
        validator.ruleFor("vehicleGroupIdList").notEmpty();
        validator.ruleFor("channelIdList").notEmpty();
        validator.ruleFor("code").notNull();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("startDis").notNull();
        validator.ruleFor("endDis").notNull();
        validator.ruleFor("state").notNull();
        validator.ruleFor("operator").notNull();
    }

    @Override
    public SaveShortDistanceStrategyResponseType execute(SaveShortDistanceStrategyRequestType requestType) {
        try {
            SaveShortDistanceStrategyCommand cmd = shortDistanceStrategyConverter.convert(requestType);
            Result<SaveShortDistanceStrategyResDTO> result = strategyExeCmd.save(cmd);
            SaveShortDistanceStrategyResponseType responseType = new SaveShortDistanceStrategyResponseType();
            if (result.isSuccess()) {
                return ServiceResponseUtils.success(responseType);
            }
            if (ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.getCode().equals(result.getCode()) && result.getData() != null) {
                responseType.setConflictingStrategyId(result.getData().getConflictingStrategyId());
            }
            return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
        } catch (Exception e) {
            logger.error("SaveShortDistanceStrategyExecutor_Exp", e);
            return ServiceResponseUtils.fail(new SaveShortDistanceStrategyResponseType());
        }
    }
}
