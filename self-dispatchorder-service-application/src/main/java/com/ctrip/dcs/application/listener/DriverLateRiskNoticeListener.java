package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.service.risk.DriverLateRiskService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;

/**
 * 司机迟到风险通知
 * <AUTHOR>
 * @since 2024/11/7 11:12
 */
@Component
public class DriverLateRiskNoticeListener {
    private static Logger logger = LoggerFactory.getLogger(DriverLateRiskNoticeListener.class);
    private static String TITLE = "send_late_risk_notice_";
    private static String OLD_ORDER_TITLE = "q_send_late_risk_notice_";
    @Resource
    DriverLateRiskService driverLateRiskService;
    
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ARISE_LATE_RISK, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Integer lateRiskType = message.getIntProperty("lateRiskType");
        logger.info(TITLE + dspOrderId + "_" + lateRiskType, "send late risk notice start.");
        driverLateRiskService.sendLateRiskNotice(dspOrderId, lateRiskType);
        logger.info(TITLE + dspOrderId + "_" + lateRiskType, "send late risk notice end.");
    }
    
    
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.OLD_DRIVER_ORDER_ARISE_LATE_RISK, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void oldOrderOnMessage(Message message) {
        String supplyOrderId = message.getStringProperty("supplyOrderId");
        Integer lateRiskType = message.getIntProperty("lateRiskType");
        logger.info(OLD_ORDER_TITLE + supplyOrderId + "_" + lateRiskType, "send late risk notice start.");
        driverLateRiskService.sendLateRiskNotice(supplyOrderId, lateRiskType);
        logger.info(OLD_ORDER_TITLE + supplyOrderId + "_" + lateRiskType, "send late risk notice end.");
    }
    
    
}
