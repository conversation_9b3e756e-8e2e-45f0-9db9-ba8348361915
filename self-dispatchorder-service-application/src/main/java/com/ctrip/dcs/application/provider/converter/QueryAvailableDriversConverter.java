package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.query.api.AvailableDriversQuery;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.AvailableDriverDTO;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class QueryAvailableDriversConverter {

    public static AvailableDriversQuery toAvailableDriversQuery(QueryAvailableDriversRequestType requestType) {
        return new AvailableDriversQuery(requestType.getDspOrderId(), requestType.getDuid());
    }

    public static List<AvailableDriverDTO> toAvailableDriverDTO(List<SortModel> dspModels, String duid) {
        return Optional.ofNullable(dspModels)
                .orElse(Collections.emptyList())
                .stream()
                .map(m -> {
                    AvailableDriverDTO dto = new AvailableDriverDTO();
                    dto.setDspOrderId(m.getModel().getOrder().getDspOrderId());
                    dto.setDuid(duid);
                    dto.setDriverId(m.getModel().getDriver().getDriverId());
                    dto.setValue(String.valueOf(m.getScore()));
                    return dto;
                })
                .collect(Collectors.toList());
    }
}
