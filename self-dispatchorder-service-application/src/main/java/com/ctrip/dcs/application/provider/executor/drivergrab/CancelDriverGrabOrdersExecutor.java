package com.ctrip.dcs.application.provider.executor.drivergrab;

import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDriverGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDriverGrabOrdersResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 迁移前抢单大厅订单
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class CancelDriverGrabOrdersExecutor extends AbstractRpcExecutor<CancelDriverGrabOrdersRequestType, CancelDriverGrabOrdersResponseType> implements Validator<CancelDriverGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(CancelDriverGrabOrdersExecutor.class);

    @Autowired
    private GrabCentreRepository grabCentreRepository;
    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;


    @Override
    public CancelDriverGrabOrdersResponseType execute(CancelDriverGrabOrdersRequestType requestType) {
        try {
            if (CollectionUtils.isEmpty(requestType.getDriverIds())) {
                cancelAll(requestType.getDspOrderIds());
            } else {
                cancel(requestType.getDspOrderIds(), requestType.getDriverIds());
            }
            return ServiceResponseUtils.success(new CancelDriverGrabOrdersResponseType());
        } catch (Exception e) {
            logger.error("CreateDriverGrabOrdersExecutorError", e);
        }
        return ServiceResponseUtils.fail(new CancelDriverGrabOrdersResponseType());
    }

    private void cancel(List<String> dspOrderIds, List<Long> driverIds) {
        if (CollectionUtils.isEmpty(dspOrderIds) || CollectionUtils.isEmpty(driverIds)) {
            return;
        }
        for (String dspOrderId : dspOrderIds) {
            for (Long driverId : driverIds) {
                grabCentreRepository.delete(dspOrderId, driverId);
                grabOrderDetailRepository.delete(dspOrderId, driverId);
            }
        }
    }

    private void cancelAll(List<String> dspOrderIds) {
        if (CollectionUtils.isEmpty(dspOrderIds)) {
            return;
        }
        for (String dspOrderId : dspOrderIds) {
            grabCentreRepository.deleteAll(dspOrderId);
            grabOrderDetailRepository.deleteAll(dspOrderId);
        }
    }

    @Override
    public void validate(AbstractValidator<CancelDriverGrabOrdersRequestType> validator) {
        validator.ruleFor("dspOrderIds").notNull().notEmpty();
    }
}
