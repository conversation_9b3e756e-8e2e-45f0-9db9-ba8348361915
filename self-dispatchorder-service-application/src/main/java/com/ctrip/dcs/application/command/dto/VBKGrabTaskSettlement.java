package com.ctrip.dcs.application.command.dto;

import java.math.BigDecimal;

public class VBKGrabTaskSettlement {
    private Integer type;
    private BigDecimal value;
    private BigDecimal rate;
    private String currency;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
