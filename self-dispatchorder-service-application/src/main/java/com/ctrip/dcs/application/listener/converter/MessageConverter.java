package com.ctrip.dcs.application.listener.converter;

import com.ctrip.dcs.application.command.api.*;
import com.ctrip.dcs.application.command.dto.SubmitGrabDTO;
import com.ctrip.dcs.domain.common.enums.GrabOrderType;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qmq.Message;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public interface MessageConverter {

    MessageConverter INSTANCE = new MessageConverter() {};

    /**
     * 创建调度命令
     * @param message
     * @return
     */
    default CreateScheduleCommand toCreateScheduleCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        return new CreateScheduleCommand(dspOrderId, ScheduleEventType.DSP_ORDER_BOOK);
    }

    /**
     * 创建调度命令
     * @param message
     * @return
     */
    default CreateScheduleCommand toCreateScheduleCommandByVBKGrabTask(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        return new CreateScheduleCommand(dspOrderId, ScheduleEventType.VBK_GRAB_TASK);
    }

    /**
     * 执行调度命令
     * @param message
     * @return
     */
    default ExecuteScheduleCommand toExecuteScheduleCommand(Message message) {
        Long scheduleId = message.getLongProperty("scheduleId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        String event = message.getStringProperty("event");
        Integer round = message.getIntProperty("round");
        return new ExecuteScheduleCommand(dspOrderId, scheduleId, round, ScheduleEventType.valuesOf(event));
    }

    /**
     * 执行任务命令
     * @param message
     * @return
     */
    default ExecuteTaskCommand toExecuteTaskCommand(Message message) {
        Long taskId = message.getLongProperty("taskId");
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long scheduleId = message.getLongProperty("scheduleId");
        String scheduleTaskId = message.getStringProperty("scheduleTaskId");
        return new ExecuteTaskCommand(taskId, scheduleTaskId, scheduleId, dspOrderId);
    }

    /**
     * 司机提交抢单命令
     * @param message
     * @return
     */
    default SubmitGrabOrderCommand toJntSubmitGrabOrderCommand(Message message) {
        String data = message.getStringProperty("data");
        SubmitGrabDTO dto = JsonUtil.fromJson(data, SubmitGrabDTO.class);
        GrabOrderType type = dto.getGrabOrder() ? GrabOrderType.GRAB_CENTRE : GrabOrderType.BROADCAST;
        return new SubmitGrabOrderCommand(dto.getDrivId(), dto.getOrderId(), dto.getDuid(), type, dto.getNonPremiumOrders(), ParentCategoryEnum.JNT.getCode());
    }


    /**
     * 司机提交抢单命令
     * @param message
     * @return
     */
    default SubmitGrabOrderCommand toDaySubmitGrabOrderCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String duid = message.getStringProperty("duid");
        long driverId = message.getLongProperty("driverId");
        boolean grabOrder = message.getBooleanProperty("grabOrder");
        GrabOrderType type = grabOrder ? GrabOrderType.GRAB_CENTRE : GrabOrderType.BROADCAST;
        return new SubmitGrabOrderCommand(driverId, dspOrderId, duid, type, "", ParentCategoryEnum.DAY.getCode());
    }

    default ExpireGrabOrderCommand toExpireGrabOrderCommand(Message message) {
        String duid = message.getStringProperty("duid");
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long driverId = message.getLongProperty("driverId");
        Long expire = message.getLongProperty("expire");
        String driverIdsStr = message.getStringProperty("driverIds");
        List<Long> driverIds = new ArrayList<>();
        if(StringUtils.isNotBlank(driverIdsStr)){
            String[] strArray = driverIdsStr.split(",");
            List<String> strings = new ArrayList<>(Arrays.asList(strArray));
            driverIds = strings.stream().map(Long::valueOf).collect(Collectors.toList());
        }
        return new ExpireGrabOrderCommand(duid, dspOrderId, driverId, expire, driverIds);
    }
    
    default SelectGrabOrderCommand toSelectGrabOrderCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Integer subSkuId = message.getIntProperty("subSkuId");
        return new SelectGrabOrderCommand(dspOrderId, subSkuId);
    }

    default DriverOrderConfirmFailCommand toDriverOrderConfirmFailCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String driverOrderId = message.getStringProperty("driverOrderId");
        Long driverId = message.getLongProperty("driverId");
        return new DriverOrderConfirmFailCommand(driverOrderId, dspOrderId, driverId);
    }

    default RemindDispatcherGrabOrderCommand toRemindDispatcherGrabOrderCommand(Message message) {
        String ids = message.getStringProperty("dispatcherGrabOrderIds");
        if (StringUtils.isBlank(ids)) {
            return new RemindDispatcherGrabOrderCommand(Collections.emptyList());
        }
        List<Long> list = JsonUtil.fromJson(ids, new TypeReference<List<Long>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            return new RemindDispatcherGrabOrderCommand(Collections.emptyList());
        }
        return new RemindDispatcherGrabOrderCommand(list);
    }

    default ReDispatchConfirmCommand toReDispatchConfirmCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String oldDspOrderId = message.getStringProperty("oldDspOrderId");
        return new ReDispatchConfirmCommand(dspOrderId, oldDspOrderId);
    }

    default CreateOrderWayPointTagCommand toCreateOrderWayPointTagCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String userOrderId = message.getStringProperty("userOrderId");
        return new CreateOrderWayPointTagCommand(userOrderId, dspOrderId);
    }

    default UpdateOrderSettlementBeforeTakenCommand toUpdateOrderSettlementBeforeTakenCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String userOrderId = message.getStringProperty("userOrderId");
        String categoryCode = message.getStringProperty("categoryCode");
        Integer carTypeId = message.getIntProperty("carTypeId");
        Integer serviceProviderId = message.getIntProperty("serviceProviderId");
        Integer orderSourceCode = message.getIntProperty("orderSourceCode");
        Integer orderVersion = message.getIntProperty("orderVersion");
        Integer settleToDriver = message.getIntProperty("settleToDriver");
        Long supplierId = message.getLongProperty("supplierId");
        Integer cityId = message.getIntProperty("cityId");
        return new UpdateOrderSettlementBeforeTakenCommand(userOrderId, dspOrderId, carTypeId, categoryCode, supplierId, serviceProviderId, orderSourceCode, orderVersion, settleToDriver, cityId);
    }

    default CreateGrabOrderDriverIndexCommand toCreateGrabOrderDriverIndexCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        String driverIds = message.getStringProperty("driverIds");
        return new CreateGrabOrderDriverIndexCommand(dspOrderId, JacksonSerializer.INSTANCE().deserialize(driverIds, new TypeReference<List<Long>>() {
        }));
    }

    default ConfirmGrabOrderSnapshotCommand toConfirmGrabOrderSnapshotCommand(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        return new ConfirmGrabOrderSnapshotCommand(dspOrderId);
    }
}
