package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSCreateDspOrderExeCmd;
import com.ctrip.dcs.application.provider.converter.SaaSCreateDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Component
@ServiceLogTagPair(key = "saasCreateDspOrderInfo.baseInfo.vbkOrderId", alias = "vbkOrderId")
public class SaasCreateDspOrderExecutor extends AbstractRpcExecutor<SaasCreateDspOrderRequestType, SaasCreateDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(SaasCreateDspOrderExecutor.class);

    @Autowired
    private SaaSCreateDspOrderExeCmd saaSCreateDspOrderExeCmd;

    @Override
    public SaasCreateDspOrderResponseType execute(SaasCreateDspOrderRequestType requestType) {
        SaasCreateDspOrderResponseType responseType = new SaasCreateDspOrderResponseType();
        if (Objects.isNull(requestType.getCreateDspOrderInfo())
                || Objects.isNull(requestType.getCreateDspOrderInfo().getBaseInfo())
                || Objects.isNull(requestType.getCreateDspOrderInfo().getFromPoi())
                || Objects.isNull(requestType.getCreateDspOrderInfo().getToPoi())) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            String dspOrderId = saaSCreateDspOrderExeCmd.execute(SaaSCreateDspOrderConverter.converter(requestType));
            responseType.setDspOrderId(dspOrderId);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("saasCreateDspOrderException", ex);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }
}
