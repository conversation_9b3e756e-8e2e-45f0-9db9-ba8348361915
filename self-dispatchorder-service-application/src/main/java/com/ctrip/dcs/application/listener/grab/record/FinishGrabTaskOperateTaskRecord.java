package com.ctrip.dcs.application.listener.grab.record;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component(value = VBKGrabOperationTypeEnum.FINISH_SERVICE)
public class FinishGrabTaskOperateTaskRecord extends AbstractOperateTaskRecord{

    @Override
    protected String buildRecordContent(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        return String.format(map.getString(FINISH_CONTENT, FINISH_CONTENT_STR), vbkGrabTaskOperateDTO.getSuccessNum());
    }

    @Override
    protected void assembleParam(VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO) {
        VBKDriverGrabTaskDO vbkDriverGrabTaskDO = vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(vbkGrabTaskOperateDTO.getTaskId());
        if(Objects.nonNull(vbkDriverGrabTaskDO)){
            vbkGrabTaskOperateDTO.setCityId(vbkDriverGrabTaskDO.getCityId());
            vbkGrabTaskOperateDTO.setSupplierId(vbkDriverGrabTaskDO.getSupplierId());
        }
        int successNum = vbkDriverGrabOrderRepository.countByTaskIdAndStatus(vbkGrabTaskOperateDTO.getTaskId(), GrabTaskStatus.SUCCESS.getCode());
        vbkGrabTaskOperateDTO.setSuccessNum(successNum);
    }

}
