package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SaaSUpdateDspOrderCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.*;


@Component
public class SaaSUpdateDspOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SaaSUpdateDspOrderExeCmd.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private DspOrderDetailRepository dspOrderDetailRepository;
    @Autowired
    private DspOrderOperateRepository dspOrderOperateRepository;
    @Autowired
    private DriverOrderGateway driverOrderGateway;
    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;
    @Autowired
    protected DistributedLockService distributedLockService;
    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;
    @Autowired
    protected QueryDspOrderService orderQueryService;
    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;


    public void execute(SaaSUpdateDspOrderCommand cmd) throws SQLException {
        logger.info("SaaSUpdateDspOrderExeCmd_execute", LocalJsonUtils.toJson(cmd));
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, cmd.getDspOrderDO());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            //是否为新流程
            if (Objects.equals(cmd.getNewProcess(), CommonConstants.NEW_PROCESS)) {
                executeNew(cmd);
                return;
            }
            //校验
            DspOrderVO dspOrderVO = selfOrderQueryGateway.queryDspOrder(cmd.getDspOrderDO().getDspOrderId());
            if (!dspOrderVO.getSupplierId().toString().equals(cmd.getSupplierId().toString())) {
                logger.info("SaaSUpdateDspOrderExeCmd_execute_valid_SupplierId", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.CHECK_VALIDATE_SUPPLIERID_ERROR.getBizException();
            }
            if (OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderVO.getOrderStatus())) {
                logger.info("SaaSUpdateDspOrderExeCmd_execute_valid_ORDER_CANCEL", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }
            if (OrderStatusEnum.ORDER_FINISH.getCode().equals(dspOrderVO.getOrderStatus())) {
                logger.info("SaaSUpdateDspOrderExeCmd_execute_valid_ORDER_FINISH", LocalJsonUtils.toJson(cmd));
                throw ErrorCode.ORDER_STATUS_UN_SUPPORT_ASSIGN_ERROR.getBizException();
            }

            //以下为处理携程新单和非携程单，携程单只允许修改司机可见备注
            DspOrderDO oldDspOrderDO = dspOrderRepository.find(cmd.getDspOrderDO().getDspOrderId());
            if(Objects.isNull(oldDspOrderDO)){
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            DspOrderDetailDO oldSDspOrderDetailDO = dspOrderDetailRepository.find(oldDspOrderDO.getDspOrderId());
            if(Objects.isNull(oldSDspOrderDetailDO)){
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            //组装出发点与到达点
            dealFromAndToPoi(cmd, oldDspOrderDO);
            //组装详情表中的拓展字段
            dealExtendDriverRemark(cmd, oldSDspOrderDetailDO, dspOrderVO.getOrderSourceCode());

            //修改数据库
            dspOrderOperateRepository.updateDspOrderHandle(cmd.getDspOrderDO(), cmd.getDspOrderDetailDO(), cmd.getDspOrderFeeDO());

            if (dspOrderVO.getOrderStatus() > OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) {
                DspOrderDO newDspOrderDO = dspOrderRepository.find(cmd.getDspOrderDO().getDspOrderId());
                if(Objects.isNull(newDspOrderDO)){
                    throw ErrorCode.NULL_ORDER_ERROR.getBizException();
                }
                DspOrderDetailDO newSDspOrderDetailDO = dspOrderDetailRepository.find(oldDspOrderDO.getDspOrderId());
                if(Objects.isNull(newSDspOrderDetailDO)){
                    throw ErrorCode.NULL_ORDER_ERROR.getBizException();
                }
                //调用司机端
                updateDriverOrder(oldDspOrderDO, newDspOrderDO, oldSDspOrderDetailDO, newSDspOrderDetailDO, null);
            }
        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        }catch (BizException e){
            throw e;
        }catch (Exception e) {
            logger.error(e);
            throw ErrorCode.SERVER_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }
    
    private void executeNew(SaaSUpdateDspOrderCommand cmd) throws SQLException {
        //查询派发单
        DspOrderDO oldDspOrderDO = dspOrderRepository.find(cmd.getDspOrderDO().getDspOrderId());
        if (!oldDspOrderDO.getSupplierId().toString().equals(cmd.getSupplierId().toString())) {
            logger.info("SaaSUpdateDspOrderExeCmd_executeNew_valid_SupplierId", LocalJsonUtils.toJson(cmd));
            throw ErrorCode.CHECK_VALIDATE_SUPPLIERID_ERROR.getBizException();
        }
        //携程渠道单不允许操作
        if (Objects.equals(OrderSourceCodeEnum.TRIP.getCode(), oldDspOrderDO.getOrderSourceCode())) {
            throw new BizException("ctrip source order not allow update");
        }
        //状态校验
        if (!CommonConstants.UPDATE_ORDER_ALLOW_STATUS_LIST.contains(oldDspOrderDO.getOrderStatus())) {
            logger.info("SaaSUpdateDspOrderExeCmd_executeNew_orderStatus_not_allow", LocalJsonUtils.toJson(cmd));
            throw ErrorCode.ORDER_STATUS_NOT_SUPPORT_MODIFY.getBizException();
        }
        //查询派发单详情
        DspOrderDetailDO oldSDspOrderDetailDO = dspOrderDetailRepository.find(oldDspOrderDO.getDspOrderId());
        if(Objects.isNull(oldSDspOrderDetailDO)) {
            throw ErrorCode.NULL_ORDER_ERROR.getBizException();
        }
        //组装出发点与到达点
        dealFromAndToPoi(cmd, oldDspOrderDO);
        //设置派发详情的扩展内容
        setDspOrderDetailExtendInfo(cmd.getDspOrderDetailDO(), oldSDspOrderDetailDO);
        //修改数据库
        boolean isUpdateUserOrderId = !Objects.equals(cmd.getDspOrderDO().getUserOrderId(), oldDspOrderDO.getUserOrderId());
        dspOrderOperateRepository.updateDspOrderHandleNew(cmd.getDspOrderDO(), cmd.getDspOrderDetailDO(), cmd.getDspOrderFeeDO(), isUpdateUserOrderId);
        //同步司机单
        if (oldDspOrderDO.getOrderStatus() > OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) {
            DspOrderDO newDspOrderDO = dspOrderRepository.find(cmd.getDspOrderDO().getDspOrderId());
            if(Objects.isNull(newDspOrderDO)){
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            DspOrderDetailDO newSDspOrderDetailDO = dspOrderDetailRepository.find(oldDspOrderDO.getDspOrderId());
            if(Objects.isNull(newSDspOrderDetailDO)){
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            //调用司机端
            updateDriverOrder(oldDspOrderDO, newDspOrderDO, oldSDspOrderDetailDO, newSDspOrderDetailDO, CommonConstants.NEW_PROCESS);
        }
    }
    
    private void setDspOrderDetailExtendInfo(DspOrderDetailDO dspOrderDetailDO, DspOrderDetailDO oldSDspOrderDetailDO) {
        Map<String, Object> extendInfoMap = Optional.ofNullable(oldSDspOrderDetailDO.getExtendInfoMap()).orElse(Maps.newHashMap());
        extendInfoMap.put(SysConstants.Order.REMARK,dspOrderDetailDO.getRemark());
        extendInfoMap.put(SysConstants.Order.VBK_DISTRIBUTOR_NAME,dspOrderDetailDO.getVbkDistributorName());
        extendInfoMap.put(SysConstants.Order.VBK_DRIVER_SETTLE_CURRENCY,dspOrderDetailDO.getVbkDriverSettleCurrency());
        extendInfoMap.put(SysConstants.Order.VBK_DRIVER_SETTLE_PRICE,dspOrderDetailDO.getVbkDriverSettlePrice());
        dspOrderDetailDO.setExtendInfo(JsonUtil.toJson(extendInfoMap));
    }
    
    
    private static void dealExtendDriverRemark(SaaSUpdateDspOrderCommand cmd, DspOrderDetailDO oldSDspOrderDetailDO, Integer orderSourceCode) {
        HashMap extendHashMap = JsonUtil.fromJson(oldSDspOrderDetailDO.getExtendInfo(), HashMap.class);
        if(Objects.isNull(extendHashMap)){
            extendHashMap = new HashMap();
        }
        extendHashMap.put("driverRemark", cmd.getDspOrderDetailDO().getDriverRemark());
        if(Objects.nonNull(orderSourceCode) &&  !orderSourceCode.equals(OrderSourceCodeEnum.TRIP.getCode())) {
            extendHashMap.put("vbkDistributorName", cmd.getDspOrderDetailDO().getVbkDistributorName());
        }
        cmd.getDspOrderDetailDO().setExtendInfo(LocalJsonUtils.toJson(extendHashMap));
    }

    private static void dealFromAndToPoi(SaaSUpdateDspOrderCommand cmd, DspOrderDO oldDspOrderDO) {
        HashMap fromJsonHashMap = JsonUtil.fromJson(oldDspOrderDO.getFromPoi(), HashMap.class);
        if(Objects.isNull(fromJsonHashMap)){
            fromJsonHashMap = new HashMap();
        }
        fromJsonHashMap.put("cityId", cmd.getDspOrderDO().getFromPoiDTO().getCityId());
        fromJsonHashMap.put("fromAddress", cmd.getDspOrderDO().getFromPoiDTO().getFromAddress());
        cmd.getDspOrderDO().setFromPoi(LocalJsonUtils.toJson(fromJsonHashMap));

        HashMap toJsonHashMap = JsonUtil.fromJson(oldDspOrderDO.getToPoi(), HashMap.class);
        if(Objects.isNull(toJsonHashMap)){
            toJsonHashMap = new HashMap();
        }
        toJsonHashMap.put("cityId", cmd.getDspOrderDO().getToPoiDTO().getCityId());
        toJsonHashMap.put("toAddress", cmd.getDspOrderDO().getToPoiDTO().getToAddress());
        cmd.getDspOrderDO().setToPoi(JsonUtil.toJson(toJsonHashMap));
    }


    private void updateDriverOrder(DspOrderDO oldDspOrderDO, DspOrderDO newDspOrderDO,
                                   DspOrderDetailDO oldSDspOrderDetailDO, DspOrderDetailDO newSDspOrderDetailDO, Integer newProcess) {
        try {
            driverOrderGateway.update(oldDspOrderDO, newDspOrderDO, oldSDspOrderDetailDO, newSDspOrderDetailDO, newProcess);
        } catch (Exception e) {
            logger.error("SaaSUpdateDspOrderExeCmd_updateDriverOrder_ex", e);
        }
    }
    
    public void updateOrderRemark(UpdateOrderRemarkRequestType requestType) throws Exception {
        DistributedLockService.DistributedLock lock = null;
        try {
            String dspOrderId = requestType.getDspOrderId();
            //加锁
            String key = String.format(SysConstants.DISTRIBUTED_LOCK_UPDATE_DSP_ORDER_FROM_SAAS, dspOrderId);
            lock = distributedLockService.getLock(key);
            if (!lock.tryLock()) {
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            //查询派发单
            DspOrderDO dspOrder = dspOrderRepository.find(dspOrderId);
            if(Objects.isNull(dspOrder)) {
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            //查询派发单详情
            DspOrderDetailDO dspOrderDetail = dspOrderDetailRepository.find(dspOrderId);
            if(Objects.isNull(dspOrderDetail)) {
                throw ErrorCode.NULL_ORDER_ERROR.getBizException();
            }
            List<Integer> finalStatusList = Lists.newArrayList(OrderStatusEnum.DRIVER_SERVICE_END.getCode(), OrderStatusEnum.ORDER_FINISH.getCode(), OrderStatusEnum.ORDER_CANCEL.getCode());
            String oldDriverRemark = (String) Optional.ofNullable(dspOrderDetail.getExtendInfoMap()).orElse(Maps.newHashMap()).get(SysConstants.Order.DRIVER_REMARK);
            //订单处于终态,不允许修改司机可见备注
            if (finalStatusList.contains(dspOrder.getOrderStatus()) && !Objects.equals(Optional.ofNullable(oldDriverRemark).orElse(""), Optional.ofNullable(requestType.getDriverRemark()).orElse(""))) {
                throw ErrorCode.ORDER_STATUS_NOT_SUPPORT_MODIFY.getBizException();
            }
            //非携程渠道及携程渠道新流程 -更新订单备注
            updateOrderRemarkByNewProcess(requestType, dspOrder, dspOrderDetail);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }
    
    private void updateOrderRemarkByNewProcess(UpdateOrderRemarkRequestType requestType, DspOrderDO dspOrder, DspOrderDetailDO dspOrderDetail) throws SQLException {
        Map<String, Object> extendInfoMap = Optional.ofNullable(dspOrderDetail.getExtendInfoMap()).orElse(Maps.newHashMap());
        String oldDriverRemark = (String) extendInfoMap.get(SysConstants.Order.DRIVER_REMARK);
        //大于220状态则调用司机单服务更新
        if (dspOrder.getOrderStatus() > OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) {
            //调用司机端-更新司机可见备注
            driverOrderGateway.updateOrderRemark(dspOrder, oldDriverRemark, requestType.getDriverRemark());
        }
        //更新扩展内容
        setExtendInfo(extendInfoMap, requestType);
        dspOrderDetail.setExtendInfo(LocalJsonUtils.toJson(extendInfoMap));
        //更新DB
        dspOrderOperateRepository.updateDspOrderDetail(dspOrderDetail);
        // 更新抢单快照
        updateGrabDspOrderSnapshotDriverVisibleRemark(requestType, dspOrder);

    }

    public void updateGrabDspOrderSnapshotDriverVisibleRemark(UpdateOrderRemarkRequestType requestType, DspOrderDO dspOrder) {
        try {
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(dspOrder.getDspOrderId());
            if (snapshot != null) {
                snapshot.setDriverVisibleRemark(requestType.getDriverRemark());
                grabDspOrderSnapshotRepository.updateDriverVisibleRemark(snapshot);
            }
        } catch (Exception e) {
            logger.error("SaaSUpdateDspOrderExeCmd_updateDriverOrder_ex", e);
        }
    }

    private void setExtendInfo(Map<String, Object> extendInfoMap, UpdateOrderRemarkRequestType requestType) {
        extendInfoMap.put(SysConstants.Order.DRIVER_REMARK,requestType.getDriverRemark());
        extendInfoMap.put(SysConstants.Order.SUPPLIER_REMARK, requestType.getSupplierRemark());
        extendInfoMap.put(SysConstants.Order.VBK_DRIVER_SETTLE_PRICE, requestType.getVbkDriverSettlePrice());
        extendInfoMap.put(SysConstants.Order.VBK_DRIVER_SETTLE_CURRENCY,requestType.getVbkDriverSettleCurrency());
    }
}
