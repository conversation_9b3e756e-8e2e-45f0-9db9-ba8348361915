package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.api.ConfirmGrabOrderSnapshotCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ConfirmGrabOrderSnapshotExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(ConfirmGrabOrderSnapshotExeCmd.class);

    public void execute(ConfirmGrabOrderSnapshotCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, command.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
            }
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(command.getDspOrderId());
            if (snapshot == null) {
                return;
            }
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(command.getDspOrderId());
            // 更新订单抢单快照和映射
            updateGrabDspOrderSnapshot(dspOrder, snapshot);
            // 更是司机抢单大厅
            updateOtherGrabDriverIndex(dspOrder);
        } catch (Exception e) {
            logger.warn("UpdateGrabOrderSnapshotError", e);
            throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }

    private void updateGrabDspOrderSnapshot(DspOrderVO dspOrder, GrabDspOrderSnapshotDO snapshot) {
        if (Objects.isNull(dspOrder) || Objects.isNull(snapshot) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.SUCCESS)) {
            return;
        }
        List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(dspOrder.getDspOrderId());
        if (Objects.equals(dspOrder.getOrderStatus(), OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode())) {
            snapshot.taken(dspOrder, indexes);
        }
        if (Objects.equals(dspOrder.getOrderStatus(), OrderStatusEnum.DRIVER_CONFIRMED.getCode())) {
            snapshot.driverConfirm(dspOrder);
        }
        if (Objects.equals(dspOrder.getOrderStatus(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode())) {
            snapshot.dispatcherConfirm(dspOrder);
        }
        if (CollectionUtils.isNotEmpty(indexes)) {
            for (GrabDspOrderDriverIndexDO index : indexes) {
                index.updateValid(2);
            }
        }
        // 更新db
        saveOrUpdate(snapshot, indexes);
    }
}
