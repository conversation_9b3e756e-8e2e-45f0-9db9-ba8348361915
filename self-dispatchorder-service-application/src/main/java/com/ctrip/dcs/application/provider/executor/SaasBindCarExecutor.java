package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSOtherBindCarExeCmd;
import com.ctrip.dcs.application.command.SaaSTripBindCarExeCmd;
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.util.LocalStringUtils;
import com.ctrip.dcs.domain.dsporder.repository.CkLogRepository;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSCarInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOperatorInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOrderInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSSupplierInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * saas绑定车辆，包含了携程渠道订单和非携程渠道订单
 */
@Component
@ServiceLogTagPair(key = "order.dspOrderId", alias = "dspOrderId")
public class SaasBindCarExecutor extends AbstractRpcExecutor<SaasBindCarRequestType, SaasBindCarResponseType> implements Validator<SaasBindCarRequestType> {



    private static final Logger logger = LoggerFactory.getLogger(SaasBindCarExecutor.class);

    @Autowired
    private SaaSOtherBindCarExeCmd saaSOtherBindCarExeCmd;
    @Autowired
    private SaaSTripBindCarExeCmd saaSTripBindCarExeCmd;
    @Autowired
    private CkLogRepository ckLogRepository;

    @Override
    public SaasBindCarResponseType execute(SaasBindCarRequestType requestType) {
        SaasBindCarResponseType responseType = new SaasBindCarResponseType();
        long startTime = System.currentTimeMillis();
        long timeConsume =  0L;
        Date assignTimeBj = new Date();
        String resCode="200";
        String resMessage="success";
        SaaSOperateDriverCarCommand converter=null;
        try {
            if (!validRequestByOrderSourceCode(requestType)) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());

            }
            converter = SaaSOperateDriverCarConverter.converter(requestType);
            if (Objects.equals(converter.getOrderSourceCode(), OrderSourceCodeEnum.TRIP)) {
                saaSTripBindCarExeCmd.execute(converter);
            } else {
                saaSOtherBindCarExeCmd.execute(converter);
            }
        } catch (BizException e) {
            HashMap<String, String> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("1", e.getCode());
            objectObjectHashMap.put("2", e.getMessage());
            logger.error("SaaSTripBindCarExeCmd_handleSelfBindCarOldOrder111", LocalJsonUtils.toJson(objectObjectHashMap));
            resCode =e.getCode();
            resMessage=e.getMessage();
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());

        } catch (Exception ex) {
            logger.error("SaasChangeDriverExecutor", ex);
            resCode ="500";
            resMessage="";
            return ServiceResponseUtils.fail(responseType);
        }finally {
            //记录ck日志
            timeConsume = System.currentTimeMillis() - startTime;
            if(converter != null && converter.getAssignResLogVO() != null){
                converter.getAssignResLogVO().setAssignResCode(resCode);
                converter.getAssignResLogVO().setAssignResDesc(resMessage);
                converter.getAssignResLogVO().setTimeConsumption(timeConsume);
                converter.getAssignResLogVO().setAssignTimeBj(assignTimeBj);
                ckLogRepository.saveAssignResLog(converter.getAssignResLogVO());
            }
        }
        return ServiceResponseUtils.success(responseType);
    }

    private boolean validRequestByOrderSourceCode(SaasBindCarRequestType requestType) {
        SaaSOrderInfo order = requestType.getOrder();


        Integer orderSourceCode = order.getOrderSourceCode();
        String dspOrderId = order.getDspOrderId();
        if(Objects.isNull(orderSourceCode) || LocalStringUtils.isEmpty(dspOrderId)){
            logger.warn("SaasBindCarExecutor_validRequestByOrderSourceCode","orderSourceCode | dspOrderId is null");
            return false;
        }

        SaaSSupplierInfo supplier = requestType.getSupplier();
        Long supplierId = supplier.getSupplierId();
        if(Objects.isNull(supplierId)){
            logger.warn("SaasBindCarExecutor_validRequestByOrderSourceCode","supplierId is null");
            return false;
        }


        SaaSOperatorInfo operator = requestType.getOperator();
        String operatorUserType = operator.getOperatorUserType();
        String operatorUserAccount = operator.getOperatorUserAccount();
        String operatorUserName = operator.getOperatorUserName();
        if(LocalStringUtils.isEmpty(operatorUserType) || LocalStringUtils.isEmpty(operatorUserAccount) || LocalStringUtils.isEmpty(operatorUserName)){
            logger.warn("SaasBindCarExecutor_validRequestByOrderSourceCode","operatorUserType|operatorUserAccount|operatorUserName is null");
            return false;
        }

        SaaSCarInfo car = requestType.getCar();
        Integer carId = car.getCarId();
        if(OrderSourceCodeEnum.TRIP.getCode().equals(order.getOrderSourceCode())){
            if(Objects.isNull(carId)){
                logger.warn("SaasBindCarExecutor_validRequestByOrderSourceCode","carId is null");
                return false;
            }
        }

        //非携程渠道，如果车、司机是自营的则传入id
        if(Objects.isNull(carId)){
            String carLicense = car.getCarLicense();
            if(LocalStringUtils.isEmpty(carLicense)){
                logger.warn("SaasBindCarExecutor_validRequestByOrderSourceCode","carLicense is null");
                return false;
            }
        }
        return true;
    }

    @Override
    public void validate(AbstractValidator<SaasBindCarRequestType> validator) {
        validator.ruleFor("order").notNull();
        validator.ruleFor("car").notNull();
        validator.ruleFor("supplier").notNull();
        validator.ruleFor("operator").notNull();

    }
}