package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.AllOrderStatus;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public abstract class AbstractSaaSOperateValidator {

    public static final Logger logger = LoggerFactory.getLogger(AbstractSaaSOperateValidator.class);


    @Autowired
    protected DcsVbkSupplierOrderGateway dcsVbkSupplierOrderGateway;

    public SaaSBusinessVO validate(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        validDspOrderVONonNull(saaSBusinessVO);
        //vbk融合项目中，可以没有车辆信息
        if(!cmd.isNewProcess()){
            validVehicleVONonNull(saaSBusinessVO);
        }
        validDriverVONonNull(saaSBusinessVO);
        validBusiness(cmd, saaSBusinessVO);

        return saaSBusinessVO;
    }

    protected boolean isOpenChangeButton(SaaSOperateDriverCarCommand cmd, DspOrderVO order) {
        if (Boolean.TRUE.equals(cmd.isNewProcess()) && (SysConstants.AssignRole.SYS_ROLE_SUPPLIER.equals(cmd.getAssignRole()) || SysConstants.AssignRole.SYS_ROLE_SAAS.equals(cmd.getAssignRole()))) {
            return dcsVbkSupplierOrderGateway.queryOrderCanChangeDriverNew(order, cmd.getSupplierId().intValue(), order.getTakenType() == null ? TakenType.SUPPLIER.getCode() : order.getTakenType(), cmd.isNewProcess(), order.getShortDisOrder());
        }
        return true;
    }

    protected void validVehicleVONonNull(SaaSBusinessVO saaSBusinessVO) {
        if (saaSBusinessVO.getVehicleVO() == null) {
            throw ErrorCode.ABSENT_VEHICLE_EXCEPTION.getBizException();
        }
    }


    protected void validDriverVONonNull(SaaSBusinessVO saaSBusinessVO) {
        if (saaSBusinessVO.getDriverVO() == null) {
            throw ErrorCode.FAIL_BY_DRIVER_INFO.getBizException();
        }
    }


    protected void validDspOrderVONonNull(SaaSBusinessVO saaSBusinessVO) {
        DspOrderVO dspOrderVO = saaSBusinessVO.getDspOrderVO();
        if (dspOrderVO == null) {
            throw ErrorCode.NULL_ORDER_ERROR.getBizException();
        }
    }

    protected abstract void validBusiness(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO);

    protected void validateSuppliers(SaaSOperateDriverCarCommand cmd, SaaSBusinessVO saaSBusinessVO) {
        String supplierIdDspOrder = saaSBusinessVO.getDspOrderVO().getSupplierId().toString();
        DriverVO driverVO = saaSBusinessVO.getDriverVO();
        Long carSupplierId = saaSBusinessVO.getVehicleVO() == null ? null : saaSBusinessVO.getVehicleVO().getSupplierId();
        String driverSuppliers = driverVO.getSupplier().getSupplierId().toString();
        HashMap<Object, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("saaSBusinessVO", LocalJsonUtils.toJson(saaSBusinessVO));
        objectObjectHashMap.put("cmd", LocalJsonUtils.toJson(cmd));
        //首先派发单和操作人必须一致
        if(!supplierIdDspOrder.equals(cmd.getSupplierId().toString())){
            logger.warn("AbstractSaaSOperateValidator_supplierIdDspOrder_getSupplierId", LocalJsonUtils.toJson(objectObjectHashMap));
            throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
        }
        //司机供应商和操作人供应商一致，证明是关联关系，需要：操作人、车辆、司机的供应商必须完全一致
        if(driverSuppliers.equals(cmd.getSupplierId().toString())){
            if(!cmd.isNewProcess()){
                if ( carSupplierId != null && !driverSuppliers.equals(carSupplierId.toString())){
                    logger.warn("AbstractSaaSOperateValidator_driverSuppliers_carSupplierId", LocalJsonUtils.toJson(objectObjectHashMap));
                    throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
                }
                //司机和操作人供应商一致 同时 司机和 车辆供应商也一致 并且派发单和操作人供应商也一致，证明四个都是一致的
            }
        }else{
            //司机派遣供应商包含操作人供应商，证明是派遣关系，需要：操作人、订单、司机的供应商必须完全一致
            List<Long> dispatchSupplierIdList = driverVO.getSupplier().getDispatchSupplierIdList();
            Set<String> accreditSuppliers = CollectionUtils.isEmpty(dispatchSupplierIdList) ? new HashSet<>(0) : dispatchSupplierIdList.stream().map(Object::toString).collect(Collectors.toSet());
            if(!accreditSuppliers.contains(cmd.getSupplierId().toString())){
                logger.warn("AbstractSaaSOperateValidator_accreditSuppliers_getSupplierId", LocalJsonUtils.toJson(objectObjectHashMap));
                throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
            }
            //司机无绑定车辆
            if(Objects.isNull(driverVO.getCar()) || driverVO.getCar().getCarId() == 0L){
                //用供应商自己的车，同时用派遣的司机是可以指派
                if(!cmd.isNewProcess()){ //vbk融合项目中不再判断车辆所属供应商是否与订单上一致
                    if(carSupplierId != null && !carSupplierId.toString().equals(supplierIdDspOrder)){
                        logger.warn("AbstractSaaSOperateValidator_noncar_carSupplierId_supplierIdDspOrder", LocalJsonUtils.toJson(objectObjectHashMap));
                        throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
                    }
                }
            }else {
                //判断填入的车和司机绑定的车是不是同一辆车
                Long carId = saaSBusinessVO.getVehicleVO() == null ? 0L :saaSBusinessVO.getVehicleVO().getCarId();
                if (!driverVO.getCar().getCarId().equals(carId)){
                    if(!cmd.isNewProcess()) { //vbk融合项目中不再判断车辆所属供应商是否与订单上一致
                        //如果判断填入的车和司机绑定的车不是同一辆车，判断车辆供应商与派发单供应商是不是同一个
                        if(carSupplierId != null && !carSupplierId.toString().equals(supplierIdDspOrder)){
                            logger.warn("AbstractSaaSOperateValidator_car_carSupplierId_supplierIdDspOrder", LocalJsonUtils.toJson(objectObjectHashMap));
                            throw ErrorCode.SUPPLIER_ID_INCONSISTENT_ERROR.getBizException();
                        }
                    }

                }
            }
        }
    }
}
