package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.UpdateGrabOrderSnapshotCommand;
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand;
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDetailDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderDetailPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.common.converter.DspOrderVOConvertor;
import com.ctrip.dcs.infrastructure.handler.DispatcherGrabOrderPushRuleHandler;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class UpdateGrabOrderSnapshotExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabOrderSnapshotExeCmd.class);

    @Autowired
    private DispatcherGrabOrderPushRuleHandler dispatcherGrabOrderPushRuleHandler;

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @Autowired
    private UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DspOrderDao dspOrderDao;

    @Autowired
    private DspOrderDetailDao dspOrderDetailDao;

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    public void execute(UpdateGrabOrderSnapshotCommand command) {
        try {
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(command.getDspOrderId());
            if (Objects.isNull(snapshot) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.SUCCESS) || Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.CANCEL)) {
                return;
            }
            execute(snapshot, command.getEvent());
        } catch (Exception e) {
            logger.warn("UpdateGrabOrderSnapshotError", e);
            throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
        }
    }

    public void execute(GrabDspOrderSnapshotDO snapshot, GrabDspOrderSnapshotEventEnum event) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, snapshot.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
            }
            logger.info("GrabDspOrderSnapshotInfo_" + snapshot.getDspOrderId(), "dspOrderId: {}, event: {}", snapshot.getDspOrderId(), event.name());
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(snapshot.getDspOrderId());
            List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.queryFomCache(snapshot.getDspOrderId());
            boolean ok = validate(dspOrder, snapshot);
            if (!ok) {
                // 状态不合法， 取消抢单快照
                logger.info("GrabDspOrderSnapshotInfo_" + snapshot.getDspOrderId(), "order status illegal, cancel snapshot, dspOrderId: {}", snapshot.getDspOrderId());
                snapshot.cancel(indexes);
                saveOrUpdate(snapshot, indexes);
                return;
            }
            // 更新发单规则
            boolean success = snapshot
                    .updateOrderDetail(dspOrder)
                    .rule(indexes);
            if (!success) {
                // 未匹配到发单规则， 取消抢单快照
                logger.info("GrabDspOrderSnapshotInfo_" + snapshot.getDspOrderId(), "rule is empty, cancel snapshot, dspOrderId: {}", snapshot.getDspOrderId());
                if (DateUtil.isAfter(snapshot.getGrabPushTimeBj(), new Date())) {
                    // 余进说的，若无规则，发单时间晚于当前的，才取消
                    snapshot.cancel(indexes);
                    saveOrUpdate(snapshot, indexes);
                }
                return;
            }
            // 过检查项，更新司机映射状态
            List<Long> driverIds = indexes.stream().map(GrabDspOrderDriverIndexDO::getDriverId).distinct().toList();
            // 检查
            List<CheckModel> check = check(dspOrder, snapshot.getDuid(), driverIds);
            // 更新映射状态
            updateIndexesCheckCode(indexes, check);
            // 更新司机映射的用车时间
            updateIndexesEstimatedUseTime(snapshot, indexes);
            saveOrUpdate(snapshot, indexes);
        } catch (Exception e) {
            logger.warn("UpdateGrabOrderSnapshotError", e);
        } finally {
            lock.unlock();
        }
    }

    public void updateIndexesEstimatedUseTime(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
        for (GrabDspOrderDriverIndexDO index : indexes) {
            index.updateEstimatedUseTime(snapshot.getEstimatedUseTimeBj());
        }
    }
}
