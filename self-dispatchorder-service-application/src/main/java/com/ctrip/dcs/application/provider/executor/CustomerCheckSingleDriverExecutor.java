package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CustomerCheckSingleDriverCmd;
import com.ctrip.dcs.application.command.api.CustomerCheckSingleDriverCommand;
import com.ctrip.dcs.domain.dsporder.entity.CustomerCheckSingleDriverResDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckSingleDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckSingleDriverResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.DriverCheckDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class CustomerCheckSingleDriverExecutor extends AbstractRpcExecutor<CustomerCheckSingleDriverRequestType, CustomerCheckSingleDriverResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCheckSingleDriverExecutor.class);

    @Autowired
    private CustomerCheckSingleDriverCmd customerCheckSingleDriverCmd;

    @Override
    public CustomerCheckSingleDriverResponseType execute(CustomerCheckSingleDriverRequestType requestType) {
        CustomerCheckSingleDriverResponseType responseType = new CustomerCheckSingleDriverResponseType();
        try {
            CustomerCheckSingleDriverResDTO resDTO = customerCheckSingleDriverCmd.check(buildReq(requestType));
            convert(responseType, resDTO);
            return ServiceResponseUtils.success(responseType);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("driverCheckList error", e);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    private void convert(CustomerCheckSingleDriverResponseType responseType, CustomerCheckSingleDriverResDTO resDTO) {
        DriverCheckDTO checkDTO = new DriverCheckDTO();
        BeanUtils.copyProperties(resDTO, checkDTO);
        responseType.setData(checkDTO);
    }

    private CustomerCheckSingleDriverCommand buildReq(CustomerCheckSingleDriverRequestType requestType) {
        CustomerCheckSingleDriverCommand customerCheckSingleDriverCommand = new CustomerCheckSingleDriverCommand();
        BeanUtils.copyProperties(requestType, customerCheckSingleDriverCommand);
        return customerCheckSingleDriverCommand;
    }

}
