package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.PushOrderRemindExeCmd;
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd;
import com.ctrip.dcs.application.command.api.*;
import com.ctrip.dcs.application.command.dispatchergrab.Approve2SuccessDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.OrderVersionEnum;
import com.ctrip.dcs.domain.common.enums.PushOrderRemindType;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.ModifyJntOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;

/**
 * 调度确认后处理服务
 * <AUTHOR>
 */
@Component
public class DispatcherConfirmedService {

    private static final Logger logger = LoggerFactory.getLogger(DispatcherConfirmedService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private PushOrderRemindExeCmd pushOrderRemindExeCmd;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Autowired
    private GrabCentreRepository grabCentreRepository;

    @Autowired
    private GrabOrderDetailRepository grabOrderDetailRepository;

    @Autowired
    private UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Autowired
    private DspOrderDao dspOrderDao;

    @Autowired
    private Approve2SuccessDispatcherGrabOrderExeCmd approve2SuccessDispatcherGrabOrderExeCmd;


    public void confirmed(String dspOrderId, Long confirmRecordId, Long transportGroupId) {
        DspOrderVO dspOrder = queryDspOrderService.query(dspOrderId);
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(transportGroupId);
        DspOrderConfirmRecordVO confirmRecord = dspOrderConfirmRecordRepository.find(confirmRecordId);

        // 原单修改商家同意中，将修改待确认更新处理成功
        approve2success(dspOrder, confirmRecord);
        // 进单提醒
        pushOrderRemind(dspOrder, transportGroup);
        // 发送客服日志
        sendWorkBenchLog(dspOrder, confirmRecord);
        // 发送VBK订单操作记录
        sendVBKOperationRecord(dspOrder, confirmRecord);
        // 终止调度
        shutdownSchedule(dspOrder);
        // 取消调度抢单
        cancelDispatcherGrabOrder(dspOrder);
        // 取消抢单大厅
        cancelGrabCentre(dspOrderId);
        // 保存结算信息
        updateOrderDetailSettlementInfo(dspOrder, confirmRecord);
    }

    private void approve2success(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        Approve2SuccessDispatcherGrabOrderCommand cmd = new Approve2SuccessDispatcherGrabOrderCommand();
        cmd.setUserOrderId(dspOrder.getUserOrderId());
        cmd.setOriDspOrderId(dspOrder.getOriginalDspOrderId());
        cmd.setSupplierId(confirmRecord.getSupplierInfo().getSupplierId());
        cmd.setModifyVersion(dspOrder.getModifyVersion());
        approve2SuccessDispatcherGrabOrderExeCmd.execute(cmd);
    }

    public void updateOrderDetailSettlementInfo(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        // 查询订单是否给司机结算
        updateOrderSettleToDriverBeforeTakenCmd.execute(new UpdateOrderSettleToDriverBeforeTakenCommand(
                dspOrder.getUserOrderId(),
                dspOrder.getDspOrderId(),
                dspOrder.getCarTypeId(),
                dspOrder.getCategoryCode().getType(),
                confirmRecord.getSupplierInfo().getSupplierId(),
                confirmRecord.getSupplierInfo().getServiceProviderId(),
                dspOrder.getOrderSourceCode(),
                OrderVersionEnum.CTRIP.getVersion(),
                dspOrder.getSalesMode(),
                dspOrder.getCityId()
                )
        );
    }

    private void cancelGrabCentre(String dspOrderId) {
        grabCentreRepository.deleteAll(dspOrderId);
        grabOrderDetailRepository.deleteAll(dspOrderId);
    }

    /**
     * 进单提醒
     * @param dspOrderVO
     * @param transportGroup
     */
    public void pushOrderRemind(DspOrderVO dspOrderVO, TransportGroupVO transportGroup) {
        try {
            // 压测订单不进行调度通知
            if (StringUtils.isNotBlank(dspOrderVO.getUid()) && dspOrderVO.getUid().startsWith("test")) {
                logger.info("dispatchConfirmPush", String.format("dispatchConfirmPush testUid need not push,UID=%s,userOrderID=%s,supplyOrderID=%s", dspOrderVO.getUid(), dspOrderVO.getUserOrderId(), dspOrderVO.getSupplyOrderId()));
                return;
            }
            // 原单修改订单，且原供应商接单
            if (YesOrNo.isYes(dspOrderVO.getOriOrderModify()) && YesOrNo.isYes(dspOrderVO.getOriginalSupplierConfirm())) {
                if (YesOrNo.isYes(dspOrderVO.getNeedSupplierAgree())) {
                    // 有损，不发送进单提醒,但正常发送超时未派遣提醒
                    pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrderVO, transportGroup, Lists.newArrayList(PushOrderRemindType.getOriModifyDispatcherConfirmNeedConfirmRemindTypes())));
                    return;
                }
                // 无损，发送修改通知
                ModifyJntOrderVO modifyJntOrderVO = buildModifyJntOrderVO(dspOrderVO);
                pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrderVO, transportGroup, Lists.newArrayList(PushOrderRemindType.getOriModifyDispatcherConfirmNoNeedConfirmRemindTypes()), modifyJntOrderVO));
            } else {
                pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrderVO, transportGroup, Lists.newArrayList(PushOrderRemindType.getDispatcherConfirmRemindTypes())));
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    @NotNull
    private ModifyJntOrderVO buildModifyJntOrderVO(DspOrderVO dspOrderVO) throws SQLException {
        // 无损，发送修改通知和送超时未派遣提醒
        // 查询用户单修改信息
        ModifyJntOrderVO modifyJntOrderVO = igtOrderQueryServiceGateway.queryModifyJntOrder(dspOrderVO.getUserOrderId(), dspOrderVO.getModifyVersion());
        if (Objects.isNull(modifyJntOrderVO)) {
            return null;
        }
        // 查询旧派单，获取旧单的预估用车时间
        DspOrderPO oldDspOrder = dspOrderDao.findByDspOrderId(dspOrderVO.getOriginalDspOrderId());
        if (Objects.nonNull(oldDspOrder)) {
            modifyJntOrderVO.getOldOrderInfo().setEstimatedUseTime(DateUtil.getStringDate(oldDspOrder.getEstimatedUseTime()));
        }
        // 设置新单的最晚确认时间和预估用车时间
        modifyJntOrderVO.getNewOrderInfo().setLastConfirmTime(DateUtil.getStringDate(dspOrderVO.getLastConfirmTime()));
        modifyJntOrderVO.getNewOrderInfo().setEstimatedUseTime(DateUtil.getStringDate(dspOrderVO.getEstimatedUseTime()));
        return modifyJntOrderVO;
    }

    /**
     * 发送客服工作台日志
     * @param dspOrderVO
     * @param confirmRecord
     */
    public void sendWorkBenchLog(DspOrderVO dspOrderVO, DspOrderConfirmRecordVO confirmRecord) {
        try {
            WorkBenchLogMessage message = workBenchLogMessageFactory.createDispatcherConfirmWorkBenchLog(dspOrderVO, confirmRecord);
            workBenchLogGateway.sendWorkBenchLogMessage(message);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public void sendVBKOperationRecord(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        try {
            VBKOperationRecordVO record = vbkOperationRecordFactory.createOperationRecord(dspOrder, confirmRecord);
            vbkOperationRecordGateway.record(record);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public void shutdownSchedule(DspOrderVO dspOrder) {
        try {
            List<ScheduleDO> schedules = scheduleRepository.query(dspOrder.getDspOrderId());
            if (CollectionUtils.isEmpty(schedules)) {
                return;
            }
            for (ScheduleDO schedule : schedules) {
                shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(schedule, ScheduleEventType.DSP_ORDER_NOT_DISPATCHING));
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public void cancelDispatcherGrabOrder(DspOrderVO dspOrderDO) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(dspOrderDO.getUserOrderId());
            cancelDispatcherGrabOrderExeCmd.execute(command);
        } catch (Exception e) {
            logger.error("cancel_dispatcher_grab_order_error", String.format("cancel_dispatcher_grab_order_error,userOrderId=%s", dspOrderDO.getUserOrderId()), e);
        }
    }
}
