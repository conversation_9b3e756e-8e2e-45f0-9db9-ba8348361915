package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSUpdateDspOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/20 20:03
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
@ServiceLogTagPair(key = "vbkOrderId", alias = "vbkOrderId")
public class UpdateOrderRemarkExecutor extends AbstractRpcExecutor<UpdateOrderRemarkRequestType, UpdateOrderRemarkResponseType> implements Validator<UpdateOrderRemarkRequestType> {
    
    private static final Logger logger = LoggerFactory.getLogger(UpdateOrderRemarkExecutor.class);
    @Resource
    private SaaSUpdateDspOrderExeCmd saaSUpdateDspOrderExeCmd;
    
    @Override
    public UpdateOrderRemarkResponseType execute(UpdateOrderRemarkRequestType requestType) {
        UpdateOrderRemarkResponseType responseType = new UpdateOrderRemarkResponseType();
        try {
            saaSUpdateDspOrderExeCmd.updateOrderRemark(requestType);
            return ServiceResponseUtils.success(responseType);
        } catch(BizException ex) {
            logger.warn("update order remark","update order remark is BizException", ex, Maps.newHashMap());
            return ServiceResponseUtils.fail(responseType, ex.getCode(), ex.getMessage());
        } catch (Exception ex) {
            logger.warn("update order remark","update order remark is Exception", ex, Maps.newHashMap());
            return ServiceResponseUtils.fail(responseType);
        }
    }
    @Override
    public void validate(AbstractValidator<UpdateOrderRemarkRequestType> validator, UpdateOrderRemarkRequestType requestType) {
        if (StringUtils.isBlank(requestType.getDspOrderId())) {
            throw ErrorCode.ERROR_PARAMS.getBizException();
        }
        //"供应商司机结算价"和"供应商司机币种" 要么都为空,要么都不为空
        if (Objects.nonNull(requestType.getVbkDriverSettlePrice()) && StringUtils.isBlank(requestType.getVbkDriverSettleCurrency())) {
            throw ErrorCode.VBK_DRIVER_SETTLE_CURRENCY_IS_NULL.getBizException();
        }
        if (StringUtils.isNotBlank(requestType.getVbkDriverSettleCurrency()) && Objects.isNull(requestType.getVbkDriverSettlePrice())) {
            throw ErrorCode.VBK_DRIVER_SETTLE_PRICE_IS_NULL.getBizException();
        }
    }
}
