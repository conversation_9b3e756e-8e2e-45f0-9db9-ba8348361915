package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.QueryIvrRecordCommand;
import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO;
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordRequestType;
import org.springframework.beans.BeanUtils;

public class QueryIvrRecordConverter {

    public static QueryIvrRecordCommand converter(QueryIvrRecordRequestType request) {
        Assert.notNull(request);
        QueryIvrRecordCommand cmd = new QueryIvrRecordCommand();
        BeanUtils.copyProperties(request,cmd);
        return cmd;
    }

    public static IvrRecordInfo converterInfo(IvrRecordDO ivrRecordDO){
        Assert.notNull(ivrRecordDO);
        IvrRecordInfo ivrRecordInfo = new IvrRecordInfo();
        BeanUtils.copyProperties(ivrRecordDO,ivrRecordInfo);
        return ivrRecordInfo;
    }
}
