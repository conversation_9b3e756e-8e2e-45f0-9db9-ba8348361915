package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.domain.common.value.ReasonDetailVO;
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ReDispatchReason;

import java.util.Objects;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class ReDispatchSubmitConverter {

    public static ReDispatchSubmitCommand converter(ReDispatchSubmitRequestType requestType) {
        Assert.notNull(requestType);
        ReDispatchSubmitCommand cmd = new ReDispatchSubmitCommand();
        cmd.setUserOrderId(requestType.getUserOrderId());
        cmd.setDspOrderId(requestType.dspOrderId);
        cmd.setDrvId(requestType.getDriverId());
        cmd.setDriverOrderId(requestType.driverOrderId);
        cmd.setRoleId(requestType.roleId);
        cmd.setReasonDetailId(requestType.reasonDetailId);
        cmd.setReasonId(requestType.reasonId);
        cmd.setBookTime(requestType.bookTime);
        cmd.setExtraFee(requestType.extraFee);
        cmd.setDriverPhone(requestType.driverPhone);
        cmd.setTransportGroupId(requestType.transportGroupId);
        cmd.setSupplierId(requestType.supplierId);
        cmd.setSutSource(requestType.getSutSource());
        cmd.setNewRights(requestType.newRights);
        if (requestType.getIsUseReDispatchRight() != null) {
            cmd.setIsUseReDispatchRight(requestType.getIsUseReDispatchRight());
        }
        return cmd;
    }

    public static ReDispatchReason converter(ReasonDetailVO reason) {
        if (Objects.isNull(reason)) {
            return null;
        }
        ReDispatchReason cmd = new ReDispatchReason();
        cmd.setEffectTime(reason.getEffectTime());
        cmd.setReasonId(reason.getReasonId());
        cmd.setReasonDetailId(reason.getId());
        cmd.setPunishRoleId(reason.getPunishRole());
        cmd.setResponsible(false);
        cmd.setShowFront(false);
        if (Objects.nonNull(reason.getResponsible())) {
            cmd.setResponsible(reason.getResponsible() == 0 ? false : true);
        }
        if (Objects.nonNull(reason.getFrontShow())) {
            cmd.setShowFront(reason.getFrontShow() == 0 ? false : true);
        }
        cmd.setPunishRoleName(reason.getPunishName());
        cmd.setPunishRoleId(reason.getPunishRole());
        cmd.setReasonName(reason.getReasonDetail());
        //checkResultTips
        cmd.setAlert(reason.getAlert());
        cmd.setCarFaultTime(LocalDateUtils.toCalendar(reason.getCarFaultTime(),LocalDateUtils.YYYY_MM_DD_HH_MM_SS));
        return cmd;
    }
}
