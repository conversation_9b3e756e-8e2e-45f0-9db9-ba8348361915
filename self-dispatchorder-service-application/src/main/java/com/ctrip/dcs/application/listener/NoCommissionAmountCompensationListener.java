package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.sql.SQLException;


@Component
public class NoCommissionAmountCompensationListener {

    private static final Logger logger = LoggerFactory.getLogger(NoCommissionAmountCompensationListener.class);

    @Autowired
    private DspOrderFeeDao dspOrderFeeDao;

    @QmqLogTag(tagKeys = {"dspOrderId","userOrderId"})
    @QmqConsumer(prefix = EventConstants.DCS_DSP_ORDER_NO_COMMISSION_AMOUNT_COMPENSATION, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) throws SQLException {
        String dspOrderId = message.getStringProperty("dspOrderId");
        BigDecimal realNoCommissionAmount = new BigDecimal(message.getStringProperty("realNoCommissionAmount"));
        BigDecimal dbNoCommissionAmount = new BigDecimal(message.getStringProperty("dbNoCommissionAmount"));
        if (StringUtils.isBlank(dspOrderId)) {
            return;
        }
        logger.info("dsp_order_fee_not_equal" + dspOrderId, "update dsp_order_fee realNoCommissionAmount=" + realNoCommissionAmount + "dbNoCommissionAmount=" + dbNoCommissionAmount);
        dspOrderFeeDao.updateOrderNoCommissionAmount(dspOrderId, realNoCommissionAmount);
    }
}
