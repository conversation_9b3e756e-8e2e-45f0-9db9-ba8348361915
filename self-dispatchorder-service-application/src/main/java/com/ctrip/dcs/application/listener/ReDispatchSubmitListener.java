package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd;
import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.SharkKey;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.enums.RoleReflectEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.ReasonDetailVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.LogContentDTO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseOrderDispatchCaseGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.event.ImNoticeEvent;
import com.ctrip.dcs.infrastructure.adapter.redis.IDispatchModifyInfoCacheService;
import com.ctrip.dcs.infrastructure.common.constants.NoticeEnum;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ctrip.dcs.domain.common.constants.EventConstants.QMQ_RETRY_TIMES;

/**
 * 改派消息
 *
 * <AUTHOR>
 */
@Component
public class ReDispatchSubmitListener {
    private static final Logger logger = LoggerFactory.getLogger(ReDispatchSubmitListener.class);
    @Autowired
    private DspOrderRepository dspOrderRepository;
    @Autowired
    private PurchaseOrderDispatchCaseGateway purchaseOrderDispatchCaseGateway;
    @Autowired
    private IDispatchModifyInfoCacheService cacheService;
    @Autowired
    private DspOrderConfirmRecordRepository recordRepository;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private ReDispatchSubmitExeCmd reDispatchSubmitExeCmd;

    @Autowired
    private MessageProviderService messageProducer;

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_REDISPATCH_SUBMIT_TOPIC, consumerGroup = "100041593_redispatch_notice_platform", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        logger.info("ReDispatchSubmitListener.onMessage begin", JacksonUtil.serialize(message));
        String dspOrderId = message.getStringProperty("dspOrderId");
        String userOrderId = message.getStringProperty("userOrderId");
        String driverOrderId = message.getStringProperty("driverOrderId");

        Integer roleId = message.getIntProperty("roleId");
        Integer reasonDetailId = message.getIntProperty("reasonDetailId");

        if (Strings.isBlank(dspOrderId) || Strings.isBlank(userOrderId)) {
            return;
        }
        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("ReDispatchSubmitListener_redispatch_notice_platform qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
            if (dspOrderDO == null) {
                logger.info("ReDispatchSubmitListener_redispatch_notice_platform dspOrder is null,dspOrderId=" + dspOrderId);
                return;
            }
            //存储用户手机号-改派过的司机id
            //存储订单id-改派过的供应商id
            cacheDspInfo(dspOrderDO, reasonDetailId);

            DspOrderConfirmRecordVO recordVO = recordRepository.findByDspOrderId(dspOrderId, driverOrderId);

            String sourceDriverId = "";
            String sourceSupplierId = "";

            if (recordVO != null) {
                sourceDriverId = recordVO.queryDriverIdStr();
                sourceSupplierId = recordVO.querySupplierIdStr();
            }

            messageProducer.send(new ImNoticeEvent(userOrderId, "", NoticeEnum.REASSIGN.getCode(), sourceSupplierId, sourceDriverId, "", ""));

            purchaseOrderDispatchCaseGateway.notice(userOrderId, dspOrderId, dspOrderDO.getSupplyOrderId(), roleId, reasonDetailId);
        } catch (Exception e) {
            logger.error("ReDispatchSubmitListener.error", e);
            throw e;
        }
    }

    @QmqConsumer(prefix = EventConstants.DSP_ORDER_CARFAULTFREEZE_DISPATCH_TOPIC, consumerGroup = "100041593_carfaultfreeze_redispatch", idempotentChecker = "redisIdempotentChecker")
    public void onCarFaultFreezeMessage(Message message) {
        logger.info("ReDispatchSubmitListener.onCarFaultFreezeMessage begin", JacksonUtil.serialize(message));

        String drvId = message.getStringProperty("driverId");
        String log = message.getStringProperty("log");
//        String dspOrderId = message.getStringProperty("dspOrderId");
        String userOrderId = message.getStringProperty("userOrderId");
        String oldSysExceptBookTimeStr = message.getStringProperty("sysExceptBookTimeBj");
        Integer freezeTotalHours = message.getIntProperty("freezeTotalHours");
        boolean onlyNew = message.getBooleanProperty("onlyNew");
        if (StringUtils.isBlank(drvId) || Long.valueOf(drvId) == 0L || StringUtils.isBlank(userOrderId) || StringUtils.isBlank(oldSysExceptBookTimeStr)
                || freezeTotalHours == null) {
            return;
        }

        if (message.times() > QMQ_RETRY_TIMES) {
            logger.info("ReDispatchSubmitListener_carfaultfreeze_redispatch qmq retries exceeds the maximum", JacksonUtil.serialize(message));
            return;
        }
        try {
            Date now = new Date();
            String sysExpectBookTimeStart = DateUtil.getStringDate(now);
            String sysExpectBookTimeEnd = DateUtil.getStringDate(DateUtil.addSeconds(now, freezeTotalHours * 3600));
            List<DspOrderVO> orderVOList = queryDspOrderService.selectSectionWaitingServiceOrder(Long.valueOf(drvId), sysExpectBookTimeStart, sysExpectBookTimeEnd, onlyNew);
            LogContentDTO logContentDTO = JacksonUtil.deserialize(log, LogContentDTO.class);

            if (CollectionUtils.isEmpty(orderVOList)) {
                WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(userOrderId, JacksonUtil.serialize(logContentDTO), SharkKey.REASSIGNMENT_TITLE_3, SharkKey.REASSIGNMENT_CONTENT_3);
                workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
                logger.info("ReDispatchSubmitListener.onCarFaultFreezeMessage_info", "The current offline driver has no impact on the order and does not need to be processed" + JacksonUtil.serialize(message));
                return;
            }
            ReDispatchSubmitCommand cmd = new ReDispatchSubmitCommand();
            cmd.setRoleId(RoleReflectEnum.ROLE_SYSTEM.getCode());
            cmd.setReasonDetailId(Integer.valueOf(ReassignTaskEnum.SpecialReflectReasonEnum.CAR_FAULT_FREEZE.getCode()));
            cmd.setUserName("system");
            cmd.setNewRights(0);
            orderVOList.forEach(index -> {
                try {
                    cmd.setUserOrderId(index.getUserOrderId());
                    logger.info("ReDispatchSubmitListener.onCarFaultFreezeMessage_info", "Recurrent execution of reassignment orders." + JacksonUtil.serialize(cmd));
                    Result<ReasonDetailVO> result = reDispatchSubmitExeCmd.execute(cmd);
                    if (!result.isSuccess()) {
                        MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags(result.getCode()));
                    }
                    MetricsUtil.recordValue("redispatch.count",MetricsUtil.getMetricsTags("200"));

                } catch (Exception e) {
                    logger.error("ReDispatchSubmitListener.onCarFaultFreezeMessage_error" + cmd.getUserOrderId(), e);
                }
            });
            List<String> orderIds = orderVOList.stream().map(DspOrderVO::getUserOrderId).collect(Collectors.toList());
            logContentDTO.setReassignmentOrderId(StringUtils.join(orderIds, ","));
            WorkBenchLogMessage workBenchLogMessage = workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(userOrderId, JacksonUtil.serialize(logContentDTO), SharkKey.REASSIGNMENT_TITLE_3, SharkKey.REASSIGNMENT_CONTENT_3);
            workBenchLogGateway.sendWorkBenchLogMessage(workBenchLogMessage);
        } catch (Exception e) {
            logger.error("ReDispatchSubmitListener.error", e);
            throw e;
        }
    }


    /**
     * 存储用户订单id-改派过的司机id
     * 存储用户订单id-改派过的供应商id
     *
     * @param dspOrderDO
     * @param reasonDetailId
     */
    private void cacheDspInfo(DspOrderDO dspOrderDO, Integer reasonDetailId) {
        //改派过的司机
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = recordRepository.findByDspOrderId(dspOrderDO.getDspOrderId(), dspOrderDO.getDriverOrderId());
        if (dspOrderConfirmRecordVO != null && dspOrderConfirmRecordVO.getDriverInfo() != null) {
            Long driverId = dspOrderConfirmRecordVO.getDriverInfo().getDriverId();
            if (driverId != null) {
                cacheService.cacheOrderDspModifyDriver(dspOrderDO.getUserOrderId(), driverId.toString());
            }
        }
        //自营供应商发起的全程改派
        if (ReassignTaskEnum.SpecialReflectReasonEnum.SELF_ALL_DISPATCH.getCode().equals(reasonDetailId.toString())) {
            //改派过的供应商
            cacheService.cacheOrderDspModifySupplier(dspOrderDO.getUserOrderId(), dspOrderDO.getSupplierId().toString());
        }
    }

}
