package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.service.OrderModifyBufferService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.schedule.dto.UserOrderModifyDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;


@Component
public class OrderModifyBufferListener {

    private static final Logger logger = LoggerFactory.getLogger(OrderModifyBufferListener.class);

    @Autowired
    private OrderModifyBufferService orderModifyBufferService;

    /**
     * 用户订单修改buffer
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=3780082667
     */
    @QmqConsumer(prefix = EventConstants.DCS_PURCHASE_DISPATCH_USETIME_CHANGE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "userOrderId", alias = "userOrderId")
    public void userOrderModifyBuffer(Message message) {
        String userOrderId = message.getStringProperty("userOrderId");
        if (StringUtils.isEmpty(userOrderId)) {
            return;
        }
        // 检查重试次数 times是这个消息总重试次数
        if (message.times() > 4) {
            logger.warn("userOrderModifyBuffer", "retry " + message.times());
            return;
        }
        Boolean modifyBufferCityFlag = message.getBooleanProperty("modifyBufferCityFlag");
        //非用户订单修改buffer的时间变更，不需要发送邮件和站内信
        if(!Boolean.TRUE.equals(modifyBufferCityFlag)){
            return;
        }
        int userChoseBufferMinutes = message.getIntProperty("userChoseBufferMinutes");
        //原始预估用车时间
        String orignalSysExpectBookTime = message.getStringProperty("orignalSysExpectBookTime");
        //预估用车时间
        String sysExpectBookTime = message.getStringProperty("sysExpectBookTime");

        UserOrderModifyDTO orderModify = new UserOrderModifyDTO();
        orderModify.setUserOrderId(userOrderId);
        orderModify.setOrignalSysExpectBookTime(orignalSysExpectBookTime);
        orderModify.setSysExpectBookTime(sysExpectBookTime);
        orderModify.setUserChoseBufferMinutes(userChoseBufferMinutes);
        //调用接口发送站内信和邮件
        orderModifyBufferService.orderModifyBuffer(orderModify);
    }
}
