package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.enums.ScheduleStatus;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.ScheduleDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.SchedulePO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调度补偿
 * <AUTHOR>
 */
@Component
public class RecreateScheduleJob extends BaseJob {

    private static final Logger logger = LoggerFactory.getLogger(RecreateScheduleJob.class);

    /**
     * 默认10分钟
     */
    private static final int DEFAULT_BEGIN_SECONDS = 60 * 10;

    private static final int DEFAULT_END_SECONDS = 60;

    @Autowired
    private DspOrderDao dspOrderDao;

    @Autowired
    private ScheduleDao scheduleDao;

    @Autowired
    private CreateScheduleExeCmd createScheduleExeCmd;

    @QSchedule("com.ctrip.dcs.dsporder.schedule.recreate.job")
    public void execute(Parameter parameter) throws Exception {
        Integer begin = getProperty(parameter, "beginSecond", DEFAULT_BEGIN_SECONDS);
        Integer end = getProperty(parameter, "endSecond", DEFAULT_END_SECONDS);
        Date beginTime = DateUtil.addSeconds(new Date(), -begin);
        Date endTime = DateUtil.addSeconds(new Date(), -end);
        int recreateCount = execute(beginTime, endTime);
        MetricsUtil.recordValue(MetricsConstants.RECREATE_DSP_ORDER_SCHEDULE_COUNT, recreateCount);
    }

    public int execute(Date beginTime, Date endTime) throws SQLException {
        List<DspOrderPO> dspOrders = dspOrderDao.queryToBeConfirmed(beginTime, endTime);
        if (CollectionUtils.isEmpty(dspOrders)) {
            logger.info("RecreateScheduleJob_Empty", "no dsp order to be confirmed");
            return NumberUtils.INTEGER_ZERO;
        }
        Set<String> dspOrderIds = dspOrders.stream().map(DspOrderPO::getDspOrderId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<SchedulePO> schedules = scheduleDao.queryByDspOrderIds(dspOrderIds);
        Set<String> schedulingDspOrderIds = Optional.ofNullable(schedules)
                .orElse(Collections.emptyList())
                .stream()
                .filter(s -> Objects.equals(s.getScheduleStatus(), ScheduleStatus.EXECUTING.getCode()))
                .map(SchedulePO::getDspOrderId)
                .collect(Collectors.toSet());
        logger.info("RecreateScheduleJob_SchedulingDspOrder", "schedulingDspOrderIds:{}", JacksonUtil.serialize(schedulingDspOrderIds));
        int result = 0;
        for (String dspOrderId : dspOrderIds) {
            if (schedulingDspOrderIds.contains(dspOrderId)) {
                continue;
            }
            logger.info("RecreateScheduleJob_CreateSchedule", "dspOrderId:{}", dspOrderId);
            createScheduleExeCmd.execute(new CreateScheduleCommand(dspOrderId, ScheduleEventType.DSP_ORDER_BOOK));
            result++;
        }
        return result;
    }
}
