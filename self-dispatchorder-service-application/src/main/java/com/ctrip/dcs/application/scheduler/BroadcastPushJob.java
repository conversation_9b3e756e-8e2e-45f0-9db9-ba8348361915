package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.event.BroadcastEvent;
import com.ctrip.dcs.domain.schedule.event.DspOrderBroadcastPushEvent;
import com.ctrip.dcs.domain.schedule.event.GuideBroadcastEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverInServiceGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统播报任务
 * <AUTHOR>
 */
@Component
public class BroadcastPushJob extends BaseJob {

    private static final Logger logger = LoggerFactory.getLogger(BroadcastPushJob.class);

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private SubSkuRepository subSkuRepository;

    @Autowired
    private TRocksProviderAdapter rocksProviderAdapter;

    @Autowired
    private DriverInServiceGateway driverInServiceGateway;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd;

    @QSchedule("com.crtip.dcs.dsporder.broadcast.push.job")
    public void execute(Parameter parameter) throws Exception {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer interval = getProperty(parameter, "interval", 1400);
        Integer size = getProperty(parameter, "size", 100);
        Long count = grabDspOrderDriverIndexRepository.queryCountBroadcastPushTime(DateUtil.addMinutes(new Date(), -interval), new Date());
        logger.info("BroadcastPushJobInfo_TotalCount", "queryCountBroadcastPushTime" + count);
        if (count <= 0L) {
            return;
        }
        long page = Math.ceilDiv(count, size);
        for (int i = 1; i <= page; i++) {
            List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.queryBroadcastPushTime(DateUtil.addMinutes(new Date(), -interval), new Date(), i, size);
            if (CollectionUtils.isEmpty(indexes)) {
                logger.info("BroadcastPushJobInfo_EmptyIndexes", "No broadcast push order");
                return;
            }
            logger.info("BroadcastPushJobInfo_Begin", "index: {}", JacksonSerializer.INSTANCE().serialize(indexes));
            updateBroadcastPushTime(indexes);
            sendBroadcastMessage(indexes);
            logger.info("BroadcastPushJobInfo_End", "index: {}", JacksonSerializer.INSTANCE().serialize(indexes));
        }
    }

    private void updateBroadcastPushTime(List<GrabDspOrderDriverIndexDO> indexes) {
        Map<String /*duid*/, SubSkuVO> subSkuMap = Maps.newHashMap();
        for (GrabDspOrderDriverIndexDO index : indexes) {
            if (!subSkuMap.containsKey(index.getDuid())) {
                DuidVO duid = DuidVO.of(index.getDuid());
                SubSkuVO subSkuVO = subSkuRepository.find(duid.getSubSkuId());
                subSkuMap.put(index.getDuid(), subSkuVO);
            }
            index.updateBroadcastPushTime(subSkuMap.get(index.getDuid()));
        }
        grabDspOrderDriverIndexRepository.updateBroadcastPushTime(indexes);
    }

    public void sendBroadcastMessage(List<GrabDspOrderDriverIndexDO> indexes) {
        long delay = 0L;
        Map<String, List<GrabDspOrderDriverIndexDO>> map = indexes.stream().collect(Collectors.groupingBy(GrabDspOrderDriverIndexDO::getDspOrderId));
        for (Map.Entry<String, List<GrabDspOrderDriverIndexDO>> entry : map.entrySet()) {
            String dspOrderId = entry.getKey();
            List<Long> driverIds = entry.getValue().stream().map(GrabDspOrderDriverIndexDO::getDriverId).distinct().toList();
            messageProviderService.send(new DspOrderBroadcastPushEvent(dspOrderId, driverIds, delay));
            delay += 500L;
        }
    }

}
