package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.provider.converter.ReDispatchedConverter;
import com.ctrip.dcs.application.query.ReasonsQuery;
import com.ctrip.dcs.domain.common.value.ReasonDetailVO;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsResponseType;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询改派原因
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/5/16 19:43
 */
@Component
@ServiceLogTagPair(key = "userOrderId")
public class QueryReasonsExecutor extends AbstractRpcExecutor<QueryReasonsRequestType, QueryReasonsResponseType> implements Validator<QueryReasonsRequestType> {

    @Resource
    private ReasonsQuery reasonsQuery;

    @Override
    public QueryReasonsResponseType execute(QueryReasonsRequestType req) {
        QueryReasonsResponseType responseType = new QueryReasonsResponseType();
        Result<List<ReasonDetailVO>> queryReasonRes = reasonsQuery.queryReason(req.getRoleId(), req.getUserOrderId());
        if (queryReasonRes.isSuccess() && queryReasonRes.getData() != null) {
            responseType.setReDispatchReasonList(ReDispatchedConverter.build(queryReasonRes.getData()));
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, queryReasonRes.getCode(), queryReasonRes.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryReasonsRequestType> validator) {
        validator.ruleFor("roleId").notNull().greaterThan(0);
        validator.ruleFor("userOrderId").notNull().notEmpty();
    }

}