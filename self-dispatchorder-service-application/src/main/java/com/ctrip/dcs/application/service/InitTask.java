package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.service.reDispath.ReassignTaskManger;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 初始化任务
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/5/25 16:22
 */
@Service
public class InitTask {

    private static final Logger logger = LoggerFactory.getLogger(InitTask.class);

    @PostConstruct
    public void init() {
        long startTime = System.currentTimeMillis();
        ReassignTaskManger.init();
        logger.info("InitTask", "Finish cost:{} ms", System.currentTimeMillis() - startTime);
    }

}