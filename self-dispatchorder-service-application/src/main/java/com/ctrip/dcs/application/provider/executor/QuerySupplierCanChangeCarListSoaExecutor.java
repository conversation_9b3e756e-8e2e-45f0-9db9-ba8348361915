package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.QueryCanChangeCarListCmd;
import com.ctrip.dcs.application.command.api.QuerySupplierCanChangeCarListCommand;
import com.ctrip.dcs.application.command.dto.CarInfo4VbkDTO;
import com.ctrip.dcs.application.provider.converter.SupplierCanChangeCarListConverter;
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaRequestType;
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QunarCheckDriverForDspRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.tools.picocli.CommandLine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class QuerySupplierCanChangeCarListSoaExecutor extends AbstractRpcExecutor<QuerySupplierCanChangeCarListSoaRequestType, QuerySupplierCanChangeCarListSoaResponseType> implements Validator<QuerySupplierCanChangeCarListSoaRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QuerySupplierCanChangeCarListSoaExecutor.class);

    @Autowired
    QueryCanChangeCarListCmd queryCanChangeCarListCmd;

    @Override
    public QuerySupplierCanChangeCarListSoaResponseType execute(QuerySupplierCanChangeCarListSoaRequestType requestType) {
        QuerySupplierCanChangeCarListSoaResponseType responseType = new QuerySupplierCanChangeCarListSoaResponseType();
        try {
            QuerySupplierCanChangeCarListCommand command = new QuerySupplierCanChangeCarListCommand(requestType.getDspOrderId(),requestType.getSupplierId(),requestType.getCityId());
            List<CarInfo4VbkDTO> carInfo4VbkDTOS = queryCanChangeCarListCmd.querySupplierCanChangeCarList(command);
            responseType.setData(SupplierCanChangeCarListConverter.convert(carInfo4VbkDTOS));
        }catch (Exception e){
            logger.error("QuerySupplierCanChangeCarListSoaExecutor", e);
            return ServiceResponseUtils.fail(responseType);
        }
        return ServiceResponseUtils.success(responseType);
    }


    @Override
    public void validate(AbstractValidator<QuerySupplierCanChangeCarListSoaRequestType> validator, QuerySupplierCanChangeCarListSoaRequestType req) {
        if (StringUtils.isEmpty(req.getDspOrderId())) {
            throw new BizException("dspOrderId is empty");
        }
        if (req.getCityId() == null) {
            throw new BizException("cityId is empty");
        }
        if (Objects.isNull(req.getSupplierId())) {
            throw new BizException("supplierId is null");
        }
    }
}
