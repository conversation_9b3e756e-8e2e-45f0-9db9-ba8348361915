package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ConfirmDelayDspOrderExeCmd;
import com.ctrip.dcs.application.command.api.ConfirmDelayDspOrderCommand;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.application.provider.converter.ConfirmDelayDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ConfirmDelayDspOrderExecutor extends AbstractRpcExecutor<ConfirmDelayDspOrderRequestType, ConfirmDelayDspOrderResponseType> implements Validator<ConfirmDelayDspOrderRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(ConfirmDelayDspOrderExecutor.class);

    @Autowired
    private ConfirmDelayDspOrderExeCmd cmd;

    @Override
    public ConfirmDelayDspOrderResponseType execute(ConfirmDelayDspOrderRequestType requestType) {
        try {
            ConfirmDelayDspOrderCommand command = ConfirmDelayDspOrderConverter.toConfirmDelayDspOrderCommand(requestType);
            cmd.execute(command);
            return ServiceResponseUtils.success(new ConfirmDelayDspOrderResponseType());
        } catch (ValidateException e) {
            logger.warn(e);
            return ServiceResponseUtils.fail(new ConfirmDelayDspOrderResponseType(), e.getErrorCode().getCode(), e.getErrorCode().getDesc());
        } catch (OrderStatusException e) {
            logger.warn(e);
            return ServiceResponseUtils.fail(new ConfirmDelayDspOrderResponseType(), e.getErrorCode().getCode(), e.getErrorCode().getDesc());
        }catch (BizException e) {
            logger.warn(e);
            return ServiceResponseUtils.fail(new ConfirmDelayDspOrderResponseType(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error(e);
            return ServiceResponseUtils.fail(new ConfirmDelayDspOrderResponseType(), ErrorCode.CONFIRM_DELAY_DSP_ORDER_ERROR.getCode(), ErrorCode.CONFIRM_DELAY_DSP_ORDER_ERROR.getDesc());
        }
    }

    @Override
    public void validate(AbstractValidator<ConfirmDelayDspOrderRequestType> validator) {
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("driverId").notNull();
        validator.ruleFor("transportGroupId").notNull();
        validator.ruleFor("duid").notNull().notEmpty();
    }
}
