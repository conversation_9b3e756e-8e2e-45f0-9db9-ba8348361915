package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.service.dto.*;

public interface IGrabOrderPushRuleService {
    public boolean add(GrabOrderPushRuleDTO grabOrderPushRuleDTO);
    public boolean deleteById(Long ruleId,String operateUser,Long supplierId);
    public boolean update(GrabOrderPushRuleDTO grabOrderPushRuleDTO);
    public QueryGrabOrderPushRuleResultDTO query(QueryGrabOrderPushRuleParamDTO paramDTO);
    public QueryGrabOrderPushRuleRecordResultDTO queryRuleRecord(QueryGrabOrderPushRuleRecordParamDTO paramDTO);
    public GrabOrderPushRuleDTO queryById(Long ruleId);
}
