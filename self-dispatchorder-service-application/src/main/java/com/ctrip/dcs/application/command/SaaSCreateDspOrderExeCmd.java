package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.IdGeneratorService;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;



@Component
public class SaaSCreateDspOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SaaSCreateDspOrderExeCmd.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderOperateRepository dspOrderOperateRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    private IdGeneratorService idGeneratorService;


    public String execute(CreateDspOrderCommand cmd) {
        logger.info("SaaSCreateDspOrderExeCmd_enter", JacksonUtil.serialize(cmd));
        DspOrderDO dspOrderDO = cmd.getDspOrderDO();
        String idempotentKey = String.format(SysConstants.DISTRIBUTED_LOCK_CREATE_DSP_ORDER_FROM_SAAS, dspOrderDO.getVbkOrderId());
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(idempotentKey);
        try {
            if (!lock.tryLock()) {
                // 加锁失败
                throw ErrorCode.IDEMPOTENT_ERR_SAAS.getBizException();
            }
            //数据库幂等
            String dbDspOrderId = checkDbExist(dspOrderDO);
            logger.info("SaaSCreateDspOrderExeCmd_checkDbExist", JacksonUtil.serialize(dbDspOrderId));
            if (!StringUtils.isEmpty(dbDspOrderId)) {
                return dbDspOrderId;
            }
            String dspOrderId = idGeneratorService.generateId().toString();
            dspOrderDO.setDspOrderId(dspOrderId);
            dspOrderDO.setOrderStatus(OrderStatusEnum.DISPATCH_CONFIRMED.getCode());
            cmd.getDspOrderDetailDO().setDspOrderId(dspOrderId);
            cmd.getDspOrderFeeDO().setDspOrderId(dspOrderId);
            logger.info("SaaSCreateDspOrderExeCmd_dealComplete", JacksonUtil.serialize(cmd));
            dspOrderOperateRepository.save(cmd.getDspOrderDO(), cmd.getDspOrderDetailDO(), cmd.getDspOrderFeeDO(), null, Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
            return dspOrderId;

        } catch (DistributedLockRejectedException e) {
            logger.error(e);
            throw ErrorCode.LOCK_FAILED.getBizException();
        } catch (BizException e){
            throw e;
        }catch (Exception e) {
            logger.error(e);
            throw ErrorCode.SERVER_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }

    private String checkDbExist(DspOrderDO dspOrderDO) {
        List<DspOrderDO> list = dspOrderRepository.queryDspOrdersByVbkOrderId(dspOrderDO.getVbkOrderId());
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0).getDspOrderId();
        }
        return null;
    }
}
