package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.service.FlightChangeService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;

@Component
public class FlightInfoListener {

    private static final Logger logger = LoggerFactory.getLogger(FlightInfoListener.class);


    @Autowired
    FlightChangeService flightChangeService;

    @QmqConsumer(prefix = EventConstants.DCS_FLIGHT_CHG_PROTECT_DEAL, consumerGroup =  EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "notifyOrderId", alias = "userOrderId")
    public void onFlightOrderNotifyMessage(Message message) {

        String userOrderId = message.getStringProperty("notifyOrderId");
        if (StringUtils.isEmpty(userOrderId)) {
            return;
        }

        // 检查重试次数 times是这个消息总重试次数
        if (message.times() > 4) {
            logger.warn("dispatcherRemind_qmq_consume", "dispatcherRemind_qmq_consume retry " + message.times() + ", messageId：" + message.getMessageId());
            return;
        }
        // localRetries是连续本地重试了多少次的意思(防止死循环)
        if (message.localRetries() > 2) {
            //60秒后再来看看能不能更新成功
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "Multiple local retries still failed");
        }
        flightChangeService.flightProtectNoticeVbk(userOrderId);
    }
}
