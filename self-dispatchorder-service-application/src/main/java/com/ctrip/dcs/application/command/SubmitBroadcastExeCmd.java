package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand;
import com.ctrip.dcs.application.command.validator.SubmitGrabValidator;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.FlowSwitchScenesEnum;
import com.ctrip.dcs.domain.common.enums.GrabOrderCode;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.IdempotentCheckService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.value.DriverUdlVO;
import com.ctrip.dcs.domain.common.value.OldDuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.event.GrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.event.SelectGrabOrderEvent;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository;
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;


/**
 * 播报抢单
 * <AUTHOR>
 */
@Component
public class SubmitBroadcastExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SubmitBroadcastExeCmd.class);

    private static final long DEFAULT_SELECT_SECONDS = 300L;

    @Autowired
    private BroadcastRepository broadcastRepository;

    @Autowired
    private SelectGrabOrderRepository selectGrabOrderRepository;

    @Autowired
    private SubmitGrabValidator validator;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private TRocksProviderAdapter rocksProviderAdapter;

    @Autowired
    private IdempotentCheckService idempotentCheckService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;
    
    @Resource
    QueryDriverService queryDriverService;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    public void execute(SubmitGrabOrderCommand command) {
        if (OldDuidVO.isOldDuid(command.getDuid())) {
            logger.info("SubmitBroadcastInfo", "old duid, duid is {}", command.getDuid());
            return;
        }
        GrabOrderDO grabOrder = broadcastRepository.find(command.getDuid(), command.getDriverId());
        logger.info("SubmitBroadcastExeCmd_GrabOrderDO" + command.getOrderId(), JsonUtils.toJson(command));
        logger.info("SubmitBroadcastExeCmd_GrabOrderDO" + command.getDriverId(), JsonUtils.toJson(command));
        try {
            validator.validate(grabOrder);
            grabOrder.submit();
            selectGrabOrderRepository.submit(grabOrder);
            if (isSelect(grabOrder.getDspOrderId(), grabOrder.getSubSkuId())) {
                // 发送轮选消息
                Long delay = broadcastGrabConfig.getLong(ConfigKey.BROADCAST_SELECT_TIMING_KEY, DEFAULT_SELECT_SECONDS);
                messageProvider.send(new SelectGrabOrderEvent(grabOrder.getDspOrderId(), grabOrder.getSubSkuId(), delay * 1000));
            }
        } catch (ValidateException e) {
            logger.warn(e);
            if (e.getErrorCode() == ErrorCode.GRAB_ORDER_EXPIRE_ERROR) {
                // 抢单失效异常，需要发送抢单失败消息
                sendGrabFailEvent(command, grabOrder);
            }
        } catch (Exception e) {
            logger.error(e);
            // 异常，需要发送抢单失败消息
            sendGrabFailEvent(command, grabOrder);
        }
    }

    private void sendGrabFailEvent(SubmitGrabOrderCommand command, GrabOrderDO grabOrder) {
        // 抢单结果映射
        Map<String /*code*/, String /*desc*/> mapping = broadcastGrabConfig.getMap(ConfigKey.BROADCAST_GRAB_RESULT_KEY);
        // 发送抢单失败消息
        String code = GrabOrderCode.ORDER_TAKEN_FAIL.getCode();
        String desc = mapping.getOrDefault(code, "");
        String userOrderId = Optional.ofNullable(grabOrder).map(GrabOrderDO::getUserOrderId).orElse("");
        if (!sysSwitchConfigGateway.getDriverQmqAddUcsSwitch()) {
            messageProvider.send(new GrabOrderFailEvent(command.getDriverId(), command.getDuid(), command.getOrderId(), userOrderId, code, desc));
            return;
        }
        DriverUdlVO driverUdlVO = queryDriverService.getDrvUdl(command.getDriverId());
        logger.info("submitBroadcastExeCmd sendGrabFailEvent", "driverUdlVO:" + JsonUtil.toJson(driverUdlVO));
        CatUtil.doWithUdlOverride(() -> {
            messageProvider.send(new GrabOrderFailEvent(command.getDriverId(), command.getDuid(), command.getOrderId(), userOrderId, code, desc));
            return Boolean.TRUE;
        }, driverUdlVO.getUdl(), driverUdlVO.getRequestFrom());
    }

    /**
     * 轮选幂等
     * @param dspOrderId
     * @param subSkuId
     * @return
     */
    private boolean isSelect(String dspOrderId, Integer subSkuId) {
        try {
            String key = GrabOrderDO.toSelectKey(dspOrderId, subSkuId);
            Long seconds = broadcastGrabConfig.getLong(ConfigKey.BROADCAST_SELECT_TIMING_KEY, DEFAULT_SELECT_SECONDS);
            return idempotentCheckService.isNotProcessed(key, seconds);
        } catch (Exception e) {
            logger.error(e);
        }
        return true;
    }

}
