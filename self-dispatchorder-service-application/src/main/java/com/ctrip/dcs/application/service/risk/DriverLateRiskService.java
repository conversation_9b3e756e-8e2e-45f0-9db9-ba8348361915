package com.ctrip.dcs.application.service.risk;

import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.DataSwitchVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.domain.schedule.value.PerformanceConfigVO;
import com.ctrip.igt.framework.common.exception.BizException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/7 11:26
 */
@Service
public class DriverLateRiskService {
    
    @Resource
    LateRiskTypeManager lateRiskTypeManager;
    @Resource
    DriverOrderGateway driverOrderGateway;
    @Resource
    QueryTransportGroupService queryTransportGroupService;
    @Resource
    SelfOrderQueryGateway selfOrderQueryGateway;
    
    public void sendLateRiskNotice(String dspOrderId, Integer lateRiskType) {
        DataSwitchVO dataSwitchVO = new DataSwitchVO();
        dataSwitchVO.setBaseDetailSwitch(Boolean.TRUE);
        dataSwitchVO.setConfirmRecordSwitch(Boolean.TRUE);
        DspOrderVO dspOrder = selfOrderQueryGateway.queryDspOrder(dspOrderId, dataSwitchVO);
        if (Objects.isNull(dspOrder)) {
            throw new BizException("dspOrder is null");
        }
        if (Objects.isNull(dspOrder.getTransportGroupId()) || Objects.equals(dspOrder.getTransportGroupId(), 0L)) {
            return;
        }
        LateRiskTypeStrategy strategy = lateRiskTypeManager.get(lateRiskType);
        if (Objects.isNull(strategy)) {
            throw new BizException("lateRiskType is unknown:" + lateRiskType);
        }
        //查询履约配置
        PerformanceConfigVO performanceConfig = driverOrderGateway.queryPerformanceConfig(dspOrder.getSupplierId());
        if (Objects.isNull(performanceConfig)) {
            throw new BizException("query performance config is failed");
        }
        //查询运力组信息
        TransportGroupVO transportGroupDetail = queryTransportGroupService.queryTransportGroup(dspOrder.getTransportGroupId());
        if (transportGroupDetail == null) {
            throw new BizException("query transport group is failed");
        }
        if (performanceConfig.isOpenEmailRemind()) {
            //发送邮件
            strategy.sendEmail(dspOrder, transportGroupDetail);
        }
        if (performanceConfig.isOpenPcStationNotice()) {
            //发送站内信
            strategy.sendPcStationLetter(dspOrder, transportGroupDetail);
        }
        if (performanceConfig.isOpenMobileSupplierRemind()) {
            //发送移动端商家提醒
            strategy.sendMobileSupplierRemind(dspOrder, transportGroupDetail);
        }
        if (performanceConfig.isOpenIvr()) {
            //发送ivr提醒
            strategy.sendLateRiskIVR(dspOrder, transportGroupDetail);
        }
    }
}
