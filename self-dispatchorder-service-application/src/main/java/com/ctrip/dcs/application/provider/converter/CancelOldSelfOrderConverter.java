package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand;
import com.ctrip.dcs.infrastructure.adapter.http.dto.QOrderCancelParamDTO;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class CancelOldSelfOrderConverter {


    public static QOrderCancelParamDTO converter(CancelDspOrderCommand cmd) {
        QOrderCancelParamDTO dto = new QOrderCancelParamDTO();
        dto.setUserOrderId(cmd.getUserOrderId());
        dto.setSupplyOrderId(cmd.getDspOrderId());
        dto.setCancelReasonId(cmd.getCancelReasonId());
        dto.setCancelReason(cmd.getCancelReason());
        dto.setCancelRole(cmd.getCancelRole());
        if (cmd.getCancelFineRate() != null) {
            dto.setFineRatio(cmd.getCancelFineRate().doubleValue());
        }
        dto.setCheckType(cmd.getCheckType());
        dto.setHasLose(cmd.getHasLose());
        if (cmd.getOrderFine() != null) {
            dto.setOrderFine(cmd.getOrderFine().doubleValue());
        }
        dto.setNoDriverCancel(cmd.getNoDriverCancel());
        dto.setNeedBackQMainOrderStatus(cmd.getNeedBackQMainOrderStatus());
        return dto;
    }
}
