package com.ctrip.dcs.application.provider.executor;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.application.command.UpdateVBKGrabTaskExeCmd;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO;
import com.ctrip.dcs.application.command.dto.VBKGrabTaskSettlement;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateVBKGrabTaskRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateVBKGrabTaskResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@ServiceLogTagPair(key = "taskId", alias = "taskId")
public class UpdateVBKGrabTaskExecutor extends AbstractRpcExecutor<UpdateVBKGrabTaskRequestType, UpdateVBKGrabTaskResponseType> implements Validator<UpdateVBKGrabTaskRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateVBKGrabTaskExecutor.class);

    @Autowired
    private UpdateVBKGrabTaskExeCmd updateVBKGrabTaskExeCmd;

    @Override
    public UpdateVBKGrabTaskResponseType execute(UpdateVBKGrabTaskRequestType requestType) {
        UpdateVBKGrabTaskResponseType responseType = new UpdateVBKGrabTaskResponseType();
        try {
            Pair<Integer, Integer> pair = updateVBKGrabTaskExeCmd.execute(buildVBKDriverGrabTaskDTO(requestType));
            responseType.setSuccessNum(pair.getKey());
            responseType.setFailNum(pair.getValue());
            return ServiceResponseUtils.success(responseType);
        }catch (BizException e) {
            if(!e.getCode().equals(ErrorCode.DRIVER_SETTLEMENT_TWO_MULTIPLE.getCode())){
                MetricsUtil.recordValue(MetricsConstants.VBK_UPDATE_VBK_GRAB_TASK_EXECUTOR_ERROR_COUNT);
            }
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            MetricsUtil.recordValue(MetricsConstants.VBK_UPDATE_VBK_GRAB_TASK_EXECUTOR_ERROR_COUNT);
            logger.error("UpdateVBKGrabTaskExecutor", ex);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    private VBKGrabTaskDTO buildVBKDriverGrabTaskDTO(UpdateVBKGrabTaskRequestType requestType) {
        VBKGrabTaskDTO vbkGrabTaskDTO = new VBKGrabTaskDTO();
        BeanUtils.copyProperties(requestType, vbkGrabTaskDTO);
        VBKGrabTaskSettlement rewardsVBKkDriverGrabTaskSettlement = new VBKGrabTaskSettlement();
        BeanUtils.copyProperties(requestType.getRewards(), rewardsVBKkDriverGrabTaskSettlement);
        VBKGrabTaskSettlement initVBKkDriverGrabTaskSettlement = new VBKGrabTaskSettlement();
        BeanUtils.copyProperties(requestType.getInitial(), initVBKkDriverGrabTaskSettlement);
        vbkGrabTaskDTO.setRewards(rewardsVBKkDriverGrabTaskSettlement);
        vbkGrabTaskDTO.setInitial(initVBKkDriverGrabTaskSettlement);
        return vbkGrabTaskDTO;
    }

    @Override
    public void validate(AbstractValidator<UpdateVBKGrabTaskRequestType> validator) {
        validator.ruleFor("initial").notNull();
        validator.ruleFor("rewards").notNull();
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("payForDriver").notNull();
        validator.ruleFor("taskId").notNull();
    }
}
