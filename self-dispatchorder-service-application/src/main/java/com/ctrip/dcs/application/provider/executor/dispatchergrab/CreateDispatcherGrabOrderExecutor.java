package com.ctrip.dcs.application.provider.executor.dispatchergrab;

import com.ctrip.dcs.application.command.dispatchergrab.CreateDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.command.api.CreateDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.provider.converter.CreateDispatcherGrabOrderConverter;
import com.ctrip.dcs.self.dispatchorder.interfaces.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
public class CreateDispatcherGrabOrderExecutor extends AbstractRpcExecutor<CreateDispatcherGrabOrdersRequestType, CreateDispatcherGrabOrdersResponseType> implements Validator<CreateDispatcherGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(CreateDispatcherGrabOrderExecutor.class);

    @Autowired
    private CreateDispatcherGrabOrderExeCmd cmd;

    @Override
    public CreateDispatcherGrabOrdersResponseType execute(CreateDispatcherGrabOrdersRequestType requestType) {
        try {
            //兼容修改场景
            CreateDispatcherGrabOrderCommand command = CreateDispatcherGrabOrderConverter.toCreateDispatcherGrabOrderCommand(requestType);
            cmd.execute(command);
            return ServiceResponseUtils.success(new CreateDispatcherGrabOrdersResponseType());
        } catch (BizException e) {
            logger.error("CreateDispatcherGrabOrderExecutorError", e);
            return ServiceResponseUtils.fail(new CreateDispatcherGrabOrdersResponseType(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("CreateDispatcherGrabOrderExecutorError", e);
            return ServiceResponseUtils.fail(new CreateDispatcherGrabOrdersResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<CreateDispatcherGrabOrdersRequestType> validator) {
        validator.ruleFor("orders").notNull().notEmpty();
    }
}
