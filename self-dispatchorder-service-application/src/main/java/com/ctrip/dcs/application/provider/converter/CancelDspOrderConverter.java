package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CancelDspOrderCommand;
import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.domain.common.enums.CancelReasonIdEnum;
import com.ctrip.dcs.domain.common.enums.CancelRoleEnum;
import com.ctrip.dcs.domain.common.enums.CancelTypeEnum;
import com.ctrip.dcs.domain.common.enums.QCancelUrlIdEnum;
import com.ctrip.dcs.domain.dsporder.YesOrNo;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderCancelDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO;
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderRequestType;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class CancelDspOrderConverter {

    public static CancelDspOrderCommand converter(CancelDspOrderRequestType requestType) {
        Assert.notNull(requestType);
        CancelDspOrderCommand cmd = new CancelDspOrderCommand();
        cmd.setUserOrderId(requestType.getUserOrderId());
        cmd.setDspOrderId(requestType.dspOrderId);
        cmd.setCancelReasonId(requestType.getCancelReasonId());
        cmd.setSupplyOrderId(requestType.getSupplyOrderId());
        cmd.setCancelReason(requestType.getCancelReason());
        cmd.setCancelFineRate(requestType.getCancelFineRate());
        cmd.setCancelRole(requestType.getCancelRole());
        cmd.setOrderFine(requestType.getOrderFine());
        cmd.setCheckType(requestType.getCheckType());
        cmd.setHasLose(requestType.isHasLose());
        cmd.setNoDriverCancel(requestType.getNoDriverCancel());
        cmd.setqCancelUrlId(requestType.getQCancelUrlId());
        cmd.setNeedBackQMainOrderStatus(requestType.isNeedBackQMainOrderStatus());
        cmd.setOriModify(requestType.isOriModify());
        cmd.setOriModifyCancelScene(requestType.getOriModifyCancelScene());

        return cmd;
    }

    public static CancelDspOrderCommand converter(CreateDspOrderCommand requestType,String cancelReason) {
        Assert.notNull(requestType);
        DspOrderDO dspOrderDO = requestType.getDspOrderDO();
        Assert.notNull(dspOrderDO);
        Assert.notBlank(dspOrderDO.getOldDspOrderId());
        CancelDspOrderCommand cmd = new CancelDspOrderCommand();
        cmd.setUserOrderId(dspOrderDO.getUserOrderId());
        cmd.setSupplyOrderId(dspOrderDO.getSupplyOrderId());
        cmd.setDspOrderId(dspOrderDO.getOldDspOrderId());
        cmd.setCancelReasonId(CancelReasonIdEnum.BUFFER_CHANGE_CANCEL.getCode());
        cmd.setCancelReason(cancelReason);
        cmd.setCancelRole(CancelRoleEnum.SYSCANCEL.getCode());
        cmd.setCancelFineRate(BigDecimal.ZERO);
        cmd.setOrderFine(BigDecimal.ZERO);
        cmd.setHasLose(false);
        cmd.setCheckType(CancelTypeEnum.FORCE.getCode());
        cmd.setqCancelUrlId(QCancelUrlIdEnum.self_cancel.getCode());
        cmd.setNeedBackQMainOrderStatus(false);

        return cmd;
    }

    public static CancelDspOrderCommand converter(DspOrderDO dspOrderDO,String cancelReason) {
        Assert.notNull(dspOrderDO);
        CancelDspOrderCommand cmd = new CancelDspOrderCommand();
        cmd.setUserOrderId(dspOrderDO.getUserOrderId());
        cmd.setSupplyOrderId(dspOrderDO.getSupplyOrderId());
        cmd.setDspOrderId(dspOrderDO.getDspOrderId());
        cmd.setCancelReasonId(CancelReasonIdEnum.REDISPATCH.getCode());
        cmd.setCancelReason(cancelReason);
        cmd.setCancelRole(CancelRoleEnum.SYSCANCEL.getCode());
        cmd.setCancelFineRate(BigDecimal.ZERO);
        cmd.setOrderFine(BigDecimal.ZERO);
        cmd.setHasLose(false);
        cmd.setCheckType(CancelTypeEnum.FORCE.getCode());
        cmd.setqCancelUrlId(QCancelUrlIdEnum.self_cancel.getCode());
        cmd.setNeedBackQMainOrderStatus(false);

        return cmd;
    }

    public static CancelDspOrderCommand converter(DspOrderDO dspOrderDO,Integer cancelReasonId,String cancelReason) {
        Assert.notNull(dspOrderDO);
        CancelDspOrderCommand cmd = new CancelDspOrderCommand();
        cmd.setUserOrderId(dspOrderDO.getUserOrderId());
        cmd.setSupplyOrderId(dspOrderDO.getSupplyOrderId());
        cmd.setDspOrderId(dspOrderDO.getDspOrderId());
        cmd.setCancelReasonId(cancelReasonId);
        cmd.setCancelReason(cancelReason);
        cmd.setCancelRole(CancelRoleEnum.SYSCANCEL.getCode());
        cmd.setCancelFineRate(BigDecimal.ZERO);
        cmd.setOrderFine(BigDecimal.ZERO);
        cmd.setHasLose(false);
        cmd.setCheckType(CancelTypeEnum.FORCE.getCode());
        cmd.setqCancelUrlId(QCancelUrlIdEnum.self_cancel.getCode());
        cmd.setNeedBackQMainOrderStatus(false);

        return cmd;
    }

    public static DspOrderCancelDO buildDspOrderCancelDO(CancelDspOrderCommand cmd) {
        DspOrderCancelDO cancelDO = new DspOrderCancelDO();
        cancelDO.setUserOrderId(cmd.getUserOrderId());
        cancelDO.setDspOrderId(cmd.getDspOrderId());
        cancelDO.setCancelCode(cmd.getCancelReasonId().toString());
        cancelDO.setCancelReasonId(cmd.getCancelReasonId());
        cancelDO.setNeedFine(cmd.getOrderFine().doubleValue() > 0 ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        cancelDO.setOrderFine(cmd.getOrderFine());
        cancelDO.setCancelType(cmd.getCancelRole());
        cancelDO.setCancelDesc(cmd.getCancelReason());
        cancelDO.setRemark("");
        cancelDO.setOperator("");
        cancelDO.setIsForceCancel(cmd.getCheckType());
        cancelDO.setDspOrderStatus(cmd.getOrderCurrentStatus());
        return cancelDO;
    }

    public static DspOrderFeeDO buildDspOrderFeeDO(CancelDspOrderCommand cmd, DspOrderDO dspOrderDO) {
        if (cmd.getOrderFine().doubleValue() > 0 || cmd.getCancelFineRate().doubleValue() > 0) {
            DspOrderFeeDO dspOrderFeeDO = new DspOrderFeeDO();
            dspOrderFeeDO.setUserOrderId(cmd.getUserOrderId());
            dspOrderFeeDO.setDspOrderId(dspOrderDO.getDspOrderId());
            dspOrderFeeDO.setOrderFine(cmd.getOrderFine());
            dspOrderFeeDO.setCancelFineRate(cmd.getCancelFineRate());
            return dspOrderFeeDO;
        }
        return null;
    }


}
