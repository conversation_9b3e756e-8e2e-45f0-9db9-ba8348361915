package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.carconfig.UpdateDriverAfterTimeCarConf;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.event.ImNoticeEvent;
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.common.constants.NoticeEnum;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class VbkChangeDriverExeCmd extends AbstractAssignDriverExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(VbkChangeDriverExeCmd.class);

    @Autowired
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Autowired
    private DcsVbkSupplierOrderGateway dcsVbkSupplierOrderGateway;

    @Autowired
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Autowired
    private UpdateDriverAfterTimeCarConf updateDriverAfterTimeCarConf;

    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;

    @Autowired
    protected MessageProviderService messageProducer;

    @Override
    protected void preCheck(DspOrderVO order, DriverVO driverVO, OperateAssignDriverCommand cmd) {
        super.preCheck(order, driverVO, cmd);
        Long orderSupplierId = 0L;
        DspOrderDO dspOrderDO = dspOrderRepository.find(order.getDspOrderId());
        if (dspOrderDO.getConfirmRecordId() != null && dspOrderDO.getConfirmRecordId() > 0L) {
            DspOrderConfirmRecordVO recordVO = dspOrderConfirmRecordRepository.find(dspOrderDO.getConfirmRecordId());
            orderSupplierId = recordVO.getSupplierInfo().getSupplierId();
        }
        // 供应商更改司机，校验是否允许更改
        if (!isOpenChangeButton(cmd, order)) {
            throw ErrorCode.VBK_UPDATE_DRIVER_TAKEN_ILLEGAL_ERROR.getBizException();
        }
        if (cmd.getAssignRole().equals(SysConstants.AssignRole.SYS_ROLE_SUPPLIER)) {
            //canAssistSupplerIds
            Set<String> canAssistSuppliers = Sets.newHashSet(driverVO.getSupplier().getSupplierId().toString());
            canAssistSuppliers.addAll(CollectionUtils.isEmpty(driverVO.getSupplier().getDispatchSupplierIdList()) ? Lists.newArrayList() : driverVO.getSupplier().getDispatchSupplierIdList().stream().map(Object::toString).collect(Collectors.toList()));
            if (!canAssistSuppliers.contains(orderSupplierId.toString())
                    || !cmd.getSupplierId().equals(orderSupplierId)
                    || !canAssistSuppliers.contains(cmd.getSupplierId().toString())) {
                logger.info("vbk_change_driver_info", "Cross supplier assignment not allowed.dspOrderId=" + cmd.getDspOrderId());
                throw ErrorCode.ERROR_PARAMS.getBizException();
            }
        }

        BaseDetailVO baseDetailVO = selfOrderQueryGateway.queryOrderBaseDetail(cmd.getUserOrderId(), cmd.getDspOrderId());

        //包车需要车
        if (CategoryCodeEnum.isCharterOrder(dspOrderDO.getCategoryCode())) {
            if (StringUtils.isEmpty(cmd.getCarLicense()) && !Integer.valueOf(YesOrNo.YES.getCode()).equals(driverVO.getDrvTemporaryMark())) {
                throw ErrorCode.ERROR_PARAMS.getBizException();
            }
            if (baseDetailVO.getOrderStatus() > DriverOrderStatusEnum.BEGIN.getCode()) {
                throw ErrorCode.ORDER_STATUS_LIMIT_ASSIGN_ERR.getBizException();
            }
            // 通过 useDays 识别是否多日包车 > 1 是 <= 1 非
            boolean isMultiDayBooking = MoreObjects.firstNonNull(baseDetailVO.getUseDays(), BigDecimal.ONE).compareTo(BigDecimal.ONE) > 0;
            // 单日包车 && 开始服务或者已经服务完成 不可更换
            if (!isMultiDayBooking && baseDetailVO.getOrderStatus() >= DriverOrderStatusEnum.BEGIN.getCode()) {
                throw ErrorCode.ORDER_STATUS_LIMIT_ASSIGN_ERR.getBizException();
            }
            // 操作过司机完成服务SOP事件
            boolean doCompleteRes = selfOrderQueryGateway.currentStopFlag(dspOrderDO.getUserOrderId(), DateUtil.formatDate(new Date(), DateUtil.DATE_FORMAT));

            // status IN (300,400,500)
            boolean statusCheck = baseDetailVO.getOrderStatus() >= DriverOrderStatusEnum.DEPART.getCode() && baseDetailVO.getOrderStatus() <= DriverOrderStatusEnum.BEGIN.getCode();

            // 多日包车订单 && 不含当日司机服务完成SOP事件 && 非 (300,400,500)
            if (isMultiDayBooking && statusCheck && !doCompleteRes) {
                throw ErrorCode.CURRENT_STOP_SOP_LIMIT_ERR.getBizException();
            }

        }
        //接送机订单状态限制
        if (CategoryCodeEnum.isAirportOrStationOrPoint(dspOrderDO.getCategoryCode())) {
            if (baseDetailVO.getOrderStatus() > DriverOrderStatusEnum.ARRIVE.getCode()) {
                throw ErrorCode.ORDER_STATUS_LIMIT_ASSIGN_ERR.getBizException();
            }
            if (OrderStatusEnum.isConfirmOrSP(dspOrderDO.getOrderStatus())) {
                throw ErrorCode.ORDER_STATUS_LIMIT_ASSIGN_ERR.getBizException();
            }
        }
        //2.已过最晚时间，需要同时选择司机和车辆 //
        if (isUpdateDriverAfterTime(dspOrderDO, cmd, driverVO)) {
            throw ErrorCode.DUPLICATE_ASSIGN_DRIVER_ERR.getBizException();
        }
    }

    public boolean isUpdateDriverAfterTime(DspOrderDO dspOrderDO, OperateAssignDriverCommand cmd, DriverVO driverVO) {
        if (SysConstants.AssignRole.SYS_ROLE_SUPPLIER.equals(cmd.getAssignRole())) {
            Integer time = updateDriverAfterTimeCarConf.get(dspOrderDO.getCategoryCode(), dspOrderDO.getCityId());
            logger.info("isUpdateDriverAfterTime", "get config time. categoryCode:" + dspOrderDO.getCategoryCode() + ", cityId:" + dspOrderDO.getCityId() + ", time:" + time);
            if (time != null) {
                Date date = DateUtil.addHours(dspOrderDO.getEstimatedUseTimeBj(), time);
                return new Date().getTime() > date.getTime();
            }
        }
        Date lastConfirmCarTime = dspOrderDO.getLastConfirmCarTimeBj();
        if (lastConfirmCarTime != null
                && lastConfirmCarTime.before(new Date())
                && StringUtils.isBlank(cmd.getCarLicense()) && StringUtils.isBlank(driverVO.getCar().getCarLicense())) {
            return true;
        }
        return false;
    }

    private boolean isOpenChangeButton(OperateAssignDriverCommand cmd, DspOrderVO order) {
        if (SysConstants.AssignRole.SYS_ROLE_SUPPLIER.equals(cmd.getAssignRole())) {
            return dcsVbkSupplierOrderGateway.queryOrderCanChangeDriver(AllOrderStatus.mappingOldStatus(order.getOrderStatus()),
                    AllOrderStatus.mappingOldStatusDetail(order.getOrderStatus()),
                    order.getCategoryCode().getDesc(),
                    cmd.getSupplierId().intValue(),
                    TakenType.SUPPLIER.getCode(),
                    order.getShortDisOrder());
        }
        return true;
    }

    @Override
    public boolean execute(OperateAssignDriverCommand cmd) {

        DspOrderVO order = queryDspOrderService.queryBase(cmd.getDspOrderId());

        if (order == null) {
            throw ErrorCode.NULL_ORDER_ERROR.getBizException();
        }
        // 原单修改待确认时不允许任何操作
        OrderModifyHelper.assertNotToBeConfirmed(order.getDspOrderId());

        boolean res = super.execute(cmd);
        sendVBKOperationRecord(order, cmd);
        return res;
    }

    private void sendVBKOperationRecord(DspOrderVO order, OperateAssignDriverCommand cmd) {
        try {
            Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), order.getCityId());
            if (SysConstants.AssignRole.SYS_ROLE_SUPPLIER.equals(cmd.getAssignRole())) {
                String comment = org.apache.commons.lang.StringUtils.EMPTY;
                if (org.apache.commons.lang.StringUtils.isNotBlank(cmd.getChangeReasonDesc())) {
                    comment = String.format(SysConstants.OperationRecordLog.VBK_CHANGE_REASON, cmd.getChangeReasonDesc());
                }
                String operUserName = String.format(SysConstants.OperationRecordLog.SUPPLIER_BRACKET, cmd.getOperUserName());
                VBKOperationRecordVO record = vbkOperationRecordFactory.createUpdateDriverOperationRecord(order
                        , cmd.getOperUserType(), operUserName, cmd.getSysUserAccount(), cmd.getSupplierId()
                        , comment,localTimeNow);
                vbkOperationRecordGateway.record(record);
            }
            if (SysConstants.AssignRole.SYS_ROLE_BOSS.equals(cmd.getAssignRole())) {
                VBKOperationRecordVO record = vbkOperationRecordFactory.createUpdateDriverOperationRecord(order
                        , cmd.getOperUserType(), SysConstants.OperationRecordLog.BOSS_BRACKET, cmd.getSysUserAccount(), cmd.getSupplierId()
                        , "",localTimeNow);
                vbkOperationRecordGateway.record(record);
            }
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, OperateAssignDriverCommand cmd) {
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, groupVO.getTransportGroupMode().getCode(), SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
    }
}
