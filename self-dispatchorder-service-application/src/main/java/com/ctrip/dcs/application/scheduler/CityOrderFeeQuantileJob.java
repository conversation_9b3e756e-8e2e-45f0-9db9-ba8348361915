package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.orderPriority.service.OrderFeePriorityServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Objects;

@Component
public class CityOrderFeeQuantileJob {
    private static final Double HIFH_PRIORITY_DEFAULT = 0.8;
    private static final Double MEDIUM_PRIORITY_DEFAULT = 0.5;

    @Autowired
    private OrderFeePriorityServiceImpl orderFeePriorityServiceImpl;

    @QSchedule("city.order.fee.quantile.job")
    public void execute(Parameter param) {

        Double highPriority = param.getProperty("highPriority", Double.class);
        if (Objects.isNull(highPriority)) {
            highPriority = HIFH_PRIORITY_DEFAULT;
        }
        Double mediumPriority = param.getProperty("mediumPriority", Double.class);
        if (Objects.isNull(mediumPriority)) {
            mediumPriority = MEDIUM_PRIORITY_DEFAULT;
        }
        orderFeePriorityServiceImpl.dealOrderFeePriority(highPriority,mediumPriority);
    }
}
