package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.ModifyCharteredLineOrderExeCmd;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

import java.math.BigDecimal;

/**
 * 线路包车订单修改时间、地址
 */
@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
public class ModifyCharteredLineOrderExecutor extends AbstractRpcExecutor<ModifyCharteredLineOrderRequestType, ModifyCharteredLineOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(ModifyCharteredLineOrderExecutor.class);

    @Autowired
    private ModifyCharteredLineOrderExeCmd modifyCharteredLineOrderExeCmd;

    @Override
    public ModifyCharteredLineOrderResponseType execute(ModifyCharteredLineOrderRequestType requestType) {
        ModifyCharteredLineOrderResponseType response = new ModifyCharteredLineOrderResponseType();
        // 默认返回值false
        response.setResult(false);
        // 参数校验
        if (Strings.isBlank(requestType.getUserOrderId())
                || Strings.isBlank(requestType.getEstimateUseTime())
                || Strings.isBlank(requestType.getEstimateUseTimeBJ())
                || Strings.isBlank(requestType.getPredicServiceStopTime())
                || Strings.isBlank(requestType.getPredicServiceStopTimeBJ())
                || requestType.getAmountInfo() == null
                || requestType.getFromAddressInfo() == null
                || requestType.getToAddressInfo() == null
                || StringUtils.isBlank(requestType.getUniqueKey())) {
            return ServiceResponseUtils.fail(response, String.valueOf(ErrorCode.ERROR_PARAMS.getCode()), ErrorCode.ERROR_PARAMS.getDesc());
        }
        try {
            logger.info("ModifyCharteredLineOrder_" + requestType.getUserOrderId(), "start userOrderId=" + requestType.getUserOrderId());

            // 埋点，用户金额为0，但是供应商金额不为0
            if (requestType.getAmountInfo() != null) {
                BigDecimal userAmount = requestType.getAmountInfo().getAmount();
                BigDecimal supplierAmount = requestType.getAmountInfo().getSupplierAmount();
                if (userAmount != null && supplierAmount != null) {
                    if (userAmount.compareTo(BigDecimal.ZERO) == 0 && supplierAmount.compareTo(BigDecimal.ZERO) != 0) {
                        MetricsUtil.recordValue(MetricsConstants.MODIFY_CHARTERED_LINE_PRICE);
                    }
                }
            }

            // 执行业务逻辑
            boolean result = modifyCharteredLineOrderExeCmd.modifyCharteredLineOrder(requestType);
            response.setResult(result);
            if (!result) {
                MetricsUtil.recordValue(MetricsConstants.MODIFY_CHARTERED_LINE_ORDER_FAIL);
            }

        } catch (BizException e) {
            logger.error("ModifyCharteredLineOrderBizException_" + requestType.getUserOrderId(), "create dispatchOrder Fail");
            return ServiceResponseUtils.fail(response, e.getCode(), e.getMessage());
        } catch (Exception exc) {
            logger.error("ModifyCharteredLineOrderException_" + requestType.getUserOrderId(), exc);
            return ServiceResponseUtils.fail(response);
        }
        return ServiceResponseUtils.success(response);
    }

}
