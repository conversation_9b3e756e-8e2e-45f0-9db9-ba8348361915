package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderVersionEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.QueryOrderSettlePriceService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.infrastructure.adapter.dto.SyncSettleToDriverDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 更新订单明细结算信息
 * <AUTHOR>
 */
@Component
public class UpdateOrderSettlementBeforeTakenCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateOrderSettlementBeforeTakenCmd.class);

    @Autowired
    private QueryOrderSettlePriceService queryOrderSettlePriceService;

    @Autowired
    private DspOrderDetailRepository dspOrderDetailRepository;

    public OrderSettlePriceVO execute(UpdateOrderSettlementBeforeTakenCommand command) {
        try {
            if (!OrderSourceCodeEnum.isTrip(command.getOrderSourceCode())) {
                // 非携程订单，无结算信息
                return null;
            }
            // 查询结算信息
            OrderSettlePriceVO orderSettlePrice = queryOrderSettlePriceService.queryOrderSettlePrice(command.getDspOrderId(), command.getCarTypeId(), command.getCategoryCode(), command.getSupplierId(), command.getServiceProviderId(), command.getSettleToDriver());
            if (Objects.equals(OrderVersionEnum.CTRIP.getVersion(), command.getOrderVersion())) {
                // 迁移后订单
                updateOrderDetailExtendInfo(orderSettlePrice);
            }
            return orderSettlePrice;
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.UPDATE_ORDER_DETAIL_SETTLEMENT_ERROR);
            logger.warn("UpdateOrderDetailSettlementInfoError", e);
            throw ErrorCode.UPDATE_ORDER_DETAIL_SETTLEMENT_ERROR.getBizException();
        }
    }

    public void updateOrderDetailExtendInfo(OrderSettlePriceVO orderSettlePrice) {
        DspOrderDetailDO dspOrderDetailDO = dspOrderDetailRepository.find(orderSettlePrice.getDspOrderId());
        if (dspOrderDetailDO == null) {
            throw ErrorCode.NULL_ORDER_ERROR.getBizException();
        }
        Map<String, Object> extendInfoMap = dspOrderDetailDO.getExtendInfoMap();
        if (extendInfoMap == null) {
            extendInfoMap = Maps.newHashMap();
        }
        putExtendInfoMap(orderSettlePrice, extendInfoMap);
        dspOrderDetailDO.setExtendInfo(JacksonUtil.serialize(extendInfoMap));
        dspOrderDetailRepository.updateExtendInfo(dspOrderDetailDO);
    }

    public void putExtendInfoMap(OrderSettlePriceVO orderSettlePrice, Map<String, Object> extendInfoMap) {
        extendInfoMap.put("settleToDriver", orderSettlePrice.getSettleToDriver());
        if (Objects.nonNull(orderSettlePrice.getPreDriverSettleAmount())) {
            extendInfoMap.put("preDriverSettleAmount", orderSettlePrice.getPreDriverSettleAmount());
        }
        if (Objects.nonNull(orderSettlePrice.getPreDriverSettleCurrency())) {
            extendInfoMap.put("preDriverSettleCurrency", orderSettlePrice.getPreDriverSettleCurrency());
        }
        if (Objects.nonNull(orderSettlePrice.getPreSupplierSettleAmount())) {
            extendInfoMap.put("preSupplierSettleAmount", orderSettlePrice.getPreSupplierSettleAmount());
        }
        if (Objects.nonNull(orderSettlePrice.getPreSupplierSettleCurrency())) {
            extendInfoMap.put("preSupplierSettleCurrency", orderSettlePrice.getPreSupplierSettleCurrency());
        }
        if (Objects.nonNull(orderSettlePrice.getPreDriverGuideAmount())) {
            extendInfoMap.put("preDriverGuideAmount", orderSettlePrice.getPreDriverGuideAmount());
        }
        if (Objects.nonNull(orderSettlePrice.getPreDriverGuideCurrency())) {
            extendInfoMap.put("preDriverGuideCurrency", orderSettlePrice.getPreDriverGuideCurrency());
        }
    }

    public SyncSettleToDriverDTO toSyncSettleToDriverDTO(OrderSettlePriceVO orderSettlePrice) {
        SyncSettleToDriverDTO settleToDriverDTO = new SyncSettleToDriverDTO();
        settleToDriverDTO.setOrderId(orderSettlePrice.getDspOrderId());
        settleToDriverDTO.setSettleToDriver(orderSettlePrice.getSettleToDriver() == YesOrNo.YES.getCode());
        settleToDriverDTO.setPreDriverSettleCurrency(orderSettlePrice.getPreDriverSettleCurrency());
        settleToDriverDTO.setPreDriverSettleAmount(orderSettlePrice.getPreDriverSettleAmount());
        settleToDriverDTO.setPreSupplierSettleCurrency(orderSettlePrice.getPreSupplierSettleCurrency());
        settleToDriverDTO.setPreSupplierSettleAmount(orderSettlePrice.getPreSupplierSettleAmount());
        settleToDriverDTO.setPreDriverGuideAmount(orderSettlePrice.getPreDriverGuideAmount());
        settleToDriverDTO.setPreDriverGuideCurrency(orderSettlePrice.getPreDriverGuideCurrency());
        return settleToDriverDTO;
    }

}
