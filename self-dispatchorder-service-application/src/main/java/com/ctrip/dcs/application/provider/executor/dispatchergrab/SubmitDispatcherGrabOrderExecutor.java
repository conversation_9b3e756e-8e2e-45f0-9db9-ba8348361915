package com.ctrip.dcs.application.provider.executor.dispatchergrab;

import com.ctrip.dcs.application.command.dispatchergrab.SubmitDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dto.DispatcherGrabSubmitResDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class SubmitDispatcherGrabOrderExecutor extends AbstractRpcExecutor<SubmitDispatcherGrabOrdersRequestType, SubmitDispatcherGrabOrdersResponseType> implements Validator<SubmitDispatcherGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(SubmitDispatcherGrabOrderExecutor.class);

    @Autowired
    private SubmitDispatcherGrabOrderExeCmd cmd;

    @Override
    public SubmitDispatcherGrabOrdersResponseType execute(SubmitDispatcherGrabOrdersRequestType requestType) {
        try {
            SubmitDispatcherGrabOrderCommand command = new SubmitDispatcherGrabOrderCommand(requestType.getUserOrderId(), requestType.getDspOrderId(), requestType.getSupplierId(), requestType.getOperatorUserId(), requestType.getOperatorUserName(), requestType.getUrgent(),requestType.getSource(), requestType.getScene(), requestType.getPkId(), requestType.getLastConfirmTime(), requestType.getLastConfirmTimeBj(), requestType.getEstimatedUseTime());
            Result<DispatcherGrabSubmitResDTO> result = cmd.execute(command);
            SubmitDispatcherGrabOrdersResponseType response = new SubmitDispatcherGrabOrdersResponseType();
            response.setTaskId(result.getData().getId());
            response.setGrabStatus(result.getData().getGrabStatus());
            return ServiceResponseUtils.success(response);
        } catch (BizException e) {
            logger.error("SubmitDispatcherGrabOrderExecutorError", e);
            return ServiceResponseUtils.fail(new SubmitDispatcherGrabOrdersResponseType(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("SubmitDispatcherGrabOrderExecutorError", e);
            return ServiceResponseUtils.fail(new SubmitDispatcherGrabOrdersResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<SubmitDispatcherGrabOrdersRequestType> validator) {
        validator.ruleFor("userOrderId").notNull().notEmpty();
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("operatorUserId").notNull().notEmpty();
        validator.ruleFor("operatorUserName").notNull().notEmpty();
        validator.ruleFor("urgent").notNull();
    }
}
