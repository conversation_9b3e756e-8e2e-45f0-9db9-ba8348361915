package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.api.DispatcherGrabOrderIvrCommand;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 急单IVR提醒
 */
@Component
public class DispatcherGrabOrderIvrExeCmd{
    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private IvrCallService ivrCallService;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    public void execute(DispatcherGrabOrderIvrCommand command){
        List<DispatcherGrabOrderDO> list = dispatcherGrabOrderGateway.query(Lists.newArrayList(command.getDispatcherGrabOrderId()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        DispatcherGrabOrderDO order = list.get(0);

        DspOrderVO dspOrderVO = queryDspOrderService.queryDspOrder(order);
        ivrCallService.urgentIvr(dspOrderVO,order);
    }

}
