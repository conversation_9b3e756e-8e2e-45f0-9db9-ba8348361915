package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class DeleteGrabOrderPushRuleExecutor extends AbstractRpcExecutor<DeleteGrabOrderPushRuleRequestType, DeleteGrabOrderPushRuleResponseType> implements Validator<DeleteGrabOrderPushRuleRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(DeleteGrabOrderPushRuleExecutor.class);
    @Autowired
    private IGrabOrderPushRuleService grabOrderPushRuleService;
    @Override
    public DeleteGrabOrderPushRuleResponseType execute(DeleteGrabOrderPushRuleRequestType requestType) {
        try{
            boolean result = grabOrderPushRuleService.deleteById(requestType.getRuleId(),requestType.getOperateUser(),requestType.getSupplierId());
            if(result){
                return ServiceResponseUtils.success(new DeleteGrabOrderPushRuleResponseType());
            }
            return ServiceResponseUtils.fail(new DeleteGrabOrderPushRuleResponseType());
        }catch (Exception e){
            logger.error("DeleteGrabOrderPushRuleExecutorExp","DeleteGrabOrderPushRuleExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new DeleteGrabOrderPushRuleResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<DeleteGrabOrderPushRuleRequestType> validator) {
        validator.ruleFor("ruleId").notNull().notEmpty();
        validator.ruleFor("operateUser").notNull().notEmpty();
    }
}
