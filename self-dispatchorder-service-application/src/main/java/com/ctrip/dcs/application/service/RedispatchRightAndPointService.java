package com.ctrip.dcs.application.service;

import cn.hutool.core.date.DateUnit;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.provider.converter.ReDispatchedConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.QueryFlightService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.schedule.gateway.CarDriverComplaintServiceGateway;
import com.ctrip.dcs.infrastructure.common.config.ReasonDetailConfig;
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig;
import com.ctrip.dcs.infrastructure.common.constants.DescribeConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.joda.time.Minutes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 判罚改派权益
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/5/27 18:03
 */
@Component
public class RedispatchRightAndPointService {

    private static final Logger logger = LoggerFactory.getLogger(RedispatchRightAndPointService.class);

    /**
     * 确认订单描述状态
     */
    private final int CONFIRMED_ORDER_STATUS_DETAIL = 201;

    @Resource
    private RedispatchRightConfig rightConfig;

    @Resource
    private ReasonDetailConfig reasonDetailConfig;

    @Resource
    private QueryFlightService queryFlightService;

    @Resource
    private CarDriverComplaintServiceGateway complaintService;

    /**
     * 用车时间描述
     */
    public String getChangTimeDesc(String sysExpectBookTimeBjStr) {
        Date nowBj = new Date();
        Date sysExpectBookTimeBj = DateUtil.parseDateStr2Date(sysExpectBookTimeBjStr);
        String changTimeDesc = DescribeConstants.PAST_TRIP_TIME;
        if (!sysExpectBookTimeBj.before(nowBj)) {
            // 最晚确认时间bj - 当前时间bj = 分钟数
            int gapMinutes = (int) cn.hutool.core.date.DateUtil.between(nowBj, sysExpectBookTimeBj, DateUnit.MINUTE);
            if (gapMinutes > rightConfig.getChangeTimeLimit()) {
                changTimeDesc = String.format(DescribeConstants.BEFORE_TRIP_BEYOND_HOURS, 2);
            } else {
                changTimeDesc = String.format(DescribeConstants.BEFORE_TRIP_WITHIN_HOURS, 2);
            }
        }
        return changTimeDesc;
    }

    /**
     * 查询判罚信息及权益
     */
    public Result<RightAndPointVO> checkPunishReason(RightAndPointCommand command, BaseDetailVO detailVO) {
        // 定位 - 改派原因
        ReasonDetailVO dispatchReason = reasonDetailConfig.getReasonDetail(command.getReasonDetailId());
        // 缺失改派原因 - 跳出 - fixme review点 是否应该阻断
        if (dispatchReason == null) {
            logger.error("dispatchReasonError", "Dispatch Reason Is Null reasonDetailId:{}", detailVO.attrs(), command.getReasonDetailId());
            return Result.Builder.<RightAndPointVO>newResult().fail().
                    withCode(ErrorCode.REASON_DETAIL_ID_ERROR.getCode()).
                    withMsg(ErrorCode.REASON_DETAIL_ID_ERROR.getDesc()).build();
        }
        // 定位 - 改派司机类型
        ReassignTaskEnum.ChangeDriveTypeEnum changeDriverTypeEnum = confirmDispatchType(detailVO);
        // 获取判罚信息
        Result<RightAndPointVO> draftRes = complaintService.checkPunishReasonDraft(ReDispatchedConverter.buildQueryPunishReason(command.getPunishOperateType(), detailVO, dispatchReason, changeDriverTypeEnum, command.getRoleId()));
        // 判罚请求失败 - 结束
        if (!draftRes.isSuccess() || draftRes.getData() == null) {
            return draftRes;
        }
        // finally - 结果
        RightAndPointVO res = draftRes.getData();
        // 设置 - 改派类型
        res.setDispatchType(changeDriverTypeEnum.getReasonId());
        // 改派原因 与 航班有关
        if (needArriveTime(dispatchReason)) {
            DriverPunishVO punishVO = res.getDriverPunishInfo();
            // fixme review点 咱们没有是吧？
            punishVO.setTakenExpectArriveTime(null);
            FlightVO flightVO = queryFlightService.queryFlight(command.getUserOrderId());
            if (flightVO != null) {
                punishVO.setNowExpectArriveTime(flightVO.getEstimateArriveDate());
            }
        }
        if (res.getPunishInfo() != null) {
            res.getPunishInfo().setReasonDetail(dispatchReason.getReasonDetail());
        }
        return Result.Builder.<RightAndPointVO>newResult().success().withData(res).build();
    }

    /**
     * 是否需要航班时间
     */
    private boolean needArriveTime(ReasonDetailVO dispatchReason) {
        return judgeFlightReason(Integer.valueOf(dispatchReason.getReasonId())) ||
                Objects.equals(Integer.valueOf(dispatchReason.getReasonId()), ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode());
    }

    /**
     * 判定改派原因是否为航班导致
     */
    private boolean judgeFlightReason(Integer reasonId) {
        return (Objects.equals(reasonId, ReassignTaskEnum.DispatchReasonIdEnum.FLIGHT_DELAY_ONE_HOUR.getCode()) ||
                Objects.equals(reasonId, ReassignTaskEnum.DispatchReasonIdEnum.FLIGHT_EARLY_NOT_SERVICE.getCode()) ||
                Objects.equals(reasonId, ReassignTaskEnum.DispatchReasonIdEnum.FLIGHT_DELAY_OVER_WORK_TIME.getCode()));
    }

    /**
     * 检查当前订单是否已确认
     */
    private boolean checkIsConfirmed(BaseDetailVO detailVO) {
        if (detailVO.getOrderStatus().intValue() >= OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode().intValue()) {
            return Boolean.TRUE;
        }
        if (detailVO.getOrderStatus().intValue() >= OrderStatusEnum.DISPATCH_CONFIRMED.getCode().intValue() && detailVO.getOldOrderStatusDetail().intValue() >= CONFIRMED_ORDER_STATUS_DETAIL) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 匹配改派类型
     * 根据用车时间以及订单状态等信息
     */
    public ReassignTaskEnum.ChangeDriveTypeEnum confirmDispatchType(BaseDetailVO detailVO) {
        // 当前时间 - 北京
        Date nowBj = new Date();
        // 下单时间 - 北京
        Date orderTimeBj = DateUtil.parseDateStr2Date(detailVO.getDspOrderCreateTime());
        // 预估用车时间 - 北京
        Date sysExpectBookTimeBj = DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj());
        // 订单已确认 或 系统预估用车时间 小于 || 等于 当前时间
        if (checkIsConfirmed(detailVO) || !sysExpectBookTimeBj.after(nowBj)) {
            return ReassignTaskEnum.ChangeDriveTypeEnum.AFTER_CONFIRM;
        }
        // 系统预估用车时间 - 当前时间 = 小时数
        int gapBookTimeHours = Math.abs(Hours.hoursBetween(new DateTime(sysExpectBookTimeBj), new DateTime(nowBj)).getHours());
        // 当前时间 - 下单时间 = 分钟数
        int gapOrderTimeMinutes = Math.abs(Minutes.minutesBetween(new DateTime(orderTimeBj), new DateTime(nowBj)).getMinutes());
        // 距离用车时间 > 24h，下单 30 min无应单
        if (gapBookTimeHours >= rightConfig.getGapBookTimeHoursA().intValue() && gapOrderTimeMinutes >= rightConfig.getGapOrderTimeMinutesA().intValue()) {
            return ReassignTaskEnum.ChangeDriveTypeEnum.BEFORE_CONFIRM_NO_CAR;
        }
        // 距离用车时间 < 24h，下单 10min 无应单
        if (gapBookTimeHours < rightConfig.getGapBookTimeHoursB().intValue() && gapOrderTimeMinutes >= rightConfig.getGapOrderTimeMinutesB().intValue()) {
            return ReassignTaskEnum.ChangeDriveTypeEnum.BEFORE_CONFIRM_NO_CAR;
        }
        // 距离用车时间 > 24h，下单不足 30min
        if (gapBookTimeHours >= rightConfig.getGapBookTimeHoursC().intValue() && gapOrderTimeMinutes < rightConfig.getGapOrderTimeMinutesC().intValue()) {
            return ReassignTaskEnum.ChangeDriveTypeEnum.BEFORE_CONFIRM_NORMAL;
        }
        // 距离用车时间 < 24h，下单不足 10min
        if (gapBookTimeHours < rightConfig.getGapBookTimeHoursD().intValue() && gapOrderTimeMinutes < rightConfig.getGapOrderTimeMinutesD().intValue()) {
            return ReassignTaskEnum.ChangeDriveTypeEnum.BEFORE_CONFIRM_NORMAL;
        }
        // 兜底 - 确认后改派
        return ReassignTaskEnum.ChangeDriveTypeEnum.AFTER_CONFIRM;
    }

}