package com.ctrip.dcs.application.command.validator;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabDriverStatus;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleType;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.VBKDriverGrabOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.grab.VBKDriverGrabOrderPO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

;

/**
 * <AUTHOR>
 */
@Component
public class ExecuteVbkScheduleValidator {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteVbkScheduleValidator.class);

    @Autowired
    private MessageProviderService messageProducer;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConfig;

    public void validate(ExecuteScheduleCommand command, DspOrderVO dspOrderVO, ScheduleDO schedule, List<ScheduleTaskDO> tasks, VBKDriverGrabOrderDO vbkGrabOrder) throws ValidateException {
        Assert.notNull(dspOrderVO);
        Assert.notNull(schedule);
        Assert.notEmpty(tasks);
        if (schedule.isShutdown()) {
            logger.info("schedule execute validator", "schedule has shutdown.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_SHUTDOWN_ERROR);
        }
        if (command.getRound() != 0 && command.getRound() < schedule.getRound()) {
            logger.info("schedule execute validator", "schedule round less.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_EXECUTING_ERROR);
        }
        if (!Objects.equals(OrderStatusEnum.DISPATCH_CONFIRMED.getCode(), dspOrderVO.getOrderStatus())) {
            logger.info("schedule execute validator", "dsp order not dispatch confirm.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR);
        }
        if (schedule.getRound() != null && schedule.getRound() > getMaxScheduleRound()) {
            // 调度轮次超过最大值
            logger.info("schedule execute validator", "schedule round more than max.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
            throw new ValidateException(ErrorCode.SCHEDULE_EXECUTING_ERROR);
        }
        if (Objects.equals(schedule.getType(), ScheduleType.VBK)) {
            if (vbkGrabOrder == null || DateUtil.isAfter(new Date(), vbkGrabOrder.getGrabLimitTimeBJ())) {
                logger.info("schedule execute validator", "after vbk grab order limit time.schedule id:{}, order id:{}", schedule.getScheduleId(), schedule.getDspOrderId());
                throw new ValidateException(ErrorCode.SCHEDULE_TIMEOUT_ERROR);
            }
        }
    }

    private Integer getMaxScheduleRound() {
        String max = commonConfig.get("default_max_schedule_round");
        if (StringUtils.isBlank(max)) {
            return 99999;
        }
        return Integer.valueOf(max);
    }
}
