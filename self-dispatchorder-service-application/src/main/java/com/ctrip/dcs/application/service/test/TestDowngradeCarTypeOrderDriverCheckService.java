package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.item.impl.DowngradeCarTypeOrderDriverCheck;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class TestDowngradeCarTypeOrderDriverCheckService implements ITestDspOrderService {
    @Autowired
    @Qualifier("checkConfig")
    private ConfigService checkConfig;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private DspContextService dspContextService;
    @Override
    public String test(Map<String, String> params) {
        DspOrderVO order = queryDspOrderService.query(params.get("dspOrderId"));
        OrderExtendAttributeVO extendAttributeVO1 = new OrderExtendAttributeVO("is_downgrade_car_type_order","1");
        OrderExtendAttributeVO extendAttributeVO2 = new OrderExtendAttributeVO("upgrade_car_type_id","117");
        order.setOrderExtendAttributeInfo(Arrays.asList(extendAttributeVO1,extendAttributeVO2));
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId().longValue() : 0L;
        DriverVO driver = queryDriverService.queryDriver(Long.valueOf(params.get("driverId")), CategoryUtils.selfGetParentType(order), supplierId);
        SubSkuVO subSkuVO = new SubSkuVO();
        subSkuVO.setSubSkuId(Integer.valueOf(params.get("subSkuId")));
        subSkuVO.setDspType(DspType.of(Integer.valueOf(params.get("dspTyp"))));
        CheckContext context = CheckContext.builder()
                .context(new DspContext(dspContextService))
                .configService(checkConfig)
                .subSku(subSkuVO)
                .dspOrder(order)
                .dspStage(DspStage.DSP)
                .build();
        List<CheckModel> models = Arrays.asList(new CheckModel(new DspModelVO(order,driver)));
        DowngradeCarTypeOrderDriverCheck check = new DowngradeCarTypeOrderDriverCheck();
        List<CheckModel> result = check.check(models,context);
        return LocalJsonUtils.toJson(result);
    }
}
