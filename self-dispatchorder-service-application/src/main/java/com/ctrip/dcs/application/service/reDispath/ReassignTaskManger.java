package com.ctrip.dcs.application.service.reDispath;

import com.ctrip.igt.framework.common.spring.InstanceLocator;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 改派权益策略管理类
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/5/25 16:26
 */
public class ReassignTaskManger {

    /**
     * 策略容器
     */
    private static final Map<String, RightAndPointService> rightAndPointMap = Maps.newHashMap();

    /**
     * 初始化策略注册
     */
    public static void init() {
        Map<String, RightAndPointService> commonAgent = InstanceLocator.getBeansOfType(RightAndPointService.class);
        for (Map.Entry<String, RightAndPointService> entry : commonAgent.entrySet()) {
            rightAndPointMap.put(entry.getKey(), entry.getValue());
        }
    }

    public static void addAgent(String strategyName, RightAndPointService agent) {
        rightAndPointMap.put(strategyName, agent);
    }

    /**
     * 获取适配策略
     */
    public static RightAndPointService getAgent(String strategyName) {
        return rightAndPointMap.get(strategyName);
    }

}