package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;

@Component
public class DispatchDBBinlogListener {

    private static final Logger logger = LoggerFactory.getLogger(DispatchDBBinlogListener.class);

    @Autowired
    private Map<String, BinlogDeal> map;


    @QmqConsumer(prefix = EventConstants.DCS_DCS_SELF_DISPATCH_DB_BINLOG, consumerGroup =  EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void dispatchDBBinlogMessage(Message message) {
        // times是这个消息总重试次数
        if (message.times() > 5) {
            logger.warn("DispatchDBBinlogListener_dispatchDBBinlogMessage", String.format("retry %s times,messageID=%s", message.times(), message.getMessageId()));
            return;
        }
        logger.info("dispatchDBBinlogMessage_msg");
        String data = message.getStringProperty("dataChange");
        DataChange dataChange = JsonUtils.fromJson(data, DataChange.class);
        String tableName = dataChange.getTableName();
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        try {
            logger.info("clearCache_" + tableName, dspOrderId);
            map.get(tableName).clearCache(dataChange);
        }catch (Exception e) {
            logger.error("dispatchDBBinlogMessage", e);
            throw new NeedRetryException("dispatchDBBinlogMessage");
        }
    }
}
