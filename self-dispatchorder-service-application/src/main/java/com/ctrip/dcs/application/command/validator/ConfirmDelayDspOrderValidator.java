package com.ctrip.dcs.application.command.validator;

import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ConfirmDelayDspOrderValidator {

    public void validate(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dspOrder, DriverVO driver, TransportGroupVO transportGroup, ScheduleTaskDO scheduleTask) throws ValidateException  {
        if (Objects.isNull(dspOrder) || Objects.isNull(driver) || Objects.isNull(transportGroup) || Objects.isNull(scheduleTask)) {
            throw new ValidateException(ErrorCode.ERROR_PARAMS);
        }
        if (!Objects.equals(dspOrder.getOrderStatus(), OrderStatusEnum.SERVICE_PROVIDER_CONFIRMED.getCode())) {
            throw new ValidateException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR);
        }
    }
}
