package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.UpdateDspOrderUseTimeExeCmd;
import com.ctrip.dcs.application.command.api.UpdateDspOrderUseTimeCommand;
import com.ctrip.dcs.application.provider.converter.UpdateDspOrderUseTimeConverter;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.UpdateDspOrderUseTimeRequestType;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.UpdateDspOrderUseTimeResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UpdateDspOrderUseTimeExecutor extends AbstractRpcExecutor<UpdateDspOrderUseTimeRequestType, UpdateDspOrderUseTimeResponseType> implements Validator<UpdateDspOrderUseTimeRequestType> {

   @Autowired
   private UpdateDspOrderUseTimeExeCmd updateDspOrderUseTimeExeCmd;

    @Override
    public UpdateDspOrderUseTimeResponseType execute(UpdateDspOrderUseTimeRequestType requestType) {
        UpdateDspOrderUseTimeCommand command = UpdateDspOrderUseTimeConverter.toUpdateDspOrderUseTimeCommand(requestType);
        updateDspOrderUseTimeExeCmd.execute(command);
        return ServiceResponseUtils.success(new UpdateDspOrderUseTimeResponseType());
    }

    @Override
    public void validate(AbstractValidator<UpdateDspOrderUseTimeRequestType> validator) {
        validator.ruleFor("orderUseTimes").notNull().notEmpty();
    }
}
