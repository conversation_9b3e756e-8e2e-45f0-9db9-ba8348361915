package com.ctrip.dcs.application.provider.executor.test;

import com.ctrip.dcs.application.service.test.TestDspOrderServiceContext;
import com.ctrip.dcs.self.dispatchorder.interfaces.TestDspOrderServiceRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.TestDspOrderServiceResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TestDspOrderServiceExecutor extends AbstractRpcExecutor<TestDspOrderServiceRequestType, TestDspOrderServiceResponseType> implements Validator<TestDspOrderServiceRequestType> {
    @Autowired
    private TestDspOrderServiceContext context;
    @Override
    public TestDspOrderServiceResponseType execute(TestDspOrderServiceRequestType requestType) {
        try{
            String result = context.getService(requestType.getParams().get("serviceName")).test(requestType.getParams());
            TestDspOrderServiceResponseType responseType = new TestDspOrderServiceResponseType();
            responseType.setResult(result);
            return ServiceResponseUtils.success(responseType);
        }catch (Exception e){
            return ServiceResponseUtils.fail(new TestDspOrderServiceResponseType());
        }
    }
}
