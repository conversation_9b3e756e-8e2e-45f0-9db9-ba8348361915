package com.ctrip.dcs.application.service.risk;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;

/**
 * <AUTHOR>
 * @since 2024/11/7 11:48
 */
public interface LateRiskTypeStrategy {
    
    Integer getType();
    
    /**
     * 发送email
     */
    void sendEmail(DspOrderVO dspOrder, TransportGroupVO transportGroupDetail);
    
    /**
     * 发送pc站内信
     */
    void sendPcStationLetter(DspOrderVO dspOrder, TransportGroupVO transportGroupDetail);
    
    /**
     * 发送移动端旅游商家提醒
     * @param dspOrder
     */
    void sendMobileSupplierRemind(DspOrderVO dspOrder, TransportGroupVO transportGroupDetail);
    
    /**
     * 发送迟到风险IVR
     */
    void sendLateRiskIVR(DspOrderVO dspOrderVO, TransportGroupVO transportGroupDetail);
    
}
