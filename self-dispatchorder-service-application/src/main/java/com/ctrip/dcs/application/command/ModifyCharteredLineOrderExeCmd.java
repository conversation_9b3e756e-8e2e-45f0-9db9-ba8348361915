package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.dto.ModifyAddressDTO;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.VbkOperateRecordConstant;
import com.ctrip.dcs.domain.common.enums.CharteredQueryTypeEnum;
import com.ctrip.dcs.domain.common.enums.OrderExtendAttributeCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.service.SendEmailService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.ModifyCharteredLineOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.TripSendEmailVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.*;
import com.ctrip.dcs.domain.dsporder.entity.extend.AmountItemDO;
import com.ctrip.dcs.domain.dsporder.entity.extend.DspOrderFeeExtendInfoDO;
import com.ctrip.dcs.domain.dsporder.entity.extend.LineOrderExtendDO;
import com.ctrip.dcs.domain.dsporder.entity.line.DayModifyHistoryInnerDTO;
import com.ctrip.dcs.domain.dsporder.factory.PushOrderRemindRecordFactory;
import com.ctrip.dcs.domain.dsporder.gateway.DCSOrderPaymentGateway;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderExtendAttributeRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderUseTimeAndStopTimeAndAddressVO;
import com.ctrip.dcs.domain.schedule.event.ChangeCharteredLineDspOrderEvent;
import com.ctrip.dcs.domain.schedule.event.ModifyCharteredLineOrderEvent;
import com.ctrip.dcs.domain.schedule.event.ModifyCharteredLineOrderPriceStatusEvent;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderConfirmRecordDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderConfirmRecordPO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.self.dispatchorder.interfaces.AddressInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountItem;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 修改包车线路订单
 */
@Component
public class ModifyCharteredLineOrderExeCmd {
    private static final Logger logger = LoggerFactory.getLogger(ModifyCharteredLineOrderExeCmd.class);

    @Autowired
    private DspOrderRepository dspOrderRepository;

    @Autowired
    private DspOrderExtendAttributeRepository extendAttributeRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    private DspOrderFeeRepository orderFeeRepository;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;

    @Autowired
    IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Autowired
    SendEmailService sendEmailService;

    @Autowired
    PushOrderRemindRecordFactory pushOrderRemindRecordFactory;

    @Autowired
    QueryTransportGroupService queryTransportGroupService;

    @Autowired
    DspOrderConfirmRecordDao confirmRecordDao;

    @Autowired
    BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    DCSOrderPaymentGateway dcsOrderPaymentGateway;

    @QConfig("common_conf.properties")
    private Map<String,String> commonConf;

    /**
     * 修改线路订单
     *
     * @param requestType
     * @return
     */
    public boolean modifyCharteredLineOrder(ModifyCharteredLineOrderRequestType requestType) {

        // 1.查询派发单
        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(requestType.getUserOrderId());
        if (CollectionUtils.isEmpty(dspOrderDOS)) {
            logger.warn("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "dspOrder not exist or orderStatus error");
            return false;
        }

        DspOrderDO dspOrderDO = dspOrderDOS.get(0);

        // 2.检查唯一key
        DspOrderFeeDO dspOrderFeeDO = orderFeeRepository.find(dspOrderDO.getDspOrderId());
        if (dspOrderFeeDO == null) {
            logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "DspOrderFeeDO is null");
            return false;
        }
        boolean judgeUniqueKey = this.judgeUniqueKey(dspOrderFeeDO, requestType.getUniqueKey());
        if (!judgeUniqueKey) {
            logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "judgeUniqueKey exist, do nothing, return true");
            return true;
        }

        // 3.构建数据，更新DB
        DspOrderUseTimeAndStopTimeAndAddressVO buildModifyVO = this.buildModifyVO(dspOrderDOS.get(0), requestType);
        int updateCount = 0;
        logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "buildModifyVO={}", JacksonSerializer.INSTANCE().serialize(buildModifyVO));
        if (StringUtils.isNotBlank(buildModifyVO.getLastConfirmCarTime()) && StringUtils.isNotBlank(buildModifyVO.getLastConfirmCarTimeBJ())) {
            // 带有最晚派遣时间的更新
            updateCount = dspOrderRepository.updateDspEstimateUseTimeAndStopTimeAndAddressInfoAndLastConfirmCarTime(buildModifyVO);
        } else {
            // 不带最晚派遣时间的更新
            updateCount = dspOrderRepository.updateDspEstimateUseTimeAndStopTimeAndAddressInfo(buildModifyVO);
        }
        if (updateCount == 0) {
            logger.warn("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "updateDspEstimateUseTimeAndStopTimeAndAddressInfo fail");
            return false;
        }

        // 4.更新查询类型为：有时间-有地址
        int updateAttributeRes = extendAttributeRepository.updateOrderExtendAttributeByDspOrderIdAndAttributeCode(dspOrderDO.getDspOrderId(), OrderExtendAttributeCodeEnum.simplified_type.name(), CharteredQueryTypeEnum.HAS_TIME_HAS_ADDRESS.getCode());
        if (updateAttributeRes == 0) {
            logger.warn("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "updateOrderExtendAttributeByDspOrderIdAndAttributeCode fail");
            return false;
        }

        // 5.保存费用信息
        String extendFeeInfo = this.buildExtendFeeInfo(requestType, dspOrderFeeDO);
        if (StringUtils.isBlank(extendFeeInfo)) {
            logger.warn("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "buildExtendFeeInfo is empty");
            return false;
        }
        int updateExtendFeeRes = orderFeeRepository.updateDspOrderFeeForExtendFeeInfo(dspOrderDO.getDspOrderId(), extendFeeInfo);
        if (updateExtendFeeRes == 0) {
            logger.warn("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "updateDspOrderFeeForExtendFeeInfo fail");
            return false;
        }

        // 6.如果是已经接单(非取消)，发送修改MQ，通知司导：100041595消费
        if (!OrderStatusEnum.ORDER_CANCEL.getCode().equals(dspOrderDO.getOrderStatus()) && dspOrderDO.getOrderStatus() >= OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode()) {
            messageProvider.send(new ModifyCharteredLineOrderEvent(dspOrderDO.getDspOrderId(), dspOrderDO.getUserOrderId()));
            logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "orderStatus more than taken, sendQMQ");
        }
        // 7.记录VBK工作台日志
        this.buildVBKWorkbenchLogForModify(dspOrderDO, requestType);

        // 8.发送供应商邮件
        this.noticeEmail2Supplier(this.buildModifyCharteredLineOrderVO(dspOrderDO, requestType, buildModifyVO));

        // 9、发送消息
        messageProvider.send(new ChangeCharteredLineDspOrderEvent(dspOrderDO.getDspOrderId(), dspOrderDO.getUserOrderId()));
        logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "modifyCharteredLineOrder end");
        return true;
    }

    /**
     * 判断是否存在唯一key，如果存在，则证明之前确认过，直接返回成功
     *
     * @param dspOrderFeeDO
     * @param uniqueKey
     * @return
     */
    public boolean judgeUniqueKey(DspOrderFeeDO dspOrderFeeDO, String uniqueKey) {
        if (StringUtils.isNotBlank(dspOrderFeeDO.getExtendFeeInfo())) {
            DspOrderFeeExtendInfoDO extendInfoDO = JacksonSerializer.INSTANCE().deserialize(dspOrderFeeDO.getExtendFeeInfo(), DspOrderFeeExtendInfoDO.class);
            List<LineOrderExtendDO> lineOrderExtend = extendInfoDO.getLineOrderExtend();
            if (CollectionUtils.isNotEmpty(lineOrderExtend)) {
                List<LineOrderExtendDO> collect = lineOrderExtend.stream().filter(oe -> (uniqueKey.equals(oe.getUniqueKey()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 构建发邮件数据VO
     *
     * @param dspOrderDO
     * @param requestType
     * @return
     */
    public ModifyCharteredLineOrderVO buildModifyCharteredLineOrderVO(DspOrderDO dspOrderDO, ModifyCharteredLineOrderRequestType requestType, DspOrderUseTimeAndStopTimeAndAddressVO buildModifyVO) {

        // 应单记录ID
        Long confirmRecordId = dspOrderDO.getConfirmRecordId();
        if (confirmRecordId == null) {
            return null;
        }
        // 通过派发单查询应单记录
        DspOrderConfirmRecordPO dspOrderConfirmRecordPO = confirmRecordDao.query(confirmRecordId);
        if (dspOrderConfirmRecordPO == null) {
            logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "dspOrder no confirmRecord");
            return null;
        }

        // 获取运力组ID
        Long transportGroupId = dspOrderConfirmRecordPO.getTransportGroupId();
        TransportGroupVO transportGroup = queryTransportGroupService.queryTransportGroup(transportGroupId);
        if (transportGroup == null || StringUtils.isBlank(transportGroup.getInformEmail())) {
            logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "transportGroup is null OR email is empty,transportGroupId={}", transportGroupId);
            return null;
        }

        ModifyCharteredLineOrderVO modifyCharteredLineOrderVO = new ModifyCharteredLineOrderVO();
        // 用户单号
        modifyCharteredLineOrderVO.setUserOrderId(requestType.getUserOrderId());

        // 取绝对值：修改供应商币种(优先使用供应商币种，兜底用户币种)
        double amountABS = Math.abs(requestType.getAmountInfo().getSupplierAmount() == null ? requestType.getAmountInfo().getAmount().doubleValue() : requestType.getAmountInfo().getSupplierAmount().doubleValue());
        // 金额相关
        modifyCharteredLineOrderVO.setAmount(String.valueOf(amountABS));
        modifyCharteredLineOrderVO.setCurrency(StringUtils.isBlank(requestType.getAmountInfo().getSupplierCurrency()) ? requestType.getAmountInfo().getCurrency() : requestType.getAmountInfo().getSupplierCurrency());

        // 修改后时间、修改后地址
        modifyCharteredLineOrderVO.setNewEstimateUseTime(buildModifyVO.getEstimateUseTime());
        modifyCharteredLineOrderVO.setNewFromAddress("-");
        if (StringUtils.isNotBlank(buildModifyVO.getFromAddressInfo())) {
            ModifyAddressDTO addressDTO = JacksonSerializer.INSTANCE().deserialize(buildModifyVO.getFromAddressInfo(), ModifyAddressDTO.class);
            if (addressDTO != null) {
                modifyCharteredLineOrderVO.setNewFromAddress(addressDTO.getFromAddress());
            }
        }

        BigDecimal compareVal = requestType.getAmountInfo().getSupplierAmount() != null ? requestType.getAmountInfo().getSupplierAmount() : requestType.getAmountInfo().getAmount();
        // 退款、补款
        modifyCharteredLineOrderVO.setOperateType(BigDecimal.ZERO.compareTo(compareVal) > 0 ? "refund" : "payment");

        // 收件人：运力邮箱，运力组名称
        modifyCharteredLineOrderVO.setReceiverName(transportGroup.getTransportGroupName());
        modifyCharteredLineOrderVO.setTransGroupName(transportGroup.getTransportGroupName());
        modifyCharteredLineOrderVO.setReceiverEmail(transportGroup.getInformEmail());

        return modifyCharteredLineOrderVO;
    }

    /**
     * 邮件通知供应商
     *
     * @param modifyCharteredLineOrderVO
     */
    public void noticeEmail2Supplier(ModifyCharteredLineOrderVO modifyCharteredLineOrderVO) {
        if (modifyCharteredLineOrderVO == null) {
            return;
        }

        EmailPushModifyCharteredLineOrderRemindRecord record = pushOrderRemindRecordFactory.createModifyCharteredLineOrderEmail(modifyCharteredLineOrderVO);
        TripSendEmailVO email = TripSendEmailVO.builder()
                .contentType(TripSendEmailVO.CONTENT_TYPE)
                .channel(TripSendEmailVO.CHANNEL)
                .hasAttach(Boolean.FALSE)
                .orderId(record.getUserOrderId())
                .producer(TripSendEmailVO.Producer.builder()
                        .email(record.getSenderEmail())
                        .name(record.getSenderName())
                        .build())
                .receiver(Lists.newArrayList(TripSendEmailVO.Receiver.builder().email(record.getReceiverEmail()).name(record.getReceiverName()).build()))
                .subject(record.getTitle())
                .content(record.format())
                .build();
        logger.info("modifyCharteredLineOrder_" + modifyCharteredLineOrderVO.getUserOrderId(), "start email={}", JacksonSerializer.INSTANCE().serialize(email));
        sendEmailService.send(email);
        logger.info("modifyCharteredLineOrder_" + modifyCharteredLineOrderVO.getUserOrderId(), "end email");
    }

    /**
     * 构建VBK工作台日志对象-修改动作触发
     *
     * @param dspOrderDO
     * @param requestType
     */
    public void buildVBKWorkbenchLogForModify(DspOrderDO dspOrderDO, ModifyCharteredLineOrderRequestType requestType) {
        try {
            // 查询用户订单修改历史
            UserOrderDetail userOrderDetail = igtOrderQueryServiceGateway.queryUserOrderDetail(dspOrderDO.getUserOrderId());
            if (userOrderDetail == null || userOrderDetail.getDayModifyInfoInnerDTO() == null) {
                logger.warn("buildVBKWorkbenchLogForModify_" + dspOrderDO.getUserOrderId(), "userOrder is error");
                return;
            }
            List<DayModifyHistoryInnerDTO> modifyHistory = userOrderDetail.getDayModifyInfoInnerDTO().getModifyHistory();
            if (CollectionUtils.isEmpty(modifyHistory)) {
                logger.warn("buildVBKWorkbenchLogForModify_" + dspOrderDO.getUserOrderId(), "history is empty");
                return;
            }

            // 记录是否是第一次修改
            boolean firstModify = true;
            // 排序，获取最新操作数据
            DayModifyHistoryInnerDTO dayModifyHistoryInnerDTO = modifyHistory.get(0);
            if (modifyHistory.size() > 1) {
                List<DayModifyHistoryInnerDTO> sortList = modifyHistory.stream().sorted(Comparator.comparing(DayModifyHistoryInnerDTO::getIndex).reversed()).collect(Collectors.toList());
                dayModifyHistoryInnerDTO = sortList.get(0);
                firstModify = false;
            }

            // QConfig配置模板
            String addressAndTimeTemp = businessTemplateInfoConfig.getValueByKey(ConfigKey.MODIFY_LINE_ORDER_LOG_CONTENT);
            String payment = businessTemplateInfoConfig.getValueByKey(ConfigKey.MODIFY_LINE_ORDER_LOG_COMMENT_PAYMENT);
            String refund = businessTemplateInfoConfig.getValueByKey(ConfigKey.MODIFY_LINE_ORDER_LOG_COMMENT_REFUND);

            // 操作数据
            String beforeData = dspOrderDO.getOrderStatus().toString();
            String afterData = dspOrderDO.getOrderStatus().toString();

            // 修改之前时间:按照产品的逻辑，如果修改之前的时间是：00:00，则只展示日期（2024/12/27-包车首页优化-产品王朦朦，最终讨论结果）
            String format = this.transformTime(dayModifyHistoryInnerDTO.getBeforTime());
            String beforeTime = format;
            logger.info("buildVBKWorkbenchLogForModify_" + dspOrderDO.getUserOrderId(), "format={}", format);
            if (firstModify && StringUtils.isNotBlank(format) && format.contains(" ") && format.contains("00:00")) {
                String[] split = format.split(" ");
                beforeTime = split[0];
            }
            // 修改之后时间
            String afterTime = this.transformTime(dayModifyHistoryInnerDTO.getAfterTime());

            String beforeDataContent = String.format(addressAndTimeTemp, beforeTime, StringUtils.isBlank(dayModifyHistoryInnerDTO.getBeforAddress()) ? "-" : dayModifyHistoryInnerDTO.getBeforAddress());
            String afterDataContent = String.format(addressAndTimeTemp, afterTime, StringUtils.isBlank(dayModifyHistoryInnerDTO.getAfterAddress()) ? "-" : dayModifyHistoryInnerDTO.getAfterAddress());

            // 金额相关:优先使用供应商金额，如果没有，使用用户金额兜底
            String comment = "";
            BigDecimal amount = requestType.getAmountInfo().getSupplierAmount() == null ? requestType.getAmountInfo().getAmount() : requestType.getAmountInfo().getSupplierAmount();
            String currency = StringUtils.isBlank(requestType.getAmountInfo().getSupplierCurrency()) ? requestType.getAmountInfo().getCurrency() : requestType.getAmountInfo().getSupplierCurrency();
            if (BigDecimal.ZERO.compareTo(amount) > 0) {
                // 退款
                comment = String.format(refund, currency, Math.abs(amount.doubleValue()), beforeDataContent, afterDataContent);
            } else {
                // 补款
                comment = String.format(payment, currency, Math.abs(amount.doubleValue()), beforeDataContent, afterDataContent);
            }

            // 当地时间
            Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderDO.getCityId());
            // 构建VO
            String operateUserType =  "user";
            String operator = "user";
            if (requestType.getRequestHeader() != null && StringUtils.isNotEmpty(requestType.getRequestHeader().getUid())) {
                String uid = requestType.getRequestHeader().getUid();
                operateUserType = StringUtils.substringBefore(uid, "_");
                operator = StringUtils.substringAfter(uid, "_");
            }
            // 用于vbk工作台日志描述
            if (StringUtils.equalsIgnoreCase("vbk", operateUserType)) {
                operator = String.format(commonConf.get("operate.user.name.desc"), StringUtils.defaultIfEmpty(operator, "user"));
            } else {
                operator = "user";
                operateUserType = "user";
            }
            VBKOperationRecordVO recordVO = VBKOperationRecordVO.builder()
                    .supplyOrderId(dspOrderDO.getDspOrderId())
                    .userOrderId(dspOrderDO.getUserOrderId())
                    .vendorOrderId("")
                    .sysUserAccount("system")
                    .operUserName(operator)
                    .operUserType(operateUserType)
                    .operateType(VbkOperateRecordConstant.LINE_ORDER_MODIFY)
                    .operateName(operator)
                    .operateLocalTime(localTimeNow.getTime())
                    .recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW)
                    .beforeOperateData(beforeData)
                    .afterOperateData(afterData)
                    .comment(comment)
                    .supplierId(dspOrderDO.getSupplierId())
                    .orderSourceCode(String.valueOf(dspOrderDO.getOrderSourceCode()))
                    .timeZone(dspOrderDO.getTimeZone() == null ? 0D : dspOrderDO.getTimeZone().doubleValue())
                    .build();

            vbkOperationRecordGateway.record(recordVO);
            logger.info("buildVBKWorkbenchLog_" + dspOrderDO.getUserOrderId(), "buildVBKWorkbenchLog_end,record={}", JacksonSerializer.INSTANCE().serialize(recordVO));
        } catch (Exception e) {
            logger.warn("buildVBKWorkbenchLog_error_" + dspOrderDO.getUserOrderId(), e);
        }
    }

    /**
     * 转换时间
     *
     * @param source
     * @return
     */
    public String transformTime(String source) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");
            LocalDateTime localDateTime = LocalDateTime.parse(source, dateTimeFormatter);
            return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            logger.error("transformTimeError", e);
            return source;
        }
    }

    /**
     * 构建更新费用表中：扩展费用数据
     *
     * @param requestType
     * @param dspOrderFeeDO
     * @return
     */
    public String buildExtendFeeInfo(ModifyCharteredLineOrderRequestType requestType, DspOrderFeeDO dspOrderFeeDO) {

        DspOrderFeeExtendInfoDO infoDO = null;
        if (StringUtils.isNotBlank(dspOrderFeeDO.getExtendFeeInfo())) {
            DspOrderFeeExtendInfoDO extendInfoDO = JacksonSerializer.INSTANCE().deserialize(dspOrderFeeDO.getExtendFeeInfo(), DspOrderFeeExtendInfoDO.class);
            infoDO = this.buildUpdateDspOrderFeeExtendInfoDO(extendInfoDO, requestType);
        } else {
            infoDO = this.buildInsertDspOrderFeeExtendInfoDO(requestType);
        }

        String serialize = JacksonSerializer.INSTANCE().serialize(infoDO);
        logger.info("modifyCharteredLineOrder_" + requestType.getUserOrderId(), "buildExtendFeeInfo val={}", serialize);
        return serialize;
    }

    /**
     * 首次修改，构建入库DO
     *
     * @param extendInfoDO
     * @param requestType
     * @return
     */
    public DspOrderFeeExtendInfoDO buildUpdateDspOrderFeeExtendInfoDO(DspOrderFeeExtendInfoDO extendInfoDO, ModifyCharteredLineOrderRequestType requestType) {

        // 终态的数据：已完成支付 或者 已完成退款
        List<LineOrderExtendDO> finalExtendDO = extendInfoDO.getLineOrderExtend().stream().filter(extend -> (extend.getStatus() == 1)).collect(Collectors.toList());

        // 构建新费用信息
        DspOrderFeeExtendInfoDO newExtendDO = this.buildInsertDspOrderFeeExtendInfoDO(requestType);

        // 如果有已经走到终态的，需要把终态的数据补齐到新的数据集合中
        if (CollectionUtils.isNotEmpty(finalExtendDO)) {
            List<LineOrderExtendDO> lineOrderExtend = newExtendDO.getLineOrderExtend();
            lineOrderExtend.addAll(finalExtendDO);

            newExtendDO.setLineOrderExtend(lineOrderExtend);
        }
        return newExtendDO;
    }

    /**
     * 首次修改，构建入库DO
     *
     * @param requestType
     * @return
     */
    public DspOrderFeeExtendInfoDO buildInsertDspOrderFeeExtendInfoDO(ModifyCharteredLineOrderRequestType requestType) {

        // 费用信息、费项信息
        AmountInfo amountInfo = requestType.getAmountInfo();
        List<AmountItem> reqItemList = amountInfo.getItemList();

        // 返回值
        DspOrderFeeExtendInfoDO infoDO = new DspOrderFeeExtendInfoDO();

        // 入库DO
        LineOrderExtendDO extendDO = new LineOrderExtendDO();
        // 费用金额、币种
        extendDO.setAmount(amountInfo.getAmount());
        extendDO.setCurrency(amountInfo.getCurrency());
        // 供应商币种、 供应商币种金额
        extendDO.setSupplierCurrency(amountInfo.getSupplierCurrency());
        extendDO.setSupplierAmount(amountInfo.getSupplierAmount());

        // 创建时间、更新时间
        extendDO.setcTime(amountInfo.getCreateTime());
        extendDO.setUpTime(amountInfo.getCreateTime());
        // 状态，默认为0=操作中，0元直接变更为已处理完成（使用用户金额判断，因为支付回调是用户维度回调）
        if (BigDecimal.ZERO.compareTo(amountInfo.getAmount()) == 0) {
            extendDO.setStatus(1);
        } else {
            extendDO.setStatus(0);
        }
        // 唯一标识
        extendDO.setUniqueKey(requestType.getUniqueKey());

        // 费项集合
        List<AmountItemDO> itemList = Lists.newArrayList();
        for (AmountItem item : reqItemList) {
            AmountItemDO target = new AmountItemDO();
            target.setAmount(item.getItemAmount());
            target.setCode(item.getItemCode());

            itemList.add(target);
        }
        extendDO.setItemList(itemList);

        infoDO.setLineOrderExtend(Lists.newArrayList(extendDO));
        return infoDO;
    }

    /**
     * 构建更新VO
     *
     * @param dspOrderDO
     * @param requestType
     * @return
     */
    public DspOrderUseTimeAndStopTimeAndAddressVO buildModifyVO(DspOrderDO dspOrderDO, ModifyCharteredLineOrderRequestType requestType) {

        // 返回值
        DspOrderUseTimeAndStopTimeAndAddressVO target = new DspOrderUseTimeAndStopTimeAndAddressVO();

        // 派发单号
        target.setDspOrderId(dspOrderDO.getDspOrderId());

        // 预估用车时间-当地
        target.setEstimateUseTime(requestType.getEstimateUseTime());
        // 预估用车时间-北京
        target.setEstimateUseTimeBJ(requestType.getEstimateUseTimeBJ());

        // 预估结束时间-当地
        target.setPredictServiceStopTime(requestType.getPredicServiceStopTime());
        // 预估结束时间-北京
        target.setPredictServiceStopTimeBJ(requestType.getPredicServiceStopTimeBJ());

        // 最晚派遣时间-当地
        target.setLastConfirmCarTime(requestType.getLastConfirmCarTime());
        // 最晚派遣时间-北京
        target.setLastConfirmCarTimeBJ(requestType.getLastConfirmCarTimeBJ());


        // 出发地
        AddressInfo fromAddressInfo = requestType.getFromAddressInfo();

        FromPoiDTO fromPoi = new FromPoiDTO();
        fromPoi.setActualFromCoordsys(fromAddressInfo.getCoordsys());
        fromPoi.setActualFromLatitude(fromAddressInfo.getLatitude());
        fromPoi.setActualFromLongitude(fromAddressInfo.getLongitude());
        fromPoi.setFromName(fromAddressInfo.getAddressName());
        fromPoi.setFromAddress(fromAddressInfo.getAddressDetail());
        fromPoi.setDeptCarPlaceId(fromAddressInfo.getCarPlaceId());
        target.setFromAddressInfo(JacksonSerializer.INSTANCE().serialize(fromPoi));


        // 目的地地
        AddressInfo toAddressInfo = requestType.getToAddressInfo();

        ToPoiDTO toPoi = new ToPoiDTO();
        toPoi.setActualToCoordsys(toAddressInfo.getCoordsys());
        toPoi.setActualToLatitude(toAddressInfo.getLatitude());
        toPoi.setActualToLongitude(toAddressInfo.getLongitude());
        toPoi.setToName(toAddressInfo.getAddressName());
        toPoi.setToAddress(toAddressInfo.getAddressDetail());
        toPoi.setArriveCarPlaceId(toAddressInfo.getCarPlaceId());
        target.setToAddressInfo(JacksonSerializer.INSTANCE().serialize(toPoi));

        return target;
    }

    /**
     * 修改线路包车支付状态相关方法
     *
     * @param userOrderId
     * @param payStatus
     * @return
     */
    public boolean modifyCharteredLineOrderPriceStatus(String userOrderId, int payStatus) {
        // 1.查询派发单
        List<DspOrderDO> dspOrderDOS = dspOrderRepository.queryValidDspOrders(userOrderId);
        if (CollectionUtils.isEmpty(dspOrderDOS)) {
            logger.warn("modifyCharteredLineOrderPriceStatus_" + userOrderId, "dspOrder not exist or orderStatus error");
            return false;
        }

        DspOrderDO dspOrderDO = dspOrderDOS.get(0);

        // 2.查询派发单费用
        DspOrderFeeDO dspOrderFeeDO = orderFeeRepository.find(dspOrderDO.getDspOrderId());
        if (dspOrderFeeDO == null || StringUtils.isBlank(dspOrderFeeDO.getExtendFeeInfo())) {
            logger.warn("modifyCharteredLineOrderPriceStatus_" + userOrderId, "dspOrderFeeDO not exist or extendFee is empty");
            return false;
        }
        logger.info("modifyCharteredLineOrderPriceStatus_" + userOrderId, "original ExtendFeeInfo={}", dspOrderFeeDO.getExtendFeeInfo());

        // 当前系统时间
        Date currentTime = new Date();

        // 3.处理数据
        boolean needUpdate = false;
        // 存储金额和币种
        BigDecimal amount = null;
        String currency = "";
        String uniqueKey = "";
        DspOrderFeeExtendInfoDO extendInfoDO = JacksonSerializer.INSTANCE().deserialize(dspOrderFeeDO.getExtendFeeInfo(), DspOrderFeeExtendInfoDO.class);
        List<LineOrderExtendDO> lineOrderExtend = extendInfoDO.getLineOrderExtend();
        for (LineOrderExtendDO extendDO : lineOrderExtend) {
            if (extendDO.getStatus() == 0) {
                extendDO.setStatus(1);
                extendDO.setUpTime(DateUtil.getStringDate(currentTime));
                logger.info("modifyCharteredLineOrderPriceStatus_" + userOrderId, "change priceStatus");
                needUpdate = true;
                amount = extendDO.getSupplierAmount() != null ? extendDO.getSupplierAmount() : extendDO.getAmount() ;
                currency = StringUtils.isNotBlank(extendDO.getSupplierCurrency()) ? extendDO.getSupplierCurrency() : extendDO.getCurrency();
                uniqueKey = extendDO.getUniqueKey();
            }
        }

        if (!needUpdate) {
            logger.info("modifyCharteredLineOrderPriceStatus_" + userOrderId, "all priceStatus is end, not update db");
            MetricsUtil.recordValue(MetricsConstants.MODIFY_CHARTERED_LINE_ORDER_PRICE_DO_NOTHING);
            return true;
        }

        // 4.更新DB
        DspOrderFeeExtendInfoDO newExtendInfo = new DspOrderFeeExtendInfoDO();
        newExtendInfo.setLineOrderExtend(lineOrderExtend);
        String newExtendInfoStr = JacksonSerializer.INSTANCE().serialize(newExtendInfo);
        int count = orderFeeRepository.updateDspOrderFeeForExtendFeeInfo(dspOrderDO.getDspOrderId(), newExtendInfoStr);
        logger.info("modifyCharteredLineOrderPriceStatus_" + userOrderId, "operate db, res={}, param={}", count, newExtendInfoStr);

        // 5.更新DB成功，发送结算MQ
        if (count > 0) {
            messageProvider.send(new ModifyCharteredLineOrderPriceStatusEvent(dspOrderDO.getDspOrderId(), dspOrderDO.getUserOrderId(), dspOrderDO.getDriverOrderId(), uniqueKey));
            logger.info("modifyCharteredLineOrderPriceStatus_" + userOrderId, "PriceStatus change, sendQMQ");
        }

        // 6.记录工作台日志
        this.buildVBKWorkbenchLogForPayment(dspOrderDO, payStatus, amount, currency);

        return count > 0;
    }

    /**
     * 构建VBK工作台日志对象--支付回调触发
     *
     * @param dspOrderDO
     * @param payStatus
     */
    public void buildVBKWorkbenchLogForPayment(DspOrderDO dspOrderDO, int payStatus, BigDecimal amount, String currency) {
        try {
            if (amount == null) {
                return;
            }
            // 绝对值
            double amountABS = Math.abs(amount.doubleValue());

            // 查询用户实付金额
            BigDecimal actualAmount = dcsOrderPaymentGateway.getOrderActualAmount(dspOrderDO.getUserOrderId());
            if (actualAmount == null) {
                logger.warn("getOrderActualAmount_" + dspOrderDO.getUserOrderId(), "getOrderActualAmount is null");
                return;
            }

            // QConfig配置模板
            String paymentTemp = businessTemplateInfoConfig.getValueByKey(ConfigKey.MODIFY_LINE_ORDER_LOG_PAYMENT);
            String refundTemp = businessTemplateInfoConfig.getValueByKey(ConfigKey.MODIFY_LINE_ORDER_LOG_REFUND);


            // 金额相关
            String comment = "";
            // 1=退款
            if (payStatus == 1) {
                comment = String.format(refundTemp, currency, amountABS, currency, actualAmount.toString());
            } else {
                // 0=补款
                comment = String.format(paymentTemp, currency, amountABS, currency, actualAmount.toString());
            }

            // 当地时间
            Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderDO.getCityId());
            // 构建VO
            VBKOperationRecordVO recordVO = VBKOperationRecordVO.builder()
                    .supplyOrderId(dspOrderDO.getDspOrderId())
                    .userOrderId(dspOrderDO.getUserOrderId())
                    .vendorOrderId("")
                    .sysUserAccount("system")
                    .operUserName("user")
                    .operUserType("user")
                    .operateType(payStatus == 1 ? VbkOperateRecordConstant.ORDER_REFUND : VbkOperateRecordConstant.ORDER_PAYMENT)
                    .operateName("user")
                    .operateLocalTime(localTimeNow.getTime())
                    .recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW)
                    .beforeOperateData(dspOrderDO.getOrderStatus().toString())
                    .afterOperateData(dspOrderDO.getOrderStatus().toString())
                    .comment(comment)
                    .supplierId(dspOrderDO.getSupplierId())
                    .orderSourceCode(String.valueOf(dspOrderDO.getOrderSourceCode()))
                    .timeZone(dspOrderDO.getTimeZone() == null ? 0D : dspOrderDO.getTimeZone().doubleValue())
                    .build();

            vbkOperationRecordGateway.record(recordVO);
        } catch (Exception e) {
            logger.warn("buildVBKWorkbenchLog_error_" + dspOrderDO.getUserOrderId(), e);
        }
    }

}
