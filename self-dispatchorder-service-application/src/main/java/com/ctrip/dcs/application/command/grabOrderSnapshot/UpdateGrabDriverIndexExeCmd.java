package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.api.UpdateGrabDriverIndexCommand;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class UpdateGrabDriverIndexExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(UpdateGrabDriverIndexExeCmd.class);

    public void execute(UpdateGrabDriverIndexCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, command.getDriverId()));
        try {
            if (lock.tryLock()) {
                List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(command.getDriverId(), command.getCategory().getCode());
                if (CollectionUtils.isEmpty(indexes)) {
                    logger.info("GrabDspOrderDriverIndexInfo_" + command.getDriverId(), "indexes is empty, driver id:{}", command.getDriverId());
                    return;
                }
                List<GrabDspOrderSnapshotDO> snapshots = querySnapshots(indexes);
                if (CollectionUtils.isEmpty(snapshots)) {
                    logger.info("GrabDspOrderDriverIndexInfo_" + command.getDriverId(), "snapshots is empty, driver id:{}", command.getDriverId());
                    return;
                }
                List<DspOrderVO> dspOrderList = queryOrders(snapshots);
                if (CollectionUtils.isEmpty(dspOrderList)) {
                    logger.info("GrabDspOrderDriverIndexInfo_" + command.getDriverId(), "dsp order list is empty, driver id:{}", command.getDriverId());
                    return;
                }
                List<DriverPushConfigVO> configs = queryDriverPushConfigs(Lists.newArrayList(command.getDriverId()), command.getCategory().getCode());
                List<CheckModel> check = check(dspOrderList, snapshots, command.getDriverId(), command.getEvent());
                updateIndexes(indexes, check, configs);
                logger.info("GrabDspOrderDriverIndexInfo_" + command.getDriverId(), JacksonSerializer.INSTANCE().serialize(indexes));
                grabDspOrderDriverIndexRepository.update(indexes);
            }
        } catch (Exception e) {
            logger.warn("GrabDspOrderDriverIndexError", e);
        } finally {
            lock.unlock();
        }
    }

    public List<GrabDspOrderSnapshotDO> querySnapshots( List<GrabDspOrderDriverIndexDO> indexes) {
        List<String> dspOrderIds = indexes.stream().map(GrabDspOrderDriverIndexDO::getDspOrderId).distinct().toList();
        List<GrabDspOrderSnapshotDO> snapshots = grabDspOrderSnapshotRepository.query(dspOrderIds, null, Lists.newArrayList(GrabDspOrderSnapshotStatusEnum.INIT.getCode(), GrabDspOrderSnapshotStatusEnum.GRAB.getCode()));
        return Optional.ofNullable(snapshots)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> Objects.equals(item.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.INIT) || Objects.equals(item.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.GRAB))
                .toList();
    }

    public List<DspOrderVO> queryOrders(List<GrabDspOrderSnapshotDO> snapshots) {
        List<String> dspOrderIds = snapshots.stream().map(GrabDspOrderSnapshotDO::getDspOrderId).distinct().toList();
        return queryDspOrderService.selectDspOrderVOs(dspOrderIds);
    }

    public List<CheckModel> check(List<DspOrderVO> dspOrderList, List<GrabDspOrderSnapshotDO> snapshots, Long driverId, GrabDspOrderSnapshotEventEnum event) {
        List<CheckModel> result = Lists.newArrayList();
        Map<String, GrabDspOrderSnapshotDO> snapshotMap = Maps.newHashMap();
        for (GrabDspOrderSnapshotDO snapshot : snapshots) {
            snapshotMap.put(snapshot.getDspOrderId(), snapshot);
        }
        for (DspOrderVO dspOrder : dspOrderList) {
            try {
                GrabDspOrderSnapshotDO snapshot = snapshotMap.get(dspOrder.getDspOrderId());
                if (Objects.isNull(snapshot) || StringUtils.isBlank(snapshot.getDuid())) {
                    continue;
                }
                List<CheckModel> check = check(dspOrder, snapshot.getDuid(), Lists.newArrayList(driverId));
                result.addAll(check);
            } catch (Exception e) {
                logger.warn("UpdateGrabDriverIndexError", "driver index check error", e);
            }
        }
        return result;
    }

    @Override
    public void updateIndexesCheckCode(List<GrabDspOrderDriverIndexDO> indexes, List<CheckModel> check) {
        Map<String /*dspOrderId*/, CheckCode> orderCheckMap = Maps.newHashMap();
        for (CheckModel model : check) {
            String dspOrderId = Optional.ofNullable(model).map(CheckModel::getModel).map(DspModelVO::getOrder).map(DspOrderVO::getDspOrderId).orElse("");
            CheckCode checkCode = Optional.ofNullable(model).map(CheckModel::getCheckCode).orElse(CheckCode.NULL);
            orderCheckMap.put(dspOrderId, checkCode);
        }
        for (GrabDspOrderDriverIndexDO index : indexes) {
            CheckCode checkCode = (CheckCode) MapUtils.getObject(orderCheckMap, index.getDspOrderId(), CheckCode.NULL);
            index.updateValid(checkCode.isPass() ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        }
    }
}
