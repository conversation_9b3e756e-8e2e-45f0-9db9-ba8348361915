package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.PackageTypeEnum;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.PlatformPriceStrategyRepository;
import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.dto.DrvOrderRewardVO;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderRewardFactory;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.ctrip.dcs.domain.common.constants.ConfigKey.STRATEGY_ID_FOR_CHATERED;

/**
 * <AUTHOR>
 */
@Component
public class MockConfirmDspOrderExecutor extends AbstractRpcExecutor<MockConfirmDspOrderRequestType, MockConfirmDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(MockConfirmDspOrderExecutor.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;


    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;


    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;


    @Autowired
    private DriverOrderGateway driverOrderGateway;

    @Autowired
    private QueryVehicleService queryVehicleService;

    @Autowired
    PlatformPriceStrategyRepository platformPriceStrategyRepository;

    @Autowired
    BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Autowired
    private DriverOrderRewardFactory driverOrderRewardFactory;

    @Override
    public MockConfirmDspOrderResponseType execute(MockConfirmDspOrderRequestType request) {
        try {
            Integer confirmType = request.getType();
            if (Objects.equals(confirmType, ConfirmType.SERVICE_PROVIDER_CONFIRMED.getStatus())) {
                serviceProviderConfirm(request);
            }
            if (Objects.equals(confirmType, ConfirmType.DISPATCH_CONFIRMED.getStatus())) {
                dispatcherConfirm(request);
            }
            if (Objects.equals(confirmType, ConfirmType.DRIVER_CONFIRMED.getStatus())) {
                driverConfirm(request);
            }
            if (Objects.equals(confirmType, ConfirmType.DRIVER_CAR_CONFIRMED.getStatus())) {
                driverCarConfirm(request);
            }
            return ServiceResponseUtils.success(new MockConfirmDspOrderResponseType());
        } catch (OrderStatusException e) {
            logger.error(e);
        }
        return ServiceResponseUtils.fail(new MockConfirmDspOrderResponseType());
    }

    private void driverConfirm(MockConfirmDspOrderRequestType request) throws OrderStatusException {
        DspOrderVO order = queryDspOrder(request.getDspOrderId());
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId() : 0L;
        DriverVO driver = queryDriver(request.getDriverId(), CategoryUtils.selfGetParentType(order), supplierId);
        TransportGroupVO transportGroup = queryTransportGroups(request.getTransportGroupId());
        String driverOrderId = createDriverOrderId(order, driver, transportGroup, request.getCheckCode());

        DriverConfirmVO confirmVO = DriverConfirmVO.builder()
                .dspOrder(order)
                .serviceProvider(new ServiceProviderVO(order.getSpId()))
                .supplier(driver.getSupplier())
                .transportGroup(transportGroup)
                .driver(driver)
                .duid(duid())
                .driverOrderId(driverOrderId)
                .rewardAmount("")
                .event(OrderStatusEvent.of(request.getEvent()))
                .operator(OperatorVO.systemOperator())
                .build();
        logger.info("driverConfirm", JacksonSerializer.INSTANCE().serialize(confirmVO));
        confirmDspOrderService.confirm(confirmVO);
    }

    private void driverCarConfirm(MockConfirmDspOrderRequestType request) throws OrderStatusException {
        DspOrderVO order = queryDspOrder(request.getDspOrderId());
        ParentCategoryEnum parentCategoryEnum = CategoryUtils.selfGetParentType(order);
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId() : 0L;
        DriverVO driver = queryDriver(request.getDriverId(),parentCategoryEnum, supplierId);
        VehicleVO vehicle = queryVehicle(driver.getCar().getCarId(),parentCategoryEnum);
        TransportGroupVO transportGroup = queryTransportGroups(request.getTransportGroupId());
        String driverOrderId = createDriverOrderId(order, driver, transportGroup, request.getCheckCode());
        DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                .dspOrder(order)
                .serviceProvider(new ServiceProviderVO(order.getSpId()))
                .supplier(new SupplierVO(transportGroup.getSupplierId()))
                .transportGroup(transportGroup)
                .driver(driver)
                .vehicle(vehicle)
                .duid(duid())
                .driverOrderId(driverOrderId)
                .rewardAmount("")
                .event(OrderStatusEvent.of(request.getEvent()))
                .operator(OperatorVO.systemOperator())
                .build();
        logger.info("driverCarConfirm", JacksonUtil.serialize(confirmVO));
        confirmDspOrderService.confirm(confirmVO);
    }

    private void dispatcherConfirm(MockConfirmDspOrderRequestType request) throws OrderStatusException {
        TransportGroupVO transportGroup = queryTransportGroups(request.getTransportGroupId());
        DspOrderVO order = queryDspOrder(request.getDspOrderId());
        DispatcherConfirmVO confirmVO = DispatcherConfirmVO.builder()
                .dspOrder(order)
                .serviceProvider(new ServiceProviderVO(order.getSpId()))
                .supplier(new SupplierVO(transportGroup.getSupplierId()))
                .transportGroup(transportGroup)
                .duid(duid())
                .event(OrderStatusEvent.of(request.getEvent()))
                .operator(OperatorVO.systemOperator())
                .build();
        confirmDspOrderService.confirm(confirmVO);
    }

    private void serviceProviderConfirm(MockConfirmDspOrderRequestType request) throws OrderStatusException {
        DspOrderVO order = queryDspOrder(request.getDspOrderId());
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId() : 0L;
        DriverVO driver = queryDriver(request.getDriverId(),CategoryUtils.selfGetParentType(order), supplierId);
        TransportGroupVO transportGroup = queryTransportGroups(request.getTransportGroupId());
        String driverOrderId = createDriverOrderId(order, driver, transportGroup, request.getCheckCode());
        ServiceProviderConfirmVO confirmVO = ServiceProviderConfirmVO.builder()
                .dspOrder(order)
                .serviceProvider(new ServiceProviderVO(order.getSpId()))
                .duid(duid())
                .driver(driver)
                .driverOrderId(driverOrderId)
                .event(OrderStatusEvent.of(request.getEvent()))
                .delayExecuteTimeDeadline(order.getLastConfirmCarTime())
                .build();
        confirmDspOrderService.confirm(confirmVO);
    }

    private DspOrderVO queryDspOrder(String dspOrderId) {
        return queryDspOrderService.query(dspOrderId);
    }

    private DriverVO queryDriver(Long driverId, ParentCategoryEnum parentCategoryEnum, Long supplierId) {
        return queryDriverService.queryDriver(driverId,parentCategoryEnum, supplierId);
    }

    private TransportGroupVO queryTransportGroups(Long transportGroupsId) {
        return queryTransportGroupService.queryTransportGroup(transportGroupsId);
    }

    private VehicleVO queryVehicle(Long carId,ParentCategoryEnum parentCategoryEnum) {
        return queryVehicleService.query(carId,parentCategoryEnum);
    }

    private String createDriverOrderId(DspOrderVO order, DriverVO driver, TransportGroupVO transportGroup,Integer checkCode) {
        List<DrvOrderRewardVO> driverOrderRewardList = driverOrderRewardFactory.create(order);
        String driverOrderId = driverOrderGateway.create(
                order,
                driver,
                driver.getCar(),
                transportGroup,
                driver.getSupplier(),
                new ServiceProviderVO(order.getSpId()),
                BigDecimal.ONE,
                driver.supportedProduct(order.getXproductList()),
                platformPriceStrategyRepository.existSomeStrategyForSupplyOrder(order.getDspOrderId(), businessTemplateInfoConfig.getValueByKey(STRATEGY_ID_FOR_CHATERED)),
                1,
                SysConstants.AssignRole.BOSS_DISPATCH_TYPE,
                checkCode,
                driverOrderRewardList,
                DspType.MOCK_TEST.getCode()
        );
        if (StringUtils.isBlank(driverOrderId)) {
            throw new BizException();
        }
        return driverOrderId;
    }

    private DuidVO duid() {
        return DuidVO.of("0-0-0-0-0-0-0-0-0-0");
    }
}
