package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.IDriverSupplierModifyEventHandler;
import com.ctrip.dcs.application.event.dto.DriverSupplierModifyEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverSupplierModifyTestService")
public class DriverSupplierModifyTestService implements ITestDspOrderService{
    @Autowired
    private IDriverSupplierModifyEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverSupplierModifyEvent event = new DriverSupplierModifyEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setSupplierId(Long.valueOf(params.get("supplierId")));
        event.setAccountType(params.get("accountType"));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
