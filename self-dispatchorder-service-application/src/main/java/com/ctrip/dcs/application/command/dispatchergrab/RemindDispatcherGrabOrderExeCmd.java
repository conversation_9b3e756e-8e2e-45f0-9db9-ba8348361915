package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.PushOrderRemindExeCmd;
import com.ctrip.dcs.application.command.api.PushOrderRemindCommand;
import com.ctrip.dcs.application.command.api.RemindDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.validator.RemindDispatcherGrabOrderValidator;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.PushOrderRemindType;
import com.ctrip.dcs.domain.common.enums.SupplierConfirmSceneEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.ModifyJntOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.CalculateEstimatedUseTimeParam;
import com.ctrip.dcs.domain.dsporder.entity.EstimatedUseTimeDTO;
import com.ctrip.dcs.domain.dsporder.entity.ivr.UrgentOrderIvrConfig;
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway;
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseOrderDispatchCaseGateway;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.domain.schedule.event.GrabOrderIvrEvent;
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.adapter.qconfig.IvrConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.ctrip.dcs.domain.common.constants.EventConstants.DCS_DSP_DISPATCH_GRAB_ORDER_IVR_CREATE;

/**
 * 调度抢单进单提醒
 *
 * <AUTHOR>
 */
@Component
public class RemindDispatcherGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(RemindDispatcherGrabOrderExeCmd.class);

    @Autowired
    private PushOrderRemindExeCmd pushOrderRemindExeCmd;

    @Autowired
    private DispatcherGrabOrderGateway dispatcherGrabOrderGateway;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;


    @Autowired
    private RemindDispatcherGrabOrderValidator validator;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private IvrConfig ivrConfig;

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @Autowired
    private PurchaseOrderDispatchCaseGateway purchaseOrderDispatchCaseGateway;

    @Autowired
    private DspOrderDao dspOrderDao;

    public void execute(RemindDispatcherGrabOrderCommand command) {
        List<DispatcherGrabOrderDO> list = dispatcherGrabOrderGateway.query(command.getDispatcherGrabOrderIds());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DispatcherGrabOrderDO order : list) {
            if (!Objects.equals(SupplierConfirmSceneEnum.URGENT.getScene(), order.getConfirmScene())
                    && !Objects.equals(SupplierConfirmSceneEnum.ORI_MODIFY.getScene(), order.getConfirmScene())) {
                // 非急单和原单修改
                continue;
            }
            if (SupplierConfirmSceneEnum.ORI_MODIFY.getScene().equals(order.getConfirmScene())) {
                // 原单修改待确认通知
                oriOrderModifyPush(order);
            } else {
                // 急单待确认通知
                push(order);
            }
            // IVR通知
            sendUrgentIvrMsg(order);
        }
    }

    private void oriOrderModifyPush(DispatcherGrabOrderDO order) {
        try {
            logger.info("RemindDispatcherGrabOrderExeCmd.oriOrderModifyPush_" + order.getDspOrderId(), "start process");
            // 查询用户单修改信息
            ModifyJntOrderVO modifyJntOrderVO = igtOrderQueryServiceGateway.queryModifyJntOrder(order.getUserOrderId(), order.getModifyVersion());
            if (Objects.isNull(modifyJntOrderVO)) {
                logger.error("oriOrderModifyPush", "queryModifyJntOrder is null");
                return;
            }
            // 查询旧派单，获取旧单的预估用车时间
            DspOrderPO oldDspOrder = dspOrderDao.findByDspOrderId(order.getDspOrderId());
            if (Objects.nonNull(oldDspOrder)) {
                modifyJntOrderVO.getOldOrderInfo().setEstimatedUseTime(DateUtil.getStringDate(oldDspOrder.getEstimatedUseTime()));
            }
            // 计算新行程的预估用车时间
            CalculateEstimatedUseTimeParam param = new CalculateEstimatedUseTimeParam();
            param.setCategoryCode(modifyJntOrderVO.getNewOrderInfo().getCategoryCode());
            param.setUseTime(modifyJntOrderVO.getNewOrderInfo().getUseTime());
            param.setUserChoseBufferMinutes(modifyJntOrderVO.getNewOrderInfo().getUserChoseBufferMinutes());
            param.setLocalCityId(modifyJntOrderVO.getNewOrderInfo().getLocalCityId());
            param.setUserOrderId(order.getUserOrderId());
            if (CategoryCodeEnum.isFromAirport(modifyJntOrderVO.getNewOrderInfo().getCategoryCode())) {
                param.setUserBookFlightInfo(modifyJntOrderVO.getNewOrderInfo().getUserBookFlightInfo());
            }
            EstimatedUseTimeDTO estimatedUseTimeDTO = purchaseOrderDispatchCaseGateway.calculateEstimatedUseTime(param);
            if (estimatedUseTimeDTO != null) {
                modifyJntOrderVO.getNewOrderInfo().setEstimatedUseTime(estimatedUseTimeDTO.getEstimatedUseTime());
            }
            // 这里是最晚同意时间
            modifyJntOrderVO.getNewOrderInfo().setLastConfirmTime(DateUtil.getStringDate(order.getLastConfirmTime()));
            // 原单修改待确认通知
            push(order, modifyJntOrderVO);
            Cat.logEvent("dcs.self.oriOrderModifyPush.result", "success");
        } catch (Exception e) {
            Cat.logEvent("dcs.self.oriOrderModifyPush.result", "error");
            logger.error("oriOrderModifyPush error", e);
        }
    }

    /**
     * 急单&原单修改待确认-发送IVR延迟消息记录
     *
     * @param order
     */
    public void sendUrgentIvrMsg(DispatcherGrabOrderDO order) {
        try {
            if (!Objects.equals(order.getConfirmScene(), SupplierConfirmSceneEnum.URGENT.getScene())
                    && !Objects.equals(order.getConfirmScene(), SupplierConfirmSceneEnum.ORI_MODIFY.getScene())) {
                logger.info("order_" + order.getUserOrderId(), "order is not urgentOrder or oriOrderModify, orderInfo=" + JacksonUtil.serialize(order));
                return;
            }
            //订单不是待抢单
            if (DispatcherGrabOrderStatusEnum.INIT != order.getGrabStatus()) {
                logger.info("order_" + order.getUserOrderId(), "order is not init order orderInfo=" + JacksonUtil.serialize(order));
                return;
            }
            logger.info("send_msg" + order.getUserOrderId(), "order=" + JacksonUtil.serialize(order));
            DspOrderVO dspOrderVO = queryDspOrderService.queryDspOrder(order);
            logger.info("send_msg" + order.getUserOrderId(), "isUrgentGray=" + ivrConfig.isUrgentGraySupplier(order.getSupplierId().intValue()));
            //发送延迟消息,默认时间5分钟
            UrgentOrderIvrConfig urgentOrderIvrConfig = ivrConfig.queryUrgentIvrConfig(dspOrderVO.getCityId());
            if (urgentOrderIvrConfig != null) {
                messageProvider.send(new GrabOrderIvrEvent(DCS_DSP_DISPATCH_GRAB_ORDER_IVR_CREATE, urgentOrderIvrConfig.getQmqDelayMillTime(), order.getId(), order.getUserOrderId()));
            }
        } catch (Exception e) {
            logger.error("ivr_" + order.getUserOrderId(), e);
        }
    }

    private void push(DispatcherGrabOrderDO order) {
        try {
            DspOrderVO dspOrderVO = queryDspOrderService.queryDspOrder(order);
            dspOrderVO.setDispatcherGrabOrderDO(order);
            TransportGroupVO transportGroupVO = queryTransportGroupService.queryTransportGroup(order.getTransportGroupId());
            if (validator.validate(order, dspOrderVO, transportGroupVO)) {
                pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrderVO, transportGroupVO, getDispatcherGrabRemindTypes(order.getConfirmScene())));
            }
        } catch (Exception e) {
            logger.error("RemindDispatcherGrabOrderExeCmdError", e);
        }
    }

    private void push(DispatcherGrabOrderDO order, ModifyJntOrderVO modifyJntOrderVO) {
        try {
            DspOrderVO dspOrderVO = queryDspOrderService.queryDspOrder(order);
            dspOrderVO.setDispatcherGrabOrderDO(order);
            TransportGroupVO transportGroupVO = queryTransportGroupService.queryTransportGroup(order.getTransportGroupId());
            if (validator.validate(order, dspOrderVO, transportGroupVO)) {
                pushOrderRemindExeCmd.execute(new PushOrderRemindCommand(dspOrderVO, transportGroupVO, getDispatcherGrabRemindTypes(order.getConfirmScene()), modifyJntOrderVO));
            }
        } catch (Exception e) {
            logger.error("RemindDispatcherGrabOrderExeCmdError", e);
        }
    }

    private List<PushOrderRemindType> getDispatcherGrabRemindTypes(Integer confirmScene) {
        if (Objects.equals(SupplierConfirmSceneEnum.URGENT.getScene(), confirmScene)) {
            return PushOrderRemindType.getUrgentDispatcherGrabRemindTypes();
        }
        if (Objects.equals(SupplierConfirmSceneEnum.ORI_MODIFY.getScene(), confirmScene)) {
            return PushOrderRemindType.getOriModifyDispatcherGrabRemindTypes();
        }
        return Lists.newArrayList();
    }
}
