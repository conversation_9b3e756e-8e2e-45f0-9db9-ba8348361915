package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.CustomerCheckDriversCmd;
import com.ctrip.dcs.application.command.api.CustomerCheckDriversCommand;
import com.ctrip.dcs.domain.dsporder.entity.CustomerCheckDriversResDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckDriversRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerCheckDriversResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTagPair(key = "userOrderId", alias = "userOrderId")
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class CustomerCheckDriversExecutor extends AbstractRpcExecutor<CustomerCheckDriversRequestType, CustomerCheckDriversResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCheckDriversExecutor.class);

    @Autowired
    private CustomerCheckDriversCmd customerCheckDriversCmd;

    @Override
    public CustomerCheckDriversResponseType execute(CustomerCheckDriversRequestType requestType) {
        CustomerCheckDriversResponseType responseType = new CustomerCheckDriversResponseType();
        try {
            CustomerCheckDriversResDTO resDTO = customerCheckDriversCmd.query(buildReq(requestType));
            convert(responseType, resDTO);
            return ServiceResponseUtils.success(responseType);
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("driverCheckList error", e);
            return ServiceResponseUtils.fail(responseType);
        }
    }

    private CustomerCheckDriversCommand buildReq(CustomerCheckDriversRequestType requestType) {
        CustomerCheckDriversCommand command = new CustomerCheckDriversCommand();
        command.setUserOrderId(requestType.getUserOrderId());
        command.setDspOrderId(requestType.getDspOrderId());
        if (null == requestType.getPaginator() || requestType.getPaginator().getPageNo() <= 0 || requestType.getPaginator().getPageSize() <= 0) {
            command.setPageNo(1);
            command.setPageSize(10);
        } else {
            command.setPageNo(requestType.getPaginator().getPageNo());
            command.setPageSize(requestType.getPaginator().getPageSize());
        }

        return command;
    }

    private void convert(CustomerCheckDriversResponseType responseType, CustomerCheckDriversResDTO resDTO) {
        responseType.setPagination(resDTO.getPaginationDTO());
        responseType.setDrvs(resDTO.getDrvs());
        responseType.setDriverFee(resDTO.getDriverFee());
    }

}
