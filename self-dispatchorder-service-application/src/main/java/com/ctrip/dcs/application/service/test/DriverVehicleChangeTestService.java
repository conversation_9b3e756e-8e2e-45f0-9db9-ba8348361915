package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.event.dto.DriverVehicleChangeEvent;
import com.ctrip.dcs.application.event.impl.DriverVehicleChangeEventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("DriverVehicleChangeTestService")
public class DriverVehicleChangeTestService implements ITestDspOrderService{
    @Autowired
    private DriverVehicleChangeEventHandler handler;
    @Override
    public String test(Map<String, String> params) {//已测试
        DriverVehicleChangeEvent event = new DriverVehicleChangeEvent();
        event.setDriverId(Long.valueOf(params.get("driverId")));
        event.setAccountType(params.get("accountType"));
        boolean result = handler.handle(event);
        return String.valueOf(result);
    }
}
