package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class DspOrderRewardStrategyListener {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderRewardStrategyListener.class);

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    @QmqLogTag(tagKeys = {"scheduleId", "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DCS_DSP_ORDER_REWARD_STRATEGY_ACTIVE, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            Long strategyId = message.getLongProperty("strategyId");
            dspOrderRewardStrategyService.updateRewardStrategy(strategyId);
        } catch (Exception e) {
            logger.warn("DspOrderRewardStrategyListenerError", "DspOrderRewardStrategyListenerError", e);
            throw new NeedRetryException("DspOrderRewardStrategyListenerError");
        }
    }
}
