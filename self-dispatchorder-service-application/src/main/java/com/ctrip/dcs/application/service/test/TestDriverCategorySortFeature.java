package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.TransportGroupMode;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.feature.impl.AbstractFeature;
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverCategoryFeature;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class TestDriverCategorySortFeature implements ITestDspOrderService{
    @Autowired
    @Qualifier("sortConfig")
    private ConfigService sortConfig;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private DspContextService dspContextService;
    @Override
    public String test(Map<String, String> params) {
        String category = params.get("driverCategory");
        TransportGroupVO transportGroupVO = new TransportGroupVO();
        transportGroupVO.setTransportGroupMode(TransportGroupMode.REGISTER_DISPATCH);
        DriverVO driverVO = new DriverVO(3452498L);
        if("1".equals(category)){
            driverVO.setCoopMode(4);
            driverVO.setTransportGroups(Arrays.asList(transportGroupVO));
        }else if("1".equals(category)){
            driverVO.setCoopMode(5);
            driverVO.setTransportGroups(Arrays.asList(transportGroupVO));
        }else{
            driverVO.setCoopMode(12);
        }
        DspOrderVO dspOrderVO = queryDspOrderService.queryOrderDetail(params.get("dspOrderId"));
        SortModel sortModel = new SortModel(new DspModelVO(dspOrderVO,driverVO));
        DspContext dspContext = new DspContext(dspContextService);
        SortContext context = new SortContext(dspOrderVO,dspContext,sortConfig);
        AbstractFeature feature = new DriverCategoryFeature();
        List<Value> values = feature.value(Arrays.asList(sortModel),context);
        return LocalJsonUtils.toJson(values);
    }
}
