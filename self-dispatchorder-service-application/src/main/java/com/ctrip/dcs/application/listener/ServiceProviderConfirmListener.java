package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.service.ServiceProviderConfirmedService;
import com.ctrip.dcs.application.service.SpContractInfoService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class ServiceProviderConfirmListener {


    @Autowired
    private ServiceProviderConfirmedService serviceProviderConfirmedService;

    @Autowired
    private SpContractInfoService spContractInfoService;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_SERVICE_PROVIDER_CONFIRM_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        serviceProviderConfirmedService.confirmed(dspOrderId, confirmRecordId);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_SERVICE_PROVIDER_CONFIRM_TOPIC, consumerGroup = "self_order_contract", idempotentChecker = "redisIdempotentChecker")
    public void contractInfoUpdate(Message message) {
        String dspOrderId = message.getStringProperty("dspOrderId");
        spContractInfoService.updateContractInfo(dspOrderId);    }
}
