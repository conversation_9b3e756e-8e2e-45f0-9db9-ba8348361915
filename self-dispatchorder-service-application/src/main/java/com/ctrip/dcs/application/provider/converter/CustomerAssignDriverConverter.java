package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverRequestType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class CustomerAssignDriverConverter {

    public static OperateAssignDriverCommand converter(CustomerAssignDriverRequestType request) {
        Assert.notNull(request);

        OperateAssignDriverCommand cmd = new OperateAssignDriverCommand();
        BeanUtils.copyProperties(request, cmd);
        cmd.setLockPrefixKey("CS_ASSIGN_DRIVER");
        cmd.setEvent(OrderStatusEvent.OFFLINE_ASSIGN);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_BOSS);
        if (cmd.getExtraFee() == null) {
            cmd.setExtraFee(BigDecimal.ZERO);
        }
        return cmd;
    }

    public static OperateAssignDriverCommand converter(VbkAssignDriverRequestType request) {
        Assert.notNull(request);

        OperateAssignDriverCommand cmd = new OperateAssignDriverCommand();
        BeanUtils.copyProperties(request, cmd);
        cmd.setLockPrefixKey("VBK_ASSIGN_DRIVER");
        cmd.setEvent(OrderStatusEvent.VBK_ASSIGN);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        if (cmd.getExtraFee() == null) {
            cmd.setExtraFee(BigDecimal.ZERO);
        }
        return cmd;
    }

    public static OperateAssignDriverCommand converter(VbkChangeDriverRequestType request) {
        Assert.notNull(request);

        OperateAssignDriverCommand cmd = new OperateAssignDriverCommand();
        BeanUtils.copyProperties(request, cmd);
        cmd.setLockPrefixKey("VBK_UPDATE_TAKEN_DRIVER");
        cmd.setEvent(OrderStatusEvent.VBK_UPDATE_DRIVER);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        if (StringUtils.isEmpty(request.getChangeReason())) {
            cmd.setChangeReason("904");
        }
        if (cmd.getExtraFee() == null) {
            cmd.setExtraFee(BigDecimal.ZERO);
        }
        return cmd;
    }

    public static OperateAssignDriverCommand converter(VbkBindCarAndTakenRequestType request) {
        Assert.notNull(request);
        OperateAssignDriverCommand cmd = new OperateAssignDriverCommand();
        BeanUtils.copyProperties(request, cmd);
        cmd.setLockPrefixKey("VBK_BIND_CAR_DRIVER");
        cmd.setEvent(OrderStatusEvent.VBK_BIND_CAR);
        cmd.setAssignRole(SysConstants.AssignRole.SYS_ROLE_SUPPLIER);
        if (cmd.getExtraFee() == null) {
            cmd.setExtraFee(BigDecimal.ZERO);
        }
        return cmd;
    }
}
