package com.ctrip.dcs.application.listener.grab;

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus;
import com.ctrip.dcs.domain.common.enums.VBKGrabOperationTypeEnum;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.schedule.event.VBKGrabTaskOperateEvent;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;


@Component
public class VBKGrabTaskTaskFinishListener extends MqListener{

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabTaskTaskFinishListener.class);

    @Autowired
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;
    @Autowired
    protected MessageProviderService messageProducer;

    @QmqConsumer(prefix = EventConstants.VBK_GRAB_TASK_FINISH_BY_SCHEDULER_TOPIC, consumerGroup = "vbk_grab" + EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    public void consumeMessage(Message message) {
        try {
            int pageSize = message.getIntProperty("pageSize");
            logger.info("VBKGrabTaskTaskFinishListener_pageSize", JsonUtils.toJson(pageSize));
            Integer num = vbkDriverGrabTaskRepository.countByGrabTaskStatus(GrabTaskStatus.IN_PROGRESS.getCode());
            int totalPage = new BigDecimal(num).divide(new BigDecimal(pageSize), 0, RoundingMode.CEILING).intValue();
            logger.info("VBKGrabTaskTaskFinishListener_totalPage", JsonUtils.toJson(totalPage));
            int currentPageNum = totalPage;

            while (currentPageNum > 0){
                logger.info("VBKGrabTaskTaskFinishListener_currentPageNum", JsonUtils.toJson(currentPageNum));
                List<VBKDriverGrabTaskDO> list =  vbkDriverGrabTaskRepository.queryByGrabStatusPage(GrabTaskStatus.IN_PROGRESS.getCode(), pageSize, currentPageNum);
                logger.info("VBKGrabTaskTaskFinishListener_queryByGrabStatusPage", JsonUtils.toJson(list));
                list.forEach(this::finishGrabTask);
                currentPageNum-- ;
            }
        }catch (Exception ex){
            logger.error("VBKGrabTaskTaskFinishListener_consumeMessage", ex);
            MetricsUtil.recordValue(MetricsConstants.VBK_GRAB_TASK_FINISH_ERROR_COUNT);
        }
    }



    private void finishGrabTask(VBKDriverGrabTaskDO x) {
        logger.info("VBKGrabTaskTaskFinishListener_finishGrabTask_VBKDriverGrabTaskDO", JsonUtils.toJson(x));
        int orderNum = vbkDriverGrabOrderRepository.countByTaskIdAndStatus(x.getVbkGrabTaskId(), GrabTaskStatus.IN_PROGRESS.getCode());
        logger.info("VBKGrabTaskTaskFinishListener_finishGrabTask_orderNum", JsonUtils.toJson(orderNum));
        if(orderNum == 0){
            int finishNum = vbkDriverGrabTaskRepository.finishTaskBatch(Lists.newArrayList(x.getVbkGrabTaskId()), GrabTaskStatus.FINISH.getCode());
            logger.info("VBKGrabTaskTaskFinishListener_finishGrabTask_finishNum", JsonUtils.toJson(finishNum));
            sendVBKGrabTaskOperateEvent(x.getVbkGrabTaskId());
        }
    }

    private void sendVBKGrabTaskOperateEvent(String vbkGrabTaskId) {
        try {
            VBKGrabTaskOperateDTO vbkGrabTaskOperateDTO = new VBKGrabTaskOperateDTO();
            vbkGrabTaskOperateDTO.setTaskId(vbkGrabTaskId);
            vbkGrabTaskOperateDTO.setOperatorName("system");
            vbkGrabTaskOperateDTO.setOperatorType(VBKGrabOperationTypeEnum.OperationTypeEnum.FINISH.getCode());
            vbkGrabTaskOperateDTO.setOperatorDesc(VBKGrabOperationTypeEnum.OperationTypeEnum.FINISH.getDes());
            vbkGrabTaskOperateDTO.setCurrentDateStr(DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT));
            String jsonStr = JsonUtils.toJson(vbkGrabTaskOperateDTO);
            logger.info("VBKGrabTaskTaskFinishListener_sendVBKGrabTaskOperateEvent", jsonStr);
            VBKGrabTaskOperateEvent shutdownScheduleEvent = new VBKGrabTaskOperateEvent(jsonStr);
            messageProducer.send(shutdownScheduleEvent);
        } catch (Exception ex) {
            logger.error("VBKGrabTaskTaskFinishListener_sendVBKGrabTaskOperateEvent", ex);
        }
    }
}
