package com.ctrip.dcs.application.processor;

import com.ctrip.dcs.application.command.CreateScheduleExeCmd;
import com.ctrip.dcs.application.command.ReDispatchConfirmExeCmd;
import com.ctrip.dcs.application.command.api.CreateScheduleCommand;
import com.ctrip.dcs.application.command.api.ReDispatchConfirmCommand;
import com.ctrip.dcs.domain.common.enums.CancelReasonIdEnum;
import com.ctrip.dcs.domain.common.enums.CancelRoleEnum;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.service.QueryVehicleService;
import com.ctrip.dcs.domain.common.value.DriverAndCarConfirmVO;
import com.ctrip.dcs.domain.common.value.DriverOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.ServiceProviderVO;
import com.ctrip.dcs.domain.common.value.SupplierVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VehicleVO;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.ConfirmType;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.helper.Cats;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class OriginalOrderModifyProcessor {

    @Resource
    ReDispatchConfirmExeCmd reDispatchConfirmService;
    @Resource
    CreateScheduleExeCmd createScheduleService;
    @Resource
    QueryDspOrderService dispatchOrderService;
    @Resource
    DspContextService dispatchContextService;
    @Resource
    SubSkuRepository subSkuRepository;
    @Resource
    DspOrderRepository dispatchOrderRepository;
    @Resource
    RecommendService recommendService;
    @Resource
    CheckService checkService;
    @Resource
    ConfirmDspOrderService confirmDspOrderService;
    @Resource
    DriverOrderFactory driverOrderFactory;
    @Resource
    DriverOrderGateway driverOrderGateway;
    @Resource
    QueryDriverService queryDriverService;
    @Resource
    QueryVehicleService queryVehicleService;
    @Resource
    QueryTransportGroupService queryTransportGroupService;
    @Resource
    MessageProviderService messageProviderService;
    @Resource
    ScheduleRepository scheduleRepository;
    @Resource
    BusinessTemplateInfoConfig businessTemplateInfoConfig;

    public void apply(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "Apply", () -> doApply(newDispatchOrderId, oldDispatchOrderId, needMerchantApproveModify));
    }

    protected void doApply(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        // 尝试原司机/原供应商接单
        tryTaken(newDispatchOrderId, oldDispatchOrderId, needMerchantApproveModify);
        // 取消原司机单
        cancelOldDriverOrder(oldDispatchOrderId);
        // 判断是否需要兜底时间轴
        if (checkShouldFallback(newDispatchOrderId)) {
            // 创建兜底时间轴
            tryFallback(newDispatchOrderId, oldDispatchOrderId, needMerchantApproveModify);
        }
    }

    protected void tryTaken(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "TryTaken", () -> doTryTaken(newDispatchOrderId, oldDispatchOrderId, needMerchantApproveModify));
    }
    
    protected void doTryTaken(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        // 判断是否需要商家确认
        if (needMerchantApproveModify) {
            // 使用原车头尝试应单
            oldTransportGroupTakenNewOrder(newDispatchOrderId, oldDispatchOrderId);
        } else {
            // 判断新单是否是纯人工调度
            if (checkNewOrderIsOnlyManualDispatch(newDispatchOrderId)) {
                // 检查原车头是否可接新单
                if (checkOldTransportGroupCanTakenNewOrder(oldDispatchOrderId, newDispatchOrderId)) {
                    // 使用原车头尝试应单
                    oldTransportGroupTakenNewOrder(newDispatchOrderId, oldDispatchOrderId);
                }
                // 判断原单是否为系统指派司机
            } else if (checkOldOrderIsSystemAssignDriver(oldDispatchOrderId)) {
                // 检查原司机是否可接新单
                if (checkOldDriverCanTakenNewOrder(oldDispatchOrderId, newDispatchOrderId)) {
                    // 使用原司机尝试接单
                    oldDriverTakenNewOrder(oldDispatchOrderId, newDispatchOrderId);
                }
            }
        }
    }

    protected void tryFallback(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "TryFallback", () -> doTryFallback(newDispatchOrderId, oldDispatchOrderId, needMerchantApproveModify));
    }

    protected void doTryFallback(String newDispatchOrderId, String oldDispatchOrderId, boolean needMerchantApproveModify) {
        // 创建新的调度任务
        createNewOrderSchedule(newDispatchOrderId);
    }

    // 检查是否应该兜底
    protected boolean checkShouldFallback(String newDispatchOrderId) {
        return Cats.runOrDefault("OriginalOrderModifyProcessor", "CheckShouldFallback", () -> doCheckShouldFallback(newDispatchOrderId), true);
    }

    protected boolean doCheckShouldFallback(String newDispatchOrderId) {
        return checkOrderNotConfirmed(newDispatchOrderId) && checkScheduleIsEmpty(newDispatchOrderId);
    }

    // 检查调度是否为空
    protected boolean checkScheduleIsEmpty(String dispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckScheduleIsEmpty", () -> doCheckScheduleIsEmpty(dispatchOrderId));
    }

    protected boolean doCheckScheduleIsEmpty(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(scheduleRepository::query)
                .map(CollectionUtils::isEmpty)
                .orElse(true);
    }

    // 检查订单是否未应单
    protected boolean checkOrderNotConfirmed(String dispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckOrderNotConfirmed", () -> doCheckOrderNotConfirmed(dispatchOrderId));
    }
    
    protected boolean doCheckOrderNotConfirmed(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getOrderStatus)
                .map(OrderStatusEnum::isDispatching)
                .orElse(true);
    }

    // 原车头接单
    protected void oldTransportGroupTakenNewOrder(String newDispatchOrderId, String oldDispatchOrderId) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "OldTransportGroupTakenNewOrder", () -> doOldTransportGroupTakenNewOrder(newDispatchOrderId, oldDispatchOrderId));
    }
    
    protected void doOldTransportGroupTakenNewOrder(String newDispatchOrderId, String oldDispatchOrderId) {
        reDispatchConfirmService.execute(new ReDispatchConfirmCommand(newDispatchOrderId, oldDispatchOrderId));
    }

    // 判断是否为纯人工调度（所有运力组都是人工调度）
    protected boolean checkNewOrderIsOnlyManualDispatch(String newDispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckNewOrderIsOnlyManualDispatch", () -> doCheckNewOrderIsOnlyManualDispatch(newDispatchOrderId));
    }

    protected boolean doCheckNewOrderIsOnlyManualDispatch(String newDispatchOrderId) {
        return Optional.ofNullable(newDispatchOrderId)
                .map(dispatchOrderService::query)
                .map(dispatchContextService::queryTransports)
                .filter(CollectionUtils::isNotEmpty)
                .orElseThrow(() -> new IllegalStateException("transport groups empty"))
                .stream()
                .filter(Objects::nonNull)
                .allMatch(TransportGroupVO::isManualScheduling);
    }

    // 检查原单的车头是否可以接起新单
    protected boolean checkOldTransportGroupCanTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckOldTransportGroupCanTakenNewOrder", () -> doCheckOldTransportGroupCanTakenNewOrder(oldDispatchOrderId, newDispatchOrderId));
    }
    
    protected boolean doCheckOldTransportGroupCanTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        // 查询新派发单
        DspOrderVO newDispatchOrder = dispatchOrderService.queryOrderDetail(newDispatchOrderId);
        // 原单的子产品
        SubSkuVO subSku = getLeastDispatcherConfirmSubSku(oldDispatchOrderId);
        // 原单的运力组
        Long transportGroupId = getLatestDispatcherConfirmTransportGroupId(oldDispatchOrderId);
        // 执行运力推荐
        List<SortModel> recommends = recommendService.recommend(newDispatchOrder, subSku, DuidVO.of(newDispatchOrderId, subSku.getSubSkuId(), subSku.getDspType().getCode(), subSku.getTakenType().getCode()));
        // 判断原车头是否通过校验
        return Optional.ofNullable(recommends)
                .orElse(Collections.emptyList())
                .stream()
                .map(SortModel::getModel)
                .map(DspModelVO::getTransportGroup)
                .map(TransportGroupVO::getTransportGroupId)
                .anyMatch(transportGroupId::equals);
    }

    // 清除标记
    protected void clearOldOrderSpecialCancelFlag(String oldDispatchOrderId) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "ClearOldOrderSpecialCancelFlag", () -> doClearOldOrderSpecialCancelFlag(oldDispatchOrderId));
    }

    protected void doClearOldOrderSpecialCancelFlag(String oldDispatchOrderId) {
        dispatchOrderRepository.removeSpecialCancelScene(oldDispatchOrderId);
    }

    // 创建时间轴
    protected void createNewOrderSchedule(String newDispatchOrderId) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "CreateNewOrderSchedule", () -> doCreateNewOrderSchedule(newDispatchOrderId));
    }

    protected void doCreateNewOrderSchedule(String newDispatchOrderId) {
        createScheduleService.execute(new CreateScheduleCommand(newDispatchOrderId, ScheduleEventType.DSP_ORDER_BOOK));
    }

    // 取消司机单
    protected void cancelOldDriverOrder(String oldDispatchOrderId) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "CancelOldDriverOrder", () -> doCancelOldDriverOrder(oldDispatchOrderId));
    }

    protected void doCancelOldDriverOrder(String oldDispatchOrderId) {
        String reason = businessTemplateInfoConfig.getCancelReasonMap().getOrDefault(String.valueOf(CancelReasonIdEnum.ORI_MODIFY_CANCEl.getCode()), CancelReasonIdEnum.ORI_MODIFY_CANCEl.getDesc());
        driverOrderGateway.cancel(oldDispatchOrderId, null, null, CancelRoleEnum.USERCANCEL.getCode(), CancelReasonIdEnum.ORI_MODIFY_CANCEl.getCode(), reason, Boolean.TRUE, false);
    }

    // 判断是否为系统指派司机
    protected boolean checkOldOrderIsSystemAssignDriver(String oldDispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckOldOrderIsSystemAssignDriver", () -> doCheckOldOrderIsSystemAssignDriver(oldDispatchOrderId));
    }
    
    protected boolean doCheckOldOrderIsSystemAssignDriver(String oldDispatchOrderId) {
        return Optional.ofNullable(oldDispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .filter(this::isSystemAssign)
                .filter(this::isDriverConfirmed)
                .isPresent();
    }

    // 检查原单的司机是否可以接起新单
    protected boolean checkOldDriverCanTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        return Cats.runOrThrow("OriginalOrderModifyProcessor", "CheckOldDriverCanTakenNewOrder", () -> doCheckOldDriverCanTakenNewOrder(oldDispatchOrderId, newDispatchOrderId));
    }
    
    protected boolean doCheckOldDriverCanTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        // 新派发单
        DspOrderVO newDispatchOrder = dispatchOrderService.queryOrderDetail(newDispatchOrderId);
        // 原单的子产品
        SubSkuVO subSku = getLeastConfirmSubSku(oldDispatchOrderId);
        // 原接单司机
        Long driverId = getLatestConfirmDriverId(oldDispatchOrderId);
        // 执行运力推荐
        List<SortModel> recommends = recommendService.recommend(newDispatchOrder, subSku, DuidVO.of(newDispatchOrderId, subSku.getSubSkuId(), subSku.getDspType().getCode(), subSku.getTakenType().getCode()));
        // 判断原司机是否通过校验
        return Optional.ofNullable(recommends)
                .orElse(Collections.emptyList())
                .stream()
                .map(SortModel::getModel)
                .map(DspModelVO::getDriver)
                .map(DriverVO::getDriverId)
                .anyMatch(driverId::equals);
    }

    // 原司机接单
    protected void oldDriverTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        Cats.runOrCatch("OriginalOrderModifyProcessor", "OldDriverTakenNewOrder", () -> doOldDriverTakenNewOrder(oldDispatchOrderId, newDispatchOrderId));
    }

    protected Integer getNewDispatchOrderModifyVersion(String newDispatchOrderId) {
        return Optional.ofNullable(newDispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getModifyVersion)
                .orElse(null);
    }

    protected void doOldDriverTakenNewOrder(String oldDispatchOrderId, String newDispatchOrderId) {
        DspOrderVO newDispatchOrder = dispatchOrderService.queryOrderDetail(newDispatchOrderId);
        // 这个值必须从DB读 因为RPC不返回该字段
        Integer modifyVersion = getNewDispatchOrderModifyVersion(newDispatchOrderId);
        SubSkuVO oldSubSku = getLeastConfirmSubSku(oldDispatchOrderId);
        Long oldDriverId = getLatestConfirmDriverId(oldDispatchOrderId);
        String oldDriverOrderId = getLatestConfirmDriverOrderId(oldDispatchOrderId);
        Long oldSupplierId = getLatestConfirmSupplierId(oldDispatchOrderId);
        Long transportGroupId = getLatestConfirmTransportGroupId(oldDispatchOrderId);
        ParentCategoryEnum oldCategory = getProductCategory(oldDispatchOrderId);
        DriverVO oldDriver = queryDriverService.queryDriver(oldDriverId, oldCategory, oldSupplierId);
        TransportGroupVO oldTransportGroup = queryTransportGroupService.queryTransportGroup(transportGroupId);
        VehicleVO oldVehicle = getDriverVehicle(oldDriver, oldCategory);
        DspModelVO model = new DspModelVO(newDispatchOrder, oldDriver, oldTransportGroup);
        DuidVO newDuid = DuidVO.of(newDispatchOrderId, oldSubSku.getSubSkuId(), oldSubSku.getDspType().getCode(), oldSubSku.getTakenType().getCode());
        // 创建新司机单
        DriverOrderVO driverOrder = driverOrderFactory.createForModify(model,oldSubSku, oldDriverId, oldDriverOrderId, modifyVersion);
        // 新单创建失败
        if (Objects.isNull(driverOrder)) {
            throw new IllegalStateException("driver order create failed");
        } else {
            // 执行接单检查
            CheckModel check = checkService.check(new TakenCheckCommand(newDispatchOrder, oldSubSku, oldDriver, oldTransportGroup, newDuid));
            // 确保检查通过
            if (check.isPass()) {
                try {
                    // 构造应单记录
                    DriverAndCarConfirmVO confirm = DriverAndCarConfirmVO.builder()
                            .dspOrder(newDispatchOrder)
                            .serviceProvider(new ServiceProviderVO(newDispatchOrder.getSpId()))
                            .supplier(new SupplierVO(oldSupplierId))
                            .transportGroup(oldTransportGroup)
                            .driver(oldDriver)
                            .vehicle(oldVehicle)
                            .duid(newDuid)
                            .driverOrderId(driverOrder.getDriverOrderId())
                            .event(OrderStatusEvent.SYSTEM_ASSIGN)
                            .operator(OperatorVO.systemOperator())
                            .build();
                    // 尝试执行应单
                    confirmDspOrderService.confirm(confirm);
                } catch (Exception e) {
                    // 取消新司机单
                    messageProviderService.send(new DriverOrderConfirmFailEvent(driverOrder.getDriverOrderId(), driverOrder.getDspOrderId(), driverOrder.getDriverId()));
                }
            }
        }
    }

    /*************************************************以下为取值方法*************************************************/

    protected Long getLatestConfirmDriverId(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDriverInfo)
                .map(DspOrderConfirmRecordVO.DriverRecord::getDriverId)
                .orElseThrow(() -> new IllegalStateException("driver id not found"));
    }

    protected String getLatestConfirmDriverOrderId(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDriverOrderId)
                .orElseThrow(() -> new IllegalStateException("driver order id not found"));
    }

    protected Long getLatestConfirmSupplierId(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .map(DspOrderConfirmRecordVO::getSupplierInfo)
                .map(DspOrderConfirmRecordVO.SupplierRecord::getSupplierId)
                .orElseThrow(() -> new IllegalStateException("supplier id not found"));
    }

    protected Long getLatestConfirmTransportGroupId(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDriverInfo)
                .map(DspOrderConfirmRecordVO.DriverRecord::getTransportGroupId)
                .map(queryTransportGroupService::queryTransportGroup)
                .map(TransportGroupVO::getTransportGroupId)
                .orElseThrow(() -> new IllegalStateException("transport group id not found"));
    }

    protected ParentCategoryEnum getProductCategory(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getCategoryCode)
                .map(CategoryCodeEnum::getParentType)
                .map(ParentCategoryEnum::identify)
                .orElseThrow(() -> new IllegalStateException("product category not found"));
    }

    protected VehicleVO getDriverVehicle(DriverVO driver, ParentCategoryEnum parentCategoryCodeEnum) {
        return queryVehicleService.query(driver.getCar().getCarId(), parentCategoryCodeEnum);
    }

    protected SubSkuVO getLeastDispatcherConfirmSubSku(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::queryLeastDispatcherConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDuid)
                .map(DuidVO::of)
                .map(DuidVO::getSubSkuId)
                .map(subSkuRepository::find)
                .orElseThrow(() -> new IllegalStateException("sub sku not found"));
    }

    protected SubSkuVO getLeastConfirmSubSku(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::query)
                .map(DspOrderVO::getConfirmRecordId)
                .map(dispatchOrderService::queryDspOrderConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDuid)
                .map(DuidVO::of)
                .map(DuidVO::getSubSkuId)
                .map(subSkuRepository::find)
                .orElseThrow(() -> new IllegalStateException("sub sku not found"));
    }

    protected Long getLatestDispatcherConfirmTransportGroupId(String dispatchOrderId) {
        return Optional.ofNullable(dispatchOrderId)
                .map(dispatchOrderService::queryLeastDispatcherConfirmRecord)
                .map(DspOrderConfirmRecordVO::getDriverInfo)
                .map(DspOrderConfirmRecordVO.DriverRecord::getTransportGroupId)
                .orElseThrow(() -> new IllegalStateException("transport group id not found"));
    }

    /*************************************************以下为辅助方法*************************************************/

    // 判断是否为司机应单
    protected boolean isDriverConfirmed(DspOrderConfirmRecordVO confirm) {
        return Optional.ofNullable(confirm)
                .map(DspOrderConfirmRecordVO::getConfirmType)
                .map(ConfirmType::valuesOf)
                .map(ConfirmType.DRIVER_CAR_CONFIRMED::equals)
                .orElse(false);
    }

    // 判断是否为司机应单
    protected boolean isSystemAssign(DspOrderConfirmRecordVO confirm) {
        return Optional.ofNullable(confirm)
                .map(DspOrderConfirmRecordVO::getTakenType)
                .map(TakenType::of)
                .map(TakenType.POLICY_AUTO_ASSIGN::equals)
                .orElse(false);
    }

}