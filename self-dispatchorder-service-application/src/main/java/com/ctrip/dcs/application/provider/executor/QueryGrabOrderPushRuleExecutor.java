package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO;
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleParamDTO;
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleResultDTO;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushPriorityRuleSoaDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRewardsRuleSoaDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class QueryGrabOrderPushRuleExecutor extends AbstractRpcExecutor<QueryGrabOrderPushRuleRequestType, QueryGrabOrderPushRuleResponseType> implements Validator<QueryGrabOrderPushRuleRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryGrabOrderPushRuleExecutor.class);
    @Autowired
    private IGrabOrderPushRuleService grabOrderPushRuleService;
    @Override
    public QueryGrabOrderPushRuleResponseType execute(QueryGrabOrderPushRuleRequestType requestType) {
        try{
            QueryGrabOrderPushRuleResultDTO resultDTO = grabOrderPushRuleService.query(convertToParamDto(requestType));
            return ServiceResponseUtils.success(convertToResponseType(resultDTO));
        }catch (Exception e){
            logger.error("QueryGrabOrderPushRuleExecutorExp","QueryGrabOrderPushRuleExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new QueryGrabOrderPushRuleResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryGrabOrderPushRuleRequestType> validator) {
        validator.ruleFor("paginator").notNull();
        validator.ruleFor("paginator.pageNo").notNull().greaterThan(0);
        validator.ruleFor("paginator.pageSize").notNull().greaterThan(0);

    }

    /**
     * 封装查询参数
     * @param requestType
     * @return
     */
    public QueryGrabOrderPushRuleParamDTO convertToParamDto(QueryGrabOrderPushRuleRequestType requestType){
        QueryGrabOrderPushRuleParamDTO paramDTO = new QueryGrabOrderPushRuleParamDTO();
        paramDTO.setCityId(requestType.getCityId());
        paramDTO.setSupplierId(requestType.getSupplierId());
        paramDTO.setVehicleGroupId(requestType.getVehicleGroupId());
        Integer pageNo = requestType.getPaginator().getPageNo();
        Integer pageSize = requestType.getPaginator().getPageSize();
        paramDTO.setPageNo(pageNo);
        paramDTO.setPageSize(pageSize);
        return paramDTO;
    }

    /**
     * 封装参数
     * @param resultDTO
     * @return
     */
    public QueryGrabOrderPushRuleResponseType convertToResponseType(QueryGrabOrderPushRuleResultDTO resultDTO){
        QueryGrabOrderPushRuleResponseType responseType = new QueryGrabOrderPushRuleResponseType();
        //分页数据
        PaginationDTO paginationDTO = new PaginationDTO();
        paginationDTO.setPageNo(resultDTO.getPageNo());
        paginationDTO.setPageSize(resultDTO.getPageSize());
        paginationDTO.setTotalPages(resultDTO.getTotalPages());
        paginationDTO.setTotalSize(resultDTO.getTotalSize());
        responseType.setPaginationDTO(paginationDTO);
        //规则数据
        List<GrabOrderPushRuleSoaDTO> rules = new ArrayList<>();
        if(!LocalCollectionUtils.isEmpty(resultDTO.getRuleDTOList())){
            for (GrabOrderPushRuleDTO ruleDTO : resultDTO.getRuleDTOList()) {
                rules.add(convertToSoaDto(ruleDTO));
            }
        }
        responseType.setRules(rules);
        return responseType;
    }

    /**
     * dto -> soaDto
     * @param resultDTO
     * @return
     */
    public GrabOrderPushRuleSoaDTO convertToSoaDto(GrabOrderPushRuleDTO resultDTO){
        GrabOrderPushRuleSoaDTO soaDTO = new GrabOrderPushRuleSoaDTO();
        soaDTO.setId(resultDTO.getId());
        soaDTO.setCityId(resultDTO.getCityId());
        soaDTO.setRuleType(resultDTO.getRuleType());
        soaDTO.setCategoryCode(resultDTO.getCategoryCode());
        soaDTO.setEndBookTime(resultDTO.getEndBookTime());
        soaDTO.setStartBookTime(resultDTO.getStartBookTime());
        soaDTO.setBookTime(resultDTO.getBookTime());
        soaDTO.setFixedPushTime(resultDTO.getFixedPushTime());
        soaDTO.setImmediatePushTime(resultDTO.getImmediatePushTime());
        soaDTO.setSupplierId(resultDTO.getSupplierId());
        soaDTO.setSupplierName(resultDTO.getSupplierName());
        soaDTO.setVehicleGroupIdList(resultDTO.getVehicleGroupIdList());
        if (StringUtils.isNotBlank(resultDTO.getPriority())) {
            soaDTO.setPriority(JacksonSerializer.INSTANCE().deserialize(resultDTO.getPriority(), GrabOrderPushPriorityRuleSoaDTO.class));
        }
        if (StringUtils.isNotBlank(resultDTO.getRewards())) {
            soaDTO.setRewards(JacksonSerializer.INSTANCE().deserialize(resultDTO.getRewards(), GrabOrderPushRewardsRuleSoaDTO.class));
        }
        return soaDTO;
    }
}
