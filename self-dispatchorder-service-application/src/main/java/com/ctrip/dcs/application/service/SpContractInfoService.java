package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum;
import com.ctrip.dcs.domain.common.enums.ConnectModeEnum;
import com.ctrip.dcs.domain.common.enums.SalesModeEnum;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.NeedRetryException;

import java.util.Map;

@Component
public class SpContractInfoService {
    private static final Logger logger = LoggerFactory.getLogger(SpContractInfoService.class);

    @Autowired
    DspOrderRepository dspOrderRepository;

    @Autowired
    ScmMerchantServiceGateway scmMerchantServiceGateway;

    @Autowired
    DspOrderDetailRepository dspOrderDetailRepository;

    public void updateContractInfo(String dspOrderId){
        DspOrderDO dspOrderDO = dspOrderRepository.find(dspOrderId);
        if (dspOrderDO == null || dspOrderDO.getSupplierId() == null) {
            logger.info("updateContractInfo", String.format("dspOrderId is null or supplierId is null,dspOrderId=%s", dspOrderId));
            return;
        }

        // 查询派发详情表信息
        DspOrderDetailDO dspOrderDetailDO = dspOrderDetailRepository.find(dspOrderId);
        // 分流标识：1分流 0不分流
        if (dspOrderDetailDO != null) {
            Map<String, Object> extendInfoMap = dspOrderDetailDO.getExtendInfoMap();
            if (MapUtils.isNotEmpty(extendInfoMap) && extendInfoMap.containsKey(SysConstants.Order.SHUNT_FLAG) && extendInfoMap.get(SysConstants.Order.SHUNT_FLAG) != null && (Integer) extendInfoMap.get(SysConstants.Order.SHUNT_FLAG) == 1) {
                logger.info("updateContractInfo_shunt_" + dspOrderId, "shunt order, update contractInfo");
                this.queryContractAndUpdateDBAndSync(dspOrderDO, dspOrderId);
                return;
            }
        }

        // 自营 + 代理  更新，其他不做更新
        if (ConnectModeEnum.isVbk(dspOrderDO.getConnectMode()) && SalesModeEnum.isProxy(dspOrderDO.getSalesMode()) && BizAreaTypeEnum.IGT.getCtripCode().equals(dspOrderDO.getBizAreaType())) {
            logger.info("updateContractInfo", String.format("updateContractInfo,supplierId=%s,areaType=%s,connectMode=%s,dspOrderId=%s,orderStatus=%s", dspOrderDO.getSupplierId(), dspOrderDO.getBizAreaType(), dspOrderDO.getConnectMode(), dspOrderId,dspOrderDO.getOrderStatus()));
            this.queryContractAndUpdateDBAndSync(dspOrderDO, dspOrderId);
        }
    }

    /**
     * 查询合同，更新DB
     *
     * @param dspOrderDO
     * @param dspOrderId
     */
    public void queryContractAndUpdateDBAndSync(DspOrderDO dspOrderDO, String dspOrderId) {
        // 通过服务商 + 共供应商 + 城市 查询合同信息
        String contractJson = scmMerchantServiceGateway.queryContractByServiceProviderId(dspOrderDO.getSpId(), dspOrderDO.getCityId(), dspOrderDO.getSupplierId());
        if (StringUtils.isEmpty(contractJson)) {
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "updateContractInfo failed,retry after one minute");
        }

        DspOrderDetailDO dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setDspOrderId(dspOrderId);
        dspOrderDetailDO.setSpContractInfo(contractJson);
        dspOrderDetailRepository.updateContractInfo(dspOrderDetailDO);
    }

}
