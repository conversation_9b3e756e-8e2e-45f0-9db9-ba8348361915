package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand;
import com.ctrip.dcs.application.command.validator.SubmitGrabValidator;
import com.ctrip.dcs.application.command.validator.ValidateException;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.IdempotentCheckService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DriverUdlVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.OldDuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.event.GrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.event.GuideGrabOrderFailEvent;
import com.ctrip.dcs.domain.schedule.event.SelectGrabOrderEvent;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 播报抢单
 * <AUTHOR>
 */
@Component
public class SubmitBroadcastGrabExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(SubmitBroadcastGrabExeCmd.class);

    private static final long DEFAULT_SELECT_SECONDS = 300L;

    @Autowired
    private MessageProviderService messageProvider;

    @Autowired
    private IdempotentCheckService idempotentCheckService;

    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private SubmitBroadcastExeCmd submitBroadcastExeCmd;

    @Autowired
    private SubmitGrabCentreExeCmd submitGrabCentreExeCmd;
    @Resource
    QueryDriverService queryDriverService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;

    public void execute(SubmitGrabOrderCommand command) {
        Map<String, String> tag = Maps.newHashMap();
        tag.put("dspOrderId", command.getOrderId());
        tag.put("driverId", command.getDriverId().toString());
        logger.info("SubmitBroadcastGrabExeCmdInfo", JsonUtils.toJson(command), tag);
        GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(command.getOrderId());
        logger.info("SubmitBroadcastGrabExeCmdInfo", JsonUtils.toJson(snapshot), tag);
        if (snapshot == null || (!isGrayscaleCity(snapshot.getCityId()) && Objects.equals(ParentCategoryEnum.JNT.getCode(), command.getCategoryCode()))) {
            // 非灰度城市，老流程
            if (GrabOrderType.BROADCAST.equals(command.getType())) {
                logger.info("SubmitBroadcastGrabExeCmdInfo", "broadcast type. dspOrderId: {}", command.getOrderId());
                // 播报抢单
                submitBroadcastExeCmd.execute(command);
                return;
            }
            if (GrabOrderType.GRAB_CENTRE.equals(command.getType())) {
                logger.info("SubmitBroadcastGrabExeCmdInfo", "grab centre type. dspOrderId: {}", command.getOrderId());
                // 抢单大厅抢单
                submitGrabCentreExeCmd.execute(command);
                return;
            }
        }
        logger.info("SubmitBroadcastGrabExeCmdInfo", "broadcast grab type. dspOrderId: {}", command.getOrderId());
        execute(command, snapshot);
    }

    public void execute(SubmitGrabOrderCommand command, GrabDspOrderSnapshotDO snapshot) {
        List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(command.getOrderId(), Lists.newArrayList(command.getDriverId()));
        logger.info("SubmitBroadcastGrabExeCmdInfo", JsonUtils.toJson(indexes));
        try {
            if (!validate(snapshot, indexes)) {
                sendGrabFailEvent(command, snapshot);
                return;
            }
            GrabDspOrderDriverIndexDO index = indexes.getFirst();
            // 使用提交的duid，兼容逻辑：若没有，则使用index上的
            String submitDuid = StringUtils.isNotBlank(command.getDuid()) ? command.getDuid() : snapshot.getDuid();
            index.submit(submitDuid);
            grabDspOrderDriverIndexRepository.updateSubmitDuid(indexes);
            logger.info("GrabDspOrderDriverIndexInfo_" + snapshot.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(index));
            DuidVO duid = DuidVO.of(submitDuid);
            if (isSelect(index.getDspOrderId(), duid.getSubSkuId())) {
                // 发送轮选消息
                messageProvider.send(new SelectGrabOrderEvent(index.getDspOrderId(), duid.getSubSkuId(), snapshot.getTipsDelaySecond() * 1000));
            }
        } catch (Exception e) {
            logger.error(e);
            // 异常，需要发送抢单失败消息
            sendGrabFailEvent(command, snapshot);
        }
    }

    public boolean isGrayscaleCity(Long cityId) {
        String cityIdStr = broadcastGrabConfig.getString("new_broadcast_grab_city", "");
        List<String> cityIds = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(cityIdStr);
        return cityIds.contains("all") || cityIds.contains(ObjectUtils.defaultIfNull(cityId, 0).toString());
    }

    public boolean validate(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
        if (Objects.isNull(snapshot) || CollectionUtils.isEmpty(indexes)) {
            return false;
        }
        if (!Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.GRAB)) {
            // 抢单状态不正确
            MetricsUtil.recordValue(MetricsConstants.SUBMIT_GRAB_ORDER_INDEX_VALID_ERROR);
            return false;
        }
        GrabDspOrderDriverIndexDO index = indexes.getFirst();
        Integer valid = Optional.ofNullable(index).map(GrabDspOrderDriverIndexDO::getIsValid).orElse(YesOrNo.NO.getCode());
        return YesOrNo.isYes(valid);
    }

    public void sendGrabFailEvent(SubmitGrabOrderCommand command, GrabDspOrderSnapshotDO snapshot) {
        // 抢单结果映射
        Map<String /*code*/, String /*desc*/> mapping = broadcastGrabConfig.getMap(ConfigKey.BROADCAST_GRAB_RESULT_KEY);
        // 发送抢单失败消息
        String code = GrabOrderCode.ORDER_TAKEN_FAIL.getCode();
        String desc = MapUtils.getString(mapping, code, "");
        String userOrderId = Optional.ofNullable(snapshot).map(GrabDspOrderSnapshotDO::getUserOrderId).orElse("");
        if (ParentCategoryEnum.DAY.getCode().equalsIgnoreCase(command.getCategoryCode())) {
            messageProvider.send(new GuideGrabOrderFailEvent(command.getDriverId(), command.getDuid(), command.getOrderId(), userOrderId, code));
        } else {
            if (!sysSwitchConfigGateway.getDriverQmqAddUcsSwitch()) {
                messageProvider.send(new GrabOrderFailEvent(command.getDriverId(), command.getDuid(), command.getOrderId(), userOrderId, code, desc));
                return;
            }
            DriverUdlVO driverUdlVO = queryDriverService.getDrvUdl(command.getDriverId());
            logger.info("submitBroadcastGrabExeCmd sendGrabFailEvent", "driverUdl:" + JsonUtil.toJson(driverUdlVO));
            CatUtil.doWithUdlOverride(() -> {
                messageProvider.send(new GrabOrderFailEvent(command.getDriverId(), command.getDuid(), command.getOrderId(), userOrderId, code, desc));
                return Boolean.TRUE;
            }, driverUdlVO.getUdl(), driverUdlVO.getRequestFrom());
        }
    }

    /**
     * 轮选幂等
     * @param dspOrderId
     * @param subSkuId
     * @return
     */
    public boolean isSelect(String dspOrderId, Integer subSkuId) {
        try {
            String key = GrabOrderDO.toSelectKey(dspOrderId, subSkuId);
            Long seconds = broadcastGrabConfig.getLong(ConfigKey.BROADCAST_SELECT_TIMING_KEY, DEFAULT_SELECT_SECONDS);
            return idempotentCheckService.isNotProcessed(key, seconds);
        } catch (Exception e) {
            logger.error(e);
        }
        return true;
    }

}
