package com.ctrip.dcs.application.listener;


import com.ctrip.dcs.application.service.DispatcherConfirmedService;
import com.ctrip.dcs.application.service.SpContractInfoService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 调度确认消息
 * <AUTHOR>
 */
@Component
public class DispatcherConfirmListener {
    private static final Logger logger = LoggerFactory.getLogger(DispatcherConfirmListener.class);

    @Autowired
    private DispatcherConfirmedService dispatcherConfirmedService;

    @Autowired
    private SpContractInfoService spContractInfoService;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DISPATCHER_CONFIRM_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        // 检查重试次数 times是这个消息总重试次数
        if (message.times() > 4) {
            logger.warn("dispatcherRemind_qmq_consume", "dispatcherRemind_qmq_consume retry " + message.times() + ", messageId：" + message.getMessageId());
            return;
        }
        // localRetries是连续本地重试了多少次的意思(防止死循环)
        if (message.localRetries() > 2) {
            //60秒后再来看看能不能更新成功
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "Multiple local retries still failed");
        }
        String dspOrderId = message.getStringProperty("dspOrderId");
        Long confirmRecordId = message.getLongProperty("confirmRecordId");
        Long transportGroupId = message.getLongProperty("transportGroupId");
        logger.info("DispatcherConfirmListener_" + dspOrderId, "process tart");
        dispatcherConfirmedService.confirmed(dspOrderId, confirmRecordId, transportGroupId);
    }

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DISPATCHER_CONFIRM_TOPIC, consumerGroup =  "self_order_contract", idempotentChecker = "redisIdempotentChecker")
    public void contractInfoUpdate(Message message) {
        // 检查重试次数 times是这个消息总重试次数
        if (message.times() > 4) {
            logger.warn("dispatcherRemind_qmq_consume", "dispatcherRemind_qmq_consume retry " + message.times() + ", messageId：" + message.getMessageId());
            return;
        }
        // localRetries是连续本地重试了多少次的意思(防止死循环)
        if (message.localRetries() > 2) {
            //60秒后再来看看能不能更新成功
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "Multiple local retries still failed");
        }
        String dspOrderId = message.getStringProperty("dspOrderId");
        spContractInfoService.updateContractInfo(dspOrderId);

    }
}
