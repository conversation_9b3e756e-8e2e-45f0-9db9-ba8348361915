package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.event.VBKGrabOrderCancelEvent;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

@Component
public class VBKGrabTaskOrderFinishJob {

    private static final Logger logger = LoggerFactory.getLogger(VBKGrabTaskOrderFinishJob.class);


    @Autowired
    private MessageProviderService messageProducer;
    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;

    @QSchedule("vbk.grab.task.order.finish.job")
    public void execute(Parameter param) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        List<VBKDriverGrabOrderDO> list = vbkDriverGrabOrderRepository.queryGreatThanGrabLimitTimeBJ(new Timestamp(System.currentTimeMillis()));
        logger.info("VBKGrabTaskOrderFinishJob_queryGreatThanGrabLimitTimeBJ", JsonUtils.toJson(list));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        list.forEach(x -> messageProducer.send(new VBKGrabOrderCancelEvent(x.getDspOrderId())));
    }
}
