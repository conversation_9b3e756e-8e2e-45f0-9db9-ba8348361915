package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.SelectGrabOrderExeCmd;
import com.ctrip.dcs.application.command.api.SelectGrabOrderCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
public class SelectGrabOrderListener {

    @Autowired
    private SelectGrabOrderExeCmd selectGrabOrderExeCmd;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.SELECT_GRAB_ORDER_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        SelectGrabOrderCommand command = MessageConverter.INSTANCE.toSelectGrabOrderCommand(message);
        selectGrabOrderExeCmd.execute(command);
    }
}
