package com.ctrip.dcs.application.command.dispatchergrab;

import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.domain.common.constants.VbkOperateRecordConstant;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DispatcherGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DispatcherGrabOrderPO;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.dcs.infrastructure.common.converter.DispatcherGrabOrderDOConvertor;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class CancelDispatcherGrabOrderExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CancelDispatcherGrabOrderExeCmd.class);

    @Autowired
    private DispatcherGrabOrderDao dispatcherGrabOrderDao;

    @Autowired
    DispatcherGrabOrderRepository dispatcherGrabOrderRepository;

    @Autowired
    DspOrderRepository dspOrderRepository;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;
    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;
    @Autowired
    VBKOperationRecordGateway vbkOperationRecordGateway;

    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    DateZoneConvertGateway dateZoneConvertGateway;
    @Autowired
    @Qualifier("commonConfConfig")
    private ConfigService commonConfConfig;

    @Autowired
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    public void execute(CancelDispatcherGrabOrderCommand command) {
        try {
            if (SupplierConfirmSceneEnum.ORI_MODIFY.getScene().equals(command.getCancelScene())) {
                cancelForOriModify(command);
            } else {
                dispatcherGrabOrderDao.cancel(command.getUserOrderId(), command.getDspOrderIds(), command.getExcludeSupplierId());
            }
        } catch (Exception e) {
            logger.error("CancelDispatcherGrabOrderExeCmdError", e);
            throw ErrorCode.CANCEL_DISPATCH_GRAB_ORDER_ERROR.getBizException();
        }
    }

    public void cancelForOriModify(CancelDispatcherGrabOrderCommand command) throws SQLException {
        Cat.logEvent("dcs.self.dsp.cancelForOriModify.total", "1");
        DispatcherGrabOrderPO dispatcherGrabOrderPO = dispatcherGrabOrderDao.query(command.getUserOrderId(), command.getModifyVersion());
        if (dispatcherGrabOrderPO == null || DispatcherGrabOrderStatusEnum.isFinalState(dispatcherGrabOrderPO.getGrabStatus())) {
            logger.warn("dispatcherGrabOrderPO is null or dispatcherGrabOrderPO.getGrabStatus is final state {}");
            return;
        }
        DispatcherGrabOrderDO dispatcherGrabOrderDO = DispatcherGrabOrderDOConvertor.toDispatcherGrabOrderDO(dispatcherGrabOrderPO);
        dispatcherGrabOrderDO.setRevokeReason(String.valueOf(command.getCancelReason()));
        dispatcherGrabOrderDO.setRevokeReasonDesc(command.getCancelReasonDesc());
        dispatcherGrabOrderRepository.revokeForOriModify(dispatcherGrabOrderDO);

        Map<String, String> param = new HashMap<>();
        RevokeReasonEnum revokeReasonEnum = RevokeReasonEnum.getByType(command.getCancelReason());
        if (revokeReasonEnum == null) {
            return;
        }
        param.put("reason", commonConfConfig.getString(revokeReasonEnum.operateTypeVbkLog));
        String titleSharkKey = revokeReasonEnum.sharkKeyTitle;
        String contentSharkKey = revokeReasonEnum.sharkKeyContent;
        WorkBenchLogMessage message = workBenchLogMessageFactory.createOriModifySupplierRevokeBenchLog(dispatcherGrabOrderPO, titleSharkKey, contentSharkKey, param);
        workBenchLogGateway.sendWorkBenchLogMessage(message);
        insertVbkWorkLogOriOrderModifyRevoke(dispatcherGrabOrderPO, revokeReasonEnum);
        Cat.logEvent("dcs.self.dsp.cancelForOriModify.success", "1");
    }

    private void insertVbkWorkLogOriOrderModifyRevoke(DispatcherGrabOrderPO order, RevokeReasonEnum revokeReasonEnum) {
        // 查询派发单
        DspOrderVO dspOrderVO = queryDspOrderService.query(order.getDspOrderId());
        Date localTimeNow = dateZoneConvertGateway.getLocalTime(Calendar.getInstance(), dspOrderVO.getCityId());
        // 构建VO
        VBKOperationRecordVO recordVO = VBKOperationRecordVO.builder()
                .supplyOrderId(order.getDspOrderId())
                .userOrderId(order.getUserOrderId())
                .vendorOrderId("")
                .sysUserAccount("system")
                .operUserName("system")
                .operUserType("system")
                .operateType(revokeReasonEnum.operateTypeVbkLog)
                .operateName(businessTemplateInfoConfig.getVbkOperationName(revokeReasonEnum.operateTypeVbkLog))
                .operateLocalTime(localTimeNow.getTime())
                .recordType(VbkOperateRecordConstant.SUB_ORDER_STATE_FLOW)
                .comment(commonConfConfig.getString(revokeReasonEnum.operateTypeVbkLog))
                .supplierId(order.getSupplierId().intValue())
                .orderSourceCode(String.valueOf(dspOrderVO.getOrderSourceCode()))
                .timeZone(dspOrderVO.getTimeZone() == null ? 0D : dspOrderVO.getTimeZone().doubleValue())
                .beforeOperateData(String.valueOf(dspOrderVO.getOrderStatus()))
                .afterOperateData(String.valueOf(dspOrderVO.getOrderStatus()))
                .build();
        vbkOperationRecordGateway.record(recordVO);
    }
}
