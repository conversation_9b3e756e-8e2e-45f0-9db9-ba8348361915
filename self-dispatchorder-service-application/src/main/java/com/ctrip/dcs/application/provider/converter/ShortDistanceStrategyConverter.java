package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.SaveShortDistanceStrategyCommand;
import com.ctrip.dcs.application.command.dto.QueryShortDistanceStrategyResDTO;
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO;
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDateDTO;
import com.ctrip.dcs.domain.schedule.value.QueryShortDistanceStrategyCondition;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ShortDistanceStrategyDateInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ShortDistanceStrategyInfo;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ShortDistanceStrategyConverter {

    public SaveShortDistanceStrategyCommand convert(SaveShortDistanceStrategyRequestType request) {
        ShortDistanceStrategyDO strategyDO = new ShortDistanceStrategyDO();
        strategyDO.setId(request.getId());
        strategyDO.setCode(request.getCode());
        strategyDO.setCityId(request.getCityId());
        strategyDO.setCategoryCodeList(request.getCategoryCodeList());
        strategyDO.setChannelIdList(request.getChannelIdList());
        strategyDO.setStartDis(request.getStartDis());
        strategyDO.setEndDis(request.getEndDis());
        strategyDO.setVehicleGroupIdList(request.getVehicleGroupIdList());
        strategyDO.setStartEndDateBjList(Optional.ofNullable(request.getStartEndDateList()).orElse(Collections.emptyList()).stream().map(this::convert).collect(Collectors.toList()));
        strategyDO.setState(request.getState());
        strategyDO.setOperator(request.getOperator());
        return new SaveShortDistanceStrategyCommand(strategyDO);
    }

    public QueryShortDistanceStrategyCondition convert(QueryShortDistanceStrategyRequestType request) {
        QueryShortDistanceStrategyCondition condition = new QueryShortDistanceStrategyCondition();
        condition.setId(request.getId());
        condition.setCode(request.getCode());
        condition.setCityId(request.getCityId());
        condition.setCategoryCodeList(request.getCategoryCodeList());
        condition.setChannelIdList(request.getChannelIdList());
        condition.setVehicleGroupIdList(request.getVehicleGroupIdList());
        condition.setState(request.getState());
        condition.setPaginator(request.getPaginator());
        return condition;
    }

    public QueryShortDistanceStrategyResponseType convert(QueryShortDistanceStrategyResDTO input) {
        QueryShortDistanceStrategyResponseType outPut = new QueryShortDistanceStrategyResponseType();
        outPut.setConfigData(input.getShortDistanceStrategyList().stream().map(this::convert).collect(Collectors.toList()));
        outPut.setPagination(input.getPagination());
        return outPut;
    }

    private ShortDistanceStrategyInfo convert(ShortDistanceStrategyDO input) {
        ShortDistanceStrategyInfo outPut = new ShortDistanceStrategyInfo();
        outPut.setId(input.getId());
        outPut.setCode(input.getCode());
        outPut.setCityId(input.getCityId());
        outPut.setCategoryCodeList(input.getCategoryCodeList());
        outPut.setVehicleGroupIdList(input.getVehicleGroupIdList());
        outPut.setState(input.getState());
        outPut.setChannelIdList(input.getChannelIdList());
        outPut.setStartDis(input.getStartDis());
        outPut.setEndDis(input.getEndDis());
        outPut.setStartEndDateList(input.getStartEndDateBjList().stream().map(this::convert).collect(Collectors.toList()));
        outPut.setOperator(input.getOperator());
        outPut.setCreateTime(input.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return outPut;
    }

    private ShortDistanceStrategyDateInfo convert(ShortDistanceStrategyDateDTO input) {
        ShortDistanceStrategyDateInfo outPut = new ShortDistanceStrategyDateInfo();
        outPut.setStartDate(input.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        outPut.setEndDate(input.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        return outPut;
    }

    private ShortDistanceStrategyDateDTO convert(ShortDistanceStrategyDateInfo input) {
        ShortDistanceStrategyDateDTO outPut = new ShortDistanceStrategyDateDTO();
        outPut.setStartDate(LocalDate.parse(input.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        outPut.setEndDate(LocalDate.parse(input.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        return outPut;
    }
}
