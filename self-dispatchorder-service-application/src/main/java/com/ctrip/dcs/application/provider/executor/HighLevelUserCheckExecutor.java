package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.domain.common.service.HighLevelCheckService;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HighLevelUserCheckExecutor extends AbstractRpcExecutor<HighLevelUserRequestType, HighLevelUserResponseType> implements Validator<HighLevelUserRequestType> {

    @Autowired
    private HighLevelCheckService highLevelCheckService;

    @Override
    public HighLevelUserResponseType execute(HighLevelUserRequestType requestType) {
        HighLevelUserResponseType responseType = new HighLevelUserResponseType();
        responseType.setHighGradeOrder(highLevelCheckService.isHighLevelUserOrder(requestType.getCityId(), requestType.getVipGrade()));
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<HighLevelUserRequestType> validator) {
        validator.ruleFor("vipGrade").notNull();
        validator.ruleFor("cityId").notNull().greaterThan(0);
    }


}
