package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.dsporder.event.CompleteSettleToDriverEvent;
import com.ctrip.dcs.domain.dsporder.event.NoCommissionAmountCompensationEvent;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderFeePO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.ctrip.dcs.domain.common.util.DateUtil.DATETIME_FORMAT;

/**
 * 派发订单非抽佣金额逻辑处理
 * 执行定时任务，发送消息，侦听消息，处理金额
 *
 */
@Component
public class DspOrderNoCommissionAmountJob{

    private static final Logger logger = LoggerFactory.getLogger(DspOrderNoCommissionAmountJob.class);

    @Autowired
    private DspOrderFeeDao dspOrderFeeDao;

    @Autowired
    protected MessageProviderService messageProducer;

    @QSchedule("dsp.order.fee.no.commission.job")
    public void noCommissionJob(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        String beginTimeStr = parameter.getString("beginTime");
        String endTimeStr = parameter.getString("endTime");
        int intervalMinutes = Integer.parseInt(parameter.getString("intervalMinutes"));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime beginTime = LocalDateTime.parse(beginTimeStr, dateTimeFormatter);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, dateTimeFormatter);
        LocalDateTime queryStartTime = beginTime;
        LocalDateTime queryEndTime = beginTime.plusMinutes(intervalMinutes);
        while (queryStartTime.isBefore(endTime)) {
            String querystartTimeStr = queryStartTime.format(dateTimeFormatter);
            String queryEndTimeStr;
            if (queryEndTime.isAfter(endTime)) {
                queryEndTimeStr = endTime.format(dateTimeFormatter);
            } else {
                queryEndTimeStr = queryEndTime.format(dateTimeFormatter);
            }
            logger.info("dsp_order_fee_job", "faultDataCompensation startTime=" + querystartTimeStr + ", endTime=" + queryEndTimeStr);
            try {
                List<DspOrderFeePO> dspOrderFeePOList = dspOrderFeeDao.queryDspOrderFeeList(DateUtil.parse(querystartTimeStr, DATETIME_FORMAT), DateUtil.parse(queryEndTimeStr, DATETIME_FORMAT));
                for (DspOrderFeePO dspOrderFeePO : dspOrderFeePOList) {
                    logger.info("dsp_order_fee" + dspOrderFeePO.getDspOrderId(), "dspOrderFeePO=" + JacksonUtil.serialize(dspOrderFeePO));
                    BigDecimal realNoCommissionAmount = dspOrderFeePO.getWaypointAmount().add(dspOrderFeePO.getTollFee()).add(dspOrderFeePO.getParkingFee()).setScale(4, BigDecimal.ROUND_HALF_UP);
                    BigDecimal dbNoCommissionAmount = dspOrderFeePO.getNoCommisionAmount().setScale(4, BigDecimal.ROUND_HALF_UP);
                    //db里面的和实际的一样，那么不更新继续执行下一条
                    if (realNoCommissionAmount.compareTo(dbNoCommissionAmount) == 0) {
                        logger.info("dsp_order_fee_equal" + dspOrderFeePO.getDspOrderId(), "fee equal not need handler realNoCommissionAmount=" + realNoCommissionAmount + "dbNoCommissionAmount=" + dbNoCommissionAmount);
                        continue;
                    }
                    logger.info("dsp_order_fee_not_equal" + dspOrderFeePO.getDspOrderId(), "fee not equal need handler realNoCommissionAmount=" + realNoCommissionAmount + "dbNoCommissionAmount=" + dbNoCommissionAmount);
                    messageProducer.send(new NoCommissionAmountCompensationEvent(
                            dspOrderFeePO.getDspOrderId(),
                            dspOrderFeePO.getUserOrderId(),
                            String.valueOf(realNoCommissionAmount), String.valueOf(dbNoCommissionAmount), 0)
                    );
                }
            } catch (Exception e) {
                logger.warn("no_commission_need_handler","startTime=" + querystartTimeStr + ", endTime=" + queryEndTimeStr);
                logger.warn("no_commission_error", e);
            }
            queryStartTime = queryStartTime.plusMinutes(intervalMinutes);
            queryEndTime = queryEndTime.plusMinutes(intervalMinutes);
        }
        logger.info("dsp_order_fee_job", "faultDataCompensation end");
    }
}
