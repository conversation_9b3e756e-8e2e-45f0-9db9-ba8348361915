package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverStaticInventoryIgnoreDelayPoolVisitor;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverStaticInventoryVisitor;
import com.ctrip.dcs.infrastructure.service.DspContextServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component("TestStaticInventoryVisitor")
public class TestStaticInventoryVisitor implements ITestDspOrderService {
    @Autowired
    private ConflictGateway conflictGateway;
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private QueryDriverService queryDriverService;
    @Override
    public String test(Map<String, String> params) {
        DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(params.get("dspOrderId"));
        DriverVO driverVO = new DriverVO(Long.valueOf(params.get("driverId")));
        List<DriverVO> drivers = new ArrayList<>();
        drivers.add(driverVO);
        SubSkuVO subSku = new SubSkuVO(12313, "skuName",DspType.VBK_BROADCAST, TakenType.SUPPLIER,null,null,null, Collections.emptyList());
        CheckContext context = new CheckContext(new DspContext(new DspContextServiceImpl()), new TestConfigService(), subSku, dspOrder, driverVO, null, DspStage.DSP, null, null,null);
        //忽略延后派场景
        DriverStaticInventoryIgnoreDelayPoolVisitor visitor = new DriverStaticInventoryIgnoreDelayPoolVisitor(dspOrder,drivers,conflictGateway);
        visitor.visit(context);
        //不忽略延后派场景
        DriverStaticInventoryVisitor visitor1 = new DriverStaticInventoryVisitor(dspOrder,drivers,conflictGateway);
        visitor1.visit(context);
        return "";
    }
}
