package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.domain.common.enums.RoleReflectEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.schedule.dto.QueryPunishReasonDTO;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 改派Converter
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/5/19 15:33
 */
public class ReDispatchedConverter {

    public static RightAndPointCommand buildCommand(RightAndPointRequestType req) {
        RightAndPointCommand command = new RightAndPointCommand();

        command.setRoleId(req.getRoleId());
        command.setUserOrderId(req.getUserOrderId());
        command.setReasonDetailId(req.getReasonDetailId());
        command.setIsUseReDispatchRight(Boolean.TRUE);

        // 司机端域调用会传参数
        command.setDrvId(req.getDriverId());
        command.setNewRights(req.getNewRights());
        return command;
    }

    public static void assembleResp(RightAndPointResponseType resp, RightAndPointVO rightAndPoint) {
        resp.setResponsable(rightAndPoint.getResponsable());
        resp.setDispatchType(rightAndPoint.getDispatchType());
        resp.setChangeTime(rightAndPoint.getChangeTime());
        resp.setShowRightButton(rightAndPoint.getShowRightButton());
        // 权益信息
        resp.setDispatchRightInfo(build(rightAndPoint.getDispatchRightInfo()));
        // 判罚信息
        resp.setPunishInfo(build(rightAndPoint.getPunishInfo()));
        // 改派原因信息
        resp.setReDispatchReason(build(rightAndPoint.getReasonDetail()));
        // 给司机端展示文案
        resp.setDriverPunishInfo(build(rightAndPoint.getDriverPunishInfo()));
        // 判罚规则
        resp.setPunishRuleInfo(build(rightAndPoint.getPunishRuleInfo()));
    }

    private static DispatchPunishInfo build(PunishVO punishInfo) {
        DispatchPunishInfo res = new DispatchPunishInfo();
        if (punishInfo == null) {
            return res;
        }
        res.setPunish(punishInfo.getPunish());
        res.setPunishPoint(punishInfo.getPunishPoint());
        res.setPunishAmount(punishInfo.getPunishAmount());
        res.setPunishAmountDesc(punishInfo.getPunishAmountDesc());
        res.setPunishPointDesc(punishInfo.getPunishPointDesc());
        res.setChangeTime(punishInfo.getChangeTime());
        res.setNightChange(punishInfo.getNightChange());
        res.setPunishRoleId(punishInfo.getPunishRoleId());
        res.setPunishRoleName(punishInfo.getPunishRoleName());
        res.setCurrency(punishInfo.getCurrency());
        res.setReasonDetail(punishInfo.getReasonDetail());
        return res;
    }

    private static PunishRuleInfo build(PunishRuleVO punishRuleInfo) {
        PunishRuleInfo res = new PunishRuleInfo();
        if (punishRuleInfo == null) {
            return res;
        }
        res.setId(punishRuleInfo.getId());
        res.setBizAreaType(punishRuleInfo.getBizAreaType());
        res.setCityId(punishRuleInfo.getCityId());
        res.setCityName(punishRuleInfo.getCityName());
        res.setRedispatchType(punishRuleInfo.getReDispatchType());
        res.setTimeRange(punishRuleInfo.getTimeRange());
        res.setBufferTime(punishRuleInfo.getBufferTime());
        res.setPunishPoint(punishRuleInfo.getPunishPoint());
        res.setPunishAmount(punishRuleInfo.getPunishAmount());
        res.setCurrency(punishRuleInfo.getCurrency());
        return res;
    }

    private static DriverRedispatchPunishInfo build(DriverPunishVO driverPunishInfo) {
        DriverRedispatchPunishInfo res = new DriverRedispatchPunishInfo();
        if (driverPunishInfo == null) {
            return res;
        }
        res.setTakenExpectArriveTime(driverPunishInfo.getTakenExpectArriveTime());
        res.setNowExpectArriveTime(driverPunishInfo.getNowExpectArriveTime());
        res.setHasResponsible(driverPunishInfo.getHasResponsible());
        res.setCanAppear(driverPunishInfo.getCanAppear());
        return res;
    }

    private static ReDispatchReason build(ReasonDetailVO reasonDetail) {
        ReDispatchReason res = new ReDispatchReason();
        if (reasonDetail == null) {
            return null;
        }
        res.setReasonDetailId(reasonDetail.getId());
        res.setReasonId(reasonDetail.getReasonId());
        res.setReasonName(reasonDetail.getReasonDetail());
        res.setShowFront(Objects.equals(reasonDetail.getFrontShow(), ReassignTaskEnum.FrontShowEnum.SHOW.getCode()));
        res.setResponsible(Objects.equals(reasonDetail.getResponsible(), ReassignTaskEnum.ResponsibleEnum.HAVE.getCode()));
        res.setPunishRoleId(reasonDetail.getPunishRole());
        res.setPunishRoleName(reasonDetail.getPunishName());
        res.setEffectTime(reasonDetail.getEffectTime());
        res.setCheckResultTips(reasonDetail.getCheckResultTips());
        return res;
    }

    private static DispatchRightInfo build(DispatchRightVO dispatchRightInfo) {
        DispatchRightInfo res = new DispatchRightInfo();
        if (dispatchRightInfo == null) {
            return null;
        }
        res.setRightId(dispatchRightInfo.getRightId());
        res.setRightName(dispatchRightInfo.getRightName());
        res.setCycleType(dispatchRightInfo.getCycleType());
        res.setCycleStartTime(dispatchRightInfo.getCycleStartTime());
        res.setCycleEndTime(dispatchRightInfo.getCycleEndTime());
        res.setTimes(dispatchRightInfo.getTimes());
        res.setHasRight(dispatchRightInfo.getHasRight());
        res.setRightDetailDesc(dispatchRightInfo.getRightDetailDesc());
        return res;
    }

    public static QueryPunishReasonDTO buildQueryPunishReason(ReassignTaskEnum.PunishOperateType operateType, BaseDetailVO detailVO, ReasonDetailVO dispatchReason, ReassignTaskEnum.ChangeDriveTypeEnum changeDriverTypeEnum, Integer roleId) {

        QueryPunishReasonDTO params = new QueryPunishReasonDTO();
        // 改派前检查
        params.setOperateType(operateType.getCode());
        // 改派类型
        params.setDispatchType(changeDriverTypeEnum.getReasonId());
        // 改派原因
        params.setTargetReasonDetail(dispatchReason);
        // 订单信息
        params.setDriverId(detailVO.getDrvId());
        params.setSupplyOrderId(detailVO.getSupplyOrderId());
        params.setUserOrderId(Long.valueOf(detailVO.getUserOrderId()));

        // 预计用车时间 - 北京
        params.setEstimatedUseTimeBj(DateUtil.parseDateStr2Date(detailVO.getEstimatedUseTimeBj()));

        // 定位角色
        ReassignTaskEnum.RoleEnum role = ReassignTaskEnum.RoleEnum.getInstance(roleId);

        // 特定角色 不关心校准后的原因
        if (role != null) {
            if (role.isFromCustomer() || role.isFromDriverApp()) {
                params.setIsNeedAdjustingReasonRole(Boolean.TRUE);
            } else {
                params.setIsNeedAdjustingReasonRole(Boolean.FALSE);
            }
        }
        //传司机单号
        params.setDriverOrderId(detailVO.getDriverOrderId());

        return params;
    }

    public static List<ReDispatchReason> build(List<ReasonDetailVO> reasonDetailVOS) {
        if (CollectionUtils.isEmpty(reasonDetailVOS)) {
            return Lists.newArrayList();
        }
        List<ReDispatchReason> res = Lists.newArrayListWithCapacity(reasonDetailVOS.size());
        for (ReasonDetailVO detailVO : reasonDetailVOS) {
            ReDispatchReason temp = new ReDispatchReason();
            boolean frontShowRes = Boolean.FALSE;
            if (detailVO.getFrontShow() != null && detailVO.getFrontShow().intValue() == ReassignTaskEnum.FrontShowEnum.SHOW.getCode()) {
                frontShowRes = Boolean.TRUE;
            }
            temp.setShowFront(frontShowRes);
            temp.setEffectTime(detailVO.getEffectTime());
            temp.setReasonId(detailVO.getReasonId());
            temp.setReasonDetailId(detailVO.getId());
            temp.setPunishRoleId(detailVO.getPunishRole());
            temp.setPunishRoleName(detailVO.getPunishName());
            temp.setReasonName(detailVO.getReasonDetail());
            res.add(temp);
        }
        return res;
    }

}