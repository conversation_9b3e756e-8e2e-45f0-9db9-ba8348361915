package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand;
import com.ctrip.dcs.application.helper.OrderModifyHelper;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xingxing.yu on 2023/2/16.
 */
@Component
public class CustomerAssignDriverExeCmd extends AbstractAssignDriverExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CustomerAssignDriverExeCmd.class);

    @Autowired
    private VbkChangeDriverExeCmd vbkChangeDriverExeCmd;


    @Override
    public boolean execute(OperateAssignDriverCommand cmd) {
        DspOrderVO order = queryDspOrderService.query(cmd.getDspOrderId());
        if (order == null) {
            throw ErrorCode.NULL_ORDER_ERROR.getBizException();
        }
        // 原单修改待确认时不允许任何操作
        OrderModifyHelper.assertNotToBeConfirmed(order.getDspOrderId());
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId().longValue() : 0L;
        DriverVO driverVO = queryDriverService.queryDriver(cmd.getDriverId(), CategoryUtils.selfGetParentType(order), supplierId);
        if (driverVO == null) {
            throw ErrorCode.FAIL_BY_DRIVER_INFO.getBizException();
        }
        List<TransportGroupVO> transportGroupInfos = driverVO.getTransportGroups().stream().filter(tg -> {
            if (cmd.getTransportGroupId() == null || cmd.getTransportGroupId() == 0L) {
                return true;
            }
            return cmd.getTransportGroupId().equals(tg.getTransportGroupId());
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(transportGroupInfos)) {
            logger.warn("customer_assign_driver_warn", "The query for driver capacity group information is empty.driverId=" + cmd.getDriverId());
            throw ErrorCode.FAIL_BY_DRIVER_INFO.getBizException();
        }
        boolean res = false;
        for (int i = 0; i < transportGroupInfos.size(); i++) {
            TransportGroupVO groupVO = transportGroupInfos.get(i);
            try {
                cmd.setTransportGroupId(groupVO.getTransportGroupId());
                if (OrderStatusEnum.isBossAssignState(order.getOrderStatus())) {
                    res = super.execute(cmd);
                } else {
                    cmd.setEvent(OrderStatusEvent.OFFLINE_UPDATE_DRIVER);
                    res = vbkChangeDriverExeCmd.execute(cmd);
                }
                if (res) {
                    logger.info("customer_assign_driver_info", "assign suc.userOrderId=" + cmd.getUserOrderId());
                    return res;
                }

            } catch (Exception e) {
                if (i == transportGroupInfos.size() - 1) {
                    throw e;
                }
                Map<String, String> map = Maps.newHashMap();
                map.put("userOrderId", cmd.getUserOrderId());
                map.put("driverId", cmd.getDriverId().toString());
                logger.error("customer_assign_driver_error", e, map);
            }
        }

        return res;
    }

    @Override
    protected Integer getSubskuId(DspOrderVO order, TransportGroupVO groupVO, OperateAssignDriverCommand cmd) {
        return manualSubSkuConf.matchSubSku(order.getCountryId().intValue(), order.getCityId(), order.getCategoryCode().type, groupVO.getTransportGroupMode().getCode(), SysConstants.AssignRole.SYS_ROLE_BOSS);
    }


}
