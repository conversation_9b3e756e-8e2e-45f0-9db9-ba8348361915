package com.ctrip.dcs.application.provider.converter;

import com.ctrip.dcs.application.command.api.UpdateDspOrderUseTimeCommand;
import com.ctrip.dcs.domain.dsporder.value.DspOrderUseTimeVO;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.UpdateDspOrderUseTimeRequestType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class UpdateDspOrderUseTimeConverter {


    public static UpdateDspOrderUseTimeCommand toUpdateDspOrderUseTimeCommand(UpdateDspOrderUseTimeRequestType requestType) {
        List<DspOrderUseTimeVO> dspOrderUseTimes = requestType.getOrderUseTimes()
                .stream().map(time -> new DspOrderUseTimeVO(
                        time.getDspOrderId(),
                        time.getNewEstimatedUseTimeLocal(),
                        time.getNewEstimatedUseTimeBJ(),
                        time.getNewPredictServiceStopTimeLocal(),
                        time.getNewPredictServiceStopTimeBJ(),
                        time.getNewLastConfirmTimeLocal(),
                        time.getNewLastConfirmTimeBJ(),
                        time.getNewLastConfirmCarTimeLocal(),
                        time.getNewLastConfirmCarTimeBJ(),
                        time.getSelfBuffer()
                        )
                ).collect(Collectors.toList());
        return new UpdateDspOrderUseTimeCommand(dspOrderUseTimes);
    }
}
