package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.DriverOrderConfirmFailExeCmd;
import com.ctrip.dcs.application.command.api.DriverOrderConfirmFailCommand;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class DriverOrderConfirmFailListener {

    private static final Logger logger = LoggerFactory.getLogger(DriverOrderConfirmFailListener.class);

    @Autowired
    private DriverOrderConfirmFailExeCmd driverOrderConfirmFailExeCmd;

    @Deprecated
    @QmqLogTag(tagKeys = {"dspOrderId", "driverOrderId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_CONFIRM_FAIL_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        DriverOrderConfirmFailCommand command = MessageConverter.INSTANCE.toDriverOrderConfirmFailCommand(message);
        driverOrderConfirmFailExeCmd.execute(command);
    }

    @QmqLogTag(tagKeys = {"dspOrderId", "driverOrderId", "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_CONFIRM_FAIL_CANCEL_TOPIC, consumerGroup = EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    public void onMessageCancel(Message message) {
        try {
            DriverOrderConfirmFailCommand command = MessageConverter.INSTANCE.toDriverOrderConfirmFailCommand(message);
            driverOrderConfirmFailExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("DriverOrderConfirmFailListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "DriverOrderConfirmFailListenerError");
        }
    }
}
