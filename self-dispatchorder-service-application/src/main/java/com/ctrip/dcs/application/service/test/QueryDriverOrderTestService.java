package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.application.service.DispatcherConfirmedService;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DriverOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderPO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Component("queryDriverOrderTestService")
public class QueryDriverOrderTestService implements ITestDspOrderService{

    private static final Logger logger = LoggerFactory.getLogger(QueryDriverOrderTestService.class);
    @Autowired
    private DriverOrderDao driverOrderDao;
    @Override
    public String test(Map<String, String> params) {
        List<DrvOrderPO> result = Lists.newArrayList();
        try {
            List<DrvOrderPO> drvOrders = driverOrderDao.queryByDspOrderId(params.get("dspOrderId"));
            for (DrvOrderPO drvOrder : drvOrders) {
                DrvOrderPO temp = new DrvOrderPO();
                temp.setIsGuideOrder(drvOrder.getIsGuideOrder());
                temp.setGuideOrderId(drvOrder.getGuideOrderId());
                temp.setIsSeparateOrder(drvOrder.getIsSeparateOrder());
                temp.setId(drvOrder.getId());
                temp.setDrvOrderId(drvOrder.getDrvOrderId());
                temp.setDspOrderId(drvOrder.getDspOrderId());
                temp.setUserOrderId(drvOrder.getUserOrderId());
                temp.setDrvId(drvOrder.getDrvId());
                temp.setCategoryCode(drvOrder.getCategoryCode());
                temp.setOrderStatus(drvOrder.getOrderStatus());
                temp.setOrderCarTypeId(drvOrder.getOrderCarTypeId());
                temp.setDrvName(drvOrder.getDrvName());
                temp.setDrvPhone(drvOrder.getDrvPhone());
                temp.setDrvPhoneAreaCode(drvOrder.getDrvPhoneAreaCode());
                temp.setDrvCoopMode(drvOrder.getDrvCoopMode());
                temp.setDrvLanguageCodeList(drvOrder.getDrvLanguageCodeList());
                temp.setCarId(drvOrder.getCarId());
                temp.setCarTypeId(drvOrder.getCarTypeId());
                temp.setCarLicense(drvOrder.getCarLicense());
                temp.setCarColorId(drvOrder.getCarColorId());
                temp.setCarBrandId(drvOrder.getCarBrandId());
                temp.setCarSeriesId(drvOrder.getCarSeriesId());
                temp.setTransportGroupId(drvOrder.getTransportGroupId());
                temp.setTransportGroupMode(drvOrder.getTransportGroupMode());
                temp.setTransportGroupName(drvOrder.getTransportGroupName());
                temp.setSupplierId(drvOrder.getSupplierId());
                temp.setServiceProviderId(drvOrder.getServiceProviderId());
                temp.setSysExpectBookTimeLocal(drvOrder.getSysExpectBookTimeLocal());
                temp.setSysExpectBookTimeBj(drvOrder.getSysExpectBookTimeBj());
                temp.setSysExpectFinishTimeBj(drvOrder.getSysExpectFinishTimeBj());
                temp.setConfirmMoneyTimeLocal(drvOrder.getConfirmMoneyTimeLocal());
                temp.setConfirmMoneyTimeBj(drvOrder.getConfirmMoneyTimeBj());
                temp.setFinishTimeLocal(drvOrder.getFinishTimeLocal());
                temp.setFinishTimeBj(drvOrder.getFinishTimeBj());
                temp.setCancelTimeLocal(drvOrder.getCancelTimeLocal());
                temp.setCancelTimeBj(drvOrder.getCancelTimeBj());
                temp.setDatachangeCreatetime(drvOrder.getDatachangeCreatetime());
                temp.setDatachangeLasttime(drvOrder.getDatachangeLasttime());
                temp.setSupplyOrderId(drvOrder.getSupplyOrderId());
                temp.setIsInland(drvOrder.getIsInland());
                temp.setDepartCityId(drvOrder.getDepartCityId());
                temp.setDestinationCityId(drvOrder.getDestinationCityId());
                temp.setTakeSysExpectBookTimeLocal(drvOrder.getTakeSysExpectBookTimeLocal());
                temp.setTakenTimeBj(drvOrder.getTakenTimeBj());
                temp.setUseDays(drvOrder.getUseDays());
                temp.setSaleMode(drvOrder.getSaleMode());
                temp.setSupplyAppointment(drvOrder.getSupplyAppointment());
                temp.setIsAvailable(drvOrder.getIsAvailable());
                temp.setUid(drvOrder.getUid());
                temp.setPredicServiceFinishTimeLocal(drvOrder.getPredicServiceFinishTimeLocal());
                temp.setUseSopAreaScope(drvOrder.getUseSopAreaScope());
                temp.setDrvLate(drvOrder.getDrvLate());
                temp.setDrvStrId(drvOrder.getDrvStrId());
                temp.setTimeZone(drvOrder.getTimeZone());
                temp.setTempDispatchStatusCode(drvOrder.getTempDispatchStatusCode());
                temp.setDriverVisibleRemark(drvOrder.getDriverVisibleRemark());
                temp.setVbkOrderId(drvOrder.getVbkOrderId());
                temp.setDriverType(drvOrder.getDriverType());
                temp.setOrderSourceCode(drvOrder.getOrderSourceCode());
                temp.setTakenTimeLocal(drvOrder.getTakenTimeLocal());
                temp.setSettleToDriver(drvOrder.getSettleToDriver());
                result.add(temp);
            }
        } catch (Exception e) {
            logger.warn("QueryDriverOrderTestServiceError", e);
        }
        return LocalJsonUtils.toJson(result);
    }
}
