package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.command.SaaSUpdateDspOrderExeCmd;
import com.ctrip.dcs.application.command.api.SaaSUpdateDspOrderCommand;
import com.ctrip.dcs.application.provider.converter.SaaSUpdateDspOrderConverter;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtendInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtraFlightInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSFeeInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSPoiInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSUserCountInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSXproductInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaasUpdateDspOrderInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "updateDspOrderInfo.dspOrderId", alias = "dspOrderId")
public class SaasUpdateDspOrderExecutor extends AbstractRpcExecutor<SaasUpdateDspOrderRequestType, SaasUpdateDspOrderResponseType> {

    private static final Logger logger = LoggerFactory.getLogger(SaasUpdateDspOrderExecutor.class);

   @Autowired
   private SaaSUpdateDspOrderExeCmd saaSUpdateDspOrderExeCmd;

    @Override
    public SaasUpdateDspOrderResponseType execute(SaasUpdateDspOrderRequestType requestType) {

        SaasUpdateDspOrderResponseType responseType = new SaasUpdateDspOrderResponseType();
        //唯一标识不能为空
        if (Objects.isNull(requestType.getUpdateDspOrderInfo())
                || StringUtils.isEmpty(requestType.getUpdateDspOrderInfo().getVbkOrderId())
                || StringUtils.isEmpty(requestType.getUpdateDspOrderInfo().getDspOrderId())) {
            return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
        }
        SaasUpdateDspOrderInfo updateDspOrderInfo = requestType.getUpdateDspOrderInfo();
        if (Objects.equals(updateDspOrderInfo.getNewProcess(), CommonConstants.NEW_PROCESS)) {
            //校验参数
            validParam(requestType);
        } else {
            //修改的内容不能为空
            if (StringUtils.isEmpty(updateDspOrderInfo.getEstimatedUseTime())) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            if (Objects.isNull(updateDspOrderInfo.getOrderSourceCode())) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            if (Objects.isNull(updateDspOrderInfo.getFromPoi())) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            if (Objects.isNull(updateDspOrderInfo.getToPoi())) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
            if (Objects.isNull(requestType.getSupplierId())) {
                return ServiceResponseUtils.fail(responseType, ErrorCode.ERROR_PARAMS.getCode(), ErrorCode.ERROR_PARAMS.getDesc());
            }
        }
        try {
            SaaSUpdateDspOrderCommand command = SaaSUpdateDspOrderConverter.converter(requestType);
            saaSUpdateDspOrderExeCmd.execute(command);
            return ServiceResponseUtils.success(new SaasUpdateDspOrderResponseType());
        } catch (BizException e) {
            return ServiceResponseUtils.fail(responseType, e.getCode(), e.getMessage());
        } catch (Exception ex) {
            logger.error("SaasUpdateDspOrderExecutor", ex);
            return ServiceResponseUtils.fail(responseType);
        }


    }
    
    public void validParam(SaasUpdateDspOrderRequestType requestType) {
        SaasUpdateDspOrderInfo updateDspOrderInfo = requestType.getUpdateDspOrderInfo();
        if (StringUtils.isEmpty(updateDspOrderInfo.getUserOrderId())) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"userOrderId param is null");
        }
        if (Objects.isNull(updateDspOrderInfo.getOrderSourceCode())) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"orderSourceCode param is null");
        }
        //验证扩展内容
        validExtendInfo(Optional.ofNullable(updateDspOrderInfo.getExtendInfo()).orElse(new SaaSExtendInfo()));
        if (StringUtils.isEmpty(updateDspOrderInfo.getVehicleGroupName()) || updateDspOrderInfo.getVehicleGroupName().length() > 200) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"vehicleGroupName param is null or length more than 200");
        }
        validFlightInfo(Optional.ofNullable(updateDspOrderInfo.getExtraFlightInfo()).orElse(new SaaSExtraFlightInfo()));
        validXproductInfo(Optional.ofNullable(updateDspOrderInfo.getXproductInfo()).orElse(new SaaSXproductInfo()));
        validFeeInfo(Optional.ofNullable(updateDspOrderInfo.getFeeInfo()).orElse(new SaaSFeeInfo()));
        BigDecimal max = BigDecimal.valueOf(1000000000000000L);
        if (Objects.nonNull(updateDspOrderInfo.getEstimatedKm()) && (updateDspOrderInfo.getEstimatedKm().compareTo(BigDecimal.ZERO) < 0 || updateDspOrderInfo.getEstimatedKm().compareTo(max) > 0)) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"estimatedKm range not in 0~10000000000000000");
        }
        if (!StringUtils.isEmpty(updateDspOrderInfo.getLastConfirmCarTime()) && updateDspOrderInfo.getLastConfirmCarTime().length() > 50) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"LastConfirmCarTime length more than 50");
        }
        //校验地址信息
        validPoiInfo(Optional.ofNullable(updateDspOrderInfo.getFromPoi()).orElse(new SaaSPoiInfo()), Optional.ofNullable(updateDspOrderInfo.getToPoi()).orElse(new SaaSPoiInfo()));
        //校验用户人数
        validUserCount(Optional.ofNullable(updateDspOrderInfo.getUserCount()).orElse(new SaaSUserCountInfo()));
    }
    
    public void validPoiInfo(SaaSPoiInfo fromPoi, SaaSPoiInfo toPoi) {
        if (StringUtils.isEmpty(fromPoi.getAddress())) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "fromAddress param is null");
        }
        if (fromPoi.getAddress().length() > 255) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "fromAddress length more than 255");
        }
        if (StringUtils.isEmpty(toPoi.getAddress())) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "toAddress param is null");
        }
        if (toPoi.getAddress().length() > 255) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "toAddress length more than 255");
        }
    }
    
    public void validExtendInfo(SaaSExtendInfo saaSExtendInfo) {
        if (!StringUtils.isEmpty(saaSExtendInfo.getVbkDistributorName()) && saaSExtendInfo.getVbkDistributorName().length() > 500) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"vbkDistributorName length more than 50");
        }
        if (!StringUtils.isEmpty(saaSExtendInfo.getRemark()) && saaSExtendInfo.getRemark().length() > 200) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "remark length more than 200");
        }
    }
    
    public void validFeeInfo(SaaSFeeInfo feeInfo) {
        BigDecimal max = BigDecimal.valueOf(1000000000000000L);
        if (Objects.nonNull(feeInfo.getCostAmount()) && (feeInfo.getCostAmount().compareTo(BigDecimal.ZERO) < 0 || feeInfo.getCostAmount().compareTo(max) > 0)) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"costAmount range not in 0~10000000000000000");
        }
    }
    
    public void validXproductInfo(SaaSXproductInfo xproductInfo) {
        if (!StringUtils.isEmpty(xproductInfo.getPackageName()) && xproductInfo.getPackageName().length() > 50) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"packageName length more than 50");
        }
        if (!StringUtils.isEmpty(xproductInfo.getAdditionalServices()) && xproductInfo.getAdditionalServices().length() > 200) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "additionalServices length more than 200");
        }
    }
    
    public void validFlightInfo(SaaSExtraFlightInfo flightInfo) {
        if (!StringUtils.isEmpty(flightInfo.getTerminalName()) && flightInfo.getTerminalName().length() > 50) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(),"terminalName length more than 50");
        }
    }
    
    public void validUserCount(SaaSUserCountInfo userCountInfo) {
        if (Objects.isNull(userCountInfo.getAdultCount()) || userCountInfo.getAdultCount() < 1 || userCountInfo.getAdultCount() > 50) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "adultCount param is null or range not in 1~50");
        }
        if (Objects.nonNull(userCountInfo.getChildCount()) && (userCountInfo.getChildCount() < 1 || userCountInfo.getChildCount() > 50)) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "childCount param range not in 1~50");
        }
        if (Objects.nonNull(userCountInfo.getBagCount()) && (userCountInfo.getBagCount() < 1 || userCountInfo.getBagCount() > 50)) {
            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "bagCount param range not in 1~50");
        }
    }
    
//    private void validPassengerInfo(PassengerInfo passengerInfo) {
//        if (StringUtils.isEmpty(passengerInfo.getPassengerName()) || passengerInfo.getPassengerName().length() > 50) {
//            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "passengerName param is null or length more than 50");
//        }
//        if (!StringUtils.isEmpty(passengerInfo.getPassengerEnglishName()) || passengerInfo.getPassengerEnglishName().length() > 50) {
//            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "passengerEnglishName param is null or length more than 50");
//        }
//        if (!StringUtils.isEmpty(passengerInfo.getPassengerMobile()) || passengerInfo.getPassengerMobile().length() > 20) {
//            throw new BizException(ErrorCode.ERROR_PARAMS.getCode(), "passengerMobile param is null or length more than 20");
//        }
//    }
}
