package com.ctrip.dcs.application.listener;


import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.IvrBizTypeEnum;
import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.google.common.base.Splitter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

import static com.ctrip.dcs.domain.common.constants.EventConstants.*;


@Component
public class UrgentOrderCancelIvrConsumer {
    @Autowired
    private IvrCallService ivrCallService;


    //司机车辆已确认取消ivr
    @QmqConsumer(prefix = CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN,consumerGroup = EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "orderId", alias = "userOrderId")
    @QmqLogTagPair(key = "supplyOrderIds", alias = "supplyOrderId")
    public void orderTaken(Message msg) {
        String supplyOrderId = msg.getStringProperty("supplyOrderIds");
        //取消调度ivr任务
        ivrCallService.cancelIvrRecord(supplyOrderId, IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD);
    }
    //订单取消-取消ivr
    @QmqConsumer(prefix = CAR_QBEST_ORDER_ORDERSTATE_ORDER_CANCEL, consumerGroup = EventConstants.CONSUMER_GROUP,idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "orderId", alias = "userOrderId")
    @QmqLogTagPair(key = "supplyOrderIds", alias = "supplyOrderId")
    public void orderCancel(Message msg) {
        String supplyOrderIds = msg.getStringProperty("supplyOrderIds");
        if (Strings.isBlank(supplyOrderIds)) {
            return;
        }
        List<String> supplyOrderIdList = Splitter.on(",").splitToList(supplyOrderIds);
        for (String supplyOrderId : supplyOrderIdList) {
            //取消调度ivr任务
            ivrCallService.cancelIvrRecord(supplyOrderId, IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD);
        }
    }

    @QmqConsumer(prefix = DCS_SUPPLYORDER_CONFIRM_EVENT, consumerGroup =  EventConstants.CONSUMER_GROUP, idempotentChecker = "redisIdempotentChecker")
    @QmqLogTagPair(key = "userOrderId", alias = "userOrderId")
    @QmqLogTagPair(key = "supplyOrderId", alias = "supplyOrderId")
    public void supplyOrderConfirm(Message msg) {
        int confirmType = msg.getIntProperty("confirmType");
        String supplierOrderId = msg.getStringProperty("supplierOrderId");
        if (confirmType == 0 || StringUtils.isEmpty(supplierOrderId) || confirmType< 20) {
            return;
        }
        //取消调度ivr任务
        ivrCallService.cancelIvrRecord(supplierOrderId, IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD);
    }
}
