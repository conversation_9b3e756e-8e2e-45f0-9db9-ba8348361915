package com.ctrip.dcs.application.provider.executor;

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService;
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleRecordDTO;
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleParamDTO;
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleRecordParamDTO;
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleRecordResultDTO;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordResponseType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleRecordSoaDTO;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
public class QueryGrabOrderPushRuleRecordExecutor extends AbstractRpcExecutor<QueryGrabOrderPushRuleRecordRequestType, QueryGrabOrderPushRuleRecordResponseType> implements Validator<QueryGrabOrderPushRuleRecordRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryGrabOrderPushRuleRecordExecutor.class);
    @Autowired
    private IGrabOrderPushRuleService grabOrderPushRuleService;
    @Override
    public QueryGrabOrderPushRuleRecordResponseType execute(QueryGrabOrderPushRuleRecordRequestType requestType) {
        try{
            QueryGrabOrderPushRuleRecordResultDTO resultDTO = grabOrderPushRuleService.queryRuleRecord(convertToParamDto(requestType));
            return ServiceResponseUtils.success(convertToResponseType(resultDTO));
        }catch (Exception e){
            logger.error("QueryGrabOrderPushRuleRecordExecutorExp","QueryGrabOrderPushRuleRecordExecutorExp",e,new HashMap<>());
            return ServiceResponseUtils.fail(new QueryGrabOrderPushRuleRecordResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<QueryGrabOrderPushRuleRecordRequestType> validator) {
        validator.ruleFor("ruleId").notNull();
        validator.ruleFor("paginator").notNull();
        validator.ruleFor("paginator.pageNo").notNull().greaterThan(0);
        validator.ruleFor("paginator.pageSize").notNull().greaterThan(0);
    }

    /**
     * 封装参数
     * @param resultDTO
     * @return
     */
    public QueryGrabOrderPushRuleRecordResponseType convertToResponseType(QueryGrabOrderPushRuleRecordResultDTO resultDTO){
        QueryGrabOrderPushRuleRecordResponseType responseType = new QueryGrabOrderPushRuleRecordResponseType();
        List<GrabOrderPushRuleRecordSoaDTO> ruleRecordSoaDTOList = new ArrayList<>();
        if(!LocalCollectionUtils.isEmpty(resultDTO.getRecordDTOList())){
            for (GrabOrderPushRuleRecordDTO recordDTO : resultDTO.getRecordDTOList()) {
                ruleRecordSoaDTOList.add(convertToSoaDto(recordDTO));
            }
        }
        responseType.setRuleRecords(ruleRecordSoaDTOList);
        //分页数据
        PaginationDTO paginationDTO = new PaginationDTO();
        paginationDTO.setPageNo(resultDTO.getPageNo());
        paginationDTO.setPageSize(resultDTO.getPageSize());
        paginationDTO.setTotalPages(resultDTO.getTotalPages());
        paginationDTO.setTotalSize(resultDTO.getTotalSize());
        responseType.setPaginationDTO(paginationDTO);
        return responseType;
    }

    /**
     * dto->soa dto
     * @param recordDTO
     * @return
     */
    public GrabOrderPushRuleRecordSoaDTO convertToSoaDto(GrabOrderPushRuleRecordDTO recordDTO){
        GrabOrderPushRuleRecordSoaDTO soaDTO = new GrabOrderPushRuleRecordSoaDTO();
        soaDTO.setId(recordDTO.getId());
        soaDTO.setRuleId(recordDTO.getRuleId());
        soaDTO.setOperatorType(recordDTO.getOperatorType());
        soaDTO.setOperatorName(recordDTO.getOperatorName());
        soaDTO.setOperatorTime(DateUtil.formatDate(new Date(recordDTO.getOperatorTime().getTime()),DateUtil.DATETIME_FORMAT));
        soaDTO.setBeforeChange(recordDTO.getBeforeChange());
        soaDTO.setAfterChange(recordDTO.getAfterChange());
        soaDTO.setCreateTime(DateUtil.formatDate(new Date(recordDTO.getCreateTime().getTime()),DateUtil.DATETIME_FORMAT));
        soaDTO.setUpdateTime(DateUtil.formatDate(new Date(recordDTO.getUpdateTime().getTime()),DateUtil.DATETIME_FORMAT));
        return soaDTO;
    }

    /**
     * 封装查询参数
     * @param requestType
     * @return
     */
    public QueryGrabOrderPushRuleRecordParamDTO convertToParamDto(QueryGrabOrderPushRuleRecordRequestType requestType){
        QueryGrabOrderPushRuleRecordParamDTO paramDTO = new QueryGrabOrderPushRuleRecordParamDTO();
        Integer pageNo = requestType.getPaginator().getPageNo();
        Integer pageSize = requestType.getPaginator().getPageSize();
        paramDTO.setPageNo(pageNo);
        paramDTO.setPageSize(pageSize);
        paramDTO.setRuleId(requestType.getRuleId());
        return paramDTO;
    }
}
