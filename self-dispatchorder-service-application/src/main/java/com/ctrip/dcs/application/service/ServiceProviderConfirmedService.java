package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 服务商确认后处理服务
 * <AUTHOR>
 */
@Component
public class ServiceProviderConfirmedService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceProviderConfirmedService.class);

    @Autowired
    private QueryDspOrderService queryDspOrderService;

    @Autowired
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Autowired
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Autowired
    private WorkBenchLogGateway workBenchLogGateway;

    @Autowired
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    public void confirmed(String dspOrderId, Long confirmRecordId) {
        DspOrderVO dspOrder = queryDspOrderService.queryBase(dspOrderId);
        DspOrderConfirmRecordVO confirmRecord = dspOrderConfirmRecordRepository.find(confirmRecordId);
        // 发送客服工作台日志
        sendWorkBenchLog(dspOrder, confirmRecord);
        // 终止调度
        shutdownSchedule(dspOrder);
        cancelDispatcherGrabOrder(dspOrder);
    }

    /**
     * 发送客服工作台日志
     * @param dspOrder
     * @param confirmRecord
     */
    private void sendWorkBenchLog(DspOrderVO dspOrder, DspOrderConfirmRecordVO confirmRecord) {
        try {
            WorkBenchLogMessage message = workBenchLogMessageFactory.createServiceProviderWorkBenchLog(dspOrder, confirmRecord);
            workBenchLogGateway.sendWorkBenchLogMessage(message);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void shutdownSchedule(DspOrderVO dspOrder) {
        try {
            List<ScheduleDO> schedules = scheduleRepository.query(dspOrder.getDspOrderId());
            if (CollectionUtils.isEmpty(schedules)) {
                return;
            }
            for (ScheduleDO schedule : schedules) {
                shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(schedule, ScheduleEventType.DSP_ORDER_NOT_DISPATCHING));
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void cancelDispatcherGrabOrder(DspOrderVO dspOrderDO) {
        try {
            CancelDispatcherGrabOrderCommand command = new CancelDispatcherGrabOrderCommand(dspOrderDO.getUserOrderId());
            cancelDispatcherGrabOrderExeCmd.execute(command);
        } catch (Exception e) {
            logger.error("cancel_dispatcher_grab_order_error", String.format("cancel_dispatcher_grab_order_error,userOrderId=%s", dspOrderDO.getUserOrderId()), e);
        }
    }
}
