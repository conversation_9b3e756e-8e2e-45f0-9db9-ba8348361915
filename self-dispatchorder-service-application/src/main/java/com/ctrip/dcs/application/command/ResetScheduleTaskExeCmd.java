package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.ResetScheduleTaskCommand;
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.enums.ScheduleEventType;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.ExecuteScheduleEvent;
import com.ctrip.dcs.domain.schedule.factory.ScheduleFactory;
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory;
import com.ctrip.dcs.domain.schedule.factory.ScheduleTaskFactory;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleStrategyRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.infrastructure.adapter.carconfig.VbkDriverGrabDspStrategyConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

;


/**
 * 重置调度任务
 *
 * <AUTHOR>
 */
@Component
public class ResetScheduleTaskExeCmd {

    @Autowired
    private ScheduleFactory scheduleFactory;

    @Autowired
    private QueryDspOrderService dspOrderService;

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private ScheduleRecordRepository scheduleRecordRepository;

    @Autowired
    private ScheduleRecordFactory scheduleRecordFactory;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    private ScheduleTaskFactory taskFactory;

    @Autowired
    private VbkDriverGrabDspStrategyConfig vbkDriverGrabDspStrategyConfig;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private ScheduleStrategyRepository strategyRepository;

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    public void execute(ResetScheduleTaskCommand command) {
        ScheduleDO schedule = scheduleRepository.find(command.getScheduleId());
        // 任务
        List<ScheduleTaskDO> tasks = taskRepository.query(schedule.getScheduleId(), schedule.getDspOrderId());
        for (ScheduleTaskDO task : tasks) {
            task.init();
            taskRepository.rebuild(task);
        }
        dspOrderRewardStrategyService.rebuildRewardStrategy(schedule.getDspOrderId());
        // 发送调度执行事件
        messageProducer.send(new ExecuteScheduleEvent(schedule.getDspOrderId(), schedule.getScheduleId(), schedule.getRound() + 1, ScheduleEventType.SCHEDULE_RETRY_JOB));
    }

}
