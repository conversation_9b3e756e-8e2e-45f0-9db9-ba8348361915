package com.ctrip.dcs.application.service.reDispath;

import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.DispatchRightVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.infrastructure.common.constants.DescribeConstants;
import com.ctrip.igt.framework.common.result.Result;

/**
 * 请假权益基类
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/11/6 14:20
 */
public abstract class LeaveRightsBaseHandle implements RightAndPointService {

    /**
     * 处理司机请假权益
     *
     * @param command
     * @param detailVO
     * @param voResult
     * @return
     */
    public static Result<RightAndPointVO> dealWithDriverLeaveRights(RightAndPointCommand command, BaseDetailVO detailVO, Result<RightAndPointVO> voResult) {
        // 无效数据
        if (voResult == null || voResult.getData() == null) {
            return voResult;
        }
        // 无权益信息
        if (command.getDriverLeaveRights() == null) {
            return voResult;
        }
        // 无责
        if (!Boolean.TRUE.equals(voResult.getData().isResponsible())) {
            return voResult;
        }
        // 临近用车两小时内不无法使用权益
        if (!detailVO.isAllowUseRightSysBookTime()) {
            return voResult;
        }
        // 无法使用权益结果
        if (!command.getDriverLeaveRights().useRights(detailVO)) {
            return voResult;
        }
        // 无责处理
        RightAndPointVO rightAndPointVO = new RightAndPointVO();
        rightAndPointVO.setResponsable(Boolean.FALSE);
        rightAndPointVO.setDispatchRightInfo(buildDriverLeaveRights());
        return Result.Builder.<RightAndPointVO>newResult().success().withData(rightAndPointVO).build();
    }

    /**
     * 构建司机请假权益
     *
     * @return
     */
    private static DispatchRightVO buildDriverLeaveRights() {
        DispatchRightVO dispatchRightVO = new DispatchRightVO();
        // 权益ID
        dispatchRightVO.setRightId(String.valueOf(ReassignTaskEnum.RightTypeEnum.LEAVE_RIGHT.getCode()));
        // 权益名称
        dispatchRightVO.setRightName(ReassignTaskEnum.RightTypeEnum.LEAVE_RIGHT.getDesc());
        // 权益周期
        dispatchRightVO.setCycleType(ReassignTaskEnum.RightTypeEnum.LEAVE_RIGHT.getCycleType());
        // 权益生效开始时间
        dispatchRightVO.setCycleStartTime(DateUtil.formatDate(DateUtil.getMonthStartTime(), DateUtil.DATETIME_FORMAT));
        // 权益生效结束时间
        dispatchRightVO.setCycleEndTime(DateUtil.formatDate(DateUtil.getMonthEndTime(), DateUtil.DATETIME_FORMAT));
        // 权益次数 1 月 1 次 应该是没有地方使用本职
        dispatchRightVO.setTimes(0);
        dispatchRightVO.setHasRight(Boolean.TRUE);
        dispatchRightVO.setRightDetailDesc(String.format(DescribeConstants.LEAVE_RIGHTS, dispatchRightVO.getTimes()));
        return dispatchRightVO;
    }

}