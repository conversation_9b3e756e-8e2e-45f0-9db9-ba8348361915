package com.ctrip.dcs.application.provider.executor.dispatchergrab;

import com.ctrip.dcs.application.command.api.RefuseDispatcherGrabOrderCommand;
import com.ctrip.dcs.application.command.dispatchergrab.RefuseDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTagPair(key = "dspOrderId", alias = "dspOrderId")
public class RefuseDispatcherGrabOrdersExecutor extends AbstractRpcExecutor<RefuseDispatcherGrabOrdersRequestType, RefuseDispatcherGrabOrdersResponseType> implements Validator<RefuseDispatcherGrabOrdersRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(RefuseDispatcherGrabOrdersExecutor.class);

    @Autowired
    private RefuseDispatcherGrabOrderExeCmd cmd;

    @Override
    public RefuseDispatcherGrabOrdersResponseType execute(RefuseDispatcherGrabOrdersRequestType requestType) {
        try {
            RefuseDispatcherGrabOrderCommand command = new RefuseDispatcherGrabOrderCommand(requestType.getDspOrderId(), requestType.getSupplierId(), requestType.getSource(), requestType.getScene(), requestType.getPkId());
            cmd.execute(command);
            return ServiceResponseUtils.success(new RefuseDispatcherGrabOrdersResponseType());
        } catch (BizException e) {
            logger.error("RefuseDispatcherGrabOrdersExecutorError", e);
            return ServiceResponseUtils.fail(new RefuseDispatcherGrabOrdersResponseType(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("RefuseDispatcherGrabOrdersExecutorError", e);
            return ServiceResponseUtils.fail(new RefuseDispatcherGrabOrdersResponseType());
        }
    }

    @Override
    public void validate(AbstractValidator<RefuseDispatcherGrabOrdersRequestType> validator) {
        validator.ruleFor("dspOrderId").notNull().notEmpty();
        validator.ruleFor("supplierId").notNull().notEmpty();
    }
}
