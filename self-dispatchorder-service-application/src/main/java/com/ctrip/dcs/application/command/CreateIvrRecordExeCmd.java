package com.ctrip.dcs.application.command;

import com.ctrip.dcs.application.command.api.CreateIvrRecordCommand;
import com.ctrip.dcs.domain.dsporder.repository.IvrRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CreateIvrRecordExeCmd {

    @Autowired
    private IvrRecordRepository ivrRecordRepository;

    public long execute(CreateIvrRecordCommand cmd){
        ivrRecordRepository.save(cmd.getIvrRecordDO());
        return 1L;
    }
}
