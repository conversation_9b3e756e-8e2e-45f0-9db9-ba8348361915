package com.ctrip.dcs.application.listener.binlog;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dsp_order_fee")
public class DspOrderFeeDealImpl implements BinlogDeal{

    @Autowired
    private DspOrderFeeRepository dspOrderFeeRepository;

    @Override
    public void clearCache(DataChange dataChange) {
        String dspOrderId = dataChange.getAfterColumnValue("dsp_order_id");
        dspOrderFeeRepository.clearCache(dspOrderId);
    }

    @Override
    public void clearCache(List<String> dspOrderIds) {
        dspOrderIds.forEach(dspOrderId -> dspOrderFeeRepository.clearCache(dspOrderId));
    }
}
