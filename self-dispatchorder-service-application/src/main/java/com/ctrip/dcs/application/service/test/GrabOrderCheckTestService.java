package com.ctrip.dcs.application.service.test;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.item.impl.AbstractDriverCheck;
import com.ctrip.dcs.domain.schedule.check.item.impl.GrabOrderDriverCheck;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component("GrabOrderCheckTestService")
public class GrabOrderCheckTestService implements ITestDspOrderService{
    @Autowired
    private QueryDspOrderService queryDspOrderService;
    @Autowired
    private QueryDriverService queryDriverService;
    @Override
    public String test(Map<String, String> params) {
        DspOrderVO order = queryDspOrderService.query(params.get("dspOrderId"));
        Long supplierId = order.getSupplierId() != null ? order.getSupplierId().longValue() : 0L;
        DriverVO driver = queryDriverService.queryDriver(Long.valueOf(params.get("driverId")), CategoryUtils.selfGetParentType(order), supplierId);
        AbstractDriverCheck grabOrderDriverCheck = new GrabOrderDriverCheck();
        List<CheckModel> result = grabOrderDriverCheck.check(Arrays.asList(new CheckModel(new DspModelVO(order,driver))),new CheckContext());
        return LocalJsonUtils.toJson(result);
    }
}
