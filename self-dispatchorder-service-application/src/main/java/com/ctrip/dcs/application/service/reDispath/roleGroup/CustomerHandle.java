package com.ctrip.dcs.application.service.reDispath.roleGroup;

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand;
import com.ctrip.dcs.application.command.api.RightAndPointCommand;
import com.ctrip.dcs.application.service.RedispatchRightAndPointService;
import com.ctrip.dcs.application.service.reDispath.RightAndPointService;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.RightAndPointVO;
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/5/30 11:01
 */
@Component(value = ReassignTaskEnum.CUSTOMER_CLIENT)
public class CustomerHandle implements RightAndPointService {

    @Resource
    private RedispatchRightAndPointService redispatchRightAndPointService;

    @Override
    public Result<RightAndPointVO> queryRightAndPointDTO(RightAndPointCommand command, BaseDetailVO detailVO) {
        return redispatchRightAndPointService.checkPunishReason(command, detailVO);
    }

    @Override
    public Result convertAndCheckPrm(ReDispatchSubmitCommand command, BaseDetailVO detailVO) {
        return Result.Builder.newResult().success().build();

    }

}