package com.ctrip.dcs.application.provider.converter;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.application.command.api.CreateDspOrderCommand;
import com.ctrip.dcs.domain.common.enums.OrderExtendAttributeCodeEnum;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO;
import com.ctrip.dcs.domain.common.value.OrderWayPointVO;
import com.ctrip.dcs.domain.dsporder.entity.*;
import com.ctrip.dcs.domain.dsporder.value.UseDays;
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @user : xingxing.yu
 * @date : 2023/3/18 16:29
 */
public class CreateDspOrderConverter {

    public static CreateDspOrderCommand converter(CreateDspOrderRequestType request) {
        Assert.notNull(request);
        CreateDspOrderInfo createDspOrderInfo = request.getCreateDspOrderInfo();
        Assert.notNull(createDspOrderInfo);
        BaseInfo baseInfo = createDspOrderInfo.getBaseInfo();
        Assert.notNull(baseInfo);
        ProductInfo productInfo = createDspOrderInfo.getProductInfo();
        Assert.notNull(productInfo);
        FeeInfo feeInfo = createDspOrderInfo.getFeeInfo();
        Assert.notNull(feeInfo);

        CreateDspOrderCommand cmd = new CreateDspOrderCommand();
        cmd.setDspOrderDO(buildOrder(baseInfo));
        cmd.setDspOrderFeeDO(buildFee(baseInfo, feeInfo));
        cmd.setDspOrderDetailDO(buildOrderDetail(baseInfo, productInfo));
//        cmd.setPlatformPriceStrategyDO();
        cmd.setPlatformRewardInfo(buildPlatformRewardInfo(feeInfo));
        cmd.setOrderExtendAttributeInfo(buildOrderExtendAttributes(createDspOrderInfo.getOrderExtendAttributeInfo()));
        cmd.setOrderWayPointList(buildOrderWayPoints(createDspOrderInfo.getWayPointInfoList()));
        cmd.setOrderScene(createDspOrderInfo.getOrderScene());
        cmd.setUseTimeNoDriverCancelBuffer(baseInfo.getUseTimeNoDriverCancelBuffer());
        return cmd;
    }

    private static List<DspOrderRewardDO> buildPlatformRewardInfo(FeeInfo feeInfo) {
        List<PlatformRewardDTO> rewards = Optional.ofNullable(feeInfo).map(FeeInfo::getPlatformRewardInfo).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(rewards)) {
            return Collections.emptyList();
        }
        List<DspOrderRewardDO> result = Lists.newArrayList();
        for (PlatformRewardDTO dto : rewards) {
            DspOrderRewardDO reward = new DspOrderRewardDO();
            reward.setRuleSceneId(dto.getRuleSceneId());
            reward.setRuleId(dto.getRuleId());
            reward.setRuleTypeId(dto.getRuleTypeId());
            reward.setTotalAmount(dto.getTotalAmount());
            reward.setCurrencyCode(dto.getCurrencyCode());
            reward.setReward2driverExchangeRate(dto.getReward2driverExchangeRate());
            result.add(reward);
        }
        return result;
    }

    public static List<OrderExtendAttributeVO> buildOrderExtendAttributes(List<OrderExtendAttributeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<String> attributeCodes = Arrays.stream(OrderExtendAttributeCodeEnum.values()).map(Enum::name).collect(Collectors.toSet());
        return list.stream()
                .filter(dto -> dto != null && dto.getAttributeCode() != null && attributeCodes.contains(dto.getAttributeCode()))
                .map(dto -> new OrderExtendAttributeVO(dto.getAttributeCode(), dto.getAttributeValue()))
                .collect(Collectors.toList());
    }

    private static List<OrderWayPointVO> buildOrderWayPoints(List<WayPointInfoDTO> list) {
        return Optional.ofNullable(list)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(dto -> new OrderWayPointVO(dto.getCityId(), dto.getIndex(), dto.getName(), dto.getAddress(), dto.getLongitude(), dto.getLatitude(), dto.getCoordType(), dto.getCarPlaceId(), dto.getWaitTimeLength(), dto.getWaitUnitPrice(), dto.getPriceCurrency()))
                .collect(Collectors.toList());
    }

    private static DspOrderDO buildOrder(BaseInfo baseInfo) {
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDO.setSupplyOrderId(baseInfo.getSupplyOrderId());
        dspOrderDO.setUid(baseInfo.getUid());

        dspOrderDO.setUserOrderId(baseInfo.getUserOrderId());
        dspOrderDO.setOldDspOrderId(baseInfo.getOldDspOrderId());

        dspOrderDO.setNeedSupplierAgree(baseInfo.getNeedSupplierAgreeModify());
        dspOrderDO.setModifyVersion(baseInfo.getOrderModifyVersion());

        dspOrderDO.setProductCode(baseInfo.getProductCode());
        dspOrderDO.setProductName(baseInfo.getProductName());
        dspOrderDO.setCategoryCode(baseInfo.getCategoryCode());
        dspOrderDO.setCityId(baseInfo.getCityId().intValue());
        dspOrderDO.setFromCityId(baseInfo.getFromCityId().intValue());
        dspOrderDO.setToCityId(baseInfo.getToCityId().intValue());
        dspOrderDO.setVehicleGroupId(baseInfo.getVehicleGroupid().intValue());
        dspOrderDO.setEstimatedUseTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getEstimatedUseTime()).getTime()));
        dspOrderDO.setEstimatedUseTimeBj(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getEstimatedUseTimeBj()).getTime()));
        if (StringUtils.isNotBlank(baseInfo.getPredicServiceStopTime())) {
            dspOrderDO.setPredicServiceStopTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getPredicServiceStopTime()).getTime()));
        } else {
            dspOrderDO.setPredicServiceStopTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getPredicServiceStopTimeBj()).getTime()));
        }
        dspOrderDO.setPredicServiceStopTimeBj(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getPredicServiceStopTimeBj()).getTime()));

        dspOrderDO.setLastConfirmTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmTime()).getTime()));
        dspOrderDO.setLastConfirmTimeBj(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmTimeBj()).getTime()));
        dspOrderDO.setLastConfirmCarTime(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmCarTime()).getTime()));
        dspOrderDO.setLastConfirmCarTimeBj(new Timestamp(DateUtil.parseDateStr2Date(baseInfo.getLastConfirmCarTimeBj()).getTime()));
        dspOrderDO.setConnectMode(baseInfo.getConnectMode());
        dspOrderDO.setSalesMode(baseInfo.getSalesMode());

        dspOrderDO.setUseDays(new UseDays(baseInfo.getUseDays()));

        dspOrderDO.setEstimatedKm(baseInfo.getEstimatedKilomitors());
        dspOrderDO.setEstimatedMin(baseInfo.getEstimatedMinutes());
//        dspOrderDO.setActualKm();
//        dspOrderDO.setActualMin();
        dspOrderDO.setPriceMark(baseInfo.getPriceMark());
        dspOrderDO.setSpId(baseInfo.getSpId());
        dspOrderDO.setSpName(baseInfo.getSpName());
        dspOrderDO.setSupplierId(0);
        dspOrderDO.setCountryId(baseInfo.getCountryId());
        dspOrderDO.setProductType(baseInfo.getProductType());
        dspOrderDO.setTargetId(baseInfo.getTargetId());
        dspOrderDO.setTerminalId(baseInfo.getTerminalId());
        dspOrderDO.setBizAreaType(baseInfo.getBizAreaType());
        dspOrderDO.setTimeZone(baseInfo.getTimeZone());
        dspOrderDO.setDeptCarPlaceId(baseInfo.getDeptCarPlaceId());
        dspOrderDO.setArriveCarPlaceId(baseInfo.getArriveCarPlaceId());
        // 雷神山
        dspOrderDO.setPackageType(baseInfo.getPackageType());
        // 延后派订单立即预先确认 （订单确认时长优化项目）
        dspOrderDO.setIsPreConfirm(baseInfo.getIsPreConfirm());

        return dspOrderDO;
    }


    private static DspOrderDetailDO buildOrderDetail(BaseInfo baseInfo, ProductInfo productInfo) {
        DspOrderDetailDO dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setSupplyOrderId(baseInfo.getSupplyOrderId());
        dspOrderDetailDO.setUserOrderId(baseInfo.getUserOrderId());
        dspOrderDetailDO.setLocale(baseInfo.getLocale());
        dspOrderDetailDO.setServiceLanguageCodes(productInfo.getServiceLanguageCodes());
        dspOrderDetailDO.setSkuId(productInfo.getSkuId().intValue());
        dspOrderDetailDO.setCancelRule(productInfo.getCancelRuleJson());
        dspOrderDetailDO.setWaitingRule(productInfo.getWaitingRules());
        //跨境等待规则
        dspOrderDetailDO.setCrossWaitingRule(productInfo.getCrossWaitingRules());

        dspOrderDetailDO.setPriceResultCode(productInfo.getPriceResultCode());
        dspOrderDetailDO.setDetailSnapShotid(productInfo.getDetailSnapShotid());
        dspOrderDetailDO.setGiveFreeTime(productInfo.getGiveFreeTime());
        dspOrderDetailDO.setXproductInfo(JacksonUtil.serialize(productInfo.getXProducts()));
        dspOrderDetailDO.setSpContractInfo(baseInfo.getSpContractInfo());

        dspOrderDetailDO.setDspStrategyStr(productInfo.getDspStrategyStr());
        //跨境类型
        dspOrderDetailDO.setCrossBorderType(baseInfo.getCrossBorderType());

        // 雷神山项目 - 套餐信息
        dspOrderDetailDO.setPackageInfo(productInfo.getPackageInfo());
        // 雷神山项目 - 套餐时长
        dspOrderDetailDO.setxTimeLength(baseInfo.getXTimeLength());
        // 雷神山项目 - 套餐里程
        dspOrderDetailDO.setyKiloLength(baseInfo.getYKiloLength());
        // 分流标识
        dspOrderDetailDO.setShuntFlag(baseInfo.getShuntFlag());
        // 分流供应商ID
        dspOrderDetailDO.setShuntSupplierId(baseInfo.getShuntSupplierId());
        // 延后派立即预先确认订单（订单确认时长项目）
        dspOrderDetailDO.setIsPreConfirm(baseInfo.getIsPreConfirm());
        // 用户自选bufffer
        dspOrderDetailDO.setSelfBuffer(baseInfo.getSelfBuffer());
        return dspOrderDetailDO;
    }

    private static DspOrderFeeDO buildFee(BaseInfo baseInfo, FeeInfo feeInfo) {
        DspOrderFeeDO dspOrderFeeDO = new DspOrderFeeDO();
        dspOrderFeeDO.setUserOrderId(baseInfo.getUserOrderId());
        dspOrderFeeDO.setSalesAmount(feeInfo.getSalesAmount());
        dspOrderFeeDO.setCostAmount(feeInfo.getCostAmount());
        dspOrderFeeDO.setTollFee(feeInfo.getTollFee());
        dspOrderFeeDO.setParkingFee(feeInfo.getParkingFee());
        dspOrderFeeDO.setPeakSeasonIncreaseMoney(feeInfo.getPeakSeasonIncreaseMoney());

        dspOrderFeeDO.setNoCommisionAmount(feeInfo.getNoCommisionAmount());
        dspOrderFeeDO.setSupplierBornePrice(JacksonUtil.serialize(feeInfo.getSupplierBornePrice()));
        dspOrderFeeDO.setPremiumPrice(JacksonUtil.serialize(feeInfo.getPremiumPriceList()));
        dspOrderFeeDO.setUserCurrency(feeInfo.getUserCurrency());
        dspOrderFeeDO.setSupplierCurrency(feeInfo.getSupplierCurrency());
        dspOrderFeeDO.setSc2cnyExchangeRate(feeInfo.getSc2cnyExchangeRate());

        dspOrderFeeDO.setCny2ucExchangeRate(feeInfo.getCny2ucExchangeRate());
        dspOrderFeeDO.setSc2ucExchangeRate(feeInfo.getSc2ucExchangeRate());
        dspOrderFeeDO.setDspAddPrice(feeInfo.getDspAddPrice());
        dspOrderFeeDO.setNonOccupancyAmount(feeInfo.getEmptyDrivingFee());
        if (feeInfo.getTollFee()!= null && feeInfo.getTollFee().compareTo(BigDecimal.ZERO) > 0) {
            //下单时要把高速费抹掉，完成后司机会重新录入
            BigDecimal tollAmount = feeInfo.getTollFee();
            dspOrderFeeDO.setCostAmount(dspOrderFeeDO.getCostAmount().subtract(tollAmount));
            dspOrderFeeDO.setNoCommisionAmount(dspOrderFeeDO.getNoCommisionAmount().subtract(tollAmount));
            dspOrderFeeDO.setTollFee(BigDecimal.ZERO);
        }
        // 途经点费用
        dspOrderFeeDO.setWaypointAmount(feeInfo.getWaypointAmount());
        //增加原始高速费赋值
        dspOrderFeeDO.setOriTollFee(feeInfo.getTollFee());
        dspOrderFeeDO.setDriverCurrency(feeInfo.getDriverCurrency());
        dspOrderFeeDO.setSc2driverExchangeRate(feeInfo.getSc2driverExchangeRate());
        dspOrderFeeDO.setUc2driverExchangeRate(feeInfo.getUc2driverExchangeRate());
        return dspOrderFeeDO;
    }

    private static PlatformPriceStrategyDO buildStrategy() {
        PlatformPriceStrategyDO platformPriceStrategyDO = new PlatformPriceStrategyDO();
        return platformPriceStrategyDO;
    }
}
