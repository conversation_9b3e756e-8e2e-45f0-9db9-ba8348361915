package com.ctrip.dcs.application.provider;

import com.ctrip.dcs.application.provider.executor.SupplierDiversionListExecutor;
import com.ctrip.dcs.infrastructure.adapter.carconfig.SupplierDiversionConfig;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierDiversionListResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class SupplierDiversionListExecutorTest {

    @InjectMocks
    SupplierDiversionListExecutor supplierDiversionListExecutor;

    @Mock
    SupplierDiversionConfig supplierDiversionConfig;

    @Test
    public void test() {
        // case1:参数异常-空
        SupplierDiversionListRequestType requestType = new SupplierDiversionListRequestType();
        SupplierDiversionListResponseType responseType = supplierDiversionListExecutor.execute(requestType);
        Assert.assertTrue(responseType.getResponseResult().getReturnCode().equals("09011000"));

        // case2:参数异常-无品类
        SupplierDiversionListRequestType requestType2 = new SupplierDiversionListRequestType();
        requestType2.setCityId(1);
        SupplierDiversionListResponseType responseType2 = supplierDiversionListExecutor.execute(requestType2);
        Assert.assertTrue(responseType2.getResponseResult().getReturnCode().equals("09011000"));

        // case3:参数异常-无城市
        SupplierDiversionListRequestType requestType3 = new SupplierDiversionListRequestType();
        requestType3.setCategoryCode("categoryCode");
        SupplierDiversionListResponseType responseType3 = supplierDiversionListExecutor.execute(requestType3);
        Assert.assertTrue(responseType3.getResponseResult().getReturnCode().equals("09011000"));

        // case4:配置为空
        SupplierDiversionListRequestType requestType4 = new SupplierDiversionListRequestType();
        requestType4.setCityId(1);
        requestType4.setCategoryCode("categoryCode");

        Mockito.when(supplierDiversionConfig.getConfigValue(Mockito.anyInt(), Mockito.anyString())).thenReturn(Lists.newArrayList());

        SupplierDiversionListResponseType responseType4 = supplierDiversionListExecutor.execute(requestType4);
        Assert.assertTrue(CollectionUtils.isEmpty(responseType4.getSupplierDiversionList()));


        // case5:配置正常
        SupplierDiversionListRequestType requestType5 = new SupplierDiversionListRequestType();
        requestType5.setCityId(1);
        requestType5.setCategoryCode("categoryCode");

        SupplierDiversionConfig.Value value = new SupplierDiversionConfig.Value();
        value.setDiversionVal(new BigDecimal("100.1"));
        value.setSupplierId(1111);
        Mockito.when(supplierDiversionConfig.getConfigValue(Mockito.anyInt(), Mockito.anyString())).thenReturn(Lists.newArrayList(value));

        SupplierDiversionListResponseType responseType5 = supplierDiversionListExecutor.execute(requestType4);
        Assert.assertTrue(CollectionUtils.isNotEmpty(responseType5.getSupplierDiversionList()));

        // case5:异常
        SupplierDiversionListRequestType requestType6 = new SupplierDiversionListRequestType();
        requestType6.setCityId(1);
        requestType6.setCategoryCode("categoryCode");

        Mockito.when(supplierDiversionConfig.getConfigValue(Mockito.anyInt(), Mockito.anyString())).thenThrow(new NullPointerException());

        SupplierDiversionListResponseType responseType6 = supplierDiversionListExecutor.execute(requestType4);
        Assert.assertFalse(responseType6.getResponseResult().isSuccess());


    }

}
