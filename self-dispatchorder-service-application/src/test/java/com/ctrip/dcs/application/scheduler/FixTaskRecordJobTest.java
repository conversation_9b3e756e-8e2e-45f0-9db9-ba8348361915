package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKGrabTaskRecordDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import java.sql.SQLException;

@RunWith(MockitoJUnitRunner.class)
public class FixTaskRecordJobTest {

    @InjectMocks
    private FixTaskRecordJob fixTaskRecordJob;


    @Mock
    private VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository;
    @Mock
    private VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository;


    @Test
    public void execute() throws SQLException {
        fixTaskRecordJob.execute(null);
        Mockito.verify(vbkGrabTaskRecordRepository, Mockito.times(0)).queryBySupplierId(Mockito.any());
    }


    @Test
    public void updateSupplierIdById() {
        VBKGrabTaskRecordDO vbkGrabTaskRecordDO = new VBKGrabTaskRecordDO();
        vbkGrabTaskRecordDO.setId(1L);
        vbkGrabTaskRecordDO.setSupplierId(1L);
        vbkGrabTaskRecordDO.setOperationType("1");
        vbkGrabTaskRecordDO.setOperatorName("1");

        VBKDriverGrabTaskDO vbkDriverGrabTaskDO = new VBKDriverGrabTaskDO();
        vbkDriverGrabTaskDO.setVbkGrabTaskId("1");
        vbkDriverGrabTaskDO.setSupplierId(1L);
        PowerMockito.when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(Mockito.any())).thenReturn(vbkDriverGrabTaskDO);
        boolean b = fixTaskRecordJob.updateSupplierIdById(vbkGrabTaskRecordDO);
        Assert.assertTrue(b);
    }


}