package com.ctrip.dcs.application.processor;

import com.ctrip.dcs.domain.dsporder.repository.DspOrderChangeDriverRecordRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderChangeDriverRecordVO;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CreateDriverChangeRecordProcessorTest {

    @InjectMocks
    private CreateDriverChangeRecordProcessor processor;

    @Mock
    private DspOrderChangeDriverRecordRepository dspOrderChangeDriverRecordRepository;

    @Mock
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Test
    public void testProcess_NewConfirmRecordIdZero() {
        // 执行测试方法
        processor.process(123L, 0L);

        // 验证不会查询和插入记录
        verify(dspOrderConfirmRecordRepository, never()).find(anyLong());
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_Success() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        DspOrderConfirmRecordVO oldConfirmRecord = createConfirmRecord("order123", "user456", 789L, 101L, "admin");
        DspOrderConfirmRecordVO newConfirmRecord = createConfirmRecord("order123", "user456", 789L, 202L, "admin");

        // 设置 mock 行为
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(oldConfirmRecord);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId))).thenReturn(newConfirmRecord);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 捕获并验证插入的记录
        ArgumentCaptor<DspOrderChangeDriverRecordVO> recordCaptor = ArgumentCaptor.forClass(DspOrderChangeDriverRecordVO.class);
        verify(dspOrderChangeDriverRecordRepository).insertRecord(recordCaptor.capture());

        DspOrderChangeDriverRecordVO capturedRecord = recordCaptor.getValue();
        assertNotNull(capturedRecord);
        assertEquals("order123", capturedRecord.getDspOrderId());
        assertEquals("user456", capturedRecord.getUserOrderId());
        assertEquals(789, (int)capturedRecord.getSupplierId());
        assertEquals("101", capturedRecord.getFromDriverId());
        assertEquals("202", capturedRecord.getToDriverId());
        assertEquals("admin", capturedRecord.getOperator());
        assertNotNull(capturedRecord.getOperateTime());
    }

    @Test
    public void testProcess_OldConfirmRecordNotFound() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        // 设置 mock 行为：原记录未找到
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(null);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderConfirmRecordRepository).find(originalConfirmRecordId);
        verify(dspOrderConfirmRecordRepository, never()).find(newConfirmRecordId);
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_NewConfirmRecordNotFound() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        DspOrderConfirmRecordVO oldConfirmRecord = createConfirmRecord("order123", "user456", 789L, 101L, "admin");

        // 设置 mock 行为：原记录找到，新记录未找到
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(oldConfirmRecord);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId))).thenReturn(null);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderConfirmRecordRepository).find(originalConfirmRecordId);
        verify(dspOrderConfirmRecordRepository).find(newConfirmRecordId);
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_NullDriverInfo() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        // 创建没有司机信息的记录
        DspOrderConfirmRecordVO oldConfirmRecord = new DspOrderConfirmRecordVO();
        oldConfirmRecord.setDspOrderId("order123");
        oldConfirmRecord.setUserOrderId("user456");

        DspOrderConfirmRecordVO.SupplierRecord supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord();
        supplierInfo.setSupplierId(789L);
        oldConfirmRecord.setSupplierInfo(supplierInfo);

        // 没有设置 DriverInfo

        DspOrderConfirmRecordVO newConfirmRecord = createConfirmRecord("order123", "user456", 789L, 202L, "admin");

        // 设置 mock 行为
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(oldConfirmRecord);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId))).thenReturn(newConfirmRecord);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_NullSupplierInfo() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        // 创建没有供应商信息的记录
        DspOrderConfirmRecordVO oldConfirmRecord = new DspOrderConfirmRecordVO();
        oldConfirmRecord.setDspOrderId("order123");
        oldConfirmRecord.setUserOrderId("user456");

        // 没有设置 SupplierInfo

        DspOrderConfirmRecordVO.DriverRecord driverInfo = new DspOrderConfirmRecordVO.DriverRecord();
        driverInfo.setDriverId(101L);
        oldConfirmRecord.setDriverInfo(driverInfo);

        DspOrderConfirmRecordVO newConfirmRecord = createConfirmRecord("order123", "user456", 789L, 202L, "admin");

        // 设置 mock 行为
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(oldConfirmRecord);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId))).thenReturn(newConfirmRecord);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_NullOperateRecord() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        DspOrderConfirmRecordVO oldConfirmRecord = createConfirmRecord("order123", "user456", 789L, 101L, "admin");

        // 创建没有操作记录的新记录
        DspOrderConfirmRecordVO newConfirmRecord = new DspOrderConfirmRecordVO();
        newConfirmRecord.setDspOrderId("order123");
        newConfirmRecord.setUserOrderId("user456");

        DspOrderConfirmRecordVO.SupplierRecord supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord();
        supplierInfo.setSupplierId(789L);
        newConfirmRecord.setSupplierInfo(supplierInfo);

        DspOrderConfirmRecordVO.DriverRecord driverInfo = new DspOrderConfirmRecordVO.DriverRecord();
        driverInfo.setDriverId(202L);
        newConfirmRecord.setDriverInfo(driverInfo);

        // 没有设置 OperateRecord

        // 设置 mock 行为
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenReturn(oldConfirmRecord);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId))).thenReturn(newConfirmRecord);

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_RepositoryException() {
        // 准备测试数据
        long originalConfirmRecordId = 123L;
        long newConfirmRecordId = 456L;

        // 设置 mock 行为：抛出异常
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId))).thenThrow(new RuntimeException("Repository error"));

        // 执行测试方法
        processor.process(originalConfirmRecordId, newConfirmRecordId);

        // 验证不会插入记录
        verify(dspOrderChangeDriverRecordRepository, never()).insertRecord(any());
    }

    @Test
    public void testProcess_MultipleSuccessCalls() {
        // 准备测试数据 - 第一次调用
        long originalConfirmRecordId1 = 123L;
        long newConfirmRecordId1 = 456L;
        DspOrderConfirmRecordVO oldConfirmRecord1 = createConfirmRecord("order123", "user456", 789L, 101L, "admin1");
        DspOrderConfirmRecordVO newConfirmRecord1 = createConfirmRecord("order123", "user456", 789L, 202L, "admin1");

        // 准备测试数据 - 第二次调用
        long originalConfirmRecordId2 = 789L;
        long newConfirmRecordId2 = 1011L;
        DspOrderConfirmRecordVO oldConfirmRecord2 = createConfirmRecord("order789", "user101", 789L, 303L, "admin2");
        DspOrderConfirmRecordVO newConfirmRecord2 = createConfirmRecord("order789", "user101", 789L, 404L, "admin2");

        // 设置 mock 行为
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId1))).thenReturn(oldConfirmRecord1);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId1))).thenReturn(newConfirmRecord1);
        when(dspOrderConfirmRecordRepository.find(eq(originalConfirmRecordId2))).thenReturn(oldConfirmRecord2);
        when(dspOrderConfirmRecordRepository.find(eq(newConfirmRecordId2))).thenReturn(newConfirmRecord2);

        // 执行测试方法
        processor.process(originalConfirmRecordId1, newConfirmRecordId1);
        processor.process(originalConfirmRecordId2, newConfirmRecordId2);

        // 验证交互次数
        verify(dspOrderConfirmRecordRepository, times(4)).find(anyLong());
        verify(dspOrderChangeDriverRecordRepository, times(2)).insertRecord(any());

        // 捕获并验证插入的记录
        ArgumentCaptor<DspOrderChangeDriverRecordVO> recordCaptor = ArgumentCaptor.forClass(DspOrderChangeDriverRecordVO.class);
        verify(dspOrderChangeDriverRecordRepository, times(2)).insertRecord(recordCaptor.capture());

        // 验证两次调用的记录
        assertEquals(2, recordCaptor.getAllValues().size());

        DspOrderChangeDriverRecordVO firstRecord = recordCaptor.getAllValues().get(0);
        assertEquals("order123", firstRecord.getDspOrderId());
        assertEquals("101", firstRecord.getFromDriverId());
        assertEquals("202", firstRecord.getToDriverId());

        DspOrderChangeDriverRecordVO secondRecord = recordCaptor.getAllValues().get(1);
        assertEquals("order789", secondRecord.getDspOrderId());
        assertEquals("303", secondRecord.getFromDriverId());
        assertEquals("404", secondRecord.getToDriverId());
    }

    // 帮助方法：创建司机变更记录
    private DspOrderConfirmRecordVO createConfirmRecord(String dspOrderId, String userOrderId,
                                                        Long supplierId, Long driverId, String operator) {
        DspOrderConfirmRecordVO confirmRecord = new DspOrderConfirmRecordVO();
        confirmRecord.setDspOrderId(dspOrderId);
        confirmRecord.setUserOrderId(userOrderId);

        DspOrderConfirmRecordVO.SupplierRecord supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord();
        supplierInfo.setSupplierId(supplierId);
        confirmRecord.setSupplierInfo(supplierInfo);

        DspOrderConfirmRecordVO.DriverRecord driverInfo = new DspOrderConfirmRecordVO.DriverRecord();
        driverInfo.setDriverId(driverId);
        confirmRecord.setDriverInfo(driverInfo);

        DspOrderConfirmRecordVO.OperateRecord operateRecord = new DspOrderConfirmRecordVO.OperateRecord();
        operateRecord.setOperateUserAccount(operator);
        confirmRecord.setOperateRecord(operateRecord);

        return confirmRecord;
    }
}
