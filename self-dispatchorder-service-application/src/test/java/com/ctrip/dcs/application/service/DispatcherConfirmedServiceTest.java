package com.ctrip.dcs.application.service;

import com.ctrip.dcs.application.command.PushOrderRemindExeCmd;
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd;
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd;
import com.ctrip.dcs.application.command.dispatchergrab.Approve2SuccessDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd;
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.VBKOperationRecordVO;
import com.ctrip.dcs.domain.dsporder.entity.WorkBenchLogMessage;
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway;
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory;
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;

import java.util.Collections;

@RunWith(MockitoJUnitRunner.class)
public class DispatcherConfirmedServiceTest {

    @InjectMocks
    private DispatcherConfirmedService dispatcherConfirmedService;

    @Mock
    private QueryDspOrderService queryDspOrderService;

    @Mock
    private QueryTransportGroupService queryTransportGroupService;

    @Mock
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Mock
    private PushOrderRemindExeCmd pushOrderRemindExeCmd;

    @Mock
    private WorkBenchLogMessageFactory workBenchLogMessageFactory;

    @Mock
    private WorkBenchLogGateway workBenchLogGateway;

    @Mock
    private VBKOperationRecordFactory vbkOperationRecordFactory;

    @Mock
    private VBKOperationRecordGateway vbkOperationRecordGateway;

    @Mock
    private ShutdownScheduleExeCmd shutdownScheduleExeCmd;

    @Mock
    private ScheduleRepository scheduleRepository;

    @Mock
    private CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd;

    @Mock
    private GrabCentreRepository grabCentreRepository;

    @Mock
    private DspOrderVO dspOrderVO;

    @Mock
    private TransportGroupVO transportGroupVO;

    @Mock
    private DspOrderConfirmRecordVO dspOrderConfirmRecordVO;

    @Mock
    private UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd;

    @Mock
    private UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd;

    @Mock
    Approve2SuccessDispatcherGrabOrderExeCmd approve2SuccessDispatcherGrabOrderExeCmd;

    @Mock
    private ScheduleDO scheduleDO;
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository;

    @Test
    public void testConfirmed() {
        PowerMockito.when(dspOrderConfirmRecordVO.getSupplierInfo()).thenReturn(DspOrderConfirmRecordVO.SupplierRecord.builder().supplierId(1L).build());
        PowerMockito.when(dspOrderVO.getUserOrderId()).thenReturn("123");
        PowerMockito.when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT);
        PowerMockito.when(queryDspOrderService.query(Mockito.any())).thenReturn(dspOrderVO);
        PowerMockito.when(queryTransportGroupService.queryTransportGroup(Mockito.any())).thenReturn(transportGroupVO);
        PowerMockito.when(dspOrderConfirmRecordRepository.find(Mockito.any())).thenReturn(dspOrderConfirmRecordVO);
        dispatcherConfirmedService.confirmed("19162292994736224",3019162292994736227L, 235L);
        Mockito.verify(grabCentreRepository, Mockito.times(1)).deleteAll(Mockito.any());
    }

    @Test
    public void testPushOrderRemind() {
        PowerMockito.when(dspOrderVO.getUid()).thenReturn("test1");
        dispatcherConfirmedService.pushOrderRemind(dspOrderVO, transportGroupVO);
        Mockito.verify(pushOrderRemindExeCmd, Mockito.times(0)).execute(Mockito.any());
    }

    @Test
    public void testPushOrderRemindException() {
        PowerMockito.doThrow(new RuntimeException()).when(pushOrderRemindExeCmd).execute(Mockito.any());
        dispatcherConfirmedService.pushOrderRemind(dspOrderVO, transportGroupVO);
        Mockito.verify(pushOrderRemindExeCmd, Mockito.times(1)).execute(Mockito.any());
    }

    @Test
    public void testSendWorkBenchLog() {
        PowerMockito.when(workBenchLogMessageFactory.createDispatcherConfirmWorkBenchLog(dspOrderVO, dspOrderConfirmRecordVO)).thenReturn(new WorkBenchLogMessage());
        dispatcherConfirmedService.sendWorkBenchLog(dspOrderVO, dspOrderConfirmRecordVO);
        Mockito.verify(workBenchLogGateway, Mockito.times(1)).sendWorkBenchLogMessage(Mockito.any());
    }

    @Test
    public void testSendWorkBenchLogException() {
        PowerMockito.when(workBenchLogMessageFactory.createDispatcherConfirmWorkBenchLog(dspOrderVO, dspOrderConfirmRecordVO)).thenThrow(new RuntimeException());
        dispatcherConfirmedService.sendWorkBenchLog(dspOrderVO, dspOrderConfirmRecordVO);
        Mockito.verify(workBenchLogGateway, Mockito.times(0)).sendWorkBenchLogMessage(Mockito.any());
    }

    @Test
    public void testSendVBKOperationRecord() {
        PowerMockito.when(vbkOperationRecordFactory.createOperationRecord(dspOrderVO, dspOrderConfirmRecordVO)).thenReturn(VBKOperationRecordVO.builder().build());
        dispatcherConfirmedService.sendVBKOperationRecord(dspOrderVO, dspOrderConfirmRecordVO);
        Mockito.verify(vbkOperationRecordGateway, Mockito.times(1)).record(Mockito.any());
    }

    @Test
    public void testSendVBKOperationRecordException() {
        PowerMockito.when(vbkOperationRecordFactory.createOperationRecord(dspOrderVO, dspOrderConfirmRecordVO)).thenThrow(new RuntimeException());
        dispatcherConfirmedService.sendVBKOperationRecord(dspOrderVO, dspOrderConfirmRecordVO);
        Mockito.verify(vbkOperationRecordGateway, Mockito.times(0)).record(Mockito.any());
    }

    @Test
    public void testShutdownSchedule() {
        PowerMockito.when(scheduleRepository.query(Mockito.any())).thenReturn(Collections.emptyList()).thenReturn(Collections.singletonList(scheduleDO));
        dispatcherConfirmedService.shutdownSchedule(dspOrderVO);
        dispatcherConfirmedService.shutdownSchedule(dspOrderVO);
        Mockito.verify(shutdownScheduleExeCmd, Mockito.times(1)).execute(Mockito.any());
    }

    @Test
    public void testShutdownScheduleException() {
        PowerMockito.when(scheduleRepository.query(Mockito.any())).thenThrow(new RuntimeException());
        dispatcherConfirmedService.shutdownSchedule(dspOrderVO);
        Mockito.verify(shutdownScheduleExeCmd, Mockito.times(0)).execute(Mockito.any());
    }

    @Test
    public void testCancelDispatcherGrabOrder() {
        PowerMockito.when(dspOrderVO.getUserOrderId()).thenReturn("1234");
        dispatcherConfirmedService.cancelDispatcherGrabOrder(dspOrderVO);
        Mockito.verify(cancelDispatcherGrabOrderExeCmd, Mockito.times(1)).execute(Mockito.any());
    }

    @Test
    public void testCancelDispatcherGrabOrderException() {
        PowerMockito.when(dspOrderVO.getUserOrderId()).thenReturn(null);
        dispatcherConfirmedService.cancelDispatcherGrabOrder(dspOrderVO);
        Mockito.verify(cancelDispatcherGrabOrderExeCmd, Mockito.times(0)).execute(Mockito.any());
    }
}
