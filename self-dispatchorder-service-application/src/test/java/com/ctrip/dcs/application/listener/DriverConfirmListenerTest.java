package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.processor.CreateDriverChangeRecordProcessor;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DriverConfirmListenerTest {

    @InjectMocks
    private DriverConfirmListener listener;

    @Mock
    private CreateDriverChangeRecordProcessor createDriverChangeRecordProcessor;

    @Test
    public void testOnDriverChangeRecordMessage_OriginalConfirmRecordIdIsZero() {
        // 准备测试数据
        Message message = mock(Message.class);
        when(message.getLongProperty("originalConfirmRecordId")).thenReturn(0L);

        // 执行方法
        listener.onDriverChangeRecordMessage(message);

        // 验证结果：不应该调用 processor
        verify(createDriverChangeRecordProcessor, never()).process(anyLong(), anyLong());
    }

    @Test
    public void testOnDriverChangeRecordMessage_NotUpdateDriverEvent() {
        // 准备测试数据
        Message message = mock(Message.class);
        when(message.getLongProperty("originalConfirmRecordId")).thenReturn(123L);
        // 使用非VBK_UPDATE_DRIVER事件
        when(message.getIntProperty("statusMachineEventCode")).thenReturn(
                OrderStatusEvent.VBK_UPDATE_DRIVER.getCode() + 1); // 使用不同的事件码

        // 执行方法
        listener.onDriverChangeRecordMessage(message);

        // 验证结果：不应该调用 processor
        verify(createDriverChangeRecordProcessor, never()).process(anyLong(), anyLong());
    }

    @Test
    public void testOnDriverChangeRecordMessage_UpdateDriverEvent() {
        // 准备测试数据
        Message message = mock(Message.class);
        when(message.getLongProperty("originalConfirmRecordId")).thenReturn(123L);
        when(message.getIntProperty("statusMachineEventCode")).thenReturn(
                OrderStatusEvent.VBK_UPDATE_DRIVER.getCode());
        when(message.getLongProperty("confirmRecordId")).thenReturn(456L);

        // 执行方法
        listener.onDriverChangeRecordMessage(message);

        // 验证结果：应该调用 processor，并传入正确的参数
        verify(createDriverChangeRecordProcessor, times(1)).process(123L, 456L);
    }

    @Test
    public void testOnDriverChangeRecordMessage_NullMessage() {
        // 测试传入null消息的情况
        try {
            listener.onDriverChangeRecordMessage(null);
        } catch (NullPointerException e) {
            // 预期会抛出NullPointerException
            // 在实际代码中可能需要添加空检查，但目前代码没有处理这种情况
        }

        // 验证结果：不应该调用processor
        verify(createDriverChangeRecordProcessor, never()).process(anyLong(), anyLong());
    }

    @Test
    public void testOnDriverChangeRecordMessage_WithErrorHandling() {
        // 准备测试数据
        Message message = mock(Message.class);
        when(message.getLongProperty("originalConfirmRecordId")).thenReturn(123L);
        when(message.getIntProperty("statusMachineEventCode")).thenReturn(
                OrderStatusEvent.VBK_UPDATE_DRIVER.getCode());
        when(message.getLongProperty("confirmRecordId")).thenReturn(456L);

        // 模拟处理器抛出异常
        doThrow(new RuntimeException("处理失败")).when(createDriverChangeRecordProcessor).process(123L, 456L);

        // 执行方法 - 验证异常是否正确传播
        try {
            listener.onDriverChangeRecordMessage(message);
        } catch (RuntimeException e) {
            // 预期会抛出异常
            assert e.getMessage().equals("处理失败");
        }

        // 验证调用
        verify(createDriverChangeRecordProcessor, times(1)).process(123L, 456L);
    }
}
