package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO;
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.NeedRetryException;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class SpContractInfoServiceTest {

    @InjectMocks
    SpContractInfoService service;

    @Mock
    DspOrderRepository dspOrderRepository;

    @Mock
    ScmMerchantServiceGateway scmMerchantServiceGateway;

    @Mock
    DspOrderDetailRepository dspOrderDetailRepository;

    @Test
    public void test22() {
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDO.setSupplierId(123);
        Mockito.when(dspOrderRepository.find(Mockito.anyString())).thenReturn(dspOrderDO);

        DspOrderDetailDO detailDO = new DspOrderDetailDO();
        Map<String, Object> map = Maps.newHashMap();
        map.put(SysConstants.Order.SHUNT_FLAG, 1);
        detailDO.setExtendInfo(JacksonSerializer.INSTANCE().serialize(map));
        Mockito.when(dspOrderDetailRepository.find(Mockito.anyString())).thenReturn(detailDO);

        try {
            service.updateContractInfo("123");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NeedRetryException);
        }
    }

}
