package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderFeePO;
import com.ctrip.igt.framework.common.exception.BizException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.schedule.MockParameter;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DspOrderNoCommissionAmountJobTest {

    @Mock
    private DspOrderFeeDao dspOrderFeeDao;

    @Mock
    protected MessageProviderService messageProducer;

    @InjectMocks
    private DspOrderNoCommissionAmountJob dspOrderNoCommissionAmountJob;

    @Test
    public void noCommissionJobTest(){
        MockParameter parameter = mock(MockParameter.class);
        when(parameter.getString("beginTime")).thenReturn( "2022-01-01 01:00:00");
        when(parameter.getString("endTime")).thenReturn("2022-01-01 02:00:00");
        when(parameter.getString("intervalMinutes")).thenReturn("5");
        try {
            List<DspOrderFeePO> dspOrderFeePOList = Lists.newArrayList();
            Mockito.when(dspOrderFeeDao.queryDspOrderFeeList(Mockito.any(),Mockito.any())).thenReturn(Lists.newArrayList());
            dspOrderNoCommissionAmountJob.noCommissionJob(parameter);
            DspOrderFeePO dspOrderFeePO = new DspOrderFeePO();
            dspOrderFeePO.setNoCommisionAmount(new BigDecimal("10"));
            dspOrderFeePO.setTollFee(new BigDecimal("1"));
            dspOrderFeePO.setParkingFee(new BigDecimal("2"));
            dspOrderFeePO.setWaypointAmount(new BigDecimal("7"));
            dspOrderFeePO.setDspOrderId("11");
            dspOrderFeePOList.add(dspOrderFeePO);
            DspOrderFeePO dspFee = new DspOrderFeePO();
            dspFee.setNoCommisionAmount(new BigDecimal("4"));
            dspFee.setTollFee(new BigDecimal("1"));
            dspFee.setParkingFee(new BigDecimal("2"));
            dspFee.setWaypointAmount(new BigDecimal("5"));
            dspFee.setDspOrderId("22");
            dspOrderFeePOList.add(dspFee);
            Mockito.when(dspOrderFeeDao.queryDspOrderFeeList(Mockito.any(),Mockito.any())).thenReturn(dspOrderFeePOList);
            dspOrderNoCommissionAmountJob.noCommissionJob(parameter);
        } catch (Exception e) {

        }
        MockParameter parameter1 = mock(MockParameter.class);
        when(parameter1.getString("beginTime")).thenReturn( "2022-01-01 02:00:00");
        when(parameter1.getString("endTime")).thenReturn("2022-01-01 00:00:00");
        when(parameter1.getString("intervalMinutes")).thenReturn("5");

        dspOrderNoCommissionAmountJob.noCommissionJob(parameter1);
        Assert.assertEquals(parameter.getString("intervalMinutes") ,"5");
    }

    @Test
    public void noCommissionJobTestNotEqual(){
        MockParameter parameter = mock(MockParameter.class);
        when(parameter.getString("beginTime")).thenReturn( "2022-01-01 01:00:00");
        when(parameter.getString("endTime")).thenReturn("2022-01-01 02:00:00");
        when(parameter.getString("intervalMinutes")).thenReturn("5");
        try {
            List<DspOrderFeePO> dspOrderFeePOList = Lists.newArrayList();
            DspOrderFeePO dspOrderFeePO = new DspOrderFeePO();
            dspOrderFeePO.setNoCommisionAmount(new BigDecimal("4"));
            dspOrderFeePO.setTollFee(new BigDecimal("1"));
            dspOrderFeePO.setParkingFee(new BigDecimal("2"));
            dspOrderFeePO.setWaypointAmount(new BigDecimal("7"));
            dspOrderFeePO.setDspOrderId("11");
            dspOrderFeePOList.add(dspOrderFeePO);
            Mockito.when(dspOrderFeeDao.queryDspOrderFeeList(Mockito.any(),Mockito.any())).thenReturn(dspOrderFeePOList);
            dspOrderNoCommissionAmountJob.noCommissionJob(parameter);
        } catch (Exception e) {

        }
        MockParameter parameter1 = mock(MockParameter.class);
        when(parameter1.getString("beginTime")).thenReturn( "2022-01-01 02:00:00");
        when(parameter1.getString("endTime")).thenReturn("2022-01-01 00:00:00");
        when(parameter1.getString("intervalMinutes")).thenReturn("5");

        dspOrderNoCommissionAmountJob.noCommissionJob(parameter1);
        Assert.assertEquals(parameter.getString("intervalMinutes") ,"5");
    }

    @Test
    public void noCommissionJobTestException(){
        MockParameter parameter = mock(MockParameter.class);
        when(parameter.getString("beginTime")).thenReturn( "2022-01-01 01:00:00");
        when(parameter.getString("endTime")).thenReturn("2022-01-01 02:00:00");
        when(parameter.getString("intervalMinutes")).thenReturn("5");
        try {
            List<DspOrderFeePO> dspOrderFeePOList = Lists.newArrayList();
            Mockito.when(dspOrderFeeDao.queryDspOrderFeeList(Mockito.any(),Mockito.any())).thenThrow(new BizException("dd"));
            dspOrderNoCommissionAmountJob.noCommissionJob(parameter);
            DspOrderFeePO dspOrderFeePO = new DspOrderFeePO();
            dspOrderFeePO.setNoCommisionAmount(new BigDecimal("10"));
            dspOrderFeePO.setTollFee(new BigDecimal("1"));
            dspOrderFeePO.setParkingFee(new BigDecimal("2"));
            dspOrderFeePO.setWaypointAmount(new BigDecimal("7"));
            dspOrderFeePO.setDspOrderId("11");
            dspOrderFeePOList.add(dspOrderFeePO);
            DspOrderFeePO dspFee = new DspOrderFeePO();
            dspFee.setNoCommisionAmount(new BigDecimal("4"));
            dspFee.setTollFee(new BigDecimal("1"));
            dspFee.setParkingFee(new BigDecimal("2"));
            dspFee.setWaypointAmount(new BigDecimal("5"));
            dspFee.setDspOrderId("22");
            dspOrderFeePOList.add(dspFee);
            Mockito.when(dspOrderFeeDao.queryDspOrderFeeList(Mockito.any(),Mockito.any())).thenReturn(dspOrderFeePOList);
            dspOrderNoCommissionAmountJob.noCommissionJob(parameter);
        } catch (Exception e) {

        }
        MockParameter parameter1 = mock(MockParameter.class);
        when(parameter1.getString("beginTime")).thenReturn( "2022-01-01 02:00:00");
        when(parameter1.getString("endTime")).thenReturn("2022-01-01 00:00:00");
        when(parameter1.getString("intervalMinutes")).thenReturn("5");

        dspOrderNoCommissionAmountJob.noCommissionJob(parameter1);
        Assert.assertEquals(parameter.getString("intervalMinutes") ,"5");
    }


}
