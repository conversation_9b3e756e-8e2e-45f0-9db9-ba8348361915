package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.service.SendEmailService;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

@RunWith(MockitoJUnitRunner.class)
public class UseTimeChangeListenerTest {


    @InjectMocks
    UseTimeChangeListener useTimeChangeListener;

    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway;

    @Mock
    BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @Mock
    QueryTransportGroupService queryTransportGroupService;

    @Mock
    SendEmailService sendEmailService;

    @Test
    public void test1() {
        Message message = new BaseMessage();
        message.setProperty("supplierOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("supplierOrderId").equals("20919006437867553"));
    }

    @Test
    public void test2() {

        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(null);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test3() {

        TransportGroupVO groupVO = new TransportGroupVO();
        groupVO.setInformEmail("<EMAIL>");
        groupVO.setInformSwitch(1);
        Mockito.when(queryTransportGroupService.queryTransportGroup(Mockito.anyLong())).thenReturn(groupVO);

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("110");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test4() {
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenThrow(new NullPointerException());
        try {
            Message message = new BaseMessage();
            message.setProperty("dspOrderId", "20919006437867553");
            message.setProperty("driverId", 100004);
            useTimeChangeListener.onMessageNoticeDispatcher(message);
            Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void test5() {

        TransportGroupVO groupVO = new TransportGroupVO();
        groupVO.setInformEmail("<EMAIL>");
        groupVO.setInformSwitch(0);
        Mockito.when(queryTransportGroupService.queryTransportGroup(Mockito.anyLong())).thenReturn(groupVO);

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("110");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test6() {

        TransportGroupVO groupVO = new TransportGroupVO();
        groupVO.setInformEmail("");
        groupVO.setInformSwitch(1);
        Mockito.when(queryTransportGroupService.queryTransportGroup(Mockito.anyLong())).thenReturn(groupVO);

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("110");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test7() {

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test8() {

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("999");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test9() {

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("999");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(null);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test10() {

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("110");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(900);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

    @Test
    public void test11() {

        TransportGroupVO groupVO = new TransportGroupVO();
        groupVO.setInformEmail("");
        groupVO.setInformSwitch(1);
        Mockito.when(queryTransportGroupService.queryTransportGroup(Mockito.anyLong())).thenReturn(null);

        Mockito.when(businessTemplateInfoConfig.getValueByKey(Mockito.anyString())).thenReturn("110");

        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setTransportGroupId(1L);
        baseDetailVO.setSupplierId(110);
        baseDetailVO.setOrderStatus(300);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.anyString())).thenReturn(baseDetailVO);

        Message message = new BaseMessage();
        message.setProperty("dspOrderId", "20919006437867553");
        message.setProperty("driverId", 100004);
        useTimeChangeListener.onMessageNoticeDispatcher(message);

        Assert.assertTrue(message.getStringProperty("dspOrderId").equals("20919006437867553"));
    }

}
