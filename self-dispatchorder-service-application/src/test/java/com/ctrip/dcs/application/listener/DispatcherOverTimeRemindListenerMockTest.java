package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.domain.common.service.IvrCallService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.service.VbkAppPushService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.infrastructure.adapter.redis.IDispatchModifyInfoCacheService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

import java.util.Date;

import static com.ctrip.dcs.domain.common.util.DateUtil.DATETIME_FORMAT;

@RunWith(MockitoJUnitRunner.class)
public class DispatcherOverTimeRemindListenerMockTest {

    @Mock
    private DspOrderRepository dspOrderRepository;
    @Mock
    private IDispatchModifyInfoCacheService cacheService;

    @InjectMocks
    private SupplyOrderRedispatchCancelListener supplyOrderRedispatchCancelListener;

    @InjectMocks
    private DispatcherOverTimeRemindListener dispatcherOverTimeRemindListener;
    @Mock
    private QueryDspOrderService queryDspOrderService;

    @Mock
    private IvrCallService ivrCallService;

    @Mock
    private VbkAppPushService timeOutNoDispatchService;

    @Test
    public void onMessage() {
        Message message = new BaseMessage();
        message.setProperty("supplierOrderId","20919006437867553");
        message.setProperty("driverId",100004);
        supplyOrderRedispatchCancelListener.onMessage(message);
        message.setProperty("userOrderId","20919006437867553");
        supplyOrderRedispatchCancelListener.onMessage(message);
        Assert.assertEquals(message.getStringProperty("supplierOrderId"), "20919006437867553");
    }

    @Test
    public void onMessageTest(){
        Message message = new BaseMessage();
        message.setProperty("dspOrderId","20919006437867553");
        message.setProperty("lastConfirmCarTime","2025-03-13 15:00:00");
        message.setProperty("triggerType","20");
        DspOrderVO dspOrderVO = new DspOrderVO();
        Mockito.when(queryDspOrderService.query(Mockito.anyString())).thenReturn(dspOrderVO);

        dispatcherOverTimeRemindListener.onMessage(message);
        dspOrderVO.setLastConfirmCarTime(new Date());
        Mockito.when(queryDspOrderService.query(Mockito.anyString())).thenReturn(dspOrderVO);
        dispatcherOverTimeRemindListener.onMessage(message);
        dspOrderVO.setLastConfirmCarTime(DateUtil.parse("2025-03-13 15:00:00",DATETIME_FORMAT));
        Mockito.when(queryDspOrderService.query(Mockito.anyString())).thenReturn(dspOrderVO);
        dispatcherOverTimeRemindListener.onMessage(message);
        Assert.assertTrue(dspOrderVO.getLastConfirmCarTime() != null);
    }
}