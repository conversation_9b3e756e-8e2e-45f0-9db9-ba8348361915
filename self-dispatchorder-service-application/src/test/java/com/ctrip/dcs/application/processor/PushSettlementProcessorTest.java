package com.ctrip.dcs.application.processor;

import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository;
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DriverOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDetailDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderDetailPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderFeePO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderRewardPO;
import com.ctrip.dcs.infrastructure.common.constants.PushSettlementTypeEnum;
import com.ctrip.dcs.infrastructure.service.QueryDspOrderServiceImpl;
import com.ctrip.dcs.settlement.mq.SettlePriceMessage;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PushSettlementProcessorTest {

    @InjectMocks
    private PushSettlementProcessor processor;

    @Mock
    private QueryDspOrderServiceImpl queryDspOrderService;

    @Mock
    private DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository;

    @Mock
    private DspOrderFeeDao dspOrderFeeDao;

    @Mock
    private DspOrderDao dspOrderDao;

    @Mock
    private DriverOrderDao driverOrderDao;

    @Mock
    private MessageProviderService messageProvider;

    @Mock
    private DspOrderDetailDao dspOrderDetailDao;

    private String dspOrderId = "DSP123";
    private Long confirmRecordId = 456L;
    private String driverOrderId = "DRV789";

    @Before
    public void setup() throws SQLException {
        // 设置基础数据
        DspOrderPO dspOrder = new DspOrderPO();
        dspOrder.setSalesMode(5);
        when(dspOrderDao.findByDspOrderId(dspOrderId)).thenReturn(dspOrder);

        DspOrderConfirmRecordVO confirmRecord = new DspOrderConfirmRecordVO();
        confirmRecord.setUserOrderId("USER123");
        confirmRecord.setSupplyOrderId("SUPPLY456");
        confirmRecord.setVbkRewardAmount("100");
        when(dspOrderConfirmRecordRepository.find(confirmRecordId)).thenReturn(confirmRecord);

        DspOrderFeePO dspOrderFee = buildDspOrderFeePO();
        when(dspOrderFeeDao.find(dspOrderId)).thenReturn(dspOrderFee);

        DrvOrderPO drvOrder = new DrvOrderPO();
        drvOrder.setSettleToDriver(1);
        when(driverOrderDao.queryByDriverOrderId(driverOrderId, dspOrderId)).thenReturn(Lists.newArrayList(drvOrder));

        List<DspOrderRewardPO> rewards = buildDspOrderRewardPOs();
        when(queryDspOrderService.queryDspOrderRewardPOs(dspOrderId)).thenReturn(rewards);

        DspOrderDetailPO dspOrderDetail = new DspOrderDetailPO();
        dspOrderDetail.setUserOrderId("USER123");
        dspOrderDetail.setSupplyOrderId("SUPPLY456");
        dspOrderDetail.setSkuId(123);
        when(dspOrderDetailDao.find(dspOrderId)).thenReturn(dspOrderDetail);
    }

    @Test
    public void testProcessHappyPath() throws SQLException {
        // 执行
        processor.process(dspOrderId, confirmRecordId, driverOrderId, PushSettlementTypeEnum.SUPPLY_ORDER_TAKEN);

        // 验证
        verify(messageProvider, times(1)).sendMessage(eq("dcs.purchase.supplyorder.price.notify"),
                any(HashMap.class), any());
    }

    @Test
    public void testProcessWithNullDspOrder() throws SQLException {
        // 设置
        when(dspOrderDao.findByDspOrderId(dspOrderId)).thenReturn(null);

        // 执行
        processor.process(dspOrderId, confirmRecordId, driverOrderId, PushSettlementTypeEnum.SUPPLY_ORDER_TAKEN);

        // 验证
        verify(messageProvider, times(1)).sendMessage(anyString(), any(HashMap.class), any());
    }

    @Test
    public void testProcessWithNullConfirmRecord() throws SQLException {
        // 设置
        when(dspOrderConfirmRecordRepository.find(confirmRecordId)).thenReturn(null);

        // 执行
        processor.process(dspOrderId, confirmRecordId, driverOrderId, PushSettlementTypeEnum.SUPPLY_ORDER_TAKEN);

        // 验证
        verify(messageProvider, times(1)).sendMessage(anyString(), any(HashMap.class), any());
    }

    @Test
    public void testPushSettlementMessageWithException() {
        // 设置
        doThrow(new RuntimeException("Mock error")).when(messageProvider)
                .sendMessage(anyString(), any(HashMap.class), any());

        // 执行
        SettlePriceMessage message = new SettlePriceMessage();
        message.setUserOrderId("USER123");
        processor.pushSettlementMessage(message, PushSettlementTypeEnum.SUPPLY_ORDER_TAKEN);

        // 验证异常被捕获且记录
        verify(messageProvider, times(1)).sendMessage(anyString(), any(HashMap.class), any());
    }

    private DspOrderFeePO buildDspOrderFeePO() {
        DspOrderFeePO dspOrderFee = new DspOrderFeePO();
        dspOrderFee.setUserCurrency("CNY");
        dspOrderFee.setSupplierCurrency("USD");
        dspOrderFee.setDriverCurrency("CNY");
        dspOrderFee.setSc2cnyExchangeRate(new BigDecimal("6.5"));
        dspOrderFee.setCny2ucExchangeRate(new BigDecimal("1.0"));
        dspOrderFee.setSc2driverExchangeRate(new BigDecimal("6.5"));
        dspOrderFee.setUc2driverExchangeRate(new BigDecimal("1.0"));
        dspOrderFee.setDspAddPrice(new BigDecimal("50"));

        // Mock premium price JSON
        List<Map<String, Object>> premiumPriceList = new ArrayList<>();
        Map<String, Object> priceMap = new HashMap<>();
        priceMap.put("strategyId", "STRATEGY_001");
        priceMap.put("feeAmount", "100");
        premiumPriceList.add(priceMap);
        dspOrderFee.setPremiumPrice(premiumPriceList.toString());

        return dspOrderFee;
    }

    private List<DspOrderRewardPO> buildDspOrderRewardPOs() {
        DspOrderRewardPO reward = new DspOrderRewardPO();
        reward.setRuleSceneId("SCENE_001");
        reward.setCurrencyCode("CNY");
        reward.setReward2driverExchangeRate(new BigDecimal("1.0"));
        reward.setTotalAmount(new BigDecimal("200"));
        return Lists.newArrayList(reward);
    }
}
