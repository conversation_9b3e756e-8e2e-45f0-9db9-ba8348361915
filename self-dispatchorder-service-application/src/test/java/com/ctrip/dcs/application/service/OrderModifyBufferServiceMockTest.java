package com.ctrip.dcs.application.service;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.PartyType;
import com.ctrip.dcs.domain.common.enums.ReceiveType;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.dsporder.entity.AppPushOrderRemindRecord;
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO;
import com.ctrip.dcs.domain.dsporder.factory.PushOrderRemindRecordFactory;
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository;
import com.ctrip.dcs.domain.schedule.dto.UserOrderModifyDTO;
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.infrastructure.adapter.soa.TourVendorNoticeServiceProxy;
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig;
import com.ctrip.tour.vendor.noticesvc.soa.v1.service.type.SendNoticeResponseType;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class OrderModifyBufferServiceMockTest {

    @Mock
    private DspOrderRepository dspOrderRepository;

    @Mock
    QueryTransportGroupService queryTransportGroupService;

    @Mock
    private SelfOrderQueryGateway selfOrderQueryGateway;

    @Mock
    PushOrderRemindRecordFactory pushOrderRemindRecordFactory;

    @Mock
    TourVendorNoticeServiceProxy tourVendorNoticeServiceProxy;

    @Mock
    CityRepository cityRepositoryImpl;

    @Mock
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;

    @InjectMocks
    private OrderModifyBufferService orderModifyBufferService;

    @Test
    public void orderModifyBufferTest(){
        UserOrderModifyDTO modifyOrder = new UserOrderModifyDTO();
        modifyOrder.setUserOrderId("111");
        modifyOrder.setUserChoseBufferMinutes(1);
        modifyOrder.setSysExpectBookTime("2025-06-05");
        modifyOrder.setOrignalSysExpectBookTime("2025-06-03");
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        List<DspOrderDO> dspOrderDOS = Lists.newArrayList();
        DspOrderDO dspOrderDO = new DspOrderDO();
        dspOrderDOS.add(dspOrderDO);
        Mockito.when(dspOrderRepository.queryValidDspOrders(Mockito.any())).thenReturn(dspOrderDOS);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        BaseDetailVO baseDetailVO = new BaseDetailVO();
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.any())).thenReturn(baseDetailVO);
        baseDetailVO.setOrderStatus(900);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.any())).thenReturn(baseDetailVO);
        baseDetailVO.setOrderStatus(240);
        baseDetailVO.setSupplierId(0);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        baseDetailVO.setSupplierId(30804);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.any())).thenReturn(baseDetailVO);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        baseDetailVO.setTransportGroupId(121L);
        Mockito.when(selfOrderQueryGateway.queryOrderBaseDetail(Mockito.any())).thenReturn(baseDetailVO);
        try {
            orderModifyBufferService.orderModifyBuffer(modifyOrder);
        } catch (Exception e) {
            Assert.assertEquals(baseDetailVO.getTransportGroupId().intValue(),121);
        }
        TransportGroupVO transportGroupVO = new TransportGroupVO();
        Mockito.when(queryTransportGroupService.queryTransportGroup(Mockito.any())).thenReturn(transportGroupVO);
        baseDetailVO.setCityId(1);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        Mockito.when(cityRepositoryImpl.isChineseMainland(Mockito.any())).thenReturn(false);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        transportGroupVO.setInformSwitch(0);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        transportGroupVO.setInformSwitch(1);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        transportGroupVO.setInformEmail("<EMAIL>");
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        AppPushOrderRemindRecord build = AppPushOrderRemindRecord.builder()
                .noticeId(UUID.randomUUID().toString())
                .partyType(PartyType.VBK.getCode())
                .receiveType(ReceiveType.PROVIDER_ID.getCode())
                .bizType(Integer.valueOf(1))
                .messageProperties(new HashMap<>())
                .locale("zn")
                .receiveIds(com.google.common.collect.Lists.newArrayList(String.valueOf(baseDetailVO.getSupplierId())))
                .url("")
                .onlineUrl("")
                .build();


        //Mockito.when(pushOrderRemindRecordFactory.createOrderModifyBufferAppPush(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(build);
        SendNoticeResponseType responseType = new SendNoticeResponseType();
        //Mockito.when(tourVendorNoticeServiceProxy.sendNotice(Mockito.any())).thenReturn(responseType);
        orderModifyBufferService.orderModifyBuffer(modifyOrder);
        responseType.setNoticeId("1");
        orderModifyBufferService.orderModifyBuffer(modifyOrder);

        Assert.assertEquals(responseType.getNoticeId(),"1");
    }

}
