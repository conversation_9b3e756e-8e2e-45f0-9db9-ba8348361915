package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderFeeDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderFeePO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

import java.math.BigDecimal;
import java.sql.SQLException;

@RunWith(MockitoJUnitRunner.class)
public class NoCommissionAmountCompensationListenerTest {

    @InjectMocks
    NoCommissionAmountCompensationListener noCommissionAmountCompensationListener;

    @Mock
    private DspOrderFeeDao dspOrderFeeDao;

    @Test
    public void onMessageTest(){
        Message message = new BaseMessage();
        try {
            message.setProperty("realNoCommissionAmount",new BigDecimal("10").toString());
            message.setProperty("dbNoCommissionAmount",new BigDecimal("5").toString());
            noCommissionAmountCompensationListener.onMessage(message);
            message.setProperty("dspOrderId","20919006437867553");
            noCommissionAmountCompensationListener.onMessage(message);
            DspOrderFeePO dspOrderFeePO = new DspOrderFeePO();
            dspOrderFeePO.setDspOrderId("111");
            noCommissionAmountCompensationListener.onMessage(message);
            Assert.assertEquals(dspOrderFeePO.getDspOrderId(),"111");
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }
}
