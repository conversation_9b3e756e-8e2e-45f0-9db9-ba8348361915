package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.service.OrderModifyBufferService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

@RunWith(MockitoJUnitRunner.class)
public class OrderModifyBufferListenerMockTest {

    @Mock
    private OrderModifyBufferService orderModifyBufferService;

    @InjectMocks
    private OrderModifyBufferListener modifyBufferListener;

    @Test
    public void userOrderModifyBufferTest(){
        Message message = new BaseMessage();
        modifyBufferListener.userOrderModifyBuffer(message);
        message.setProperty("userOrderId","1");
        modifyBufferListener.userOrderModifyBuffer(message);
        message.setProperty("modifyBufferCityFlag",Boolean.TRUE);
        modifyBufferListener.userOrderModifyBuffer(message);
        Assert.assertEquals(message.getStringProperty("userOrderId"),"1");

    }
}
