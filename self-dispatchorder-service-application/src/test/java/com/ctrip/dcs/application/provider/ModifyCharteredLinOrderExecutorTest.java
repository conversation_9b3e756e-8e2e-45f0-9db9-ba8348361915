package com.ctrip.dcs.application.provider;

import com.ctrip.dcs.application.command.ModifyCharteredLineOrderExeCmd;
import com.ctrip.dcs.application.provider.executor.ModifyCharteredLineOrderExecutor;
import com.ctrip.dcs.self.dispatchorder.interfaces.AddressInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountInfo;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderResponseType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class ModifyCharteredLinOrderExecutorTest {

    @InjectMocks
    ModifyCharteredLineOrderExecutor modifyCharteredLineOrderExecutor;

    @Mock
    ModifyCharteredLineOrderExeCmd modifyCharteredLineOrderExeCmd;

    @Test
    public void test() {

        Mockito.when(modifyCharteredLineOrderExeCmd.modifyCharteredLineOrder(Mockito.any())).thenReturn(true);

        // case1:参数异常-空
        ModifyCharteredLineOrderRequestType requestType = new ModifyCharteredLineOrderRequestType();
        requestType.setUniqueKey("12");
        requestType.setLastConfirmCarTimeBJ("2024-01-01 11:12:13");
        requestType.setLastConfirmCarTime("2024-01-01 11:12:13");
        requestType.setPredicServiceStopTimeBJ("2024-01-01 11:12:13");
        requestType.setPredicServiceStopTime("2024-01-01 11:12:13");
        requestType.setUserOrderId("12121212");
        requestType.setToAddressInfo(new AddressInfo());
        requestType.setFromAddressInfo(new AddressInfo());
        requestType.setEstimateUseTimeBJ("2024-01-01 11:12:13");
        requestType.setEstimateUseTime("2024-01-01 11:12:13");
        AmountInfo amountInfo = new AmountInfo();
        amountInfo.setAmount(BigDecimal.ZERO);
        amountInfo.setSupplierAmount(BigDecimal.ONE);
        amountInfo.setCurrency("cny");
        amountInfo.setSupplierCurrency("cny");
        amountInfo.setStatus(0);
        amountInfo.setCreateTime("2024-01-01 11:12:13");
        requestType.setAmountInfo(amountInfo);
        ModifyCharteredLineOrderResponseType responseType = modifyCharteredLineOrderExecutor.execute(requestType);
        Assert.assertTrue(responseType.getResponseResult().isSuccess());

    }

}
