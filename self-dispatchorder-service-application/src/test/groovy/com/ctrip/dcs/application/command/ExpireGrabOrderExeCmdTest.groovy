package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.ExpireGrabOrderCommand
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ExpireGrabOrderExeCmdTest extends Specification {
    @Mock
    BroadcastRepository broadcastRepository
    @Mock
    GrabCentreRepository grabCentreRepository
    @InjectMocks
    ExpireGrabOrderExeCmd expireGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
        when(broadcastRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)
        when(grabCentreRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)

        when:
        expireGrabOrderExeCmd.execute(new ExpireGrabOrderCommand("1-1-1-1-1-1-1-1-1-1", "dspOrderId", 1l, 1l, null))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme