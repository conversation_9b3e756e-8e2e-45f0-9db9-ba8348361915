package com.ctrip.dcs.application.command.grabOrderSnapshot

import com.ctrip.dcs.application.command.api.CancelGrabOrderSnapshotCommand
import com.ctrip.dcs.application.command.api.UpdateGrabDriverIndexCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway
import com.ctrip.dcs.domain.schedule.gateway.DriverPushConfigGateway
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.common.config.CheckGrabDriverIndexConfig
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class UpdateGrabDriverIndexExeCmdTest extends Specification {

    protected DistributedLockService distributedLockService = Mock(DistributedLockService)

    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository = Mock(GrabDspOrderSnapshotRepository);

    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository = Mock(GrabDspOrderDriverIndexRepository);

    private QueryDspOrderService queryDspOrderService = Mock(QueryDspOrderService);

    private CheckService checkService = Mock(CheckService);

    private SubSkuRepository subSkuRepository = Mock(SubSkuRepository);

    private QueryDriverService queryDriverService = Mock(QueryDriverService);

    private CheckGrabDriverIndexConfig checkGrabDriverIndexConfig = Mock(CheckGrabDriverIndexConfig);

    private DriverDomainServiceGateway driverDomainServiceGateway = Mock(DriverDomainServiceGateway);

    private DriverPushConfigGateway driverPushConfigGateway = Mock(DriverPushConfigGateway);

    private GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService = Mock(GrabDspOrderSnapshotRecordService);

    UpdateGrabDriverIndexExeCmd cmd = new UpdateGrabDriverIndexExeCmd(
            grabDspOrderSnapshotRepository : grabDspOrderSnapshotRepository,
            grabDspOrderDriverIndexRepository : grabDspOrderDriverIndexRepository,
            queryDspOrderService : queryDspOrderService,
            checkService : checkService,
            subSkuRepository : subSkuRepository,
            queryDriverService : queryDriverService,
            driverPushConfigGateway : driverPushConfigGateway,
            checkConfig: checkGrabDriverIndexConfig,
            grabDspOrderSnapshotRecordService: grabDspOrderSnapshotRecordService,
            distributedLockService: distributedLockService
    )

    @Unroll
    def "test"() {
        given: "设定相关方法入参"
        DistributedLockService.DistributedLock lock = Mock(DistributedLockService.DistributedLock)
        CheckModel checkModel = Mock(CheckModel)
        DspOrderVO dspOrder = Mock(DspOrderVO)
        DriverPushConfigVO pushConfig = new DriverPushConfigVO()
        SubSkuVO subSku = new SubSkuVO()
        DriverVO driver = new DriverVO()
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO(dspOrderId: "1")
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                id: 1L,
                dspOrderId: "1",
                grabType: GrabDspOrderSnapshotTypeEnum.SYSTEM,
                grabStatus: GrabDspOrderSnapshotStatusEnum.INIT,
                duid: "52988019756335304-1052988019756335305-2052988019756335314-50-50-50-50-0-0-0"
        )
        dspOrder.getDspOrderId() >> "1"
        dspOrder.getEstimatedUseTimeBj() >> new Date()
        dspOrder.getBizAreaType() >> 32
        dspOrder.getOrderStatus() >> 200
        dspOrder.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        queryDspOrderService.queryOrderDetail("1") >> dspOrder
        queryDspOrderService.selectDspOrderVOs(["1"]) >> [dspOrder]
        grabDspOrderSnapshotRepository.query("1") >> snapshot
        grabDspOrderSnapshotRepository.query(["1"], null, Lists.newArrayList(GrabDspOrderSnapshotStatusEnum.INIT.getCode(), GrabDspOrderSnapshotStatusEnum.GRAB.getCode())) >> [snapshot]
        grabDspOrderSnapshotRepository.updateGrabStatus(snapshot, GrabDspOrderSnapshotStatusEnum.GRAB, GrabDspOrderSnapshotStatusEnum.INIT) >> 1
        subSkuRepository.find(50) >> subSku
        queryDriverService.queryDriver(Sets.newHashSet(1L), ParentCategoryEnum.JNT, null) >> [driver]
        checkService.check(_) >> [checkModel]
        grabDspOrderDriverIndexRepository.query("1", [1L]) >> [index]
        grabDspOrderDriverIndexRepository.query("1") >> [index]
        driverPushConfigGateway.queryDriverPushConfigs(_, _) >> [pushConfig]
        grabDspOrderDriverIndexRepository.query(_, _, _) >> [index];
        grabDspOrderDriverIndexRepository.query(1L) >> [index];
        distributedLockService.getLock(_) >> lock
        lock.tryLock() >> true
        when:
        def result = cmd.execute(new UpdateGrabDriverIndexCommand(1L, GrabDspOrderSnapshotEventEnum.CANCEL_ORDER, ParentCategoryEnum.DAY))

        then: "验证返回结果里属性值是否符合预期"
        result == null
    }
}
