package com.ctrip.dcs.application.command.converter


import com.ctrip.dcs.application.command.api.CreateDspOrderCommand
import com.ctrip.dcs.application.provider.converter.SaaSCreateDspOrderConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification

class SaaSCreateDspOrderConverterTest extends Specification {


    def "test creat convert"() {
        given:
        when: "执行校验方法"
        def cmd = SaaSCreateDspOrderConverter.converter(getReq3())

        then: "验证校验结果"
        cmd.getDspOrderDO().getUseDays().toInteger() == 1
    }

    def "test creat exception"() {
        given:
        def code = "09011000";
        when: "执行校验方法"
        def cmd = SaaSCreateDspOrderConverter.converter(getReq2())
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code.equals( code)
    }


    SaasCreateDspOrderRequestType getReq3() {
        SaasCreateDspOrderInfo saasCreateDspOrderInfo = buildSaasCreateDspOrderInfo()
        def saasCreateDspOrderRequestType = new SaasCreateDspOrderRequestType(null, null, saasCreateDspOrderInfo);
        return saasCreateDspOrderRequestType
    }

    SaasCreateDspOrderRequestType getReq2() {
        SaasCreateDspOrderInfo saasCreateDspOrderInfo = buildSaasCreateDspOrderInfo()
        saasCreateDspOrderInfo.getBaseInfo().setOrderSourceCode(2)
        saasCreateDspOrderInfo.getBaseInfo().setUseDays((new BigDecimal("2")))
        def saasCreateDspOrderRequestType = new SaasCreateDspOrderRequestType(null, null, saasCreateDspOrderInfo);
        return saasCreateDspOrderRequestType
    }

    CreateDspOrderCommand getReq() {
        SaasCreateDspOrderInfo saasCreateDspOrderInfo = buildSaasCreateDspOrderInfo()
        def saasCreateDspOrderRequestType = new SaasCreateDspOrderRequestType(null, null, saasCreateDspOrderInfo);
        def cmd = SaaSCreateDspOrderConverter.converter(saasCreateDspOrderRequestType)
        return cmd
    }


    SaasCreateDspOrderInfo buildSaasCreateDspOrderInfo(){
        def saasCreateDspOrderInfo = new SaasCreateDspOrderInfo()
        SaaSBaseInfo baseInfo = new SaaSBaseInfo()
        baseInfo.setDspOrderId(null)
        baseInfo.setVbkOrderId("111")
        baseInfo.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        baseInfo.setUserOrderId("1111")
        baseInfo.setCityId(111)
        baseInfo.setCityName("beijing")
        baseInfo.setFromCityId(111)
        baseInfo.setDeptCity("beijing")
        baseInfo.setToCityId(111)
        baseInfo.setArriveCityName("beijing")
        baseInfo.setVehicleGroupId(1)
        baseInfo.setVehicleGroupName("预订车组名称")
        baseInfo.setEstimatedUseTime(new Date() as String)
        baseInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))

        baseInfo.setUseDays(null)
        baseInfo.setSupplierId(1)
        baseInfo.setDriverOrderId("")
        baseInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        baseInfo.setOrderCancelReasonDetail(null)
        saasCreateDspOrderInfo.setBaseInfo(baseInfo)

        SaaSPoiInfo fromPoi = new SaaSPoiInfo()
        fromPoi.setAddress("beijing")
        fromPoi.setCityId("111")
        saasCreateDspOrderInfo.setFromPoi(fromPoi)

        SaaSPoiInfo toPoi = new SaaSPoiInfo()
        toPoi.setAddress("beijing")
        toPoi.setCityId("111")
        saasCreateDspOrderInfo.setToPoi(toPoi)

        SaaSExtraFlightInfo extraFlightInfo = new SaaSExtraFlightInfo()
        extraFlightInfo.setFlightNo("BJ103")
        extraFlightInfo.setTerminalName("航站楼名称")
        extraFlightInfo.setFlightArriveTime(LocalDateUtils.toString(LocalDateUtils.addDays(new Date(), 12), "yyyy-MM-dd HH:mm:ss"))
        saasCreateDspOrderInfo.setExtraFlightInfo(extraFlightInfo)


        SaaSXproductInfo xproductInfo = new SaaSXproductInfo()
        xproductInfo.setAdditionalServices("附加服务")
        saasCreateDspOrderInfo.setXproductInfo(xproductInfo)

        SaaSUserCountInfo userCount = new SaaSUserCountInfo()
        userCount.setAdultCount(1)
        userCount.setChildCount(1)
        userCount.setBagCount(1)
        saasCreateDspOrderInfo.setUserCount(userCount)

        SaaSExtendInfo extendInfo = new SaaSExtendInfo()
        extendInfo.setDriverRemark("司机可见备注")
        saasCreateDspOrderInfo.setExtendInfo(extendInfo)


        SaaSFeeInfo feeInfo = new SaaSFeeInfo()
        feeInfo.setCostAmount(null)
        feeInfo.setUserCurrency(null)
        feeInfo.setSupplierCurrency(null)
        saasCreateDspOrderInfo.setFeeInfo(feeInfo)
        return saasCreateDspOrderInfo
    }


    List<DspOrderDO> getDspOrderVO() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        return Lists.newArrayList(order)
    }

    List<DspOrderDO> getDspOrderVOExist() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        return Lists.newArrayList(order)
    }


}
