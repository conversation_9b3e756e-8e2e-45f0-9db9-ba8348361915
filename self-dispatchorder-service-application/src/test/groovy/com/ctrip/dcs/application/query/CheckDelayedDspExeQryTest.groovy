package com.ctrip.dcs.application.query

import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO
import com.ctrip.dcs.application.service.OrderDelayedDspCheckService
import com.ctrip.dcs.application.service.dto.DelayedDspCheckResultDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.dsporder.value.ConfirmType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * unit test for CheckDelayedDspExeQry
 *
 * <AUTHOR>
 * @date 2024/3/10 09:19:50
 * @version 1.0
 */
class CheckDelayedDspExeQryTest extends Specification {
    def testObj = new CheckDelayedDspExeQry()
    def orderDelayedDspCheckService = Mock(OrderDelayedDspCheckService)
    def checkConfig = Mock(ConfigService)

    def setup() {

        testObj.orderDelayedDspCheckService = orderDelayedDspCheckService
        testObj.checkConfig = checkConfig
    }

    @Unroll
    def "checkOrderDelayedDispatchTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        orderDelayedDspCheckService.checkManualSchedulling(_) >> manualSchedullingCheckRes
        orderDelayedDspCheckService.checkSysDelayedDispatch(_) >> sysDelayedDspRes

        when:
        def result = testObj.checkOrderDelayedDispatch(checkReq)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult

        where: "表格方式验证多种分支调用场景"
        checkReq                                                                               || manualSchedullingCheckRes                                                                                                                                                                             || sysDelayedDspRes                                                                                                                                                                                      || expectedResult
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, urgentOrder: Boolean.TRUE) || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime", lastConfirmCarTimeBj: "lastConfirmCarTimeBj") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime", lastConfirmCarTimeBj: "lastConfirmCarTimeBj") || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null, lastConfirmCarTime: null, lastConfirmCarTimeBj: null)
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.DAY_RENTAL.type, urgentOrder: Boolean.FALSE) || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime", lastConfirmCarTimeBj: "lastConfirmCarTimeBj") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime", lastConfirmCarTimeBj: "lastConfirmCarTimeBj") || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null, lastConfirmCarTime: null, lastConfirmCarTimeBj: null)
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, urgentOrder: Boolean.FALSE) || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime1", lastConfirmCarTimeBj: "lastConfirmCarTimeBj2") || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "", lastConfirmCarTimeBj: "")
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, urgentOrder: Boolean.FALSE) || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime1", lastConfirmCarTimeBj: "lastConfirmCarTimeBj2") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4")
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, urgentOrder: Boolean.FALSE) || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime1", lastConfirmCarTimeBj: "lastConfirmCarTimeBj2") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4") || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4")
        new CheckOrderDelayedDspDTO(categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, urgentOrder: Boolean.FALSE) || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime1", lastConfirmCarTimeBj: "lastConfirmCarTimeBj2") || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: ConfirmType.SERVICE_PROVIDER_CONFIRMED, lastConfirmCarTime: "lastConfirmCarTime3", lastConfirmCarTimeBj: "lastConfirmCarTimeBj4") || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null, lastConfirmCarTime: null, lastConfirmCarTimeBj: null)
    }
}
