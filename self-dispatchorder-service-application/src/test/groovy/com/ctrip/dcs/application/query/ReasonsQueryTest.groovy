package com.ctrip.dcs.application.query

import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.infrastructure.common.config.ReasonDetailConfig
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig
import com.google.common.collect.Lists
import spock.lang.Specification
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/21 11:25
 */
class ReasonsQueryTest extends Specification {

    def reasonDetailConfig = Mock(ReasonDetailConfig)

    def redispatchRightConfig = Mock(RedispatchRightConfig)

    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)

    def commonConf = Mock(Map.class)

    def reasonsQuery = new ReasonsQuery(reasonDetailConfig: reasonDetailConfig,
            redispatchRightConfig: redispatchRightConfig,
            selfOrderQueryGateway: selfOrderQueryGateway,commonConf:commonConf)

    def "queryReason Test"() {
        given:
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> null
        when:
        def res = reasonsQuery.queryReason(1, "")
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.DISPATCH_REASONS_PARAM_ERROR.getCode()
        res.msg == ErrorCode.DISPATCH_REASONS_PARAM_ERROR.getDesc()
    }

    def "queryReason1 Test"() {
        given:
        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_SELF_SUPPLIER.code,ruleStatus:1))
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "")
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getCode()
        res.msg == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getDesc()
    }

    def "queryReason2 Test"() {
        given:
        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code,ruleStatus:1))
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "2", salesMode: 1)
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getCode()
        res.msg == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getDesc()
    }

    def "queryReason4 Test"() {
        given:
        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, serviceType: 0,ruleStatus:1))
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "2", salesMode: 2)
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getCode()
        res.msg == ErrorCode.DISPATCH_REASONS_EMPTY_ERROR.getDesc()
    }

    def "queryReason5 Test"() {
        given:
        commonConf.get("deleteOrderConflictReasonCity") >> "2"
        commonConf.get("deleteNotArriveReasonCity") >> "2"
        redispatchRightConfig.getDriverDispatchAlertReasonIdList() >> Lists.newArrayList()
        redispatchRightConfig.getCarFaultHours() >> 2
        ReasonDetailVO reasonDetailVO106 =  new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, serviceType: 0, operateSubject: ReassignTaskEnum.OperateSubjectEnum.SELF.getCode(),ruleStatus:1, reasonId: 106)
        ReasonDetailVO reasonDetailVO100 =  new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, serviceType: 0, operateSubject: ReassignTaskEnum.OperateSubjectEnum.SELF.getCode(),ruleStatus:1, reasonId: 100)
        ReasonDetailVO reasonDetailVO140 =  new ReasonDetailVO(roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, serviceType: 0, operateSubject: ReassignTaskEnum.OperateSubjectEnum.SELF.getCode(),ruleStatus:1, reasonId: 140)

        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(reasonDetailVO106,reasonDetailVO100,reasonDetailVO140)
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "2", salesMode: 2, estimatedUseTimeBj: "2024-01-01 00:00:00",cityId: 2L,orderStatus: 400)
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        res.isSuccess()
    }

    def "queryReason6 Test"() {
        given:
        redispatchRightConfig.getCarFaultHours() >> 1
        redispatchRightConfig.getDriverDispatchAlertReasonIdList() >> Lists.newArrayList()
        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(new ReasonDetailVO(
                roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code,
                serviceType: 0,
                operateSubject: ReassignTaskEnum.OperateSubjectEnum.PLATFORM.getCode(),
                reasonId: ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode().intValue(),ruleStatus:1))
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "2", salesMode: 2, estimatedUseTimeBj: "2023-01-09 12:12:33")
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        !res.isSuccess()
    }

    def "queryReason7 Test"() {
        given:
        redispatchRightConfig.getCarFaultHours() >> 1
        redispatchRightConfig.getDriverDispatchAlertReasonIdList() >> Lists.newArrayList()
        reasonDetailConfig.queryReasonDetailList() >> Lists.newArrayList(new ReasonDetailVO(
                roleId: ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code,
                serviceType: 0,
                operateSubject: ReassignTaskEnum.OperateSubjectEnum.PLATFORM.getCode(),
                reasonId: ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode().intValue(),ruleStatus:1)
                )
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, supplyOrderId: "2", salesMode: 2, estimatedUseTimeBj: "2083-01-09 12:12:33")
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> baseDetail
        when:
        def res = reasonsQuery.queryReason(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.code, "")
        then:
        res != null
        res.isSuccess() ==false
    }

}