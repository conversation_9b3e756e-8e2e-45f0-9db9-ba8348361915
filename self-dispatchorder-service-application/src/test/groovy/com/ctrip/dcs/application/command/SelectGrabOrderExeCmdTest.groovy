package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SelectGrabOrderCommand
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.process.impl.SelectGrabOrderProcess
import com.ctrip.dcs.domain.schedule.process.impl.SelectVBKGrabOrderProcess
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SelectGrabOrderExeCmdTest extends Specification {
    @Mock
    QueryDspOrderService dspOrderService
    @Mock
    SubSkuRepository subSkuRepository
    @Mock
    SelectGrabOrderProcess selectGrabOrderProcess
    @Mock
    SelectVBKGrabOrderProcess selectVBKGrabOrderProcess
    @Mock
    DspOrderVO dspOrderVO
    @InjectMocks
    SelectGrabOrderExeCmd selectGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        SubSkuVO subSkuVO = new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l)
        when(dspOrderService.queryOrderDetail(anyString())).thenReturn(dspOrderVO)
        when(subSkuRepository.find(anyInt())).thenReturn(subSkuVO)

        when:
        selectGrabOrderExeCmd.execute(new SelectGrabOrderCommand("dspOrderId", 0))

        then:
        subSkuVO.getSubSkuId() == 0
    }

    def "test execute 1"() {
        given:
        SubSkuVO subSkuVO = new SubSkuVO(0, "subSkuName", DspType.VBK_GRAB_ORDER, TakenType.DEFAULT, null, null, 1l)
        when(dspOrderService.queryOrderDetail(anyString())).thenReturn(dspOrderVO)
        when(subSkuRepository.find(anyInt())).thenReturn(subSkuVO)

        when:
        selectGrabOrderExeCmd.execute(new SelectGrabOrderCommand("dspOrderId", 0))

        then:
        subSkuVO.getSubSkuId() == 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme