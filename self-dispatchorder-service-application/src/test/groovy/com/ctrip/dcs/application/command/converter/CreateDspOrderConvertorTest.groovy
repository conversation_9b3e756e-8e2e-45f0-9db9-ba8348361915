package com.ctrip.dcs.application.command.converter


import com.ctrip.dcs.application.provider.converter.CreateDspOrderConverter
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.BaseInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CreateDspOrderInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.FeeInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.PlatformRewardDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ProductInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.WayPointInfoDTO
import spock.lang.Specification

class CreateDspOrderConvertorTest extends Specification {


    def "test  convert"() {
        given:
        def baseInfo = new BaseInfo(userOrderId: "11", categoryCode:"airport_dropoff",cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupid: 117
                , estimatedUseTime: "2023-04-24 10:00:08",estimatedUseTimeBj:"2023-04-25 10:00:08"
                , predicServiceStopTime: "2023-04-24 10:00:08", predicServiceStopTimeBj: "2023-04-24 10:00:08"
                , lastConfirmCarTime: "2023-04-24 10:00:08", lastConfirmTimeBj: "2023-04-24 10:00:08"
                , lastConfirmCarTimeBj: "2023-04-24 10:00:08", lastConfirmTime: "2023-04-24 10:00:08"
                , useDays: 1, supplierId: 10086, crossBorderType:1, "isPreConfirm":1)

        def productInfo = new ProductInfo(skuId: 146, crossWaitingRules:"{}")

        def feeInfo = new FeeInfo(emptyDrivingFee:BigDecimal.ONE, platformRewardInfo: [new PlatformRewardDTO(ruleSceneId: "1", ruleId: "1", ruleTypeId: "1", totalAmount: BigDecimal.ONE, currencyCode: "CNY", reward2driverExchangeRate: BigDecimal.ONE)])

        def waypoint = new WayPointInfoDTO(index: 0, address: "address", name: "name", longitude: 1D, latitude: 1D)

        def createDspOrderInfo = new CreateDspOrderInfo(baseInfo: baseInfo, productInfo: productInfo, feeInfo: feeInfo, orderScene: 0, wayPointInfoList: [waypoint])


        def request = new CreateDspOrderRequestType(createDspOrderInfo: createDspOrderInfo)

        when: "执行校验方法"
        def order = CreateDspOrderConverter.converter(request)

        then: "验证校验结果"
        order.orderScene == 0
        order.dspOrderDO.userOrderId == "11"
        order.dspOrderFeeDO.nonOccupancyAmount == BigDecimal.ONE
        order.getOrderWayPointList().size() == 1
        order.getDspOrderDO().isPreConfirm == 1
        order.getDspOrderDetailDO().isPreConfirm == 1
        with(order.getPlatformRewardInfo().getFirst()) {
            ruleSceneId == "1"
            ruleId == "1"
            ruleTypeId == "1"
            totalAmount == BigDecimal.ONE
            currencyCode == "CNY"
            reward2driverExchangeRate == BigDecimal.ONE
        }
    }


}
