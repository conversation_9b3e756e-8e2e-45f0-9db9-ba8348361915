package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CreateScheduleCommand
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.factory.ScheduleFactory
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.infrastructure.adapter.carconfig.VbkDriverGrabDspStrategyConfig
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class CreateScheduleExeCmdTest extends Specification {


    def scheduleFactory = Mock(ScheduleFactory)
    def scheduleRepository = Mock(ScheduleRepository)
    def recordRepository = Mock(ScheduleRecordRepository)
    def recordFactory = Mock(ScheduleRecordFactory)
    def messageProvider = Mock(MessageProviderService)
    def schedule = Mock(ScheduleDO)
    def dspOrderService = Mock(QueryDspOrderService)
    def order = Mock(DspOrderVO)
    def vbkDriverGrabDspStrategyConfig = Mock(VbkDriverGrabDspStrategyConfig)

    def executor = new CreateScheduleExeCmd(
            scheduleFactory: scheduleFactory,
            scheduleRepository: scheduleRepository,
            scheduleRecordRepository: recordRepository,
            scheduleRecordFactory: recordFactory,
            messageProducer: messageProvider,
            dspOrderService: dspOrderService,
            vbkDriverGrabDspStrategyConfig: vbkDriverGrabDspStrategyConfig
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        scheduleFactory.create(_, _, _) >> schedule
        dspOrderService.queryOrderDetailForSchedule(_, _, _) >> order
        schedule.getScheduleId() >> 1L
        schedule.getDspOrderId() >> "1"
        def cmd1 = new CreateScheduleCommand("1", ScheduleEventType.DSP_ORDER_BOOK)
        def cmd2 = new CreateScheduleCommand("1", ScheduleEventType.VBK_GRAB_TASK)

        when: "执行校验方法"
        executor.execute(cmd1)
        executor.execute(cmd2)

        then: "验证校验结果"
        cmd1.getScheduleType() == ScheduleType.SYSTEM
        cmd2.getScheduleType() == ScheduleType.VBK
    }

    @Unroll
    def "test hasExecutingSchedule"() {

        given: "Mock数据"
        scheduleRepository.query(_) >> [schedule]
        schedule.getScheduleId() >> 1L
        schedule.getDspOrderId() >> "1"
        schedule.getStatus() >> ScheduleStatus.EXECUTING
        def cmd1 = new CreateScheduleCommand("1", ScheduleEventType.DSP_ORDER_BOOK)

        when: "执行校验方法"
        executor.execute(cmd1)

        then: "验证校验结果"
        cmd1.getScheduleType() == ScheduleType.SYSTEM
    }
}
