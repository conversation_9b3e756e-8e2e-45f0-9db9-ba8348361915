package com.ctrip.dcs.application.command.validator


import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.dsporder.carconfig.UpdateDriverAfterTimeCarConf
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class SaaSTripChangeDriverValidatorTest extends Specification {

    def updateDriverAfterTimeCarConf = Mock(UpdateDriverAfterTimeCarConf)
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)
    def dateZoneConvertUtil = Mock(DateZoneConvertUtil)

    def saaSTripChangeDriverValidator = new SaaSTripChangeDriverValidator(
            updateDriverAfterTimeCarConf: updateDriverAfterTimeCarConf,
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway,
            dateZoneConvertUtil: dateZoneConvertUtil

    )

    @Unroll
    def "test validate"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(driverId, supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        updateDriverAfterTimeCarConf.get(_, _) >> confTime
        when:
        saaSTripChangeDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then: "验证校验结果"
        true
        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords                 | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 230         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 230         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords()      | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 240         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords()      | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 230         | DateUtil.addHours(new Date(), 240) | new ArrayList<SopRecord>() | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 240         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords()      | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords()          | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"

    }


    def "test validateError"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(driverId, supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        updateDriverAfterTimeCarConf.get(_, _) >> confTime
        dateZoneConvertUtil.getLocalTimeByCityId(_, _, _) >> buildFullSopRecords().get(0).getItineraryDateLocal()
        when:
        saaSTripChangeDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords           | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 220         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | null     || "09015110"
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 500         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | null     || "09015110"
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 240         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | null     || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 220         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | null       | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 500         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | null       | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 240         | new Date()                         | null                 | 4          | 1112     | buildSupplier() | 4                 | null       | 4             | 0        || "09011000"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                 | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                 | 3          | 1112     | buildSupplier() | 1                 | "1111"     | 1             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                 | 4          | 1112     | buildSupplier() | 1                 | "1111"     | 1             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                 | 4          | 1112     | buildSupplier() | 1                 | "1111"     | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                 | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | buildSopRecords()    | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | Lists.newArrayList() | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 0        || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords()    | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 0        || "09015110"

    }


    def "test validDriverVONonNull"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = null
        DriverVO driverVO = null
        VehicleVO vehicleVO = null
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validDriverVONonNull(saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == "5002"
    }

    def "test validDspOrderVONonNull"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = null
        DriverVO driverVO = null
        VehicleVO vehicleVO = null
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validDspOrderVONonNull(saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == "09015101"
    }

    def "test validVehicleVONonNull"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = null
        DriverVO driverVO = null
        VehicleVO vehicleVO = null
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validVehicleVONonNull(saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == "09015012"
    }

    def "test validDriverVONonNull2"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = new DspOrderVO();
        DriverVO driverVO = new DriverVO()
        VehicleVO vehicleVO = new VehicleVO();
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validDriverVONonNull(saaSBusinessVO)
        then: "验证校验结果"
        true
    }

    def "test validDspOrderVONonNull2"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = new DspOrderVO();
        DriverVO driverVO = new DriverVO()
        VehicleVO vehicleVO = new VehicleVO();
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validDspOrderVONonNull(saaSBusinessVO)
        then: "验证校验结果"
        true
    }

    def "test validVehicleVONonNull2"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = new DspOrderVO();
        DriverVO driverVO = new DriverVO()
        VehicleVO vehicleVO = new VehicleVO();
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        when:
        saaSTripChangeDriverValidator.validVehicleVONonNull(saaSBusinessVO)
        then: "验证校验结果"
        true
    }

    SupplierVO buildSupplier() {
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(1)
        supplierVO.setDispatchSupplierIdList(Lists.newArrayList(2L, 4L))
        return supplierVO;
    }

    List<SopRecord> buildFullSopRecords() {
        SopRecord sopRecord1 = new SopRecord()
        sopRecord1.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord1.setEventId(20)

        SopRecord sopRecord2 = new SopRecord()
        sopRecord2.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord2.setEventId(50)
        return Lists.newArrayList(sopRecord1, sopRecord2)
    }

    List<SopRecord> buildSopRecords() {
        SopRecord sopRecord = new SopRecord()
        sopRecord.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord.setEventId(20)
        return Lists.newArrayList(sopRecord)
    }


    DspOrderVO buildDspOrderVO(Integer productType, CategoryCodeEnum categoryCode, UseDays useDays, Integer orderStatus, Date estimatedUseTimeBj, List<SopRecord> sopRecords, Integer supplierId) {
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setProductType(productType)
        dspOrderVO.setCategoryCode(categoryCode)
        dspOrderVO.setUseDays(useDays)
        dspOrderVO.setOrderStatus(orderStatus)
        dspOrderVO.setEstimatedUseTimeBj(estimatedUseTimeBj)
        dspOrderVO.setSopRecords(sopRecords)
        dspOrderVO.setSupplierId(supplierId)
        dspOrderVO.setLastConfirmCarTimeBj(estimatedUseTimeBj)
        dspOrderVO.setDriverId(1112)
        return dspOrderVO;
    }

    DriverVO buildDriverVO(Long driverId, SupplierVO supplier) {
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(driverId)
        driverVO.setSupplier(supplier)
        CarVO carVO =new CarVO()
        carVO.setCarId(22L)
        driverVO.setCar(carVO)
        return driverVO;
    }

    VehicleVO buildVehicleVO(Integer supplierId) {
        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(supplierId)
        vehicleVO.setCarId(22L)
        return vehicleVO;
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }


    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}

