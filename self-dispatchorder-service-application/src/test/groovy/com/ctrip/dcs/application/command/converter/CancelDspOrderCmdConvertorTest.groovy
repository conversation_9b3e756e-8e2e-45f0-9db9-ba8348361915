package com.ctrip.dcs.application.command.converter

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand
import com.ctrip.dcs.application.provider.converter.CancelDspOrderConverter
import com.ctrip.dcs.domain.dsporder.entity.DspOrderCancelDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderRequestType
import spock.lang.Specification

class CancelDspOrderCmdConvertorTest extends Specification {


    def "test dsp order cancel convert"() {
        given:
        def request = new CancelDspOrderRequestType(userOrderId: "11", supplyOrderId: "22")

        when: "执行校验方法"
        CancelDspOrderCommand order = CancelDspOrderConverter.converter(request)

        then: "验证校验结果"
        true
    }

    def "test cancel convert"() {
        given:
        def request = new CancelDspOrderCommand(userOrderId: "11", orderFine: BigDecimal.valueOf(100), cancelReasonId: 1)

        when: "执行校验方法"
        DspOrderCancelDO order = CancelDspOrderConverter.buildDspOrderCancelDO(request)

        then: "验证校验结果"
        true
    }

    def "test cancel convert1"() {
        given:
        def request = new CancelDspOrderCommand(userOrderId: "11", orderFine: BigDecimal.valueOf(100))
        def dspOrder = new DspOrderDO(dspOrderId: "11")

        when: "执行校验方法"
        DspOrderFeeDO order = CancelDspOrderConverter.buildDspOrderFeeDO(request, dspOrder)

        then: "验证校验结果"
        true
    }

}
