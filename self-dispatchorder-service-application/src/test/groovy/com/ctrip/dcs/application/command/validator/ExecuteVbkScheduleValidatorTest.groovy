package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.sql.Timestamp

import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteVbkScheduleValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    MessageProviderService messageProducer
    @Mock
    Map<String, String> commonConfig
    @Mock
    private static DspOrderVO dspOrderVO
    @Mock
    private static DriverVO driverVO
    @Mock
    private static TransportGroupVO transportGroupVO
    @Mock
    private static ScheduleTaskDO scheduleTaskDO
    @Mock
    private static ScheduleDO scheduleDO
    @Mock
    private static ExecuteScheduleCommand command
    @Mock
    private static VBKDriverGrabOrderDO vbkDriverGrabOrderDO
    @InjectMocks
    ExecuteVbkScheduleValidator executeVbkScheduleValidator

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate"() {
        given:
        when(scheduleDO.getType()).thenReturn(type)
        when(scheduleDO.isShutdown()).thenReturn(shutdown)
        when(dspOrderVO.getOrderStatus()).thenReturn(orderStatus)
        when(scheduleDO.getRound()).thenReturn(round)
        when(commonConfig.get("default_max_schedule_round")).thenReturn("9")
        when(vbkDriverGrabOrderDO.getGrabLimitTimeBJ()).thenReturn(new Timestamp(DateUtil.addMinutes(new Date(), -10).getTime()))

        when:
        executeVbkScheduleValidator.validate(command, dspOrderVO, scheduleDO, [scheduleTaskDO], vbkDriverGrabOrderDO)

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == code//todo - validate something

        where:
        type             | shutdown | orderStatus | tasks | round || code
        ScheduleType.VBK | true     | 200         | []    | 1     || ErrorCode.SCHEDULE_SHUTDOWN_ERROR
        ScheduleType.VBK | false    | 200         | []    | 1     || ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR
        ScheduleType.VBK | false    | 220         | []    | 10    || ErrorCode.SCHEDULE_EXECUTING_ERROR
        ScheduleType.VBK | false    | 220         | []    | 1     || ErrorCode.SCHEDULE_TIMEOUT_ERROR
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme