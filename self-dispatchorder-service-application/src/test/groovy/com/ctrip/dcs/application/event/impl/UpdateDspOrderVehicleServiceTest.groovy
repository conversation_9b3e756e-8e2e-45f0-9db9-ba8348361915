package com.ctrip.dcs.application.event.impl

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.event.ICharterOrderDispatchModifyMsgService
import com.ctrip.dcs.application.event.dto.DriverFreezeEvent
import com.ctrip.dcs.application.event.dto.DriverInfoModifyEvent
import com.ctrip.dcs.domain.common.enums.AccountTypeEnum
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum
import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryVehicleService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.factory.DspOrderConfirmRecordFactory
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.gateway.CarTypeRelationGateway
import com.ctrip.dcs.domain.schedule.gateway.DriverPlatformApiServiceGateway
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.mockito.Mockito
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateDspOrderVehicleServiceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ReDispatchSubmitExeCmd submitExeCmd
    @Mock
    QueryVehicleService queryVehicleService
    @Mock
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository
    @Mock
    DspOrderConfirmRecordFactory confirmRecordFactory
    @Mock
    ICharterOrderDispatchModifyMsgService modifyMsgService
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    MessageProviderService messageProvider
    @Mock
    DriverPlatformApiServiceGateway driverPlatformGateway
    @Mock
    CarTypeRelationGateway carTypeRelationGateway

    @InjectMocks
    UpdateDspOrderVehicleService updateDspOrderVehicleService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    /** 查询无订单情况 */
    @Unroll
    def "test handle no orders"() {
        given: "Mock数据"
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn(Lists.newArrayList())

        when: "执行校验方法"
        boolean result = updateDspOrderVehicleService.update(1L, drvVehicleId, String.valueOf(AccountTypeEnum.BD_ACCOUNT.code), 10L, fromDrvGuide)

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        drvVehicleId || fromDrvGuide || expected
        117L         || false        || false
        117L         || true         || false
    }

    @Unroll
    def "test handle qmq query vehicle failed"() {
        given: "Mock数据"
        List<OrderVO> list = getOrderList();
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), anyList())).thenReturn(list)
        when(queryVehicleService.query(any(), any())).thenReturn(null)

        when: "执行校验方法"
        boolean result = updateDspOrderVehicleService.update(1L, 117L, String.valueOf(AccountTypeEnum.BD_ACCOUNT.code), 10L, fromDrvGuide)

        then: "验证校验结果"
        def exception = thrown(expectedException)

        where: "不同case输入"
        fromDrvGuide || expectedException
        true         || BizException
        false        || BizException

    }

    /** 查询用车消息 有订单情况 */
    @Unroll
    def "test handle qmq have orders"() {
        given: "Mock数据"
        List<OrderVO> list = getOrderList();
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), anyList())).thenReturn(list)
        when(queryVehicleService.query(any(), any())).thenReturn(new VehicleVO(carTypeId: drvCarTypeId, carTypeName: drvCarTypeId+"型",  carId: 100L, carLicense: "京Axxxx", carBrandId: 101L, carBrandName: "大众", carSeriesId: 1001L, carSeriesName: "迈腾", carColorId: 11L, carColor: "黑色", energyType: 1))
        when(driverPlatformGateway.checkDrvGuideGrayFlow(new DriverVO(driverId: 1L, isDrvGuide: fromDrvGuide, supplier: new SupplierVO(supplierId: 10L)))).thenReturn(drvGuideFlow)
        when(carTypeRelationGateway.queryCarTypeLevelRelationsByDrvCarType(any())).thenReturn(getCarTypeLevelRelationsVO())
        when(dspOrderConfirmRecordRepository.findByDspOrderId(any(), any())).thenReturn(new DspOrderConfirmRecordVO())
        when(dspOrderConfirmRecordRepository.updateCarRecord(any())).thenReturn(true)
        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO> newResult().success().build())

        when: "执行校验方法"
        boolean result = updateDspOrderVehicleService.update(1L, 100L, String.valueOf(AccountTypeEnum.BD_ACCOUNT.code), 10L, fromDrvGuide)

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        drvCarTypeId || fromDrvGuide || drvGuideFlow || expected
        118L         || false        || false        || true
        118L         || false        || true        || true
        118L         || true        || true        || true

    }

    /**
     * mock 订单列表数据
     */
    List<OrderVO> getOrderList() {
        def orderVOList = new ArrayList<OrderVO>()
        // 老订单
        orderVOList.add(new OrderVO(newOrder: false, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 117L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 境外订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.IGT, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 117L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 13:00:00"))));
        // 境外订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.IGT, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 118L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 13:00:00"))));
        // 境外订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.IGT, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 119L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 13:00:00"))));
        // 非携程订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 117L, orderSourceCode: OrderSourceCodeEnum.KLOOK.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程包车订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 117L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程接送订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 117L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程包车订单 - 舒适
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 118L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程接送订单 - 舒适
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 118L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程包车订单 - 豪华
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 119L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程接送订单 - 豪华
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone: "***********", transportGroupId: 10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId: "54321", orderCarTypeId: 119L, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));

        return orderVOList
    }

    CarTypeLevelRelationsVO getCarTypeLevelRelationsVO() {
        Map<Long, CarTypeLevelRelation> otherCarTypeRelationMap = new HashMap<Long, CarTypeLevelRelation>()
        otherCarTypeRelationMap.put(117L, CarTypeLevelRelation.HIGHER)
        otherCarTypeRelationMap.put(118L, CarTypeLevelRelation.PARALLEL)
        otherCarTypeRelationMap.put(119L, CarTypeLevelRelation.BELOW)
        otherCarTypeRelationMap.put(121L, CarTypeLevelRelation.UNKOWN)
        otherCarTypeRelationMap.put(1026020206L, CarTypeLevelRelation.UNKOWN)
        CarTypeLevelRelationsVO relationsVO = new CarTypeLevelRelationsVO(otherCarTypeRelationMap)

        return relationsVO;
    }

}