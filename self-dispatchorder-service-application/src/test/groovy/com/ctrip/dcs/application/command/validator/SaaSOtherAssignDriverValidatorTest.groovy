package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class SaaSOtherAssignDriverValidatorTest extends Specification {

    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)

    def saaSOtherAssignDriverValidator = new SaaSOtherAssignDriverValidator(
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway
    )

    @Unroll
    def "test validate"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setSupplierId(cmdSupplierId)
        when:
        saaSOtherAssignDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then: "验证校验结果"
        true

        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords                 | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 220         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"

    }


    def "test validateError"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setSupplierId(cmdSupplierId)
        when:
        saaSOtherAssignDriverValidator.validBusiness(cmd, saaSBusinessVO)
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                  | sopRecords                 | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 200        | null                                | null                       | 5          | null     | buildSupplier() | null              | "1111"     | 7             | null     || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 600        | null                                | null                       | 5          | null     | buildSupplier() | null              | "1111"     | 7             | null     || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220        | null                                | null                       | 5          | null     | buildSupplier() | null              | "1111"     | 22             | null     || "09015112"

    }


    SupplierVO buildSupplier() {
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(1)
        supplierVO.setDispatchSupplierIdList(Lists.newArrayList(2L, 4L))
        return supplierVO;
    }

    List<SopRecord> buildFullSopRecords() {
        SopRecord sopRecord1 = new SopRecord()
        sopRecord1.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord1.setEventId(20)

        SopRecord sopRecord2 = new SopRecord()
        sopRecord2.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord2.setEventId(50)
        return Lists.newArrayList(sopRecord1, sopRecord2)
    }

    List<SopRecord> buildSopRecords() {
        SopRecord sopRecord = new SopRecord()
        sopRecord.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord.setEventId(20)
        return Lists.newArrayList(sopRecord)
    }


    DspOrderVO buildDspOrderVO(Integer productType, CategoryCodeEnum categoryCode, UseDays useDays, Integer orderStatus, Date estimatedUseTimeBj, List<SopRecord> sopRecords, Integer supplierId) {
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setProductType(productType)
        dspOrderVO.setCategoryCode(categoryCode)
        dspOrderVO.setUseDays(useDays)
        dspOrderVO.setOrderStatus(orderStatus)
        dspOrderVO.setEstimatedUseTimeBj(estimatedUseTimeBj)
        dspOrderVO.setSopRecords(sopRecords)
        dspOrderVO.setSupplierId(supplierId)
        dspOrderVO.setLastConfirmCarTimeBj(estimatedUseTimeBj)
        return dspOrderVO;
    }

    DriverVO buildDriverVO(Long driverId, SupplierVO supplier) {
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(driverId)
        driverVO.setSupplier(supplier)
        return driverVO;
    }

    VehicleVO buildVehicleVO(Integer supplierId) {
        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(supplierId)
        return vehicleVO;
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }


    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}

