package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CustomerCheckDriversCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.gateway.QueryOrderTakenCostGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakenCostVO
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CheckDriverItem
import com.ctrip.igt.PaginationDTO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Sets
import org.mockito.Mock
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class CustomerCheckDriversCmdTest extends Specification {
    @Mock
    Logger logger
    def queryDspOrderService = Mock(QueryDspOrderService);
    def manualSubSkuConf = Mock(ManualSubSkuConf);
    def checkService = Mock(CheckService);
    def subSkuRepository = Mock(SubSkuRepository);
    def queryDriverService = Mock(QueryDriverService);
    def vbkDriverCheckListExeCmd = Mock(VbkDriverCheckListExeCmd);
    def queryOrderTakenCostGateway = Mock(QueryOrderTakenCostGateway);
    def dspOrderRepository = Mock(DspOrderRepository);
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig);
    def dspOrderVO = Mock(DspOrderVO);
    def checkModel = Mock(CheckModel);
    def dspModel = Mock(DspModelVO);
    def transportGroup = Mock(TransportGroupVO);
    def driver = Mock(DriverVO);
    def car = Mock(CarVO);
    def sysSwitchConfig = Mock(SysSwitchConfig)

    def impl = new CustomerCheckDriversCmd(
            queryDspOrderService : queryDspOrderService,
            manualSubSkuConf : manualSubSkuConf,
            checkService : checkService,
            subSkuRepository : subSkuRepository,
            queryDriverService : queryDriverService,
            vbkDriverCheckListExeCmd : vbkDriverCheckListExeCmd,
            queryOrderTakenCostGateway : queryOrderTakenCostGateway,
            dspOrderRepository : dspOrderRepository,
            businessTemplateInfoConfig : businessTemplateInfoConfig,
            sysSwitchConfig: sysSwitchConfig
    )

    @Unroll
    def "execute  Test1"() {
        given:
        def cmd = new CustomerCheckDriversCommand(userOrderId: "111", pageNo: 1, pageSize: 10)
        dspOrderRepository.queryValidDspOrders(_) >> dspOrderDOS
        queryDspOrderService.query(_) >> dspOrderVo
        manualSubSkuConf.matchSubSku(_,_,_,_,_)>> 100

        when:
        def resdto = impl.query(cmd)
        then:
        resdto != null == res

        where:
        flowSwitch | dspOrderDOS     | dspOrderVo       || res
        false      | _               | _                || true
        true       | null            | _                || true
        true       | [getdsporder()] | null             || true
        true       | [getdsporder()] | getDspOrderVO()  || true
        true       | [getdsporder()] | getDspOrderVO1() || true
    }

    @Unroll
    def "execute  convertPageInfo"() {
        given:

        when:
        PaginationDTO pageInfo = impl.convertPageInfo(1, 10, 10)
        then:
        pageInfo.getPageNo() == 1
        pageInfo.getTotalSize() == 10
        pageInfo.getPageSize() == 10
    }

    @Unroll
    def "execute  buildAssignDriverItem"() {

        given:
        businessTemplateInfoConfig.getTransportGroupModeMap() >> [1001 : ""]
        checkModel.getModel() >> dspModel
        dspModel.getTransportGroup() >> transportGroup
        driver.getDriverId() >> 1L
        driver.getCar() >> car
        driver.getCityId() >> 1L
        car.getCarId() >> 1L
        driver.getTransportGroups() >> [transportGroup]
        transportGroup.getTransportGroupId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.FULL_TIME_ASSIGN
        sysSwitchConfig.getNotReturnDrvSensitiveSwitch() >> notReturnDrvSensitiveSwitch

        when:
        CheckDriverItem item = impl.buildAssignDriverItem(checkModel, driver)

        then:
        item != null

        where:
        notReturnDrvSensitiveSwitch || expect
        true                        || null
        false                       || null
    }

    @Unroll
    def "execute  filterAndSortCheckResult"() {

        given:
        businessTemplateInfoConfig.getTransportGroupModeMap() >> [1001 : ""]
        checkModel.getModel() >> dspModel
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        driver.getDriverId() >> 1L
        driver.getCar() >> car
        driver.getCityId() >> 1L
        car.getCarId() >> 1L
        driver.getTransportGroups() >> [transportGroup]
        transportGroup.getTransportGroupId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.FULL_TIME_ASSIGN
        queryOrderTakenCostGateway.queryOrderTakenCost(_, _) >> [new TakenCostVO(drvId: 1L, tCost: BigDecimal.ONE, kCost: BigDecimal.ONE)]
        vbkDriverCheckListExeCmd.getDriverId(_) >> 1L

        when:
        def result = impl.filterAndSortCheckResult(dspOrderVO, [checkModel], [1L: driver])

        then:
        result != null
    }

    @Unroll
    def "execute  sort"() {

        given:
        businessTemplateInfoConfig.getTransportGroupModeMap() >> [1001 : ""]
        checkModel.getModel() >> dspModel
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        driver.getDriverId() >> 1L
        driver.getCar() >> car
        driver.getCityId() >> 1L
        car.getCarId() >> 1L
        car.getCarTypeId() >> 117
        driver.getTransportGroups() >> [transportGroup]
        transportGroup.getTransportGroupId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.FULL_TIME_ASSIGN
        queryOrderTakenCostGateway.queryOrderTakenCost(_, _) >> [new TakenCostVO(drvId: 1L, tCost: BigDecimal.ONE, kCost: BigDecimal.ONE)]
        vbkDriverCheckListExeCmd.queryCarLevelRelation(_) >> [117:1]

        when:
        def result = impl.sort(dspOrderVO, [checkModel, checkModel], [1L: driver], [1L : TransportGroupMode.FULL_TIME_ASSIGN])

        then:
        result != null
    }

    @Unroll
    def "execute  queryDriverCost"() {

        given:
        businessTemplateInfoConfig.getTransportGroupModeMap() >> [1001 : ""]
        checkModel.getModel() >> dspModel
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        driver.getDriverId() >> 1L
        driver.getCar() >> car
        driver.getCityId() >> 1L
        car.getCarId() >> 1L
        car.getCarTypeId() >> 117
        driver.getTransportGroups() >> [transportGroup]
        transportGroup.getTransportGroupId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.FULL_TIME_ASSIGN
        queryOrderTakenCostGateway.queryOrderTakenCost(_, _) >> [new TakenCostVO(drvId: 1L, tCost: BigDecimal.ONE, kCost: BigDecimal.ONE)]
        vbkDriverCheckListExeCmd.queryCarLevelRelation(_) >> [117:1]

        when:
        def result = impl.queryDriverCost("1", Sets.newHashSet(1L))

        then:
        result != null
    }

    @Unroll
    def "execute  filter"() {

        given:
        businessTemplateInfoConfig.getTransportGroupModeMap() >> [1001 : ""]
        checkModel.getModel() >> dspModel
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        driver.getDriverId() >> 1L
        driver.getCar() >> car
        driver.getCityId() >> 1L
        car.getCarId() >> 1L
        car.getCarTypeId() >> 117
        driver.getTransportGroups() >> [transportGroup]
        transportGroup.getTransportGroupId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.FULL_TIME_ASSIGN
        queryOrderTakenCostGateway.queryOrderTakenCost(_, _) >> [new TakenCostVO(drvId: 1L, tCost: BigDecimal.ONE, kCost: BigDecimal.ONE)]
        vbkDriverCheckListExeCmd.queryCarLevelRelation(_) >> [117:1]
        vbkDriverCheckListExeCmd.getDriverId(_) >> driverId

        when:
        def result = impl.filter(dspOrderVO, [checkModel, checkModel], driverTransportMap, transportModeMap)

        then:
        result.size() == size

        where:
        driverId | driverTransportMap         | transportModeMap                                                                  || size
        1L       | [1: Sets.newHashSet(1)]    | [1L: TransportGroupMode.FULL_TIME_ASSIGN]                                         || 0
        0L       | [1: Sets.newHashSet(1)]    | [1L: TransportGroupMode.FULL_TIME_ASSIGN]                                         || 2
        0L       | [1: Sets.newHashSet(1, 2)] | [1L: TransportGroupMode.FULL_TIME_ASSIGN, 2L: TransportGroupMode.MANUAL_DISPATCH] || 2
        0L       | [1: Sets.newHashSet(1, 2)] | [3L: TransportGroupMode.FULL_TIME_ASSIGN, 4L: TransportGroupMode.MANUAL_DISPATCH] || 0
        0L       | [1: Sets.newHashSet(1, 2)] | [1L: TransportGroupMode.FULL_TIME_ASSIGN, 4L: TransportGroupMode.MANUAL_DISPATCH] || 2
        0L       | [1: Sets.newHashSet(1, 2)] | [3L: TransportGroupMode.FULL_TIME_ASSIGN, 2L: TransportGroupMode.MANUAL_DISPATCH] || 0


    }


    DspOrderDO getdsporder() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", dspOrderId: "123", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, spId: 1000)

        return dspOrderDO
    }

    DspOrderVO getDspOrderVO() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", productType: 1, supplierId: null)
        return dspOrderVO
    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0,countryId: 1,categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 10L)
        return dspOrderVO
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme