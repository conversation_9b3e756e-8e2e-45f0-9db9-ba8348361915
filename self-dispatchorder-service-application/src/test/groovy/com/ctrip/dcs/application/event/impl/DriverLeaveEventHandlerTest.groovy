package com.ctrip.dcs.application.event.impl

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.event.ICharterOrderDispatchModifyMsgService
import com.ctrip.dcs.application.event.dto.DriverLeaveEvent
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.domain.dsporder.value.DriverLeaveRightsVO
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverLeaveEventHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ReDispatchSubmitExeCmd submitExeCmd
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ICharterOrderDispatchModifyMsgService modifyMsgService
    @Mock
    DriverDomainServiceGateway driverDomainService
    @Mock
    DistributedLockService distributedLockService
    @Mock
    MessageProviderService messageProducer
    @Mock
    OrderVO orderVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DistributedLockServiceImpl.RedisDistributedLock distributedLock
    @InjectMocks
    DriverLeaveEventHandler driverLeaveEventHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle"() {
        given:
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO>newResult().success().build())
        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(driverDomainService.queryDriverLeaveRight(anyLong())).thenReturn(new DriverLeaveRightsVO(1l, Boolean.TRUE, 0))
        when(distributedLockService.getLock(anyString())).thenReturn(distributedLock)
        when(distributedLock.tryLock()).thenReturn(true)
        when(orderVO.getDspOrderVO()).thenReturn(dspOrderVO)
        when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(new Date())

        when:
        boolean result = driverLeaveEventHandler.handle(new DriverLeaveEvent(1l, "2023-12-19 12:00:00", "2023-12-19 13:00:00", "accountType"), new DriverLeaveRightsVO(1l, Boolean.TRUE, 0))

        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn([orderVO])
        boolean result1 = driverLeaveEventHandler.handle(new DriverLeaveEvent(1l, "2023-12-19 12:00:00", "2023-12-19 13:00:00", "accountType"), new DriverLeaveRightsVO(1l, Boolean.TRUE, 0))

        when(orderVO.isNewOrder()).thenReturn(true)
        when(orderVO.isIgtOrder()).thenReturn(false)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        boolean result2 = driverLeaveEventHandler.handle(new DriverLeaveEvent(1l, "2023-12-19 12:00:00", "2023-12-19 13:00:00", "accountType"), null)

        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)
        boolean result3 = driverLeaveEventHandler.handle(new DriverLeaveEvent(1l, "2023-12-19 12:00:00", "2023-12-19 13:00:00", "6"), null)


        then:
        result != true
        result1 == true
        result2 == true
        result3 == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme