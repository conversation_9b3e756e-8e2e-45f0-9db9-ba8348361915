package com.ctrip.dcs.application.command.dispatchergrab

import com.ctrip.dcs.application.command.api.CancelDispatcherGrabOrderCommand
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CancelDispatcherGrabOrderExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatcherGrabOrderDao dispatcherGrabOrderDao
    @InjectMocks
    CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        when:
        cancelDispatcherGrabOrderExeCmd.execute(new CancelDispatcherGrabOrderCommand("1", ["1"], 1l))

        then:
        verify(dispatcherGrabOrderDao).cancel("1", ["1"], 1l) == 0
    }

    def "test execute fail"() {
        given:
        when(dispatcherGrabOrderDao.cancel("1", ["1"], 1l)).thenThrow(new RuntimeException("test"))
        when:
        cancelDispatcherGrabOrderExeCmd.execute(new CancelDispatcherGrabOrderCommand("1", ["1"], 1l))

        then:
        def e = thrown(BizException)
        e.code == ErrorCode.CANCEL_DISPATCH_GRAB_ORDER_ERROR.getCode()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme