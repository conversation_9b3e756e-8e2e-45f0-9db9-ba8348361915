package com.ctrip.dcs.application.event.impl

import cn.hutool.core.lang.Assert
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.exception.OrderStatusException
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryVehicleService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverAndCarConfirmVO
import com.ctrip.dcs.domain.common.value.DriverOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.event.DriverOrderConfirmFailEvent
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.RedispatchOrderVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import spock.lang.Specification

class ConflictRedispatchEventHandlerTest extends Specification{
    def  recommendService = Mock(RecommendService)
    def  checkService = Mock(CheckService)
    def  queryDspOrderService = Mock(QueryDspOrderService)
    def  driverOrderFactory = Mock(DriverOrderFactory)
    def  confirmDspOrderService = Mock(ConfirmDspOrderService)
    def  messageProducer = Mock(MessageProviderService)
    def  queryVehicleService = Mock(QueryVehicleService)
    def  subSkuRepository = Mock(SubSkuRepository)
    def  commonConf = Mock(Map.class)
    def  messageProviderService = Mock(MessageProviderService)
    def  distributedLockService = Mock(DistributedLockService)

//    def sendDriverOrderConfirmFailMessage = Mock(SendDriverOrderConfirmFailMessageService)

    def handler = new ConflictRedispatchEventHandler(
            recommendService:recommendService,
            checkService:checkService,
            queryDspOrderService:queryDspOrderService,
            driverOrderFactory:driverOrderFactory,
            confirmDspOrderService:confirmDspOrderService,
            messageProducer:messageProducer,
            queryVehicleService:queryVehicleService,
            subSkuRepository:subSkuRepository,
            commonConf:commonConf,
            messageProviderService:messageProviderService,
            distributedLockService:distributedLockService,
//            queryVehicle:queryVehicle
    )

    def "redispatch"(){
        given:
        def lock = Mock(DistributedLockService.DistributedLock)
        distributedLockService.getLock(_) >> lock
        lock.tryLock() >> lockResult
        queryDspOrderService.queryOrderDetail(_) >> dspOrder
        commonConf.get("conflict_redispatch_subSkuId") >> "466"
        subSkuRepository.find(_) >> subSku
        recommendService.recommend(_,_,_) >> sortModels
        when:
        def result = handler.redispatch("46464","2343","37537","76773","2342313")
        then:
        result == redispatchResult
        where:
        lockResult| dspOrder|subSku|sortModels || redispatchResult
        false | null|null|null || false
        true | null|null|null || false
        true |getDspOrder()|null|null || false
        true | getDspOrder()|getSubSkuVO()|null || false
        true |getDspOrder()|getSubSkuVO()| getSortModelList()|| false
    }

    def "should return false when check does not pass"() {
        given:
        def model = Mock(DspModelVO)
        def subSkuVO = Mock(SubSkuVO)
        def duidVO = Mock(DuidVO)
        def redispatchOrderVO = Mock(RedispatchOrderVO)
        def order = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        def transportGroup = Mock(TransportGroupVO)

        model.getOrder() >> order
        model.getDriver() >> driver
        model.getTransportGroup() >> transportGroup

        checkService.check(_ as TakenCheckCommand) >> new CheckModel(new DspModelVO(),  CheckCode.NO_ACTIVE)

        when:
        def result = handler.execute(model, subSkuVO, duidVO, redispatchOrderVO)

        then:
        !result
    }

    def "should return false when driverOrder is null"() {
        given:
        def model = Mock(DspModelVO)
        def subSkuVO = Mock(SubSkuVO)
        def duidVO = Mock(DuidVO)
        def redispatchOrderVO = Mock(RedispatchOrderVO)
        def order = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        def transportGroup = Mock(TransportGroupVO)

        model.getOrder() >> order
        model.getDriver() >> driver
        model.getTransportGroup() >> transportGroup

        checkService.check(_ as TakenCheckCommand) >> new CheckModel(new DspModelVO(),  CheckCode.PASS)
        driverOrderFactory.createForRedispatch(_, _, _,_) >> null

        when:
        def result = handler.execute(model, subSkuVO, duidVO, redispatchOrderVO)

        then:
        !result
    }

    def "should confirm order successfully"() {
        given:
        def model = Mock(DspModelVO)
        def subSkuVO = Mock(SubSkuVO)
        def duidVO = Mock(DuidVO)
        def redispatchOrderVO = Mock(RedispatchOrderVO)
        def order = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        def transportGroup = Mock(TransportGroupVO)
        def driverOrder = Mock(DriverOrderVO)
        driverOrder.getDriverOrderId() >> "D1234"
        driverOrder.getDspOrderId() >> "D1234"
        def vehicle = Mock(VehicleVO)
        vehicle.getCarId() >> 1223223

        model.getOrder() >> order
        order.getSpId() >> 1234
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        model.getDriver() >> driver
        driver.getDriverId() >> 1234
        driver.getSupplier() >> new SupplierVO(supplierId: 1234)
        driver.getCar() >> new CarVO(carId: 123)

        model.getTransportGroup() >> transportGroup
        transportGroup.getTransportGroupId() >> 1223223

        checkService.check(_ as TakenCheckCommand) >> new CheckModel(new DspModelVO(),  CheckCode.PASS)
        driverOrderFactory.createForRedispatch(_, _, _,_) >> driverOrder
        queryVehicleService.query(_, _) >> vehicle


        when:
        def result = handler.execute(model, subSkuVO, duidVO, redispatchOrderVO)

        then:
        result
        1 * confirmDspOrderService.confirm(_ as DriverAndCarConfirmVO)
    }
//
    def "should handle OrderStatusException and send fail message"() {
        given:
        def model = Mock(DspModelVO)
        def subSkuVO = Mock(SubSkuVO)
        def duidVO = Mock(DuidVO)
        def redispatchOrderVO = Mock(RedispatchOrderVO)
        def order = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        def transportGroup = Mock(TransportGroupVO)
        def driverOrder = Mock(DriverOrderVO)
        driverOrder.getDriverOrderId() >> "D1234"
        driverOrder.getDspOrderId() >> "D1234"
        def vehicle = Mock(VehicleVO)
        vehicle.getCarId() >> 1223223

        model.getOrder() >> order
        order.getSpId() >> 1234
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        model.getDriver() >> driver
        driver.getDriverId() >> 1234
        driver.getSupplier() >> new SupplierVO(supplierId: 1234)
        driver.getCar() >> new CarVO(carId: 123)

        model.getTransportGroup() >> transportGroup
        transportGroup.getTransportGroupId() >> 1223223

        checkService.check(_ as TakenCheckCommand) >> new CheckModel(new DspModelVO(),  CheckCode.PASS)
        driverOrderFactory.createForRedispatch(_, _, _,_) >> driverOrder
        queryVehicleService.query(_, _) >> vehicle
        confirmDspOrderService.confirm(_ as DriverAndCarConfirmVO) >> { throw new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR) }

        when:
        def result = handler.execute(model, subSkuVO, duidVO, redispatchOrderVO)

        then:
        thrown(OrderStatusException)
//        1 * messageProducer.send(_ as DriverOrderConfirmFailEvent)
    }


    public DspOrderVO getDspOrder(){
        DspOrderVO dspOrderVO = new DspOrderVO();
        dspOrderVO.setDspOrderId("461313")
        return dspOrderVO;
    }
    public SubSkuVO getSubSkuVO(){
        SubSkuVO subSkuVO = new SubSkuVO();
        subSkuVO.setDspType(DspType.DISPATCHER_ASSIGN)
        subSkuVO.setSubSkuId(464613)
        subSkuVO.setTakenType(TakenType.ASSISTANT)
        return subSkuVO;
    }
    public List<SortModel> getSortModelList(){
        DspModelVO model = new DspModelVO()
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(46464L)
        model.setDriver(driverVO)
        SortModel sortModel = new SortModel(model)

        DspModelVO model2 = new DspModelVO()
        DriverVO driverVO2 = new DriverVO()
        driverVO2.setDriverId(4646444L)
        model2.setDriver(driverVO2)
        SortModel sortModel2 = new SortModel(model2)
        return Arrays.asList(sortModel,sortModel2)
    }
}
