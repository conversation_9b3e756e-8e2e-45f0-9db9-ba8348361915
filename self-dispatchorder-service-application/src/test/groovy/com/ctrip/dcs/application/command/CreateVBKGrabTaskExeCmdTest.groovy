package com.ctrip.dcs.application.command

import cn.hutool.core.lang.Pair
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO
import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.application.command.dto.VBKGrabTaskSettlement
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.IdGeneratorService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.platform.dal.dao.helper.JsonUtils
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CreateVBKGrabTaskExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    IdGeneratorService idGeneratorService
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    DistributedLockService.DistributedLock lock
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    ConfigService commonConfConfig
    @Mock
    MessageProviderService messageProducer
    @Mock
    DspOrderVO dspOrderVO
    @InjectMocks
    CreateVBKGrabTaskExeCmd createVBKGrabTaskExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test buildVbkDriverGrabOrderDO"() {
        given:
        DspOrderVO dspOrderVO1 = new DspOrderVO();
        Map<String, String> map = new HashMap<>();
        map.put("settleToDriver", settleToDriver)
        dspOrderVO1.setExtendInfo(JsonUtils.toJson(map))
        dspOrderVO1.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT)
        dspOrderVO1.setSupplierId(1)
        Map<String, DspOrderVO> dspOrderVOMap = new HashMap<>()
        dspOrderVOMap.put("1", dspOrderVO1)

        VBKGrabTaskDTO vbkGrabTaskDTO = new VBKGrabTaskDTO();
        vbkGrabTaskDTO.setPayForDriver(2)
        vbkGrabTaskDTO.setSupplierId(1L)
        when: "执行校验方法"
        String result = createVBKGrabTaskExeCmd.buildVbkDriverGrabOrderDO(vbkGrabTaskDTO, dspOrderVOMap, "1")
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res
        where:

        settleToDriver || res
        null           || "09015409"
        "1"            || "09015409"

    }


    def "test buildVbkDriverGrabOrderDO_null"() {
        given:
        DspOrderVO dspOrderVO1 = new DspOrderVO();
        Map<String, String> map = new HashMap<>();
        map.put("settleToDriver", settleToDriver)
        dspOrderVO1.setExtendInfo(JsonUtils.toJson(map))
        dspOrderVO1.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT)
        dspOrderVO1.setSupplierId(1)
        Map<String, DspOrderVO> dspOrderVOMap = new HashMap<>()
        dspOrderVOMap.put("1", dspOrderVO1)

        VBKGrabTaskDTO vbkGrabTaskDTO = new VBKGrabTaskDTO();
        vbkGrabTaskDTO.setPayForDriver(2)
        vbkGrabTaskDTO.setSupplierId(1L)
        when: "执行校验方法"
        String result = createVBKGrabTaskExeCmd.buildVbkDriverGrabOrderDO(vbkGrabTaskDTO, dspOrderVOMap, "2")
        then: "验证校验结果"

        result == res
        where:

        settleToDriver || res
        null           || null

    }


    def "test buildVbkDriverGrabOrderDO1"() {
        given:
        DspOrderVO dspOrderVO1 = new DspOrderVO();
        dspOrderVO1.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT)
        dspOrderVO1.setSupplierId(1)
        Map<String, DspOrderVO> dspOrderVOMap = new HashMap<>()
        dspOrderVOMap.put("1", dspOrderVO1)
        VBKGrabTaskDTO vbkGrabTaskDTO = new VBKGrabTaskDTO();
        vbkGrabTaskDTO.setPayForDriver(2)
        vbkGrabTaskDTO.setSupplierId(1L)
        when: "执行校验方法"
        String result = createVBKGrabTaskExeCmd.buildVbkDriverGrabOrderDO(vbkGrabTaskDTO, dspOrderVOMap, "1")
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res
        where:

        settleToDriver || res
        null           || "09015409"

    }


    def "test send Cancel Order In Other Task"() {
        when:
        def r1 = createVBKGrabTaskExeCmd.sendCancelOrderInOtherTask("vbkGrabTaskId", [])
        def r2 = createVBKGrabTaskExeCmd.sendCancelOrderInOtherTask("vbkGrabTaskId", ["String"])

        then:
        r1 == null
        r2 == null
    }

    def "test send VBK Grab Task Operate Event"() {
        when:
        def r1 = createVBKGrabTaskExeCmd.sendVBKGrabTaskOperateEvent(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), "vbkGrabTaskId")

        then:
        r1 == null
    }

    def "test send VBK Grab Dsp Task"() {
        when:
        def r1 = createVBKGrabTaskExeCmd.sendVBKGrabDspTask([])
        def r2 = createVBKGrabTaskExeCmd.sendVBKGrabDspTask([new VBKDriverGrabOrderDO(vbkGrabTaskId: "vbkGrabTaskId", supplierId: 1l, dspOrderId: "dspOrderId", cityId: 0, categoryCode: "categoryCode")])

        then:
        r1 == null
        r2 == null
    }

    def "test send Event"() {
        when:
        def r1 = createVBKGrabTaskExeCmd.sendEvent(new VBKDriverGrabOrderDO(vbkGrabTaskId: "vbkGrabTaskId", supplierId: 1l, dspOrderId: "dspOrderId", cityId: 0, categoryCode: "categoryCode"), 0l)
        def r2 = createVBKGrabTaskExeCmd.sendEvent(new VBKDriverGrabOrderDO(vbkGrabTaskId: "vbkGrabTaskId", supplierId: 1l, dspOrderId: "dspOrderId", cityId: 0, categoryCode: "airport_pickup"), 0l)

        then:
        r1 == null
        r2 == null
    }

    def "test buildRewardsSettlement 1"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildRewardsSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 1, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 1, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildRewardsSettlement 2"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildRewardsSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 1, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildRewardsSettlement 3"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildRewardsSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 2, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 1, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildRewardsSettlement 4"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when(dspOrderVO.getPreSupplierSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildRewardsSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 2, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildInitialSettlement 1"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildInitialSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 1, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 1, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildInitialSettlement 2"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildInitialSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 1, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 1, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }

    def "test buildInitialSettlement 3"() {
        given:
        when(dspOrderVO.getPreDriverSettleAmount()).thenReturn(BigDecimal.TEN)
        when(dspOrderVO.getPreSupplierSettleAmount()).thenReturn(BigDecimal.TEN)
        when:
        def r = createVBKGrabTaskExeCmd.buildInitialSettlement(new VBKGrabTaskDTO(cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 2, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 2, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "1", makeUpEffectTime: "1", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId"), dspOrderVO)

        then:
        r == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme