package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.RightAndPointCommand
import com.ctrip.dcs.application.service.reDispath.ReassignTaskManger
import com.ctrip.dcs.application.service.reDispath.RightAndPointService
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.RightAndPointVO
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class RightAndPointCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    RightAndPointService rightAndPointService
    @Mock
    BaseDetailVO baseDetailVO
    @Mock
    RightAndPointVO rightAndPointVO
    @InjectMocks
    RightAndPointCmd rightAndPointCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "execute null role Test"() {
        given:
        def roleId = null
        when:
        def res = rightAndPointCmd.execute(new RightAndPointCommand(roleId: roleId, userOrderId: "1"))
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.ROLE_MISS_ERROR.getCode()
        res.msg == ErrorCode.ROLE_MISS_ERROR.getDesc()
    }

    def "execute null service Test"() {
        given:
        def roleId = ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.code

        when:
        def res = rightAndPointCmd.execute(new RightAndPointCommand(roleId: roleId, userOrderId: "1"))
        then:
        res != null
        !res.isSuccess()
    }

    def "execute null order Test"() {
        given:
        def roleId = ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.code
        ReassignTaskManger.addAgent(ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getDesc(), rightAndPointService)
        when:
        def res = rightAndPointCmd.execute(new RightAndPointCommand(roleId: roleId, userOrderId: "1"))
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.NULL_ORDER_ERROR.getCode()
        res.msg == ErrorCode.NULL_ORDER_ERROR.getDesc()
    }

    def "execute fail Test"() {
        given:
        def roleId = ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.code
        def result = Result.Builder.<RightAndPointVO>newResult().fail().withCode(ErrorCode.UN_KNOWN.getCode()).withMsg(ErrorCode.UN_KNOWN.getDesc()).build();
        ReassignTaskManger.addAgent(ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getDesc(), rightAndPointService)
        when(selfOrderQueryGateway.queryOrderBaseDetail(any(), any())).thenReturn(baseDetailVO)
        when(rightAndPointService.queryRightAndPointDTO(any(), any())).thenReturn(result)
        when:
        def res = rightAndPointCmd.execute(new RightAndPointCommand(roleId: roleId, userOrderId: "1"))
        then:
        res != null
        !res.isSuccess()
        res.code == ErrorCode.UN_KNOWN.getCode()
        res.msg == ErrorCode.UN_KNOWN.getDesc()
    }

    def "execute normal Test"() {
        given:
        def roleId = ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.code
        def result = Result.Builder.<RightAndPointVO>newResult().success().withData(rightAndPointVO).build();
        ReassignTaskManger.addAgent(ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getDesc(), rightAndPointService)
        when(selfOrderQueryGateway.queryOrderBaseDetail(any(), any())).thenReturn(baseDetailVO)
        when(rightAndPointService.queryRightAndPointDTO(any(), any())).thenReturn(result)
        when:
        def res = rightAndPointCmd.execute(new RightAndPointCommand(roleId: roleId, userOrderId: "1"))
        then:
        res != null
        res.isSuccess()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme