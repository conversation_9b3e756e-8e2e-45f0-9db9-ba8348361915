package com.ctrip.dcs.application.command

import cn.hutool.core.math.Money
import com.ctrip.dcs.application.command.api.ShutdownScheduleCommand
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.ScheduleRecordVO
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO
import com.ctrip.dcs.domain.schedule.value.ScheduleTaskRecordVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.carconfig.ScheduleStrategyItemValueVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ShutdownScheduleExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    ScheduleRecordRepository scheduleRecordRepository
    @Mock
    ScheduleRecordFactory scheduleRecordFactory
    @Mock
    ScheduleTaskDO scheduleTask
    @InjectMocks
    ShutdownScheduleExeCmd shutdownScheduleExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        ScheduleDO scheduleDO = new ScheduleDO(1l, "dspOrderId", new ScheduleStrategyVO(1L, [new ScheduleStrategyItemValueVO(1, 1, 1, 1, 1)]), 1, ScheduleStatus.EXECUTING, [], new Date(), new Date(), new Date())
        when(taskRepository.query(anyLong(), anyString())).thenReturn([scheduleTask])
        when(scheduleRecordFactory.create(any(), any())).thenReturn(new ScheduleRecordVO(1l, "dspOrderId", 0, 0, "event", [new ScheduleTaskRecordVO(1l, 0, 0, 0, "reward", 0)]))

        when:
        shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(scheduleDO, ScheduleEventType.UNDEFINED))

        then:
        scheduleDO.getScheduleId() == 1L
    }

    def "test execute 1"() {
        given:
        ScheduleDO scheduleDO = new ScheduleDO(1l, "dspOrderId", new ScheduleStrategyVO(1L, [new ScheduleStrategyItemValueVO(1, 1, 1, 1, 1)]), 1, ScheduleStatus.EXECUTING, [], new Date(), new Date(), new Date())
        when(taskRepository.query(anyLong(), anyString())).thenReturn([scheduleTask])
        when(scheduleRecordFactory.create(any(), any())).thenReturn(new ScheduleRecordVO(1l, "dspOrderId", 0, 0, "event", [new ScheduleTaskRecordVO(1l, 0, 0, 0, "reward", 0)]))

        when:
        shutdownScheduleExeCmd.execute(new ShutdownScheduleCommand(scheduleDO, ScheduleEventType.UNDEFINED))

        then:
        scheduleDO.getScheduleId() == 1L
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme