package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteTaskValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    private static DspOrderVO dspOrderVO
    @Mock
    private static DriverVO driverVO
    @Mock
    private static TransportGroupVO transportGroupVO
    @Mock
    private static ScheduleTaskDO scheduleTaskDO
    @Mock
    private static SubSkuVO subSkuVO
    @Mock
    private static ScheduleDO scheduleDO
    @InjectMocks
    ExecuteTaskValidator executeTaskValidator

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate"() {
        given:
        when(dspOrderVO.isDispatching()).thenReturn(dispatch)
        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED.getCode())
        when(scheduleTaskDO.isWaitExecute()).thenReturn(waitExecute)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(subSkuVO.getDspType()).thenReturn(dspType)

        when:
        executeTaskValidator.validate(dspOrderVO, scheduleTaskDO)

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == code//todo - validate something

        where:
        dspType                | waitExecute | dispatch || code
        DspType.SYSTEM_ASSIGN  | false       | true     || ErrorCode.TASK_NOT_WAIT_EXECUTE
        DspType.SYSTEM_ASSIGN  | true        | false    || ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR
        DspType.VBK_BROADCAST  | true        | false    || ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR
        DspType.GRAB_BROADCAST | true        | false    || ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme