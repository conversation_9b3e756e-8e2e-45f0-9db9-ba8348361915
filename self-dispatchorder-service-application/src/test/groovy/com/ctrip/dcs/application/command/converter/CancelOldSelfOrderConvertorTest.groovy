package com.ctrip.dcs.application.command.converter

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand
import com.ctrip.dcs.application.provider.converter.CancelOldSelfOrderConverter
import spock.lang.Specification

class CancelOldSelfOrderConvertorTest extends Specification {


    def "test cancel convert"() {
        given:
        def request = new CancelDspOrderCommand(userOrderId: "11", orderFine: BigDecimal.valueOf(100))

        when: "执行校验方法"
        def order = CancelOldSelfOrderConverter.converter(request)

        then: "验证校验结果"
        order.userOrderId == "11"
    }


}
