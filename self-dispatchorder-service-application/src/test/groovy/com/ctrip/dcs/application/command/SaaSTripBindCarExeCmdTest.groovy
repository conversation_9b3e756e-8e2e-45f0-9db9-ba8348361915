package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.command.validator.SaaSTripBindCarValidator
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.*
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.entity.UserBookInfo
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSCarInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOperatorInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOrderInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSSupplierInfo
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class SaaSTripBindCarExeCmdTest extends Specification {

    def saaSTripBindCarValidator = Mock(SaaSTripBindCarValidator)
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)


    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def orderQueryService = Mock(QueryDspOrderService)

    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def confirmSaasDspOrderService = Mock(ConfirmSaasDspOrderServiceImpl)


    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)

    def driverOrderGateway = Mock(DriverOrderGateway)
    def igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
    def updateDspOrderConfirmRecordService = Mock(UpdateDspOrderConfirmRecordService)
    def queryTransportGroupService =Mock(QueryTransportGroupService)

    def executor = new SaaSTripBindCarExeCmd(
            saaSTripBindCarValidator: saaSTripBindCarValidator,
            queryDriverService: queryDriverService,
            queryVehicleService: queryVehicleService,
            orderQueryService: orderQueryService,

            checkService: checkService,
            driverOrderFactory: driverOrderFactory,
            confirmDspOrderService: confirmDspOrderService,
            confirmSaasDspOrderService: confirmSaasDspOrderService,

            messageProducer: messageProducer,
            manualSubSkuConf: manualSubSkuConf,
            subSkuRepository: subSkuRepository,
            distributedLockService: distributedLockService,

            selfOrderQueryGateway: selfOrderQueryGateway,
            driverOrderGateway: driverOrderGateway,
            igtOrderQueryServiceGateway: igtOrderQueryServiceGateway,
            updateDspOrderConfirmRecordService: updateDspOrderConfirmRecordService,
            queryTransportGroupService:queryTransportGroupService
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO
        queryVehicleService.query(_,_) >> vehicleVO
        checkService.check(_) >> checkModel
        subSkuRepository.find(_) >> subSkuVO
        queryDriverService.queryDriver(_ as Long,_ as ParentCategoryEnum, _ as Long) >> driverVO
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        queryTransportGroupService.queryTransportGroup(_) >> new TransportGroupVO()

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req           |  lock | dspOrderVO       | vehicleVO      |   checkModel      | subSkuVO        |driverVO | userOrderDetail || res
        getSelfReq()  |  true | getDspOrderVO()  | getVehicleVO() |   getCheckModel() | getSubSkuVO() |getDriverVO()  | queryUserOrderDetail()||"16211561979363356"
        getSelfReq()  |  true | getOtherStatusDspOrderVO()  | getVehicleVO() |   getCheckModel()   | getSubSkuVO() |getDriverVO()  | queryUserOrderDetail()||"16211561979363356"

    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO
        queryVehicleService.query(_,_) >> vehicleVO
        checkService.check(_) >> checkModel
        subSkuRepository.find(_) >> subSkuVO
        queryDriverService.queryDriver(_ as Long,_ as ParentCategoryEnum, _ as Long) >> driverVO
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        queryTransportGroupService.queryTransportGroup(_) >> new TransportGroupVO()

        when: "执行校验方法"
        def order = executor.execute(req)
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res

        where:
        req           |  lock | dspOrderVO       | vehicleVO      |   checkModel      | isIsNew | subSkuVO        |driverVO | userOrderDetail || res
        getSelfReq()  |  false | getDspOrderVO()  | getVehicleVO() |   getCheckModel() | true    | getSubSkuVO() |getDriverVO()  | queryUserOrderDetail()||"10033"
    }



    SaaSOperateDriverCarCommand getSelfReq() {
        def saasBindCarRequestType = new SaasBindCarRequestType()
        saasBindCarRequestType.setOrder(buildSaaSOrderInfo())
        saasBindCarRequestType.setOperator(buildSaaSOperatorInfo())
        saasBindCarRequestType.setSupplier(buildSaaSSupplierInfo())
        saasBindCarRequestType.setCar(buildSaaSSelfCarInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasBindCarRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        order.setOrderStatus(OrderStatusEnum.DRIVER_CAR_CONFIRMED.code)
        order.setDriverId(1)
        return order
    }

    DspOrderVO getOtherStatusDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        order.setOrderStatus(OrderStatusEnum.DRIVER_CONFIRMED.code)
        order.setDriverId(1)
        order.setDriverOrderId("12123")
        return order
    }


    SubSkuVO getSubSkuVO() {
        SubSkuVO order = new SubSkuVO()
        order.setDspType(DspType.VBK_ASSIGN)
        order.setTakenType(TakenType.SUPPLIER)
        order.setSubSkuId(1)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }


    CheckModel getCheckModel() {
        DspModelVO dspModelVO = new DspModelVO()
        CheckModel order = new CheckModel(dspModelVO)
        DspModelVO dspModel = new DspModelVO()
        TransportGroupVO transportGroupVO = new TransportGroupVO()
        transportGroupVO.setTransportGroupMode(TransportGroupMode.DEFAULT_MANUAL_DISPATCH)
        transportGroupVO.setTransportGroupId(111)
        dspModel.setTransportGroup(transportGroupVO)
        order.setModel(dspModel)
        return order
    }

    SaaSOrderInfo buildSaaSOrderInfo(){
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }



    SaaSCarInfo buildSaaSSelfCarInfo(){
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo(){
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo(){
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    UserOrderDetail queryUserOrderDetail(){
        UserOrderDetail userOrderDetail = new UserOrderDetail();

        UserBookInfo userBookInfo = new UserBookInfo()
        userBookInfo.setCarTypeId(111L)
        userBookInfo.setCategoryCode("station_dropoff")
        userOrderDetail.setBookInfo(userBookInfo)
        return userOrderDetail;
    }
}
