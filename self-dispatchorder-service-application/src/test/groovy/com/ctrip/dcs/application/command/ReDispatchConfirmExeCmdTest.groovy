package com.ctrip.dcs.application.command


import com.ctrip.dcs.application.command.api.ReDispatchConfirmCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.Mock
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class ReDispatchConfirmExeCmdTest extends Specification {
    @Mock
    Logger logger
    def queryDspOrderService = Mock(QueryDspOrderService);
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository);
    def confirmDspOrderService = Mock(ConfirmDspOrderService);
    def queryTransportGroupService = Mock(QueryTransportGroupService);

    def impl = new ReDispatchConfirmExeCmd(
            queryDspOrderService: queryDspOrderService,
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            confirmDspOrderService: confirmDspOrderService,
            queryTransportGroupService: queryTransportGroupService

    )

    @Unroll
    def "execute  Test1"() {
        given:
        def cmd = new ReDispatchConfirmCommand("111", "222")
        queryDspOrderService.query(_) >> dspOrderVo
        dspOrderConfirmRecordRepository.find(_) >> parentConfirmRecord
        queryTransportGroupService.queryTransportGroup(_) >> transportGroup

        when:
        def resdto = impl.execute(cmd)
        then:
        resdto != null == res

        where:
        dspOrderVo       | parentConfirmRecord | transportGroup || res
        null      | _                   | _              || false
        getDspOrderVO1() | null                | _              || false
        getDspOrderVO2() | null                | _              || false
        getDspOrderVO2() | getDspOrderConfirmRecordVO()                | getTransportGroupVO()            || false
    }


    DspOrderDO getdsporder() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", dspOrderId: "123", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, spId: 1000)

        return dspOrderDO
    }

    DspOrderVO getDspOrderVO() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", productType: 1)
        return dspOrderVO
    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.FROM_AIRPORT,orderStatus: 200)
        return dspOrderVO
    }

    DspOrderVO getDspOrderVO2() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.FROM_AIRPORT,orderStatus: 200,confirmRecordId: 33)
        return dspOrderVO
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVO() {
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord(transportGroupId: 2)
        def reocrd = new DspOrderConfirmRecordVO(driverInfo:driverRecord,duid:"47067200989216864-1702697426785-1-1-8002-3-4-0-0-0" )
        return reocrd
    }

    TransportGroupVO getTransportGroupVO() {
        def transportGroupVO = new TransportGroupVO(supplierId: 1)
        return transportGroupVO
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme