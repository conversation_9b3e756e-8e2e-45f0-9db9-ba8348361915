package com.ctrip.dcs.application.event.impl

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.event.ICharterOrderDispatchModifyMsgService
import com.ctrip.dcs.application.event.dto.DriverSupplierModifyEvent
import com.ctrip.dcs.application.event.dto.DriverVehicleChangeEvent
import com.ctrip.dcs.domain.common.enums.AccountTypeEnum
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.schedule.gateway.DriverPlatformApiServiceGateway
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Lists
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverVehicleChangeEventHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ReDispatchSubmitExeCmd submitExeCmd
    @Mock
    ICharterOrderDispatchModifyMsgService modifyMsgService
    @Mock
    DriverPlatformApiServiceGateway driverPlatformGateway

    @InjectMocks
    DriverVehicleChangeEventHandler driverVehicleChangeEventHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    /** 查询无订单情况 */
    @Unroll
    def "test handle no orders"() {
        given: "Mock数据"
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn(Lists.newArrayList())

        when: "执行校验方法"
        boolean result = driverVehicleChangeEventHandler.handle(new DriverVehicleChangeEvent(accountType: String.valueOf(AccountTypeEnum.BD_ACCOUNT.code), driverId: 1L, supplierId: 10L, fromDrvGuide: fromDrvGuide))

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        fromDrvGuide || expected
        false        || false
        true         || false
    }


    /** 查询用车消息 有订单 非灰度情况 */
    @Unroll
    def "test handle qmq have orders"() {
        given: "Mock数据"
        List<OrderVO> list = getOrderList();
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), anyList())).thenReturn(list)
        when(driverPlatformGateway.checkDrvGuideGrayFlow(new DriverVO(driverId: 1L, isDrvGuide: fromDrvGuide, supplier: new SupplierVO(supplierId: 10L)))).thenReturn(drvGuideFlow)
        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO> newResult().success().build())

        when: "执行校验方法"
        boolean result = driverVehicleChangeEventHandler.handle(new DriverVehicleChangeEvent(accountType: String.valueOf(AccountTypeEnum.BD_ACCOUNT.code), driverId: 1L, supplierId: 10L, fromDrvGuide: fromDrvGuide))

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        fromDrvGuide || drvGuideFlow || expected
        false        || false        || true
        false        || true         || true
        true         || true         || true
    }

    /**
     * mock 订单列表数据
     */
    List<OrderVO> getOrderList() {
        def orderVOList = new ArrayList<OrderVO>()
        // 老订单
        orderVOList.add(new OrderVO(newOrder: false, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"***********",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId:"54321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 境外订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.IGT, driverId: 1L, driverPhone:"***********",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId:"54321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 13:00:00"))));
        // 非携程订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"***********",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId:"54321", orderSourceCode: OrderSourceCodeEnum.KLOOK.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程包车订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"***********",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId:"54321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 携程接送订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"***********",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "12345", dspOrderId:"54321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));

        return orderVOList
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme