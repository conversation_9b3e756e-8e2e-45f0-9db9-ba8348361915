package com.ctrip.dcs.application.command.grabOrderSnapshot

import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd
import com.ctrip.dcs.application.command.api.UpdateGrabOrderRuleCommand
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderRewardVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceItemVO
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.value.TransportGroupOrderConfig
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDetailDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderDetailPO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO
import com.ctrip.dcs.infrastructure.common.config.CheckGrabDriverIndexConfig
import com.ctrip.dcs.infrastructure.handler.DispatcherGrabOrderPushRuleHandler
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.jackson.JacksonSerializer
import com.google.common.collect.Maps
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateGrabOrderRuleExeCmdTest extends Specification {
    @Mock
    UpdateGrabOrderSnapshotExeCmd updateGrabOrderSnapshotExeCmd
    @Mock
    DispatcherGrabOrderPushRuleHandler dispatcherGrabOrderPushRuleHandler
    @Mock
    CreateScheduleExeCmd createScheduleExeCmd
    @Mock
    UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    DspOrderDao dspOrderDao
    @Mock
    DspOrderDetailDao dspOrderDetailDao
    @Mock
    GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository
    @Mock
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    @Mock
    SubSkuRepository subSkuRepository
    @Mock
    QueryDriverService queryDriverService
    @Mock
    DriverDomainServiceGateway driverDomainServiceGateway
    @Mock
    CheckService checkService
    @Mock
    CheckGrabDriverIndexConfig checkConfig
    @Mock
    DistributedLockService distributedLockService
    @Mock
    GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService
    @InjectMocks
    UpdateGrabOrderRuleExeCmd updateGrabOrderRuleExeCmd

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(dispatcherGrabOrderPushRuleHandler.queryGrabOrderPushRule(anyLong(), anyLong(), anyString(), anyLong())).thenReturn(new GrabDspOrderPushRuleDO())
        when(updateOrderSettlementBeforeTakenCmd.execute(any(UpdateOrderSettlementBeforeTakenCommand.class))).thenReturn(Mock(OrderSettlePriceVO))
        when(dspOrderDao.queryDspOrdersBySupplierId(anyLong(), anyInt(), anyList(), anyInt())).thenReturn([new DspOrderPO(dspOrderId: "dspOrderId", userOrderId: "userOrderId", categoryCode: "categoryCode", cityId: 0, vehicleGroupId: 0, spId: 0, supplierId: 0, orderSourceCode: 0)])
        when(dspOrderDetailDao.find(anyString())).thenReturn(new DspOrderDetailPO(extendInfo: "extendInfo"))
        when(grabDspOrderSnapshotRepository.query(any(List<String>.class), anyLong(), any(List<Integer>.class))).thenReturn([new GrabDspOrderSnapshotDO(dspOrderId: "dspOrderId")])

        when:
        def result = updateGrabOrderRuleExeCmd.execute(new UpdateGrabOrderRuleCommand(1l, 1L, "jnt"))

        then:
        result == null
    }

    def "test create Schedule"() {
        given:
        Map<String, Object> extendInfo = Maps.newHashMap()
        extendInfo.put("preDriverGuideAmount", BigDecimal.ONE)
        extendInfo.put("preDriverGuideCurrency", "CNY")
        extendInfo.put("settleToDriver", 1)
        when(dispatcherGrabOrderPushRuleHandler.queryGrabOrderPushRule(anyLong(), anyLong(), anyString(), anyLong())).thenReturn(new GrabDspOrderPushRuleDO())
        when(updateOrderSettlementBeforeTakenCmd.execute(any(UpdateOrderSettlementBeforeTakenCommand.class))).thenReturn(Mock(OrderSettlePriceVO))
        when(dspOrderDetailDao.find(anyString())).thenReturn(new DspOrderDetailPO(extendInfo: "{}"))

        when:
        def result = updateGrabOrderRuleExeCmd.createSchedule(new DspOrderPO(dspOrderId: "dspOrderId", userOrderId: "userOrderId", categoryCode: "categoryCode", cityId: 0, vehicleGroupId: 0, spId: 0, supplierId: 0, orderSourceCode: 0))

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme