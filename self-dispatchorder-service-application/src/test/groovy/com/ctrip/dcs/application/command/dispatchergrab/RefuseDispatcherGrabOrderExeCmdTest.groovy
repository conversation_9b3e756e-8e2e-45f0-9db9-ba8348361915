package com.ctrip.dcs.application.command.dispatchergrab

import com.ctrip.dcs.application.command.api.RefuseDispatcherGrabOrderCommand
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DispatcherGrabOrderDao
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RefuseDispatcherGrabOrderExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatcherGrabOrderGateway dispatcherGrabOrderGateway
    @Mock
    DispatcherGrabOrderDao dispatcherGrabOrderDao
    @InjectMocks
    RefuseDispatcherGrabOrderExeCmd refuseDispatcherGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        DispatcherGrabOrderDO order = DispatcherGrabOrderDO.builder().id(1L).grabStatus(DispatcherGrabOrderStatusEnum.INIT).build()
        when(dispatcherGrabOrderGateway.query(anyString(), anyLong())).thenReturn(order)
        when(dispatcherGrabOrderDao.refuse(anyLong(),any())).thenReturn(1)

        when:
        refuseDispatcherGrabOrderExeCmd.execute(new RefuseDispatcherGrabOrderCommand("1", 1l,"mobile",0,null))

        then:
        order.getGrabStatus() == DispatcherGrabOrderStatusEnum.REFUSE
    }

    def "test execute fail"() {
        given:
        when(dispatcherGrabOrderGateway.query(anyString(), anyLong())).thenReturn(order)

        when:
        refuseDispatcherGrabOrderExeCmd.execute(new RefuseDispatcherGrabOrderCommand("1", 1l,"mobile",0,null))

        then:
        BizException e = thrown(BizException)
        code == e.code

        where :
        order                                                                                         | i || code
        null                                                                                          | 0 || ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getCode()
        DispatcherGrabOrderDO.builder().id(1L).grabStatus(DispatcherGrabOrderStatusEnum.GRAB).build() | 0 || ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getCode()
        DispatcherGrabOrderDO.builder().id(1L).grabStatus(DispatcherGrabOrderStatusEnum.INIT).build() | 0 || ErrorCode.REFUSE_DISPATCH_GRAB_ORDER_ERROR.getCode()

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
