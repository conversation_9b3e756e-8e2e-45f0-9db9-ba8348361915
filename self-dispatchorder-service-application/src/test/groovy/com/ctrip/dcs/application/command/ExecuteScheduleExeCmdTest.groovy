package com.ctrip.dcs.application.command

import cn.hutool.core.math.Money
import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand
import com.ctrip.dcs.application.command.validator.ExecuteScheduleValidator
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO

import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.RewardVO
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.carconfig.ScheduleStrategyItemValueVO
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ScheduleInfo
import org.assertj.core.util.Lists
import org.junit.Assert
import qunar.tc.qconfig.client.spring.QConfig
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class ExecuteScheduleExeCmdTest extends Specification {

    def taskRepository = Mock(ScheduleTaskRepository)
    def scheduleRepository = Mock(ScheduleRepository)
    def recordRepository = Mock(ScheduleRecordRepository)
    def recordFactory = Mock(ScheduleRecordFactory)
    def messageProvider = Mock(MessageProviderService)
    def scheduleTask = Mock(ScheduleTaskDO)
    def distributedLockService = Mock(DistributedLockService)
    def lock = Mock(DistributedLockService.DistributedLock)
    def dspOrderService = Mock(QueryDspOrderService)
    def schedule = Mock(ScheduleDO)
    def dspOrder = Mock(DspOrderVO)
    def validator = Mock(ExecuteScheduleValidator)
    def vbkDriverGrabOrderRepository = Mock(VBKDriverGrabOrderRepository)
    def vbkDriverGrabOrder = Mock(VBKDriverGrabOrderDO)
    def commonConf = Mock(Map<String,String>)

    def executor = new ExecuteScheduleExeCmd(
            taskRepository: taskRepository,
            scheduleRepository: scheduleRepository,
            scheduleRecordRepository: recordRepository,
            scheduleRecordFactory: recordFactory,
            messageProducer: messageProvider,
            distributedLockService: distributedLockService,
            queryDspOrderService: dspOrderService,
            validator: validator,
            vbkDriverGrabOrderRepository: vbkDriverGrabOrderRepository,
            commonConf: commonConf
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        distributedLockService.getLock(_) >> lock
        lock.tryLock() >> true
        scheduleRepository.find(_) >> schedule
        taskRepository.query(_, _) >> [scheduleTask]
        schedule.getScheduleId() >> 1
        schedule.isShutdown() >> shutdown
        schedule.isExecuting(_) >> executing
        schedule.isTimeout(_) >> timeout
        schedule.getType(_) >> ScheduleType.SYSTEM
        schedule.retry() >> retry
        dspOrderService.queryOrderDetailForSchedule(_,_,_) >> dspOrder
        def cmd = new ExecuteScheduleCommand("1", 1, 0, ScheduleEventType.DSP_ORDER_BOOK)

        when: "执行校验方法"
        executor.execute(cmd)

        then: "验证校验结果"
        cmd.getScheduleId() == 1L

        where:
        shutdown | executing | timeout | retry
        true     | null      | null    | null
        false    | null      | null    | 1L
        false    | true      | null    | 1L
        false    | false     | true    | 1L
        false    | false     | false   | 1L
    }

    @Unroll
    def "test execute_2"() {

        given: "Mock数据"
        distributedLockService.getLock(_) >> lock
        lock.tryLock() >> true
        scheduleRepository.find(_) >> schedule
        taskRepository.query(_, _) >> [scheduleTask]
        schedule.getScheduleId() >> 1
        schedule.isShutdown() >> shutdown
        schedule.isExecuting(_) >> executing
        schedule.isTimeout(_) >> timeout
        schedule.getType(_) >> ScheduleType.SYSTEM
        schedule.retry() >> retry
        dspOrderService.queryOrderDetailForSchedule(_,_,_) >> dspOrder
        def cmd = new ExecuteScheduleCommand("1", 1, 0, ScheduleEventType.DSP_ORDER_BOOK)

        when: "执行校验方法"
        executor.execute(cmd)

        then: "验证校验结果"
        cmd.getScheduleId() == 1L

        where:
        shutdown | executing | timeout | retry
        true     | null      | null    | null
        false    | null      | null    | 1L
        false    | true      | null    | 1L
        false    | false     | true    | 1L
        false    | false     | false   | 1L
    }

    def "test execute 1"() {

        given: "Mock数据"
        distributedLockService.getLock(_) >> lock
        lock.tryLock() >> true
        scheduleRepository.find(_) >> schedule
        taskRepository.query(_, _) >> [scheduleTask]
        schedule.getScheduleId() >> 1
        schedule.getType(_) >> ScheduleType.SYSTEM
        scheduleTask.isExecute() >> true
        dspOrderService.queryOrderDetailForSchedule(_,_,_) >> dspOrder
        def cmd = new ExecuteScheduleCommand("1", 1, 0, ScheduleEventType.DSP_ORDER_BOOK)

        when: "执行校验方法"
        executor.execute(cmd)

        then: "验证校验结果"
        cmd.getScheduleId() == 1L
    }

    @Unroll
    def "test query dsp reward"() {
        given: "Mock数据"
        vbkDriverGrabOrderRepository.queryBySupplierId(_, _) >> vbkDriverGrabOrder
        schedule.getType() >> ScheduleType.VBK

        when: "执行校验方法"
        RewardVO reward = executor.queryDspReward(schedule, dspOrder)

        then: "验证校验结果"
        reward.getVbkReward().getAmount() == BigDecimal.ZERO
    }

    def "test queryScheduleList"() {
        given:
        ScheduleDO scheduleDO = new ScheduleDO(1l, "dspOrderId", new ScheduleStrategyVO(1L, [new ScheduleStrategyItemValueVO(1, 1, 1, 1, 1)]), 1, ScheduleStatus.EXECUTING, [], new Date(), new Date(), new Date())
        List<ScheduleDO> scheduleDoList = Lists.newArrayList();
        scheduleDoList.add(scheduleDO);
        scheduleRepository.query(_) >> scheduleDoList;

        when:
        List<ScheduleInfo> list = executor.queryScheduleList("2131");

        then:
        Assert.assertTrue(Objects.equals(list.size(), 1))
    }
}
