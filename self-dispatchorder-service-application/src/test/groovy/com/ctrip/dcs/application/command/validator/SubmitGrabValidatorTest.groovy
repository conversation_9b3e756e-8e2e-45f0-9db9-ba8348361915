package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SubmitGrabValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    private static GrabOrderDO grabOrderDO
    @InjectMocks
    SubmitGrabValidator submitGrabValidator

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate 1"() {
        given:

        when:
        submitGrabValidator.validate(null)

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == ErrorCode.GRAB_ORDER_NULL_ERROR

    }

    def "test validate 2"() {
        given:
        when(grabOrderDO.getExpire()).thenReturn(1L)

        when:
        submitGrabValidator.validate(grabOrderDO)

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == ErrorCode.GRAB_ORDER_EXPIRE_ERROR
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme