package com.ctrip.dcs.application.command

import cn.hutool.core.math.Money
import com.ctrip.dcs.application.command.api.ExecuteTaskCommand
import com.ctrip.dcs.application.command.validator.ExecuteTaskValidator
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ExecuteTaskExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ExecuteTaskValidator validator
    @Mock
    DistributedLockServiceImpl.RedisDistributedLock lock
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    SubSkuVO subSku
    @Mock
    ConfigService commonConfConfig
    @InjectMocks
    ExecuteTaskExeCmd executeTaskExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }


    def "test execute_xiao"() {
        given:
        ScheduleTaskDO scheduleTaskDO1 = ScheduleTaskDO.builder().dspOrderId("1").reward(new Money()).subSku(SubSkuVO.builder().dspType(DspType.SYSTEM_ASSIGN).build()).build()
        ExecuteTaskCommand command = new ExecuteTaskCommand(1l,"1", "dspOrderId")
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(taskRepository.find(anyString(), anyString())).thenReturn(scheduleTaskDO1)
        when(queryDspOrderService.queryOrderDetailForTask(anyString(), any())).thenReturn(dspOrderVO)
        when(lock.tryLock()).thenReturn(true)
        when(commonConfConfig.getString(anyString())).thenReturn("1,2,3")


        when:
        executeTaskExeCmd.execute(command)

        then:
        command.getTaskId() == 1l
    }


    def "test execute_xiao1"() {
        given:
        ScheduleTaskDO scheduleTaskDO1 = ScheduleTaskDO.builder().dspOrderId("1").reward(new Money()).subSku(SubSkuVO.builder().dspType(DspType.SYSTEM_ASSIGN).build()).build()
        ExecuteTaskCommand command = new ExecuteTaskCommand(1l,"1", "dspOrderId")
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(taskRepository.find(anyString(), anyString())).thenReturn(scheduleTaskDO1)
        when(queryDspOrderService.queryOrderDetailForTask(anyString(), any())).thenReturn(dspOrderVO)
        when(lock.tryLock()).thenReturn(true)
        when(commonConfConfig.getString(anyString())).thenReturn("1,3")


        when:
        executeTaskExeCmd.execute(command)

        then:
        command.getTaskId() == 1l
    }

    def "test execute"() {
        given:
        ExecuteTaskCommand command = new ExecuteTaskCommand(1l,"1", "dspOrderId")
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(taskRepository.find(anyLong(), anyString())).thenReturn(scheduleTaskDO)
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(lock.tryLock()).thenReturn(true)

        when:
        executeTaskExeCmd.execute(command)

        then:
        command.getTaskId() == 1l
    }

    def "test execute lock false"() {
        given:
        ExecuteTaskCommand command = new ExecuteTaskCommand(1l, "1", "dspOrderId")
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(taskRepository.find(anyLong(), anyString())).thenReturn(scheduleTaskDO)
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(lock.tryLock()).thenReturn(false)

        when:
        executeTaskExeCmd.execute(command)

        then:
        command.getTaskId() == 1l
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme