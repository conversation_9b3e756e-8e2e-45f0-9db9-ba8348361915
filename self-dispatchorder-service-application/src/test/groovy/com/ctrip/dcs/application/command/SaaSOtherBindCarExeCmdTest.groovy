package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.command.validator.SaaSOtherBindCarValidator
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSCarInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOperatorInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSOrderInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSSupplierInfo
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class SaaSOtherBindCarExeCmdTest extends Specification {

    def saaSOtherBindCarValidator = Mock(SaaSOtherBindCarValidator)
    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def orderQueryService = Mock(QueryDspOrderService)

    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def confirmSaasDspOrderService = Mock(ConfirmSaasDspOrderServiceImpl)


    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)

    def driverOrderGateway = Mock(DriverOrderGateway)
    def updateDspOrderConfirmRecordService = Mock(UpdateDspOrderConfirmRecordService)



    def executor = new SaaSOtherBindCarExeCmd(
            saaSOtherBindCarValidator: saaSOtherBindCarValidator,
            queryDriverService: queryDriverService,
            queryVehicleService: queryVehicleService,
            queryDspOrderService: queryDspOrderService,

            checkService: checkService,
            driverOrderFactory: driverOrderFactory,
            confirmDspOrderService: confirmDspOrderService,
            confirmSaasDspOrderService: confirmSaasDspOrderService,

            messageProducer: messageProducer,
            manualSubSkuConf: manualSubSkuConf,
            subSkuRepository: subSkuRepository,
            distributedLockService: distributedLockService,
            orderQueryService: orderQueryService,

            driverOrderGateway: driverOrderGateway,
            updateDspOrderConfirmRecordService: updateDspOrderConfirmRecordService
    )


    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        queryDspOrderService.query(_) >> dspOrderVO
        queryVehicleService.query(_,_) >> vehicleVO
        checkService.check(_) >> checkModel

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req           |  lock | dspOrderVO       | vehicleVO      |   checkModel      | isIsNew       || res
        getOtherReq() |  true | getDspOrderVO()  | getVehicleVO() |  getCheckModel() | true           || ""

    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        queryDspOrderService.query(_) >> dspOrderVO
        queryVehicleService.query(_,_) >> vehicleVO
        checkService.check(_) >> checkModel

        when: "执行校验方法"
        def order = executor.execute(req)
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res


        where:
        req           |  lock | dspOrderVO       | vehicleVO      |   checkModel      | isIsNew  || res
        getOtherReq() |  false| getDspOrderVO()  | getVehicleVO() |   getCheckModel() | true     || "10033"
    }

    @Unroll
    def "test buildBussiness"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        queryDspOrderService.query(_) >> dspOrderVO
        queryVehicleService.query(_,_) >> vehicleVO
        checkService.check(_) >> checkModel

        when: "执行校验方法"
        def order = executor.buildSaaSBusinessVO(req)
        then: "验证校验结果"
        order.vehicleVO != null == res


        where:
        req           |  lock | dspOrderVO       | vehicleVO      |   checkModel      | isIsNew  || res
        getOtherReqNew(1) |  false| getDspOrderVO()  | getVehicleVO() |   getCheckModel() | true     || true
        getOtherReqNew(0) |  false| getDspOrderVO()  | getVehicleVO() |   getCheckModel() | true     || true
        getOtherReqNew(null) |  false| getDspOrderVO()  | getVehicleVO() |   getCheckModel() | true     || true
    }

    SaaSOperateDriverCarCommand getOtherReqNew(carId) {
        SaasBindCarRequestType saasBindCarRequestType = new SaasBindCarRequestType()
        saasBindCarRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasBindCarRequestType.setOperator(buildSaaSOperatorInfo())
        saasBindCarRequestType.setSupplier(buildSaaSSupplierInfo())
        saasBindCarRequestType.setCar(buildSaaSOtherCarInfo())
        saasBindCarRequestType.setNewProcess(true)
        saasBindCarRequestType.getCar().setCarId(carId)
        def cmd = SaaSOperateDriverCarConverter.converter(saasBindCarRequestType)
        return cmd
    }

    SaaSOperateDriverCarCommand getOtherReq() {
        SaasBindCarRequestType saasBindCarRequestType = new SaasBindCarRequestType()
        saasBindCarRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasBindCarRequestType.setOperator(buildSaaSOperatorInfo())
        saasBindCarRequestType.setSupplier(buildSaaSSupplierInfo())
        saasBindCarRequestType.setCar(buildSaaSOtherCarInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasBindCarRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        def order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        return order
    }

    VehicleVO getVehicleVO() {
        def order = new VehicleVO()
        order.setCarId(16211561979363356L)
        return order
    }

    CheckModel getCheckModel() {
        DspModelVO dspModelVO = new DspModelVO()
        def order = new CheckModel(dspModelVO)
        return order
    }

    SaaSOrderInfo buildSaaSOtherOrderInfo(){
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        return saaSOrderInfo
    }


    SaaSCarInfo buildSaaSOtherCarInfo(){
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarBrandId(1)
        saaSCarInfo.setCarColorId(2)
        saaSCarInfo.setCarLicense("京ACD933")
        saaSCarInfo.setCarSeriesId(3)
        saaSCarInfo.setCarTypeId(4)
        saaSCarInfo.setCarDesc("desc")
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo(){
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo(){
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }


}
