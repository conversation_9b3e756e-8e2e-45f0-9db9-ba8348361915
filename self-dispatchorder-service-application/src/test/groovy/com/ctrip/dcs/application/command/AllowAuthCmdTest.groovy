package com.ctrip.dcs.application.command

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DriverOrderStatusEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import spock.lang.Specification

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/20 16:47
 */
class AllowAuthCmdTest extends Specification {

    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)

    def command = new AllowAuthCmd(selfOrderQueryGateway: selfOrderQueryGateway)

    def "queryAllowAuth test"() {
        given:
        def userOrderId = "userOrderId"
        selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null) >> null
        when:
        def res = command.queryAllowAuth(excepteRoleId, userOrderId)
        then:
        res.isSuccess() == expectRes
        res.code == expectCode
        res.msg == expectMsg
        where:
        excepteRoleId                                                       || expectRes     || expectCode                                               || expectMsg
        ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getCode().intValue()      || Boolean.FALSE || ErrorCode.DISPATCH_SUBMIT_NOT_FIND_ORDER_ERROR.getCode() || ErrorCode.DISPATCH_ORDER_STATUS_ERROR.getDesc()
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.getCode().intValue() || Boolean.FALSE || ""                                                       || ErrorCode.DISPATCH_NOT_ALLOW_ERROR.getDesc()
        ReassignTaskEnum.RoleEnum.ROLE_OTA_API.getCode().intValue()         || Boolean.FALSE || ""                                                       || ErrorCode.DISPATCH_SUBMIT_NOT_FIND_ORDER_ERROR.getDesc()
    }

    def "queryAllowAuth1 test"() {
        given:
        def userOrderId = "userOrderId"
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.DAY_RENTAL.type)
        selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null) >> baseDetail
        when:
        def res = command.queryAllowAuth(excepteRoleId, userOrderId)
        then:
        res.isSuccess() == expectRes
        res.code == expectCode
        res.msg == expectMsg
        where:
        excepteRoleId                                               || expectRes     || expectCode || expectMsg
        ReassignTaskEnum.RoleEnum.ROLE_OTA_API.getCode().intValue() || Boolean.FALSE || ""         || ErrorCode.DISPATCH_SUBMIT_NOT_SUPPORT_ORDER_ERROR.getDesc()
    }

    def "queryAllowAuth2 test"() {
        given:
        def userOrderId = "userOrderId"
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, orderStatus: DriverOrderStatusEnum.NEW.getCode().intValue())
        selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null) >> baseDetail
        when:
        def res = command.queryAllowAuth(excepteRoleId, userOrderId)
        then:
        res.isSuccess() == expectRes
        res.code == expectCode
        res.msg == expectMsg
        where:
        excepteRoleId                                                       || expectRes     || expectCode || expectMsg
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.getCode().intValue() || Boolean.FALSE || ""         || ErrorCode.DISPATCH_NOT_ALLOW_STATUS_ERROR.getDesc()
    }

    def "queryAllowAuth3 test"() {
        given:
        def userOrderId = "userOrderId"
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TAXI_ONDEMAND.type, orderStatus: DriverOrderStatusEnum.VIRTUAL_OCCUPY.getCode().intValue())
        selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null) >> baseDetail
        when:
        def res = command.queryAllowAuth(excepteRoleId, userOrderId)
        then:
        res.isSuccess() == expectRes
        res.code == expectCode
        res.msg == expectMsg
        where:
        excepteRoleId                                                       || expectRes     || expectCode || expectMsg
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.getCode().intValue() || Boolean.TRUE  || null         || null
    }

    def "queryAllowAuth4 test"() {
        given:
        def userOrderId = "userOrderId"
        def baseDetail = new BaseDetailVO(categoryCode: CategoryCodeEnum.TO_AIRPORT.type, orderStatus: DriverOrderStatusEnum.VIRTUAL_OCCUPY.getCode().intValue())
        selfOrderQueryGateway.queryOrderBaseDetail(userOrderId, null) >> baseDetail
        when:
        def res = command.queryAllowAuth(ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT.getCode().intValue(), userOrderId)
        then:
        res != null
        res.isSuccess()
    }

    def "allowAuthMethod Test"() {
        given:
        def roleId = expectRoleId
        def baseDetail = new BaseDetailVO(orderStatus: expectOrderStatus,
                categoryCode: CategoryCodeEnum.DAY_RENTAL.type,
                estimatedUseTimeBj: expectTime)
        when:
        def res = command.allowAuthMethod(roleId, baseDetail)
        then: "验证校验结果"
        res == expectRes
        where:
        expectRoleId                                             | expectOrderStatus                                | expectTime            || expectRes
        ReassignTaskEnum.RoleEnum.ROLE_SELF_SUPPLIER             | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.FALSE
        ReassignTaskEnum.RoleEnum.ROLE_BD_CLIENT                 | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.FALSE
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CLIENT           | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.FALSE
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_USER             | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.FALSE
        ReassignTaskEnum.RoleEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.FALSE
        ReassignTaskEnum.RoleEnum.ROLE_OTA_API                   | DriverOrderStatusEnum.BEGIN.getCode().intValue() | "2021-02-02 12:21:12" || Boolean.TRUE
        ReassignTaskEnum.RoleEnum.ROLE_OTA_API                   | DriverOrderStatusEnum.NEW.getCode().intValue()   | "2021-02-02 12:21:12" || Boolean.TRUE
        ReassignTaskEnum.RoleEnum.ROLE_OTA_API                   | DriverOrderStatusEnum.NEW.getCode().intValue()   | "3021-02-02 12:21:12" || Boolean.FALSE
    }

}