package com.ctrip.dcs.application.command.dispatchergrab

import com.ctrip.dcs.application.command.api.DispatcherGrabOrderIvrCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.common.service.IvrCallService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class DispatcherGrabOrderIvrExeCmdTest extends Specification {
    @Mock
    DispatcherGrabOrderGateway dispatcherGrabOrderGateway
    @Mock
    IvrCallService ivrCallService
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    DspOrderVO dspOrderVO
    @InjectMocks
    DispatcherGrabOrderIvrExeCmd dispatcherGrabOrderIvrExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(dispatcherGrabOrderGateway.query(any())).thenReturn([new DispatcherGrabOrderDO(1l, "dspOrderId", "userOrderId", 1l, 1l, 0, "duid", DispatcherGrabOrderStatusEnum.INIT, 0, "operatorUserId", "operatorUserName", "orderSource", new GregorianCalendar(2024, Calendar.JANUARY, 4, 11, 17).getTime(),0)])
        when(ivrCallService.urgentIvr(any(), any())).thenReturn(true)
        when(queryDspOrderService.queryDspOrder(any())).thenReturn(dspOrderVO)

        when:
        dispatcherGrabOrderIvrExeCmd.execute(new DispatcherGrabOrderIvrCommand(1l))

        then:
        queryDspOrderService != null//todo - validate something
    }

    def "test execute empty"() {
        given:
        when(dispatcherGrabOrderGateway.query(any())).thenReturn(null)

        when:
        dispatcherGrabOrderIvrExeCmd.execute(new DispatcherGrabOrderIvrCommand(1l))

        then:
        queryDspOrderService != null//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
