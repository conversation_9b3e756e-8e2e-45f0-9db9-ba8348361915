package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand
import com.ctrip.dcs.application.command.validator.SubmitGrabValidator
import com.ctrip.dcs.application.command.validator.ValidateException
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.GrabOrderType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import javax.annotation.Resource

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SubmitBroadcastExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    BroadcastRepository broadcastRepository
    @Mock
    SelectGrabOrderRepository selectGrabOrderRepository
    @Mock
    SubmitGrabValidator validator
    @Mock
    MessageProviderService messageProvider
    @Mock
    TRocksProviderAdapter rocksProviderAdapter
    @Mock
    IdempotentCheckService idempotentCheckService
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    QueryDspOrderService queryDspOrderService;
    @Mock
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @Mock
    QueryDriverService queryDriverService;
    @InjectMocks
    SubmitBroadcastExeCmd submitBroadcastExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
        when(broadcastRepository.find(any(), any())).thenReturn(grabOrderDO)
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])

        when:
        submitBroadcastExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, "nonPremiumOrderId", ""))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }

    def "test execute 1"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
        when(broadcastRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(validator.validate(any())).thenThrow(new ValidateException(ErrorCode.GRAB_ORDER_EXPIRE_ERROR))

        when:
        submitBroadcastExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, "nonPremiumOrderId", ""))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme