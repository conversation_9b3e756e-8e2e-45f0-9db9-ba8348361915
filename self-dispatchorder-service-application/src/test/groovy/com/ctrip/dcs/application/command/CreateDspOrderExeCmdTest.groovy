package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CreateDspOrderCommand
import com.ctrip.dcs.domain.common.constants.SysConstants
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.BaseLocationVO
import com.ctrip.dcs.domain.common.value.LocationRouteVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.dsporder.entity.*
import com.ctrip.dcs.domain.dsporder.gateway.CityGateway
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.repository.ScheduleStrategyRepository
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO
import com.ctrip.dcs.domain.schedule.value.carconfig.ScheduleStrategyItemValueVO
import com.ctrip.dcs.infrastructure.adapter.carconfig.DspRotateAbTestConfig
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import org.apache.commons.lang3.tuple.Pair
import org.assertj.core.util.Maps
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class CreateDspOrderExeCmdTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderOperateRepository = Mock(DspOrderOperateRepository)
    def distributedLockService = Mock(DistributedLockService)
    def messageProviderService = Mock(MessageProviderService)
    def workBenchLogGateway = Mock(WorkBenchLogGateway)
    def workBenchLogMessageFactory = Mock(WorkBenchLogMessageFactory)
    def idGeneratorService = Mock(IdGeneratorService)
    def igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
    def queryDriverLocationService = Mock(QueryDriverLocationService)
    def dspRotateAbTestConfig = Mock(DspRotateAbTestConfig)
    def strategyRepository = Mock(ScheduleStrategyRepository)
    def shortDistanceStrategyExeCmd = Mock(ShortDistanceStrategyExeCmd)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)
    def commonConfig = Mock(BaseConfigService)
    def cityGateway = Mock(CityGateway)
    def locationBasedService = Mock(LocationBasedService)

    def executor = new CreateDspOrderExeCmd(
            dspOrderRepository: dspOrderRepository,
            dspOrderOperateRepository: dspOrderOperateRepository,
            distributedLockService: distributedLockService,
            messageProvider: messageProviderService,
            workBenchLogGateway: workBenchLogGateway,
            workBenchLogMessageFactory: workBenchLogMessageFactory,
            idGeneratorService: idGeneratorService,
            igtOrderQueryServiceGateway: igtOrderQueryServiceGateway,
            queryDriverLocationService: queryDriverLocationService,
            dspRotateAbTestConfig: dspRotateAbTestConfig,
            strategyRepository: strategyRepository,
            commonConfig: commonConfig,
            shortDistanceStrategyExeCmd: shortDistanceStrategyExeCmd,
            businessTemplateInfoConfig: businessTemplateInfoConfig,
            locationBasedService: locationBasedService,
            cityGateway: cityGateway
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"


        def dspOrderDetailDO = new DspOrderDetailDO()
        dspOrderDetailDO.setDspStrategyStr("{\"dspStrategyId\":36012,\"skuType\":1,\"dspData\":\"{\\\"skuType\\\":\\\"1\\\",\\\"beginSeconds\\\":\\\"0\\\",\\\"useSeconds\\\":\\\"\\\",\\\"addPriceRatio\\\":\\\"0\\\",\\\"addPriceMoney\\\":\\\"0\\\",\\\"maxAddPriceRatioConf\\\":\\\"100\\\",\\\"maxAddPriceMoneyConf\\\":\\\"20\\\",\\\"addPriceRatioConf\\\":\\\"0\\\",\\\"addPriceMoneyConf\\\":\\\"0\\\",\\\"reason\\\":99,\\\"sceneId\\\":\\\"2020082811\\\",\\\"canDown\\\":false,\\\"humanDsp\\\":false,\\\"bookFirstFlag\\\":true,\\\"dspStrategyId\\\":36012,\\\"retryNum\\\":0,\\\"grade\\\":\\\"1_1\\\",\\\"upgradeOrder\\\":false}\",\"bookFirstFlag\":true}")

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);
        dspOrderFeeDO.setOriTollFee(BigDecimal.ONE);


        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock

        dspOrderRepository.queryValidDspOrders(_) >> validOrder

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrder

        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 2L)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(2, 36012L)
        businessTemplateInfoConfig.getValueByKey(_) >> "250402_VAC_ddflAB2q"
        strategyRepository.find(_) >> scheduleStrategyVO


        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        dspOrderId == res

        where:
        lock | validOrder       | userOrder            | scheduleStrategyVO                                                                              || res
        true | _                | getuserOrderDetail() | new ScheduleStrategyVO(strategyId: 1L, items: [])                                               || "123"
        true | _                | getuserOrderDetail() | new ScheduleStrategyVO(strategyId: 1L, items: [new ScheduleStrategyItemValueVO(1, 1, 1, 1, 1)]) || "123"
        true | [getdsporder()]  | _                    | new ScheduleStrategyVO(strategyId: 1L, items: [])                                               || "123"
        true | [getdsporder1()] | getuserOrderDetail() | new ScheduleStrategyVO(strategyId: 1L, items: [])                                               || "123"


    }

    def "test execute 1"() {

        given: "Mock数据"


        def dspOrderDetailDO = new DspOrderDetailDO()

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);


        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 0L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock

        dspOrderRepository.queryValidDspOrders(_) >> validOrder

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrder
        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        dspOrderId == res

        where:
        lock | validOrder       | userOrder            || res
        true | _                | getuserOrderDetail() || "123"
        true | [getdsporder()]  | _                    || "123"
        true | [getdsporder1()] | getuserOrderDetail() || "123"


    }

    def "test execute 2"() {

        given: "Mock数据"


        def dspOrderDetailDO = new DspOrderDetailDO()

        dspOrderDetailDO.setExtendInfoMap(Maps.newHashMap("123", "123"))

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);


        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 1L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock

        dspOrderRepository.queryValidDspOrders(_) >> validOrder

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrder
        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        dspOrderId == res

        where:
        lock | validOrder       | userOrder            || res
        true | _                | getuserOrderDetail() || "123"
        true | [getdsporder()]  | _                    || "123"
        true | [getdsporder1()] | getuserOrderDetail() || "123"


    }

    def "test execute 3"() {

        given: "Mock数据"


        def dspOrderDetailDO = new DspOrderDetailDO()

        dspOrderDetailDO.setExtendInfoMap(Maps.newHashMap("123", "123"))

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);


        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 1L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])


        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock

        dspOrderRepository.queryValidDspOrders(_) >> validOrder

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrder
        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        dspOrderId == res

        where:
        lock | validOrder       | userOrder            || res
        true | _                | getuserOrderDetail() || "123"
        true | [getdsporder()]  | _                    || "123"
        true | [getdsporder1()] | getuserOrderDetail() || "123"


    }

    def "test execute with shortDisOrder"() {
        given: "Mock数据"
        def dspOrderDetailDO = new DspOrderDetailDO()
        dspOrderDetailDO.setExtendInfoMap(Maps.newHashMap("123", "123"))

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);

        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 1L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])

        cityGateway.isChineseMainland(_) >> true

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> true

        dspOrderRepository.queryValidDspOrders(_) >> []

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> getuserOrderDetail2()
        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        commonConfig.getString("short_dis_gray_city_ids") >> shortDisGrayCityIdList
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> checkShortDisOrder

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        dspOrderId == res

        where:
        shortDisGrayCityIdList | checkShortDisOrder || res
        ""                     | false              || "123"
        "all"                  | false              || "123"
        "1"                    | false              || "123"
        "2,10"                 | false              || "123"
        "2,10"                 | true               || "123"
    }

    def "test fixOrderEstimatedInfo"() {
        given: "Mock数据"
        def dspOrderDetailDO = new DspOrderDetailDO()
        dspOrderDetailDO.setExtendInfoMap(Maps.newHashMap("123", "123"))

        def dspOrderFeeDO = new DspOrderFeeDO()
        dspOrderFeeDO.setCostAmount(BigDecimal.ONE);
        dspOrderFeeDO.setNoCommisionAmount(BigDecimal.ZERO);

        def request = new CreateDspOrderCommand(dspOrderDO: getdsporder(), dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 1L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])

        cityGateway.isChineseMainland(_) >> true

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> true

        dspOrderRepository.queryValidDspOrders(_) >> []

        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> getuserOrderDetail2()
        idGeneratorService.generateId() >> 123
        workBenchLogMessageFactory.createDspOrderWorkBenchLog(_, _, _, _) >> new WorkBenchLogMessage()
        queryDriverLocationService.judgeXianSpecialCity(_) >> true

        request.getDspOrderDO().setEstimatedMin(null)
        request.getDspOrderDO().setFromPoiDTO(new FromPoiDTO(cityId: 1))
        request.getDspOrderDO().setToPoiDTO(new ToPoiDTO(cityId: 1))
        request.setOrderWayPointList([new OrderWayPointVO(cityId: 1, pointLatitude: 1.0, pointLongitude: 1.0)])
        locationBasedService.query(_, _, _) >> new LocationRouteVO(new BaseLocationVO(BigDecimal.ZERO, BigDecimal.ZERO, ""), new BaseLocationVO(BigDecimal.ZERO, BigDecimal.ZERO, ""), 1000, 60)

        when: "执行校验方法"
        executor.fixOrderEstimatedInfo(request)

        then: "验证校验结果"
        request.getDspOrderDO().getEstimatedMin() == BigDecimal.ONE
    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086)

        def dspOrderDetailDO = new DspOrderDetailDO()
        def dspOrderFeeDO = new DspOrderFeeDO()

        def request = new CreateDspOrderCommand(dspOrderDO: dspOrderDO, dspOrderDetailDO: dspOrderDetailDO, dspOrderFeeDO: dspOrderFeeDO, orderScene: 1)

        def lock = new DistributedLockServiceImpl.RedisDistributedLock(null)
        dspRotateAbTestConfig.getDspStrategyId(_, _, _) >> Pair.of(1, 1L)
        strategyRepository.find(_) >> new ScheduleStrategyVO(strategyId: 1L, items: [])
//        queryDspOrderService.flowSwitch(_, _) >> flowSwitch
        distributedLockService.getLock(_) >> lock
        dspOrderRepository.queryValidDspOrders(_) >> validOrder
        idGeneratorService.generateId() >> 123
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrder

        when: "执行校验方法"
        String dspOrderId = executor.execute(request)

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res

        where:
        validOrder         | old  | userOrder || res
        [new DspOrderDO()] | null | _         || "09015004"
        [new DspOrderDO()] | null | _         || "09015004"
        null               | null | _         || "09015004"

    }

    DspOrderDO getdsporder() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", dspOrderId: "123", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, spId: 1000
                , bizAreaType: 32, distributionChannelId: 5)

        return dspOrderDO
    }

    DspOrderDO getdsporder1() {
        def dspOrderDO = getdsporder()
        dspOrderDO.setSpId(2000)
        return dspOrderDO
    }

    UserOrderDetail getuserOrderDetail() {
        def bookinfo = new UserBookInfo(actualFromLatitude: 111.3, actualFromLongitude: 30.2, actualToLatitude: 99.1, actualToLongitude: 11.1, newPaymentProcess: 1, tollFee: 1.1)
        bookinfo.setOrderSysType(3)
        def userorder = new UserOrderDetail(bookInfo: bookinfo)
        return userorder
    }


    UserOrderDetail getuserOrderDetail2() {
        def bookinfo = new UserBookInfo(actualFromLatitude: 111.3, actualFromLongitude: 30.2, actualToLatitude: 99.1, actualToLongitude: 11.1, newPaymentProcess: 1, tollFee: 1.1
                , orderSysType: 1, distributionChannel: 5)
        def userorder = new UserOrderDetail(bookInfo: bookinfo)
        return userorder
    }

    def "assembleOtherFields Test"() {
        given:
        def createDspOrderCommand = Mock(CreateDspOrderCommand)
        def userOrderDetail = Mock(UserOrderDetail)
        createDspOrderCommand.getDspOrderDO() >> Mock(DspOrderDO)
        def userBookInfo = Mock(UserBookInfo)
        userOrderDetail.getBookInfo() >> userBookInfo
        userBookInfo.getActualFromLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualFromLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getAuditQuantity() >> 2
        userBookInfo.getBagQuantity() >> 1
        userBookInfo.getChildQuantity() >> 1
        userBookInfo.getNewPaymentProcess() >> true
        createDspOrderCommand.getDspOrderDetailDO() >> Mock(DspOrderDetailDO)
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        when:
        def f = executor.assembleOtherFields(createDspOrderCommand)
        then:
        f != null

    }


    def "test assembleOtherFields"() {
        given:
        def createDspOrderCommand = Mock(CreateDspOrderCommand)
        def userOrderDetail = Mock(UserOrderDetail)
        createDspOrderCommand.getDspOrderDO() >> Mock(DspOrderDO)
        def userBookInfo = Mock(UserBookInfo)
        userOrderDetail.getBookInfo() >> userBookInfo
        userBookInfo.getActualFromLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualFromLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getAuditQuantity() >> 2
        userBookInfo.getBagQuantity() >> 1
        userBookInfo.getChildQuantity() >> 1
        userBookInfo.getNewPaymentProcess() >> true
        createDspOrderCommand.getDspOrderDetailDO() >> new DspOrderDetailDO();
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        when:
        def f = executor.assembleOtherFields(createDspOrderCommand)
        then:
        createDspOrderCommand.getDspOrderDetailDO().getExtendInfoMap().get(SysConstants.Order.CROSS_BORDER_TYPE) == null;
    }

    def "test assembleOtherFields with crossBorderType"() {
        given:
        def createDspOrderCommand = Mock(CreateDspOrderCommand)
        def userOrderDetail = Mock(UserOrderDetail)
        createDspOrderCommand.getDspOrderDO() >> Mock(DspOrderDO)
        def userBookInfo = Mock(UserBookInfo)
        userOrderDetail.getBookInfo() >> userBookInfo
        userBookInfo.getActualFromLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualFromLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getAuditQuantity() >> 2
        userBookInfo.getBagQuantity() >> 1
        userBookInfo.getChildQuantity() >> 1
        userBookInfo.getNewPaymentProcess() >> true

        def dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setCrossBorderType(2)
        createDspOrderCommand.getDspOrderDetailDO() >> dspOrderDetailDO;
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        when:
        def f = executor.assembleOtherFields(createDspOrderCommand)
        then:
        createDspOrderCommand.getDspOrderDetailDO().getExtendInfoMap().get(SysConstants.Order.CROSS_BORDER_TYPE) == 2;
    }

    def "test assembleOtherFields with shunt 1"() {
        given:
        def createDspOrderCommand = Mock(CreateDspOrderCommand)
        def userOrderDetail = Mock(UserOrderDetail)
        createDspOrderCommand.getDspOrderDO() >> Mock(DspOrderDO)
        def userBookInfo = Mock(UserBookInfo)
        userOrderDetail.getBookInfo() >> userBookInfo
        userBookInfo.getActualFromLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualFromLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getAuditQuantity() >> 2
        userBookInfo.getBagQuantity() >> 1
        userBookInfo.getChildQuantity() >> 1
        userBookInfo.getNewPaymentProcess() >> true
        def dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setCrossBorderType(2)
        dspOrderDetailDO.setShuntFlag(1)
        dspOrderDetailDO.setShuntSupplierId(2L)
        createDspOrderCommand.getDspOrderDetailDO() >> dspOrderDetailDO;
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        when:
        def f = executor.assembleOtherFields(createDspOrderCommand)
        then:
        createDspOrderCommand.getDspOrderDetailDO().getExtendInfoMap().get(SysConstants.Order.SHUNT_FLAG) == 1;
    }

    def "test assembleOtherFields with shunt 2"() {
        given:
        def createDspOrderCommand = Mock(CreateDspOrderCommand)
        def userOrderDetail = Mock(UserOrderDetail)
        createDspOrderCommand.getDspOrderDO() >> Mock(DspOrderDO)
        def userBookInfo = Mock(UserBookInfo)
        userOrderDetail.getBookInfo() >> userBookInfo
        userBookInfo.getActualFromLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualFromLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLatitude() >> BigDecimal.valueOf(1)
        userBookInfo.getActualToLongitude() >> BigDecimal.valueOf(1)
        userBookInfo.getAuditQuantity() >> 2
        userBookInfo.getBagQuantity() >> 1
        userBookInfo.getChildQuantity() >> 1
        userBookInfo.getNewPaymentProcess() >> true
        def dspOrderDetailDO = new DspOrderDetailDO();
        dspOrderDetailDO.setCrossBorderType(2)
        dspOrderDetailDO.setShuntFlag(1)
        dspOrderDetailDO.setShuntSupplierId(2L)
        dspOrderDetailDO.setExtendInfo(JacksonUtil.serialize(Maps.newHashMap("a", "b")))
        createDspOrderCommand.getDspOrderDetailDO() >> dspOrderDetailDO;
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> userOrderDetail
        when:
        def f = executor.assembleOtherFields(createDspOrderCommand)
        then:
        createDspOrderCommand.getDspOrderDetailDO().getExtendInfoMap().get(SysConstants.Order.SHUNT_FLAG) == 1;
    }


}
