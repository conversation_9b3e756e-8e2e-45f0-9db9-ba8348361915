package com.ctrip.dcs.application.command.validator


import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.dsporder.carconfig.UpdateDriverAfterTimeCarConf
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class SaaSTripBindCarValidatorTest extends Specification {

    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)
    def dateZoneConvertUtil = Mock(DateZoneConvertUtil)


    def saaSTripChangeDriverValidator = new SaaSTripBindCarValidator(
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway,
            dateZoneConvertUtil: dateZoneConvertUtil
    )

    @Unroll
    def "test validate"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId, carId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        dateZoneConvertUtil.getLocalTimeByCityId(_, _, _) >> DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT);
        when:
        saaSTripChangeDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then: "验证校验结果"

        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords                 | supplierId | carId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 230         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 230         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords()      | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords()      | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 230         | DateUtil.addHours(new Date(), 240) | new ArrayList<SopRecord>() | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"

    }


    def "test validateError"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId, carId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        dateZoneConvertUtil.getLocalTimeByCityId(_, _, _) >> DateUtil.formatDate(new Date(), DateUtil.DATETIME_FORMAT);

        when:
        saaSTripChangeDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        productType | categoryCode                | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords        | supplierId | carId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 220         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 500         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | null                       | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 230         | DateUtil.addHours(new Date(), 240) | buildSopRecords()          | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | new ArrayList<SopRecord>() | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 500         | DateUtil.addHours(new Date(), 240) | buildSopRecords()          | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 500         | null                               | null                       | 4          | 111  | buildSupplier() | 4                 | "1111"     | 4             | null     || "10061"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("2")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.FROM_AIRPORT   | new UseDays(new BigDecimal("2")) | 200         | DateUtil.addHours(new Date(), 240) | buildSopRecords()     | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.FROM_AIRPORT   | new UseDays(new BigDecimal("2")) | 700         | DateUtil.addHours(new Date(), 240) | buildFullSopRecords() | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL | new UseDays(new BigDecimal("1")) | 500         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 4          | 1112  | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
        0           | CategoryCodeEnum.DAY_RENTAL | new UseDays(new BigDecimal("1")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 4          | 1112  | buildSupplier() | 4                 | null       | 4             | 12       || "09011000"
        0           | CategoryCodeEnum.DAY_RENTAL | new UseDays(new BigDecimal("1")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 1          | 1112  | buildSupplier() | 3                 | "111"       | 7             | 12       || "09015112"
        0           | CategoryCodeEnum.DAY_RENTAL | new UseDays(new BigDecimal("1")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 4          | 1112  | buildSupplier() | 3                 | "111"       | 7             | 12       || "09015112"
        0           | CategoryCodeEnum.DAY_RENTAL | new UseDays(new BigDecimal("1")) | 240         | DateUtil.addHours(new Date(), 240) | buildSopRecords() | 4          | 1112  | buildSupplier() | 4                 | "111"       | 7             | 12       || "09015112"

    }


    SupplierVO buildSupplier() {
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(4)
        supplierVO.setDispatchSupplierIdList(Lists.newArrayList(2L, 1L))
        return supplierVO;
    }

    List<SopRecord> buildFullSopRecords() {
        SopRecord sopRecord1 = new SopRecord()
        sopRecord1.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord1.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))

        sopRecord1.setEventId(20)

        SopRecord sopRecord2 = new SopRecord()
        sopRecord2.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord2.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord2.setEventId(50)
        return Lists.newArrayList(sopRecord1, sopRecord2)
    }

    List<SopRecord> buildSopRecords() {
        SopRecord sopRecord = new SopRecord()
        sopRecord.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord.setItineraryDateLocal(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord.setEventId(20)
        return Lists.newArrayList(sopRecord)
    }


    DspOrderVO buildDspOrderVO(Integer productType, CategoryCodeEnum categoryCode, UseDays useDays, Integer orderStatus, Date estimatedUseTimeBj, List<SopRecord> sopRecords, Integer supplierId) {
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setProductType(productType)
        dspOrderVO.setCategoryCode(categoryCode)
        dspOrderVO.setUseDays(useDays)
        dspOrderVO.setOrderStatus(orderStatus)
        dspOrderVO.setEstimatedUseTimeBj(estimatedUseTimeBj)
        dspOrderVO.setSopRecords(sopRecords)
        dspOrderVO.setSupplierId(supplierId)
        dspOrderVO.setLastConfirmCarTimeBj(estimatedUseTimeBj)
        dspOrderVO.setCarId(111)
        return dspOrderVO;
    }

    DriverVO buildDriverVO(SupplierVO supplier) {
        DriverVO driverVO = new DriverVO()
        driverVO.setSupplier(supplier)
        driverVO.setDriverId(12)
        CarVO car = new CarVO()
        car.setCarId(22L)
        driverVO.setCar(car)
        return driverVO;
    }

    VehicleVO buildVehicleVO(Integer supplierId, Long carId) {
        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(supplierId)
        vehicleVO.setCarId(carId)
        return vehicleVO;
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }


    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}

