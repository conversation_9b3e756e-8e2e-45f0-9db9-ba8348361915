package com.ctrip.dcs.application.command.dispatchergrab

import com.ctrip.dcs.application.command.api.SubmitDispatcherGrabOrderCommand
import com.ctrip.dcs.application.service.grab.DefaultSubmitDispatcherGrabOrderService
import com.ctrip.dcs.application.service.grab.SubmitDispatcherGrabOrderService
import com.ctrip.dcs.application.service.grab.SubmitDispatcherGrabOrderServiceContext
import com.ctrip.dcs.domain.common.constants.SysConstants
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SubmitDispatcherGrabOrderExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    SubmitDispatcherGrabOrderServiceContext submitDispatcherGrabOrderServiceContext
    @Mock
    DistributedLockServiceImpl.RedisDistributedLock lock
    @Mock
    SubmitDispatcherGrabOrderService service
    @Mock
    SubmitDispatcherGrabOrderCommand command
    @Mock
    DispatcherGrabOrderGateway dispatcherGrabOrderGateway;
    @InjectMocks
    SubmitDispatcherGrabOrderExeCmd submitDispatcherGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(true)
        when(submitDispatcherGrabOrderServiceContext.get(any())).thenReturn(service)

        when:
        submitDispatcherGrabOrderExeCmd.execute(command)

        then:
        verify(service).submit(any())
    }

    def "test generate Key"() {
        when:
        String result = submitDispatcherGrabOrderExeCmd.generateKey("1")

        then:
        result == SysConstants.APP_ID + "_DISPATCHER_GRAB_ORDER_SUBMIT_1"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
