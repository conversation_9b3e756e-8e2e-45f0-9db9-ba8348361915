package com.ctrip.dcs.application.event.impl

import com.ctrip.dcs.domain.common.constants.SysConstants
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.service.SendEmailService
import com.ctrip.dcs.domain.common.service.SendSmsService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.TripSendMessageVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CharterOrderDispatchModifyMsgServiceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    SendEmailService sendEmailService
    @Mock
    SendSmsService sendSmsService
    @Mock
    Map<String, String> configMap
    @Mock
    BusinessTemplateInfoConfig businessTemplateInfoConfig
    @Mock
    TransportGroupVO transportGroupVO
    @InjectMocks
    CharterOrderDispatchModifyMsgService charterOrderDispatchModifyMsgService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test send Msg"() {
        given:
        DspOrderVO dspOrderVO = DspOrderVO.builder().categoryCode(CategoryCodeEnum.TO_AIRPORT).userOrderId("123").estimatedUseTimeBj(new Date()).build()
        OrderVO orderVO = new OrderVO(dspOrderVO: dspOrderVO, driverId: 1L)
        when(businessTemplateInfoConfig.getValueByKey(anyString())).thenReturn("getValueByKeyResponse")
        when(configMap.get(SysConstants.Email.CHARTERED_ORDER_REDISPATCH_CONTENT)).thenReturn("<entry><content><html><![CDATA[您好，您旗下的运力组%s，订单%s，%s，%s，用车时间：%s（当地时间），司机%s，%s，%s状态发生改变，不可继续服务该订单，请及时登录后台更换司机。 <br><br> 用车供应商后台：<a href=\"https://vbooking.ctrip.com/ivbk/vendor/dashboard\">https://vbooking.ctrip.com/ivbk/vendor/dashboard</a> <br><br> 请勿直接回复本邮件，有问题联系用车客服。 <br><br><br> Hello, your transport group %s,  order %s, %s, %s, use time: %s (local time ), driver %s, %s %s, status has been changed, please log in VBK system to assign a new driver in time. <br><br> VBK system: <a href=\"https://vbooking.ctrip.com/ivbk/vendor/dashboard\">https://vbooking.ctrip.com/ivbk/vendor/dashboard</a> <br><br> Please do not reply directly to this email, contact the customer service if you have any questions.]]></html></content></entry>")

        when:
        boolean result1 = charterOrderDispatchModifyMsgService.sendMsg(orderVO)

        when(queryTransportGroupService.queryTransportGroup(any())).thenReturn(transportGroupVO)
        boolean result2 = charterOrderDispatchModifyMsgService.sendMsg(orderVO)

        when(queryTransportGroupService.queryTransportGroup(any())).thenReturn(transportGroupVO)
        when(transportGroupVO.getInformSwitch()).thenReturn(1)
        boolean result3 = charterOrderDispatchModifyMsgService.sendMsg(orderVO)

        when(queryTransportGroupService.queryTransportGroup(any())).thenReturn(transportGroupVO)
        when(transportGroupVO.getInformSwitch()).thenReturn(1)
        when(transportGroupVO.getInformPhone()).thenReturn("123")
        when(transportGroupVO.getInformEmail()).thenReturn("123")
        boolean result4 = charterOrderDispatchModifyMsgService.sendMsg(orderVO)

        then:
        result1 != true
        result2 != true
        result3 != true
        result4 == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme