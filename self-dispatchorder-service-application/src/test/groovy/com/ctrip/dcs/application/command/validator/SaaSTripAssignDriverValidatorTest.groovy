package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.dsporder.entity.UserBookInfo
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.platform.dal.dao.helper.JsonUtils
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class SaaSTripAssignDriverValidatorTest extends Specification {

    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)
    def commonConfConfig = Mock(ConfigService)

    def saaSTripAssignDriverValidator = new SaaSTripAssignDriverValidator(dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway, commonConfConfig: commonConfConfig)

    @Unroll
    def "test validate"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(driverId, supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        saaSBusinessVO.setUserOrderDetail(setUserOrderDetail)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        commonConfConfig.getInteger(_, _) >> config;
        when:
        saaSTripAssignDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then: "验证校验结果"
        true
        where:
        setUserOrderDetail                                                                                                                         | config | productType | categoryCode                  | useDays | orderStatus | estimatedUseTimeBj                 | sopRecords | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        null                                                                                                                                       | 2      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        null                                                                                                                                       | 0      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        null                                                                                                                                       | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        new UserOrderDetail(bookInfo: new UserBookInfo(useTimeNoDriverCancelGrayTag: true, fixedLocationType: 1, categoryCode: "airport_pickup"))  | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        new UserOrderDetail(bookInfo: new UserBookInfo(useTimeNoDriverCancelGrayTag: false, fixedLocationType: 1, categoryCode: "airport_pickup")) | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"

    }

    @Unroll
    def "test validate23121"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(driverId, supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        saaSBusinessVO.setUserOrderDetail(setUserOrderDetail)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        commonConfConfig.getInteger(_, _) >> config;
        when:
        saaSTripAssignDriverValidator.validBusiness(cmd, saaSBusinessVO)


        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        setUserOrderDetail                                                                                                                         | config | productType | categoryCode                  | useDays | orderStatus | estimatedUseTimeBj                  | sopRecords | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
//        null                                                                                                                                       | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), 240)  | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        new UserOrderDetail(bookInfo: null)                                                                                                        | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), -240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015113"
        new UserOrderDetail(bookInfo: new UserBookInfo(useTimeNoDriverCancelGrayTag: false, fixedLocationType: 1, categoryCode: "airport_pickup")) | 3      | 0           | CategoryCodeEnum.FROM_AIRPORT | null    | 220         | DateUtil.addHours(new Date(), -240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015111"
        new UserOrderDetail(bookInfo: new UserBookInfo(useTimeNoDriverCancelGrayTag: false, fixedLocationType: 1, categoryCode: "day_rental"))     | 3      | 0           | CategoryCodeEnum.DAY_RENTAL   | null    | 220         | DateUtil.addHours(new Date(), -240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015113"

    }


    def "test validateError"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = buildDspOrderVO(productType, categoryCode, useDays, orderStatus, estimatedUseTimeBj, sopRecords, supplierId)
        DriverVO driverVO = buildDriverVO(driverId, supplier)
        VehicleVO vehicleVO = buildVehicleVO(vehicleSupplierId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setCarLicense(carLicense)
        cmd.setSupplierId(cmdSupplierId)
        commonConfConfig.getInteger(_, _) >> 2
        when:
        saaSTripAssignDriverValidator.validBusiness(cmd, saaSBusinessVO)

        then:
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        productType | categoryCode                  | useDays                          | orderStatus | estimatedUseTimeBj                 | sopRecords | supplierId | driverId | supplier        | vehicleSupplierId | carLicense | cmdSupplierId | confTime || code
        0           | CategoryCodeEnum.FROM_AIRPORT | null                             | 230         | new Date()                         | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | null     || "09015110"
        1           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220         | new Date()                         | null       | null       | null     | null            | null              | null       | null          | null     || "09011000"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220         | new Date()                         | null       | null       | null     | null            | null              | ""         | null          | null     || "09011000"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220         | new Date()                         | null       | 5          | null     | buildSupplier() | null              | "1111"     | 7             | null     || "09015112"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220         | new Date()                         | null       | 1          | null     | buildSupplier() | null              | "1111"     | 7             | null     || "09015112"
        0           | CategoryCodeEnum.DAY_RENTAL   | null                             | 220         | new Date()                         | null       | 4          | null     | buildSupplier() | 9                 | "1111"     | 4             | null     || "09015113"
        0           | CategoryCodeEnum.DAY_RENTAL   | new UseDays(new BigDecimal("1")) | 230         | DateUtil.addHours(new Date(), 240) | null       | 4          | 1112     | buildSupplier() | 4                 | "1111"     | 4             | 12       || "09015110"
    }


    @Unroll
    def "test validateSuppliers_error"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setSupplierId(orderSupplierId)

        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(driverId)
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(driverSupplierId)
        supplierVO.setDispatchSupplierIdList(dispatchSupplierIdList)
        driverVO.setSupplier(supplierVO)
        CarVO carVO = new CarVO();
        carVO.setCarId(carId)
        driverVO.setCar(carVO)

        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(vehicleSupplierId)
        vehicleVO.setCarId(vehicleId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setSupplierId(cmdSupplierId)
        when:
        saaSTripAssignDriverValidator.validateSuppliers(cmd, saaSBusinessVO)

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == code

        where:
        orderSupplierId | driverSupplierId | dispatchSupplierIdList         | cmdSupplierId | carId | vehicleId | vehicleSupplierId | driverId || code
        2               | 1                | Lists.newArrayList()           | 1             | 220   | 220       | 1                 | 1112     || "09015112"
        1               | 1                | Lists.newArrayList()           | 1             | 220   | 220       | 2                 | 1112     || "09015112"
        1               | 2                | Lists.newArrayList()           | 1             | 220   | 220       | 2                 | 1112     || "09015112"
        2               | 2                | Lists.newArrayList(1L, 2L)     | 1             | 220   | 220       | 2                 | 1112     || "09015112"
        2               | 2                | Lists.newArrayList(1L, 3L)     | 1             | 220   | 220       | 2                 | 1112     || "09015112"
        2               | 1                | Lists.newArrayList(1L, 2L, 3L) | 2             | 220   | 221       | 9                 | 1112     || "09015112"

    }


    @Unroll
    def "test validateSuppliers_success"() {
        given:
        SaaSBusinessVO saaSBusinessVO = new SaaSBusinessVO()
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setSupplierId(orderSupplierId)

        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(driverId)
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(driverSupplierId)
        supplierVO.setDispatchSupplierIdList(dispatchSupplierIdList)
        driverVO.setSupplier(supplierVO)
        CarVO carVO = new CarVO();
        carVO.setCarId(carId)
        driverVO.setCar(carVO)

        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(vehicleSupplierId)
        vehicleVO.setCarId(vehicleId)
        saaSBusinessVO.setDspOrderVO(dspOrderVO)
        saaSBusinessVO.setDriverVO(driverVO)
        saaSBusinessVO.setVehicleVO(vehicleVO)
        SaaSOperateDriverCarCommand cmd = getSelfReq()
        cmd.setSupplierId(cmdSupplierId)
        when:
        saaSTripAssignDriverValidator.validateSuppliers(cmd, saaSBusinessVO)

        then: "验证校验结果"
        true
        where:
        orderSupplierId | driverSupplierId | dispatchSupplierIdList | cmdSupplierId | carId | vehicleId | vehicleSupplierId | driverId || code
        1               | 1                | Lists.newArrayList()   | 1             | 220   | 220       | 1                 | 1112     || "09015111"
        1               | 2                | Lists.newArrayList(1L) | 1             | 220   | 220       | 1                 | 1112     || "09015111"

    }

    SupplierVO buildSupplier() {
        SupplierVO supplierVO = new SupplierVO()
        supplierVO.setSupplierId(1)
        supplierVO.setDispatchSupplierIdList(Lists.newArrayList(2L, 4L))
        return supplierVO;
    }

    List<SopRecord> buildFullSopRecords() {
        SopRecord sopRecord1 = new SopRecord()
        sopRecord1.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord1.setEventId(20)

        SopRecord sopRecord2 = new SopRecord()
        sopRecord2.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord2.setEventId(50)
        return Lists.newArrayList(sopRecord1, sopRecord2)
    }

    List<SopRecord> buildSopRecords() {
        SopRecord sopRecord = new SopRecord()
        sopRecord.setItineraryDateBj(DateUtil.formatDate(new Date(), "yyyy-MM-dd"))
        sopRecord.setEventId(20)
        return Lists.newArrayList(sopRecord)
    }


    DspOrderVO buildDspOrderVO(Integer productType, CategoryCodeEnum categoryCode, UseDays useDays, Integer orderStatus, Date estimatedUseTimeBj, List<SopRecord> sopRecords, Integer supplierId) {
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setProductType(productType)
        dspOrderVO.setCategoryCode(categoryCode)
        dspOrderVO.setUseDays(useDays)
        dspOrderVO.setOrderStatus(orderStatus)
        dspOrderVO.setEstimatedUseTimeBj(estimatedUseTimeBj)
        dspOrderVO.setSopRecords(sopRecords)
        dspOrderVO.setSupplierId(supplierId)
        dspOrderVO.setLastConfirmCarTimeBj(estimatedUseTimeBj)
        Map<String, Object> map = new HashMap<>()
        map.put("useTimeNoDriverCancelBuffer", "10")
        dspOrderVO.setExtendInfo(JsonUtils.toJson(map))
        return dspOrderVO;
    }

    DriverVO buildDriverVO(Long driverId, SupplierVO supplier) {
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(driverId)
        driverVO.setSupplier(supplier)
        CarVO car = new CarVO()
        car.setCarId(22L)
        driverVO.setCar(car)
        return driverVO;
    }

    VehicleVO buildVehicleVO(Integer supplierId) {
        VehicleVO vehicleVO = new VehicleVO()
        vehicleVO.setSupplierId(supplierId)
        vehicleVO.setCarId(22L)
        return vehicleVO;
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }


    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}

