package com.ctrip.dcs.application.event.impl

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.event.ICharterOrderDispatchModifyMsgService
import com.ctrip.dcs.application.event.dto.DriverCityModifyEvent
import com.ctrip.dcs.application.event.dto.DriverFreezeEvent
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.domain.schedule.gateway.DriverPlatformApiServiceGateway
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverCityModifyEventHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ReDispatchSubmitExeCmd submitExeCmd
    @Mock
    ICharterOrderDispatchModifyMsgService modifyMsgService
    @Mock
    OrderVO orderVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverPlatformApiServiceGateway driverPlatformGateway

    @InjectMocks
    DriverCityModifyEventHandler driverCityModifyEventHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle"() {
        given:
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO>newResult().success().build())

        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(orderVO.isNewOrder()).thenReturn(true)
        when(orderVO.isIgtOrder()).thenReturn(false)
        when(orderVO.getDspOrderVO()).thenReturn(dspOrderVO)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(new Date())
        when(driverPlatformGateway.checkDrvGuideGrayFlow(new DriverVO(1L))).thenReturn(true)

        when:
        boolean result = driverCityModifyEventHandler.handle(new DriverCityModifyEvent(driverId: 1L, accountType: "1"))

        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn([orderVO])
        boolean result1 = driverCityModifyEventHandler.handle(new DriverCityModifyEvent(driverId: 1L, accountType: "1"))

        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)
        boolean result2 = driverCityModifyEventHandler.handle(new DriverCityModifyEvent(accountType: "1", driverId: 1l))


        then:
        result != true
        result1 == true
        result2 == true
    }


    def "test handle2"() {
        given:
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO>newResult().success().build())

        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(orderVO.isNewOrder()).thenReturn(true)
        when(orderVO.isIgtOrder()).thenReturn(false)
        when(orderVO.getDspOrderVO()).thenReturn(dspOrderVO)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)
        when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(new Date())
        when(driverPlatformGateway.checkDrvGuideGrayFlow(new DriverVO(1L))).thenReturn(true)

        when:

        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn([orderVO])
        boolean result1 = driverCityModifyEventHandler.handle(new DriverCityModifyEvent(driverId: 1L, accountType: "1", "isFromDrvGuide": true))

        then:
        result1 == true
    }

    def "test handle3"() {
        given:
        when(submitExeCmd.execute(any())).thenReturn(Result.Builder.<ReasonDetailVO>newResult().success().build())

        when(modifyMsgService.sendMsg(any())).thenReturn(true)
        when(orderVO.isNewOrder()).thenReturn(true)
        when(orderVO.isIgtOrder()).thenReturn(false)
        when(orderVO.getDspOrderVO()).thenReturn(dspOrderVO)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(new Date())
        when(driverPlatformGateway.checkDrvGuideGrayFlow(any())).thenReturn(true)

        when:

        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn([orderVO])
        boolean result2 = driverCityModifyEventHandler.handle(new DriverCityModifyEvent(driverId: 1L, accountType: "1", "isFromDrvGuide": false))

        then:
        result2 == true
    }
}
