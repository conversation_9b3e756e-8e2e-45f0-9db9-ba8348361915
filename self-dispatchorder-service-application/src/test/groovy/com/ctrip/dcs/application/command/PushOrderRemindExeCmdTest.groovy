package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.PushOrderRemindCommand
import com.ctrip.dcs.application.command.validator.PushOrderRemindValidator
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.PushOrderRemindType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*

class PushOrderRemindExeCmdTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def validator = Mock(PushOrderRemindValidator)
    def testObj = new PushOrderRemindExeCmd(dspOrderRepository: dspOrderRepository, dspOrderDetailRepository: dspOrderDetailRepository, queryTransportGroupService: queryTransportGroupService, validator: validator)

    def setup() {
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.execute(command)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        command || expectedResult
        new PushOrderRemindCommand(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new Date(), new Date(), new Date(), new Date(), new Date(), new Date(), new Date(), new Date(), 0, 0, new UseDays(BigDecimal.ZERO), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "priceMark", 0, "spName", 0, "driverOrderId", 1L, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", BigDecimal.ZERO, BigDecimal.ZERO, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", BigDecimal.ZERO, BigDecimal.ZERO, "actualToCoordsys", "toAddress", "toName", 0, BigDecimal.ZERO, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, BigDecimal.ZERO, "supplierCurrency", 0, BigDecimal.ZERO, BigDecimal.ZERO, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1L, "activityName", 0, "activityCode", BigDecimal.ZERO, "activityDesc", BigDecimal.ZERO)], [new TradeCouponVO("couponCode", BigDecimal.ZERO)]), "premiumPrice", "userCurrency", "supplierCurrency", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, 0, 1L), new TransportGroupVO(1L, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1L, 0,0, "informPhone", "informEmail", new TakeOrderLimitTimeMinuteVO(0), "standbyPhone", null), [PushOrderRemindType.EMAIL]) || null
    }
}
