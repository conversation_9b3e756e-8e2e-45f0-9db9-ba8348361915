package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RemindDispatcherGrabOrderValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatcherGrabOrderDO order
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    TransportGroupVO transportGroupVO
    @InjectMocks
    RemindDispatcherGrabOrderValidator remindDispatcherGrabOrderValidator

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate"() {
        when:
        when(order.isInit()).thenReturn(false)
        boolean r1 = remindDispatcherGrabOrderValidator.validate(order, dspOrderVO, transportGroupVO)
        when(order.isInit()).thenReturn(true)
        boolean r2 = remindDispatcherGrabOrderValidator.validate(order, null, transportGroupVO)
        when(order.isInit()).thenReturn(true)
        when(dspOrderVO.isDispatching()).thenReturn(false)
        boolean r3 = remindDispatcherGrabOrderValidator.validate(order, dspOrderVO, transportGroupVO)
        when(order.isInit()).thenReturn(true)
        when(dspOrderVO.isDispatching()).thenReturn(true)
        boolean r4 = remindDispatcherGrabOrderValidator.validate(order, dspOrderVO, null)
        when(order.isInit()).thenReturn(true)
        when(dspOrderVO.isDispatching()).thenReturn(true)
        boolean r5 = remindDispatcherGrabOrderValidator.validate(order, dspOrderVO, transportGroupVO)

        then:
        Assert.assertFalse(r1)
        Assert.assertFalse(r2)
        Assert.assertFalse(r3)
        Assert.assertFalse(r4)
        Assert.assertTrue(r5)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme