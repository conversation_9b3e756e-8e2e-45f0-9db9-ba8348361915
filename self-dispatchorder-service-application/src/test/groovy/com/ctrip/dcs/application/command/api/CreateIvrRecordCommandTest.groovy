package com.ctrip.dcs.application.command.api


import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class CreateIvrRecordCommandTest extends Specification {
    @Mock
    IvrRecordDO ivrRecordDO
    @InjectMocks
    CreateIvrRecordCommand createIvrRecordCommand

    def setup() {
        MockitoAnnotations.initMocks(this)
    }
    def "test execute"() {

        when:
        IvrRecordDO ivrRecordDO1 = new IvrRecordDO();
        createIvrRecordCommand.setIvrRecordDO(ivrRecordDO1);
        createIvrRecordCommand.getIvrRecordDO();
        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme