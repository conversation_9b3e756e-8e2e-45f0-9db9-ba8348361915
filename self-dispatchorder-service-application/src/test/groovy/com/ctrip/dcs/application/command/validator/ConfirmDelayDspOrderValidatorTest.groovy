package com.ctrip.dcs.application.command.validator


import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.*
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ConfirmDelayDspOrderValidatorTest extends Specification {

    @InjectMocks
    ConfirmDelayDspOrderValidator confirmDelayDspOrderValidator

    @Mock
    private static DspOrderVO dspOrderVO
    @Mock
    private static DriverVO driverVO
    @Mock
    private static TransportGroupVO transportGroupVO
    @Mock
    private static ScheduleTaskDO scheduleTaskDO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate"() {
        given:
        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED.getCode())
        when:
        confirmDelayDspOrderValidator.validate(order, driver, transport, task)

        then:
        def e = thrown(ValidateException)
        e.getErrorCode().getCode() == code

        where:
        order      | driver   | transport        | task           || code
        null       | null     | null             | null           || ErrorCode.ERROR_PARAMS.getCode()
        dspOrderVO | null     | null             | null           || ErrorCode.ERROR_PARAMS.getCode()
        dspOrderVO | driverVO | null             | null           || ErrorCode.ERROR_PARAMS.getCode()
        dspOrderVO | driverVO | transportGroupVO | null           || ErrorCode.ERROR_PARAMS.getCode()
        dspOrderVO | driverVO | transportGroupVO | scheduleTaskDO || ErrorCode.ERROR_PARAMS.getCode()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme