package com.ctrip.dcs.application.command.converter


import com.ctrip.dcs.application.provider.converter.CustomerAssignDriverConverter
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverRequestType
import spock.lang.Specification

class CustomerAssignDriverConvertorTest extends Specification {


    def "test  convert"() {
        given:

        def request = new CustomerAssignDriverRequestType(userOrderId: "11")


        when: "执行校验方法"
        def order = CustomerAssignDriverConverter.converter(request)

        then: "验证校验结果"
        order.userOrderId == "11"
    }

    def "test  convert1"() {
        given:

        def request = new VbkAssignDriverRequestType(userOrderId: "11")


        when: "执行校验方法"
        def order = CustomerAssignDriverConverter.converter(request)

        then: "验证校验结果"
        order.userOrderId == "11"
    }

    def "test  convert2"() {
        given:

        def request = new VbkChangeDriverRequestType(userOrderId: "11")


        when: "执行校验方法"
        def order = CustomerAssignDriverConverter.converter(request)

        then: "验证校验结果"
        order.userOrderId == "11"
    }

    def "test  convert3"() {
        given:

        def request = new VbkBindCarAndTakenRequestType(userOrderId: "11")


        when: "执行校验方法"
        def order = CustomerAssignDriverConverter.converter(request)

        then: "验证校验结果"
        order.userOrderId == "11"
    }

}
