package com.ctrip.dcs.application.command


import com.ctrip.dcs.application.command.api.CreateGrabOrderCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabOrderType
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.util.JsonUtil
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.common.constants.CommonConstants
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.clogging.LoggerContext
import com.fasterxml.jackson.core.type.TypeReference
import org.assertj.core.util.Lists
import org.assertj.core.util.Sets
import spock.lang.Specification

import java.util.concurrent.Executors

/**
 * <AUTHOR>
 */
class CreateGrabOrderExeCmdTest extends Specification {


    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway);
    def grabOrderFactory = Mock(GrabOrderFactory);
    def grabCentreRepository = Mock(GrabCentreRepository);
    def subSkuRepository = Mock(SubSkuRepository);
    def messageProducer = Mock(MessageProviderService);
    def grabOrderDetailRepository = Mock(GrabOrderDetailRepository);
    def broadcastRepository = Mock(BroadcastRepository);
    def grabOrderDealCacheThreadPool = Executors.newFixedThreadPool(5)
    def commonConf = Mock(Map)

    def createGrabOrderExeCmd = new CreateGrabOrderExeCmd(
            selfOrderQueryGateway: selfOrderQueryGateway,
            grabOrderFactory: grabOrderFactory,
            grabCentreRepository: grabCentreRepository,
            subSkuRepository: subSkuRepository,
            messageProducer: messageProducer,
            broadcastRepository: broadcastRepository,
            grabOrderDetailRepository: grabOrderDetailRepository,
            grabOrderDealCacheThreadPool: grabOrderDealCacheThreadPool,
            commonConf: commonConf
    )

    def "test execute"() {
        given:
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> buildBaseDetailVO()
        subSkuRepository.find(_) >> buildSubSkuVO()
        selfOrderQueryGateway.queryGrabDriverList(_, _, _) >> buildGrabOrderDTO()
        grabOrderFactory.create(_, _, _, _, _, _, _ as List) >> grabOrderDOList
        grabOrderFactory.create(_, _, _, _, _, _, _ as Set) >> defaultGrabOrderDOList
        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == expectRes

        where:
        req                            | grabOrderDOList             | defaultGrabOrderDOList      || expectRes
        buildCreateGrabOrderCommand1() | buildGrabOrderDOList()      | buildGrabOrderDOList()      || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildEmptyGrabOrderDOList() || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildGrabOrderDOList()      || null

    }


    def "test execute1"(){

        given:
        CreateGrabOrderCommand req = buildCreateGrabOrderCommand2();
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> buildBaseDetailVO()
        subSkuRepository.find(_) >> buildSubSkuVO()
        selfOrderQueryGateway.queryGrabDriverList(_, _, _) >> buildGrabOrderDTO()
        grabOrderFactory.create(_, _, _, _, _, _, _ as List) >> buildEmptyGrabOrderDOList();
        grabOrderFactory.create(_, _, _, _, _, _, _ as Set) >> buildEmptyGrabOrderDOList();

        grabCentreRepository.getOrderDriverMapping(_) >>  Sets.newHashSet();


        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == null


    }


    def "test execute2"() {

        given:
        CreateGrabOrderCommand req = buildCreateGrabOrderCommand2();
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> buildBaseDetailVO()
        subSkuRepository.find(_) >> buildSubSkuVO()
        selfOrderQueryGateway.queryGrabDriverList(_, _, _) >> buildGrabOrderDTO()
        grabOrderFactory.create(_, _, _, _, _, _, _ as List) >> buildEmptyGrabOrderDOList();
        grabOrderFactory.create(_, _, _, _, _, _, _ as Set) >> buildEmptyGrabOrderDOList();

        grabCentreRepository.getOrderDriverMapping(_) >>  Sets.newTreeSet(1l,2l)


        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == null
    }



    def "test execute4"() {

        given:
        CreateGrabOrderCommand req = buildCreateGrabOrderCommand2();
        BaseDetailVO baseDetailVO = buildBaseDetailVO()
        baseDetailVO.setOrderStatus(700)
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> baseDetailVO

        when:
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == null
    }



    def "test execute5"() {

        given:
        CreateGrabOrderCommand req = buildCreateGrabOrderCommand3();
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> buildBaseDetailVO()
        subSkuRepository.find(_) >> buildSubSkuVO()
        selfOrderQueryGateway.queryGrabDriverList(_, _, _) >> buildGrabOrderDTO()
        grabOrderFactory.create(_, _, _, _, _, _, _ as List) >> buildGrabOrderDOList();
        grabOrderFactory.create(_, _, _, _, _, _, _ as Set) >> buildGrabOrderDOList();
        commonConf.getOrDefault(CommonConstants.CREATE_GRAB_ORDER_FILTER_DRIVER, "1") >> "0"
        grabCentreRepository.getOrderDriverMapping(_) >>  Sets.newTreeSet(1l,2l)


        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == null
    }

    def "test execute6"() {

        given:
        CreateGrabOrderCommand req = buildCreateGrabOrderCommand3();
        selfOrderQueryGateway.queryOrderBaseDetail(_) >> buildBaseDetailVO()
        subSkuRepository.find(_) >> buildSubSkuVO()
        selfOrderQueryGateway.queryGrabDriverList(_, _, _) >> buildGrabOrderDTO()
        grabOrderFactory.create(_, _, _, _, _, _, _ as List) >> buildGrabOrderDOList();
        grabOrderFactory.create(_, _, _, _, _, _, _ as Set) >> buildGrabOrderDOList();
        commonConf.getOrDefault(CommonConstants.CREATE_GRAB_ORDER_FILTER_DRIVER, "1") >> "1"
        grabCentreRepository.getOrderDriverMapping(_) >>  Sets.newTreeSet(1l,2l)


        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.execute(req)

        then:
        res == null
    }


    def "test sendMessage"() {

        given:
        long random = new Random().nextInt(10) + 1
        // 失效时间:重试时间+随机值
        long expire = 300L + random
        long time = DateUtil.addSeconds(new Date(), Long.valueOf(expire).intValue()).getTime();
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId", "1", 1, 1L, 1l, time, 1)
        when:

        def res = createGrabOrderExeCmd.sendGrabOrderExpireMessage(Lists.newArrayList(grabOrderDO))

        then:
        res == null
    }

    def "test dealBroadCastCache"() {
        given:
        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.dealBroadCastCache(req, buildBaseDetailVO(), buildSubSkuVO(), defaultGrabOrderDOList)

        then:
        res == expectRes

        where:
        req                            | grabOrderDOList             | defaultGrabOrderDOList      || expectRes
        buildCreateGrabOrderCommand1() | buildGrabOrderDOList()      | buildGrabOrderDOList()      || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildEmptyGrabOrderDOList() || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildGrabOrderDOList()      || null
    }


    def "test dealGrabCache"() {
        given:
        when:
        LoggerContext init = LoggerContext.newContext().withGlobalTraceId("1111").init();
        LoggerContext.newContext().withGlobalTraceId(init.getGlobalTraceId()).init();
        def res = createGrabOrderExeCmd.dealGrabCache(req, buildBaseDetailVO(), buildSubSkuVO(), defaultGrabOrderDOList)

        then:
        res == expectRes

        where:
        req                            | grabOrderDOList             | defaultGrabOrderDOList      || expectRes
        buildCreateGrabOrderCommand1() | buildGrabOrderDOList()      | buildGrabOrderDOList()      || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildEmptyGrabOrderDOList() || null
        buildCreateGrabOrderCommand2() | buildEmptyGrabOrderDOList() | buildGrabOrderDOList()      || null
    }

    private List<GrabOrderDO> buildEmptyGrabOrderDOList() {
        return Lists.newArrayList()
    }

    private List<GrabOrderDO> buildGrabOrderDOList() {
        GrabOrderDO grabOrderDO1 = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId", "1", 1, 1L, 1l, 1l, 1)
        GrabOrderDO grabOrderDO2 = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId", "1", 1, 2L, 1l, 1l, 1)
        return Lists.newArrayList(grabOrderDO1, grabOrderDO2)
    }

    private List<DriverVO> buildDriverVOList() {
        DriverVO driverVO1 = new DriverVO(1L, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null,0, 1l, null, YesOrNo.NO, false,0,0,"","","","","",null)
        DriverVO driverVO2 = new DriverVO(2L, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null,0, 1l, null, YesOrNo.NO, false,0,0,"","","","","",null)
        return Lists.newArrayList(driverVO1, driverVO2)
    }


    private List<GrabOrderDTO> buildGrabOrderDTO() {
        String str = "[{\"dspOrderId\":\"881836781523483541\",\"driverId\":1,\"duid\":\"11111111\",\"basicInfo\":{\"platformSupplyOrderId\":\"162644194319204388\",\"supplyOrderId\":\"881836781523483541\",\"userOrderId\":\"31157645594\",\"cityName\":\"上海\",\"cityCode\":\"shanghai_city\",\"cityId\":2,\"serviceType\":2,\"orderCarType\":117,\"orderCarTypeName\":\"经济5座\",\"orderStatus\":3,\"bookTime\":\"2024-03-28 17:56:00\",\"sysExpectBookTime\":\"2024-03-28 17:56:00\",\"orderTime\":\"2024-03-28 15:00:00\",\"ota\":0,\"orderVersion\":4,\"pickupPointId\":\"\",\"adultCount\":4,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":0,\"bizAreaType\":0,\"extraFeeEffectiveTime\":\"\",\"kiloLength\":36.0,\"takenType\":1,\"takeUseTime\":\"2024-03-28 17:56:00\",\"specialTimePoints\":0,\"waitMinute\":0,\"uid\":\"2179341790\",\"highUser\":0},\"orderFeeInfo\":{},\"pointInfo\":{\"fromInfo\":{\"cityId\":2,\"cityCode\":\"shanghai_city\",\"name\":\"虹桥国际机场T2航站楼\",\"adress\":\"虹桥国际机场 T2\",\"longitude\":121.327447,\"latitude\":31.192568,\"type\":\"GCJ02\"},\"toInfo\":{\"cityId\":83,\"cityCode\":\"kunshan\",\"name\":\"朗绿花园-西北门\",\"adress\":\"昆山市 光明路1388号\",\"longitude\":121.083633,\"latitude\":31.29804,\"type\":\"GCJ02\"}},\"driverGrabOrderInfo\":{\"tipsDelaySecond\":10,\"distanceFormDriver\":43468.0,\"driverId\":1}},{\"dspOrderId\":\"881836781523483541\",\"driverId\":2,\"duid\":\"11111111\",\"basicInfo\":{\"platformSupplyOrderId\":\"162644194319204388\",\"supplyOrderId\":\"881836781523483541\",\"userOrderId\":\"31157645594\",\"cityName\":\"上海\",\"cityCode\":\"shanghai_city\",\"cityId\":2,\"serviceType\":2,\"orderCarType\":117,\"orderCarTypeName\":\"经济5座\",\"orderStatus\":3,\"bookTime\":\"2024-03-28 17:56:00\",\"sysExpectBookTime\":\"2024-03-28 17:56:00\",\"orderTime\":\"2024-03-28 15:00:00\",\"ota\":0,\"orderVersion\":4,\"pickupPointId\":\"\",\"adultCount\":4,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":0,\"bizAreaType\":0,\"extraFeeEffectiveTime\":\"\",\"kiloLength\":36.0,\"takenType\":1,\"takeUseTime\":\"2024-03-28 17:56:00\",\"specialTimePoints\":0,\"waitMinute\":0,\"uid\":\"2179341790\",\"highUser\":0},\"orderFeeInfo\":{},\"pointInfo\":{\"fromInfo\":{\"cityId\":2,\"cityCode\":\"shanghai_city\",\"name\":\"虹桥国际机场T2航站楼\",\"adress\":\"虹桥国际机场 T2\",\"longitude\":121.327447,\"latitude\":31.192568,\"type\":\"GCJ02\"},\"toInfo\":{\"cityId\":83,\"cityCode\":\"kunshan\",\"name\":\"朗绿花园-西北门\",\"adress\":\"昆山市 光明路1388号\",\"longitude\":121.083633,\"latitude\":31.29804,\"type\":\"GCJ02\"}},\"driverGrabOrderInfo\":{\"tipsDelaySecond\":10,\"distanceFormDriver\":6701.0,\"driverId\":2}}]";
        List<Long> list = JsonUtil.fromJson(str, new TypeReference<List<GrabOrderDTO>>() {
        });
        return list;
    }


    private SubSkuVO buildSubSkuVO() {
        return new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l)
    }


    private CreateGrabOrderCommand buildCreateGrabOrderCommand1() {
        return new CreateGrabOrderCommand("112", "1-1-1-1-1-1-1-1-1-1", new HashSet<Long>(Lists.newArrayList(1L, 2L)), GrabOrderType.BROADCAST)

    }

    private CreateGrabOrderCommand buildCreateGrabOrderCommand3() {
        return new CreateGrabOrderCommand("112", "1-1-1-1-1-1-1-1-1-1", new HashSet<Long>(Lists.newArrayList(1L, 2L)), GrabOrderType.GRAB_CENTRE)

    }
    private CreateGrabOrderCommand buildCreateGrabOrderCommand2() {
        return new CreateGrabOrderCommand("112", "1-1-1-1-1-1-1-1-1-1", new HashSet<Long>(Lists.newArrayList()), GrabOrderType.GRAB_CENTRE)

    }


    private BaseDetailVO buildBaseDetailVO() {
        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setUserOrderId("1");
        baseDetailVO.setSupplyOrderId("0");
        baseDetailVO.setDspOrderId("1");
        baseDetailVO.setDriverOrderId("0");
        baseDetailVO.setOrderStatus(200);
        baseDetailVO.setOldOrderStatus(0);
        baseDetailVO.setOldOrderStatusDetail(0);
        baseDetailVO.setProductType(0);
        baseDetailVO.setProductCode("0");
        baseDetailVO.setProductName("0");
        baseDetailVO.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType());
        baseDetailVO.setServiceType(0);
        baseDetailVO.setCityId(0);
        baseDetailVO.setFromCityId(0);
        baseDetailVO.setToCityId(0);
        baseDetailVO.setVehicleGroupId(0);
        baseDetailVO.setEstimatedUseTime("2023-10-10 10:00:00");
        baseDetailVO.setEstimatedUseTimeBj("2023-10-10 10:00:00");
        baseDetailVO.setPredicServiceStopTimeBj("0");
        baseDetailVO.setLastConfirmTime("0");
        baseDetailVO.setLastConfirmTimeBj("0");
        baseDetailVO.setLastConfirmCarTime("0");
        baseDetailVO.setLastConfirmCarTimeBj("0");
        baseDetailVO.setConnectMode(0);
        baseDetailVO.setSalesMode(0);
        baseDetailVO.setUseDays(new BigDecimal("0"));
        baseDetailVO.setEstimatedKm(new BigDecimal("0"));
        baseDetailVO.setEstimatedMin(new BigDecimal("0"));
        baseDetailVO.setActualKm(new BigDecimal("0"));
        baseDetailVO.setActualMin(new BigDecimal("0"));
        baseDetailVO.setPriceMark("0");
        baseDetailVO.setServiceProviderId(0);
        baseDetailVO.setSupplierId(0);
        baseDetailVO.setCountryId(0L);
        baseDetailVO.setLocale("0");
        baseDetailVO.setLanguageInfo("0");
        baseDetailVO.setSkuId(0);
        baseDetailVO.setCancelRule("0");
        baseDetailVO.setWaitingRule("0");
        baseDetailVO.setPriceResultCode("0");
        baseDetailVO.setDetailSnapShotid("0");
        baseDetailVO.setXproductInfo("0");
        baseDetailVO.setSpContractInfo("0");
        baseDetailVO.setDspStrategyStr("0");
        baseDetailVO.setOrderCarTypeId(0L);
        baseDetailVO.setDrvId(0L);
        baseDetailVO.setDrvName("0");
        baseDetailVO.setDrvPhone("0");
        baseDetailVO.setDrvPhoneAreaCode("0");
        baseDetailVO.setDrvCoopMode(0);
        baseDetailVO.setDrvLanguageCodeList("0");
        baseDetailVO.setCarId(0L);
        baseDetailVO.setCarTypeId(0L);
        baseDetailVO.setCarLicense("0");
        baseDetailVO.setCarColorId(0L);
        baseDetailVO.setCarBrandId(0L);
        baseDetailVO.setCarSeriesId(0L);
        baseDetailVO.setTransportGroupId(0L);
        baseDetailVO.setTransportGroupMode(0);
        baseDetailVO.setTransportGroupName("0");
        baseDetailVO.setConfirmMoneyTimeLocal("0");
        baseDetailVO.setConfirmMoneyTimeBj("0");
        baseDetailVO.setFinishTimeBj("0");
        baseDetailVO.setCancelTimeLocal("0");
        baseDetailVO.setCancelTimeBj("0");
        baseDetailVO.setBizAreaType(0);
        baseDetailVO.setAdultCount(0);
        baseDetailVO.setChildCount(0);
        baseDetailVO.setBagCount(0);
        baseDetailVO.setMaxBagCount(0);
        baseDetailVO.setCoordType("0");
        baseDetailVO.setFromLongitude(0.0D);
        baseDetailVO.setFromLatitude(0.0D);
        baseDetailVO.setToLongitude(0.0D);
        baseDetailVO.setToLatitude(0.0D);
        baseDetailVO.setTakenTimeBj("0");
        baseDetailVO.setTargetId("0");
        baseDetailVO.setTerminalId("0");
        baseDetailVO.setFixedLocationType(0);
        baseDetailVO.setFixedPosition("0");
        baseDetailVO.setPremiumOrderFlag(0);
        return baseDetailVO;
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
