package com.ctrip.dcs.application.query

import com.ctrip.dcs.application.query.api.AvailableDriversQuery
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class AvailableDriversExeQryTest extends Specification {
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ScheduleTaskRepository scheduleTaskRepository
    @Mock
    RecommendService recommendService
    @Mock
    SortModel sortModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @InjectMocks
    AvailableDriversExeQry availableDriversExeQry

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test query"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(scheduleTaskRepository.query(anyString())).thenReturn(scheduleTaskDO)
        when(recommendService.recommend(any(), any(), any())).thenReturn([sortModel])

        when:
        List<SortModel> result = availableDriversExeQry.query(new AvailableDriversQuery("dspOrderId", "0-0-0-0-0-0-0-0-0-0"))

        then:
        result.size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme