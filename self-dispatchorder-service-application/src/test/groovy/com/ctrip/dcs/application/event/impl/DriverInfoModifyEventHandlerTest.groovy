package com.ctrip.dcs.application.event.impl


import com.ctrip.dcs.application.event.dto.DriverInfoModifyEvent
import com.ctrip.dcs.domain.common.enums.BizAreaTypeEnum
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PhoneBridgeServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.gateway.ShoppingCommonServiceGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.gateway.DriverPlatformApiServiceGateway
import com.ctrip.dcs.domain.schedule.service.CommonService
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.Mockito.doNothing
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverInfoModifyEventHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDriverService driverService
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    PhoneBridgeServiceGateway phoneBridgeServiceGateway
    @Mock
    ShoppingCommonServiceGateway igtBasicServiceGateway
    @Mock
    IGTOrderQueryServiceGateway igtOrderQueryServiceGateway
    @Mock
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    MessageProviderService messageProvider
    @Mock
    DriverPlatformApiServiceGateway driverPlatformGateway
    @Mock
    CommonService commonService;

    @InjectMocks
    DriverInfoModifyEventHandler driverInfoModifyEventHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "test handle query driver failed"() {
        given: "Mock数据"
        when(driverService.queryDriverFromDb(any(), any(), any())).thenReturn(null)

        when: "执行校验方法"
        boolean result = driverInfoModifyEventHandler.handle(new DriverInfoModifyEvent(driverId: 1L, supplierId: 10L, isFromDrvGuide: fromDrvGuide))

        then: "验证校验结果"
        def exception = thrown(expectedException)


        where: "不同case输入"
        fromDrvGuide || expectedException
        true         || BizException
        false        || BizException
    }

    /** 查询无订单情况 */
    @Unroll
    def "test handle no orders"() {
        given: "Mock数据"
        when(driverService.queryDriverFromDb(any(), any(), any())).thenReturn(new DriverVO(driverId: 1L))
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), any())).thenReturn(Lists.newArrayList())

        when: "执行校验方法"
        boolean result = driverInfoModifyEventHandler.handle(new DriverInfoModifyEvent(driverId: 1L, supplierId: 1L, isFromDrvGuide: fromDrvGuide))

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        fromDrvGuide || expected
        false        || false
        true         || false
    }

    /** 查询用车消息 有订单情况 */
    @Unroll
    def "test handle qmq have orders"() {
        given: "Mock数据"
        when(driverService.queryDriverFromDb(any(), any(), any())).thenReturn(new DriverVO(driverId: 1L, driverName: "drvName", driverPhone: "13800000000", email: "<EMAIL>", driverLanguage: "cn,en", wechat: "d.webchat", driverPhoneAreaCode: "86", picUrl: "xxx/xx.jpg", isDrvGuide: fromDrvGuide, supplier: new SupplierVO(supplierId: 10L)))
        when(queryDspOrderService.queryNewOrderList(any(), any(), any(), anyList())).thenReturn(getOrderList())
        when(driverPlatformGateway.checkDrvGuideGrayFlow(new DriverVO(isDrvGuide: fromDrvGuide))).thenReturn(drvGuideFlow)
        when(dspOrderConfirmRecordRepository.findByDspOrderId(any(), any())).thenReturn(new DspOrderConfirmRecordVO())
        when(dspOrderConfirmRecordRepository.updateDriverRecord(any())).thenReturn(true)

        when: "执行校验方法"
        boolean result = driverInfoModifyEventHandler.handle(new DriverInfoModifyEvent(driverId: 1L, supplierId: 1L, isFromDrvGuide: fromDrvGuide))

        then: "验证校验结果"
        result == expected

        where: "不同case输入"
        fromDrvGuide || drvGuideFlow || expected
        false        || false        || true
        false        || true         || true
        true         || true         || true
    }

    /**
     * mock 订单列表数据
     */
    List<OrderVO> getOrderList() {
        def orderVOList = new ArrayList<OrderVO>()
        // 老订单
        orderVOList.add(new OrderVO(newOrder: false, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"13800000000",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "112345", dspOrderId:"154321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-01 12:00:00"))));
        // 境外订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.IGT, driverId: 1L, driverPhone:"13800000000",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "212345", dspOrderId:"254321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-02-01 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-02-01 13:00:00"))));
        // 非携程订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"13800000000",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "312345", dspOrderId:"354321", orderSourceCode: OrderSourceCodeEnum.KLOOK.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-02 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))));
        // 携程包车订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"13800000000",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "412345", dspOrderId:"454321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.DAY_RENTAL, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-03 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-03 12:00:00"))));
        // 携程接送订单
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 1L, driverPhone:"13800000000",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "512345", dspOrderId:"554321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-04 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-04 12:00:00"))));
        // 携程接送订单 司机ID不同
        orderVOList.add(new OrderVO(newOrder: true, bizAreaType: BizAreaTypeEnum.CHF, driverId: 2L, driverPhone:"13800000001",transportGroupId:10L, dspOrderVO: new DspOrderVO(userOrderId: "612345", dspOrderId:"654321", orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, estimatedUseTime: DateUtil.parseDateStr2Date("2024-01-05 12:00:00"), estimatedUseTimeBj: DateUtil.parseDateStr2Date("2024-01-05 12:00:00"))));


        return orderVOList
    }

}