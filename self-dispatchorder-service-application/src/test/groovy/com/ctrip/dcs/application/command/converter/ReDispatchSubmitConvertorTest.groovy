package com.ctrip.dcs.application.command.converter


import com.ctrip.dcs.application.provider.converter.ReDispatchSubmitConverter
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitRequestType
import spock.lang.Specification

class ReDispatchSubmitConvertorTest extends Specification {


    def "test redispatch convert"() {
        given:
        def request = new ReDispatchSubmitRequestType(userOrderId: "11",dspOrderId: "22",driverId: 1L,roleId: 1,reasonDetailId: 1,bookTime: "2024-08-10 10:00:00",driverOrderId: "D88",supplierId:1 ,reasonId: "rr")

        when: "执行校验方法"
        def order = ReDispatchSubmitConverter.converter(request)

        then: "验证校验结果"
        order != null == true
        order.getDspOrderId() == "22"
        order.getDriverOrderId() == "D88"
        order.getReasonId() == "rr"
        order.getSupplierId() == 1
    }

    def "test redispatch convert01"() {
        given:
        def request = new ReasonDetailVO(reasonId: 11)

        when: "执行校验方法"
        def order = ReDispatchSubmitConverter.converter(request)

        then: "验证校验结果"
        Objects.isNull(order) == false
    }
}
