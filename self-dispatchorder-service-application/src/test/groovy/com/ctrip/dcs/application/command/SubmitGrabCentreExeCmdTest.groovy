package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand
import com.ctrip.dcs.application.command.validator.SubmitGrabValidator
import com.ctrip.dcs.application.command.validator.ValidateException
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.GrabOrderType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import javax.annotation.Resource

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SubmitGrabCentreExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    GrabCentreRepository grabCentreGrabOrderRepository
    @Mock
    SelectGrabOrderRepository selectGrabOrderRepository
    @Mock
    SubmitGrabValidator validator
    @Mock
    MessageProviderService messageProvider
    @Mock
    TRocksProviderAdapter rocksProviderAdapter
    @Mock
    IdempotentCheckService idempotentCheckService
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @Mock
    QueryDriverService queryDriverService;
    @InjectMocks
    SubmitGrabCentreExeCmd submitGrabCentreExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(grabCentreGrabOrderRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])

        when:
        submitGrabCentreExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, null, ""))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }

    def "test execute 1"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
//        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 10, 33).getTime(), 0, 0, null, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [null], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", null, "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0))
        when(queryDspOrderService.query(anyString())).thenReturn(null)
        when(grabCentreGrabOrderRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])

        when:
        submitGrabCentreExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, null, ""))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }

    def "test execute 2"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
//        when(queryDspOrderService.query(anyString())).thenReturn(null)
        when(grabCentreGrabOrderRepository.find(anyString(), anyLong())).thenReturn(grabOrderDO)
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(validator.validate(any())).thenThrow(new ValidateException(ErrorCode.GRAB_ORDER_EXPIRE_ERROR))

        when:
        submitGrabCentreExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, null, ""))

        then:
        grabOrderDO.getDspOrderId() == "dspOrderId"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme