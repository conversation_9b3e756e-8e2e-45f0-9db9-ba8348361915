package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.application.command.api.ExecuteScheduleCommand
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteScheduleValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    MessageProviderService messageProducer
    @Mock
    private static DspOrderVO dspOrderVO
    @Mock
    private static DriverVO driverVO
    @Mock
    private static TransportGroupVO transportGroupVO
    @Mock
    private static ScheduleTaskDO scheduleTaskDO
    @Mock
    private static ScheduleDO scheduleDO
    @Mock
    private static ExecuteScheduleCommand command
    @Mock
    private Map<String,String> commonConfig;
    @InjectMocks
    ExecuteScheduleValidator executeScheduleValidator

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test validate"() {
        given:
        when(scheduleDO.getType()).thenReturn(type)
        when(scheduleDO.isShutdown()).thenReturn(shutdown)
        when(scheduleDO.isTimeout(dspOrderVO)).thenReturn(timeout)
        when(dspOrderVO.isDispatching()).thenReturn(dispatch)

        when:
        executeScheduleValidator.validate(command, dspOrderVO, scheduleDO, [scheduleTaskDO])

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == code//todo - validate something

        where:
        type                | shutdown | dispatch | timeout | executing || code
        ScheduleType.SYSTEM | true     | true     | true    | true      || ErrorCode.SCHEDULE_SHUTDOWN_ERROR
        ScheduleType.SYSTEM | false    | false    | true    | true      || ErrorCode.ORDER_STATUS_NOT_TO_BE_CONFIRMED_ERROR
        ScheduleType.SYSTEM | false    | true     | true    | true      || ErrorCode.SCHEDULE_TIMEOUT_ERROR
    }

    def "test validate 1"() {
        given:
        when(command.getRound()).thenReturn(1)
        when(scheduleDO.getRound()).thenReturn(2)

        when:
        executeScheduleValidator.validate(command, dspOrderVO, scheduleDO, [scheduleTaskDO])

        then:
        def e = thrown(ValidateException)
        e.getErrorCode() == ErrorCode.SCHEDULE_EXECUTING_ERROR//todo - validate something
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme