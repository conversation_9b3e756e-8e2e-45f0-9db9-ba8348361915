package com.ctrip.dcs.application.command.api

import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.*

class QueryIvrRecordCommandTest extends Specification {


    @InjectMocks
    QueryIvrRecordCommand queryIvrRecordCommand

    def setup() {
        MockitoAnnotations.initMocks(this)
    }
    def "test execute"() {

        when:
        QueryIvrRecordCommand command = new QueryIvrRecordCommand();
        command.setRecordGuid("1");
        command.setUserOrderId("11");
        command.setSupplyOrderId("1212");
        command.getRecordGuid();
        command.getSupplyOrderId();
        command.getUserOrderId()
        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme