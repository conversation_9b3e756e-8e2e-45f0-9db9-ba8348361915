package com.ctrip.dcs.application.command.converter

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.application.provider.converter.SaaSUpdateDspOrderConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import org.assertj.core.util.Lists
import spock.lang.Specification

class SaaSOperateDriverCarConverterTest extends Specification {


    def "test convert1"() {
        given:
        def code = "09011000";
        SaaSOperateDriverCarCommand command = getSelfReq()
        command.setCarId(null)
        command.setCarTypeId(null)
        command.setCarColorId(null)
        command.setCarSeriesId(null)
        when: "执行校验方法"
        def cmd = SaaSOperateDriverCarConverter.converterVehicleVO(command)
        then: "验证校验结果"
        cmd != null
    }

    def "test convert2"() {
        given:
        def code = "09011000";
        SaaSOperateDriverCarCommand command = getSelfReq()
        command.setCarId(1)
        command.setCarTypeId(1)
        command.setCarColorId(1)
        command.setCarSeriesId(1)
        when: "执行校验方法"
        def cmd = SaaSOperateDriverCarConverter.converterVehicleVO(command)
        then: "验证校验结果"
        cmd != null
    }

    def "test convert3"() {
        given:
        def code = "09011000";
        SaaSOperateDriverCarCommand command = getSelfReq()
        command.setDriverId("1")
        command.setCityId(1)
        when: "执行校验方法"
        def cmd = SaaSOperateDriverCarConverter.converterVehicleVO(command)
        then: "验证校验结果"
        cmd != null
    }

    def "test convert4"() {
        given:
        def code = "09011000";
        SaaSOperateDriverCarCommand command = getSelfReq()
        command.setDriverId(null)
        command.setCityId(null)
        when: "执行校验方法"
        def cmd = SaaSOperateDriverCarConverter.converterVehicleVO(command)
        then: "验证校验结果"
        cmd != null
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        saasAssignDriverRequestType.setNewProcess(Boolean.TRUE)
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }


    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}
