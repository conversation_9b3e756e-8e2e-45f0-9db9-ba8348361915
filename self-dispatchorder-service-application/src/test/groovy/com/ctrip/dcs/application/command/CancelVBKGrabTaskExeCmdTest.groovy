package com.ctrip.dcs.application.command

import cn.hutool.core.lang.Pair
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelVBKGrabTaskRequestType
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CancelVBKGrabTaskExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    DistributedLockService.DistributedLock lock
    @Mock
    MessageProviderService messageProducer
    @InjectMocks
    CancelVBKGrabTaskExeCmd cancelVBKGrabTaskExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(true)
        when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(anyString())).thenReturn(new VBKDriverGrabTaskDO(taskStatus: GrabTaskStatus.IN_PROGRESS.getCode()))
        when(vbkDriverGrabTaskRepository.cancelVBKGrabTask(anyString(), anyString(), anyLong())).thenReturn(0)
        when(vbkDriverGrabOrderRepository.queryByTaskIdAndStatus(anyString(), anyInt())).thenReturn([new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId")])

        when:
        Pair<Integer, Integer> result = cancelVBKGrabTaskExeCmd.execute(new CancelVBKGrabTaskRequestType(taskId: "1"))

        then:
        result != null
    }

    def "test execute error"() {
        given:
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(true)
        when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(anyString())).thenReturn(new VBKDriverGrabTaskDO(taskStatus: GrabTaskStatus.IN_PROGRESS.getCode(), supplierId: 0L))
        when(vbkDriverGrabTaskRepository.cancelVBKGrabTask(anyString(), anyString(), anyLong())).thenReturn(0)
        when(vbkDriverGrabOrderRepository.queryByTaskIdAndStatus(anyString(), anyInt())).thenReturn([new VBKDriverGrabOrderDO(dspOrderId: "dspOrderId")])

        when:
        Pair<Integer, Integer> result = cancelVBKGrabTaskExeCmd.execute(new CancelVBKGrabTaskRequestType(taskId: "1", supplierId: 1L))

        then:
        BizException e = thrown(BizException)
        e.getCode() == ErrorCode.CANCEL_GRAB_TASK_ERROR.getCode()
    }

    def "test sendVBKGrabTaskOperateEvent"() {
        when:
        def result = cancelVBKGrabTaskExeCmd.sendVBKGrabTaskOperateEvent(new CancelVBKGrabTaskRequestType(), 1)

        then:
        result == null
    }

    def "test send Shutdown Schedule Event"() {
        when:
        def result = cancelVBKGrabTaskExeCmd.sendShutdownScheduleEvent(["String"])

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme