package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.event.DspOrderCancelEvent
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.infrastructure.adapter.http.OldSelfOrderClient
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory
import com.ctrip.igt.framework.common.exception.BizException
import org.junit.platform.commons.util.StringUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class CancelDspOrderExeCmdTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderOperateRepository = Mock(DspOrderOperateRepository)
    def messageProviderService = Mock(MessageProviderService)
    def workBenchLogGateway = Mock(WorkBenchLogGateway)
    def workBenchLogMessageFactory = Mock(WorkBenchLogMessageFactory)
    def oldSelfOrderClient = Mock(OldSelfOrderClient)
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)
    def driverOrderGateway = Mock(DriverOrderGateway)
    def dateZoneConvertGateway = Mock(DateZoneConvertGateway)
    def vbkOperationRecordFactory = Mock(VBKOperationRecordFactory)
    def  businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)


    def executor = new CancelDspOrderExeCmd(
            dspOrderRepository: dspOrderRepository,
            dspOrderOperateRepository: dspOrderOperateRepository,
            messageProvider: messageProviderService,
            workBenchLogGateway: workBenchLogGateway,
            workBenchLogMessageFactory: workBenchLogMessageFactory,
            oldSelfOrderClient: oldSelfOrderClient,
            selfOrderQueryGateway: selfOrderQueryGateway,
            driverOrderGateway: driverOrderGateway,
            dateZoneConvertGateway:dateZoneConvertGateway,
            vbkOperationRecordFactory:vbkOperationRecordFactory,
            businessTemplateInfoConfig:businessTemplateInfoConfig

    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        def request = cmd
        def map = new HashMap()
        map.put(13,"ziyuan")
        dspOrderRepository.queryByDspOrderId(_) >> order
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> base
        oldSelfOrderClient.cancelSelfOrder(_) >> true
        businessTemplateInfoConfig.getCancelReasonMap()>> map


        when: "执行校验方法"
        executor.execute(request)

        then: "验证校验结果"
        true

        where:
        cmd             | flowSwitch | order  | base                               || res
        getcancelcmd1() | true       | get2() | _                                  || _
        getcancelcmd1() | true       | get1() | new BaseDetailVO(orderStatus: 200) || _
        getcancelcmd3()| true       | get1() | new BaseDetailVO(orderStatus: 200) || _

    }

    @Unroll
    def "test sendVBKOperationRecord"() {

        given: "Mock数据"
        def request = new DspOrderDO()
        def cmd = new CancelDspOrderCommand()

        dateZoneConvertGateway.getLocalTime(_,_) >> new Date()
        vbkOperationRecordFactory.createCancelDspOrderOperationRecord(_,_,_,_) >> null


        when: "执行校验方法"
        executor.sendVBKOperationRecord(request,cmd,500)

        then: "验证校验结果"
        request != null == true



    }

    @Unroll
    def "test execute 1"() {

        given: "Mock数据"
        def map = new HashMap()
        map.put(13,"ziyuan")
        dspOrderRepository.queryByDspOrderId(_) >> null
        oldSelfOrderClient.cancelSelfOrder(_) >> true
        businessTemplateInfoConfig.getCancelReasonMap()>> map

        when: "执行校验方法"
        def result = executor.execute(new CancelDspOrderCommand(cancelReason : "123"))

        then: "验证校验结果"
        result == null

    }

    def "DspOrderCancelEvent test 1"() {
        given: "Mock数据"

        def request = new DspOrderCancelEvent("1", "1", "1", 1, 1, "cancelReason",true,0L)


        when: "执行校验方法"
        String c = request.getCancelReason()
        boolean b = request.getForcedCancel()
        then: "验证校验结果"
        StringUtils.isNotBlank(c) == true
        b == true
    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"
        def request = cmd

        dspOrderRepository.queryByDspOrderId(_) >> order
        selfOrderQueryGateway.queryOrderBaseDetail(_, _) >> base
        oldSelfOrderClient.cancelSelfOrder(_) >> false


        when: "执行校验方法"
        executor.execute(request)

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res

        where:
        cmd             | flowSwitch | order  | base                               || res
        getcancelcmd1() | true       | get1() | new BaseDetailVO(orderStatus: 500) || "09015003"

    }

    DspOrderDO get1() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", orderStatus: 200, categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, connectMode: 2)
        return dspOrderDO
    }

    DspOrderDO get2() {
        def dspOrderDO = get1()
        dspOrderDO.setOrderStatus(900)
        return dspOrderDO
    }

    CancelDspOrderCommand getcancelcmd1() {
        def cmd = new CancelDspOrderCommand(userOrderId: "11", supplyOrderId: "11", cancelReasonId: 13
                , cancelReason: "资源确认超时取消", cancelRole: 1, cancelFineRate: BigDecimal.valueOf(20L)
                , orderFine: BigDecimal.valueOf(100L), hasLose: true, checkType: 0
                , noDriverCancel: 0, qCancelUrlId: 2, needBackQMainOrderStatus: false)

        return cmd
    }

    CancelDspOrderCommand getcancelcmd2() {
        def cmd = getcancelcmd1()
        cmd.setqCancelUrlId(1)
        return cmd
    }

    CancelDspOrderCommand getcancelcmd3() {
        def cmd = getcancelcmd1()
        cmd.setCancelReason("")
        return cmd
    }


}
