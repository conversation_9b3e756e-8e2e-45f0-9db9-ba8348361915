package com.ctrip.dcs.application.command.dispatchergrab

import com.ctrip.dcs.application.command.api.CreateDispatcherGrabOrderCommand
import com.ctrip.dcs.application.command.converter.DispatcherGrabOrderDOConverter
import com.ctrip.dcs.application.command.dto.DispatcherGrabOrderDTO
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.process.impl.RefuseAssignTransportProcess
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CreateDispatcherGrabOrderExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatcherGrabOrderDOConverter dispatcherGrabOrderConverter
    @Mock
    RefuseAssignTransportProcess process
    @Mock
    DispatcherGrabOrderDTO dto
    @InjectMocks
    CreateDispatcherGrabOrderExeCmd createDispatcherGrabOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        when:
        createDispatcherGrabOrderExeCmd.execute(new CreateDispatcherGrabOrderCommand([dto], 0))

        then:
        verify(process).execute(any())
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
