package com.ctrip.dcs.application.command.validator

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.domain.common.constants.SysConstants
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class AbstractSaaSOperateValidatorTest extends Specification {
    def testObj = new SaaSTripChangeDriverValidator()
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)

    def setup() {

        testObj.dcsVbkSupplierOrderGateway = dcsVbkSupplierOrderGateway
    }

    @Unroll
    def "validateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.validVehicleVONonNull(_) >> {}
        spy.validBusiness(_, _) >> {}
        spy.validDspOrderVONonNull(_) >> {}
        spy.validDriverVONonNull(_) >> {}
        when:
        def result = spy.validate(cmd, saaSBusinessVO)

        then: "验证返回结果里属性值是否符合预期"
        result != null == expectedResult
        where: "表格方式验证多种分支调用场景"
        cmd                                                | saaSBusinessVO       || expectedResult
        new SaaSOperateDriverCarCommand(newProcess: true)  | new SaaSBusinessVO() || true
        new SaaSOperateDriverCarCommand(newProcess: false) | new SaaSBusinessVO() || true
    }

    @Unroll
    def "isOpenChangeButtonTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        dcsVbkSupplierOrderGateway.queryOrderCanChangeDriverNew(_, _, _, _, _) >> queryRes

        when:
        def result = testObj.isOpenChangeButton(cmd, order)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cmd                                                                                                                       | order                        | queryRes || expectedResult
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_ROLE_SUPPLIER, newProcess: true)  | new DspOrderVO(takenType: 0) | false    || false
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_ROLE_SUPPLIER, newProcess: false) | new DspOrderVO(takenType: 0) | false    || true
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_ROLE_SAAS, newProcess: true)      | new DspOrderVO(takenType: 0) | false    || false
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_DISPATCH_TYPE, newProcess: true)  | new DspOrderVO(takenType: 0) | false    || true
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_DISPATCH_TYPE, newProcess: true)  | new DspOrderVO(takenType: 0) | true     || true
        new SaaSOperateDriverCarCommand(supplierId: 1L, assignRole: SysConstants.AssignRole.SYS_DISPATCH_TYPE, newProcess: true)  | new DspOrderVO() | true     || true
    }

    @Unroll
    def "validateSuppliersTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.validateSuppliers(cmd, saaSBusinessVO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cmd                                                                | saaSBusinessVO                                                                                                                                                                                                                                 || expectedResult
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: true)  | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1L), driverVO: new DriverVO(car: new CarVO(carId: 1L), supplier: new SupplierVO(supplierId: 1L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 1L, supplierId: 1L)) || null
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO(car: new CarVO(carId: 1L), supplier: new SupplierVO(supplierId: 1L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 1L, supplierId: 1L)) || null
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO(car: new CarVO(carId: 1L), supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 1L, supplierId: 1L)) || null
    }

    @Unroll
    def "validateSuppliersTestException"() {
        given: "设定相关方法入参"
        when:
        def  result = null
        try{
            testObj.validateSuppliers(cmd, saaSBusinessVO)
        }catch(BizException bizException){
            result = bizException.getCode()
        }



        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cmd                                                                | saaSBusinessVO                                                                                                                                                                                                                                 || expectedResult
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 1L, supplierId: 1L)) || null
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 1L, supplierId: 12L)) || "09015112"
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( car: new CarVO(carId: 1L),supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 2L, supplierId: 1L)) || null
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: false) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( car: new CarVO(carId: 1L),supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 2L, supplierId: 12L)) ||  "09015112"
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: true) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( car: new CarVO(carId: 1L),supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L])), vehicleVO: new VehicleVO(carId: 2L, supplierId: 12L)) || null
        new SaaSOperateDriverCarCommand(supplierId: 1L, newProcess: true) | new SaaSBusinessVO(dspOrderVO: new DspOrderVO(supplierId: 1l), driverVO: new DriverVO( car: new CarVO(carId: 1L),supplier: new SupplierVO(supplierId: 2L, dispatchSupplierIdList: [1L]))) || null

    }
}
